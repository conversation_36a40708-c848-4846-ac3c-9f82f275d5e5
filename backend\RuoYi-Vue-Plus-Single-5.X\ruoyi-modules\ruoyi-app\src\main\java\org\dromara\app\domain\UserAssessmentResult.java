package org.dromara.app.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户评估结果实体
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("user_assessment_result")
public class UserAssessmentResult extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 结果ID
     */
    @TableId(type = IdType.AUTO)
    private Long resultId;

    /**
     * 评估记录ID
     */
    private Long recordId;

    /**
     * 问题ID
     */
    private Long questionId;

    /**
     * 问题类型
     */
    private String questionType;

    /**
     * 问题类别
     */
    private String category;

    /**
     * 选择的选项ID（单选题）
     */
    private Long selectedOptionId;

    /**
     * 选择的分值（量表题）
     */
    private Integer selectedValue;

    /**
     * 得分
     */
    private Integer score;

    /**
     * 答题时间
     */
    private Date answerTime;

    /**
     * 答题耗时（秒）
     */
    private Integer timeSpent;
}
