package org.dromara.app.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 面试结果响应视图对象
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
public class InterviewResultResponseVo {

    /**
     * 面试结果摘要
     */
    @Data
    public static class InterviewResultSummary implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        private String id;
        private String sessionId;
        private String jobName;
        private String company;
        private String mode;
        private String date;
        private String duration;
        private Integer totalScore;
        private String rank;
        private String rankText;
        private Integer percentile;
        private Integer answeredQuestions;
        private Integer totalQuestions;
        private String status;
        private List<String> topStrengths;
        private List<String> topWeaknesses;
    }

    /**
     * 维度得分
     */
    @Data
    public static class DimensionScore implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        private String dimension;
        private Integer score;
        private Integer maxScore;
        private Integer percentile;
        private String description;
        private List<String> strengths;
        private List<String> weaknesses;
        private List<String> recommendations;
    }

    /**
     * 问题答案分析
     */
    @Data
    public static class QuestionAnalysis implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        private String id;
        private String questionId;
        private String question;
        private String category;
        private Integer difficulty;
        private String answer;
        private Integer score;
        private Integer audioScore;
        private Integer videoScore;
        private Integer textScore;
        private String feedback;
        private List<String> strengths;
        private List<String> weaknesses;
        private List<String> keywordMatches;
        private String idealAnswer;
        private Integer timeSpent;
        private Integer timeLimit;
    }

    /**
     * 详细结果
     */
    @Data
    public static class InterviewResultDetail extends InterviewResultSummary {
        @Serial
        private static final long serialVersionUID = 1L;

        private List<DimensionScore> dimensionScores;
        private List<QuestionAnalysis> questionAnalyses;
        private String overallFeedback;
        private AudioMetrics audioMetrics;
        private VideoMetrics videoMetrics;
    }

    /**
     * 音频指标
     */
    @Data
    public static class AudioMetrics implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        private Integer clarity;
        private Integer fluency;
        private Integer confidence;
        private Integer pace;
        private Integer overall;
    }

    /**
     * 视频指标
     */
    @Data
    public static class VideoMetrics implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        private Integer eyeContact;
        private Integer posture;
        private Integer expressions;
        private Integer gestures;
        private Integer overall;
    }

    /**
     * 性能指标
     */
    @Data
    public static class PerformanceMetrics implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        private Integer technical;
        private Integer communication;
        private Integer problemSolving;
        private Integer teamwork;
        private Integer leadership;
        private Integer creativity;
        private Map<String, Integer> detailedMetrics;
        private Map<String, Integer> industryAverage;
        private List<String> skillGaps;
        private List<String> skillStrengths;
    }

    /**
     * 学习资源
     */
    @Data
    public static class LearningResource implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        private String id;
        private String title;
        private String type;
        private String description;
        private String duration;
        private String difficulty;
        private String url;
        private String imageUrl;
        private List<String> tags;
        private Integer relevanceScore;
    }

    /**
     * 提升计划
     */
    @Data
    public static class ImprovementPlan implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        private List<ImprovementArea> areas;
        private List<String> shortTermGoals;
        private List<String> mediumTermGoals;
        private List<String> longTermGoals;
        private List<LearningResource> recommendedResources;
    }

    /**
     * 提升领域
     */
    @Data
    public static class ImprovementArea implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        private String name;
        private Integer currentLevel;
        private Integer targetLevel;
        private String timeframe;
        private List<String> actions;
    }

    /**
     * 分享结果响应
     */
    @Data
    public static class ShareResultResponse implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        private Boolean success;
        private String shareUrl;
    }

    /**
     * 保存历史记录响应
     */
    @Data
    public static class SaveHistoryResponse implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        private Boolean success;
        private String historyId;
    }

}
