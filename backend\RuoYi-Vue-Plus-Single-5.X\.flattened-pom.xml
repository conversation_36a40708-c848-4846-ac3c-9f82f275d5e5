<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.dromara</groupId>
  <artifactId>ruoyi-vue-plus</artifactId>
  <version>5.4.0</version>
  <packaging>pom</packaging>
  <name>RuoYi-Vue-Plus</name>
  <description>Dromara</description>
  <url>https://gitee.com/dromara/RuoYi-Vue-Plus</url>
  <modules>
    <module>ruoyi-admin</module>
    <module>ruoyi-common</module>
    <module>ruoyi-extend</module>
    <module>ruoyi-modules</module>
  </modules>
  <properties>
    <sms4j.version>3.3.4</sms4j.version>
    <velocity.version>2.3</velocity.version>
    <lock4j.version>2.2.7</lock4j.version>
    <p6spy.version>3.9.1</p6spy.version>
    <warm-flow.version>1.7.3</warm-flow.version>
    <maven-war-plugin.version>3.2.2</maven-war-plugin.version>
    <lombok.version>1.18.36</lombok.version>
    <satoken.version>1.42.0</satoken.version>
    <fastjson.version>1.2.83</fastjson.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <spring-boot.version>3.4.6</spring-boot.version>
    <hutool.version>5.8.35</hutool.version>
    <redisson.version>3.45.1</redisson.version>
    <mapstruct-plus.version>1.4.8</mapstruct-plus.version>
    <mybatis-plus.version>3.5.12</mybatis-plus.version>
    <skipTests>true</skipTests>
    <therapi-javadoc.version>0.15.0</therapi-javadoc.version>
    <maven-jar-plugin.version>3.2.2</maven-jar-plugin.version>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <dynamic-ds.version>4.3.1</dynamic-ds.version>
    <springdoc.version>2.8.8</springdoc.version>
    <aws.sdk.version>2.28.22</aws.sdk.version>
    <mapstruct-plus.lombok.version>0.2.0</mapstruct-plus.lombok.version>
    <ip2region.version>2.7.0</ip2region.version>
    <fastexcel.version>1.2.0</fastexcel.version>
    <maven-compiler-plugin.version>3.11.0</maven-compiler-plugin.version>
    <anyline.version>8.7.2-20250101</anyline.version>
    <java.version>17</java.version>
    <spring-boot-admin.version>3.4.7</spring-boot-admin.version>
    <mybatis.version>3.5.16</mybatis.version>
    <justauth.version>1.16.7</justauth.version>
    <snailjob.version>1.5.0</snailjob.version>
    <flatten-maven-plugin.version>1.3.0</flatten-maven-plugin.version>
    <maven-surefire-plugin.version>3.1.2</maven-surefire-plugin.version>
    <revision>5.4.0</revision>
    <bouncycastle.version>1.80</bouncycastle.version>
  </properties>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-dependencies</artifactId>
        <version>${spring-boot.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>cn.hutool</groupId>
        <artifactId>hutool-bom</artifactId>
        <version>${hutool.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.dromara.warm</groupId>
        <artifactId>warm-flow-mybatis-plus-sb3-starter</artifactId>
        <version>${warm-flow.version}</version>
      </dependency>
      <dependency>
        <groupId>org.dromara.warm</groupId>
        <artifactId>warm-flow-plugin-ui-sb-web</artifactId>
        <version>${warm-flow.version}</version>
      </dependency>
      <dependency>
        <groupId>me.zhyd.oauth</groupId>
        <artifactId>JustAuth</artifactId>
        <version>${justauth.version}</version>
      </dependency>
      <dependency>
        <groupId>org.dromara</groupId>
        <artifactId>ruoyi-common-bom</artifactId>
        <version>5.4.0</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.springdoc</groupId>
        <artifactId>springdoc-openapi-starter-webmvc-api</artifactId>
        <version>${springdoc.version}</version>
      </dependency>
      <dependency>
        <groupId>com.github.therapi</groupId>
        <artifactId>therapi-runtime-javadoc</artifactId>
        <version>${therapi-javadoc.version}</version>
      </dependency>
      <dependency>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok</artifactId>
        <version>${lombok.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.idev.excel</groupId>
        <artifactId>fastexcel</artifactId>
        <version>${fastexcel.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.velocity</groupId>
        <artifactId>velocity-engine-core</artifactId>
        <version>${velocity.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.dev33</groupId>
        <artifactId>sa-token-spring-boot3-starter</artifactId>
        <version>${satoken.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.dev33</groupId>
        <artifactId>sa-token-jwt</artifactId>
        <version>${satoken.version}</version>
        <exclusions>
          <exclusion>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>cn.dev33</groupId>
        <artifactId>sa-token-core</artifactId>
        <version>${satoken.version}</version>
      </dependency>
      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>dynamic-datasource-spring-boot3-starter</artifactId>
        <version>${dynamic-ds.version}</version>
      </dependency>
      <dependency>
        <groupId>org.mybatis</groupId>
        <artifactId>mybatis</artifactId>
        <version>${mybatis.version}</version>
      </dependency>
      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
        <version>${mybatis-plus.version}</version>
      </dependency>
      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-jsqlparser</artifactId>
        <version>${mybatis-plus.version}</version>
      </dependency>
      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-annotation</artifactId>
        <version>${mybatis-plus.version}</version>
      </dependency>
      <dependency>
        <groupId>p6spy</groupId>
        <artifactId>p6spy</artifactId>
        <version>${p6spy.version}</version>
      </dependency>
      <dependency>
        <groupId>software.amazon.awssdk</groupId>
        <artifactId>s3</artifactId>
        <version>${aws.sdk.version}</version>
      </dependency>
      <dependency>
        <groupId>software.amazon.awssdk</groupId>
        <artifactId>s3-transfer-manager</artifactId>
        <version>${aws.sdk.version}</version>
      </dependency>
      <dependency>
        <groupId>software.amazon.awssdk</groupId>
        <artifactId>netty-nio-client</artifactId>
        <version>${aws.sdk.version}</version>
      </dependency>
      <dependency>
        <groupId>org.dromara.sms4j</groupId>
        <artifactId>sms4j-spring-boot-starter</artifactId>
        <version>${sms4j.version}</version>
      </dependency>
      <dependency>
        <groupId>de.codecentric</groupId>
        <artifactId>spring-boot-admin-starter-server</artifactId>
        <version>${spring-boot-admin.version}</version>
      </dependency>
      <dependency>
        <groupId>de.codecentric</groupId>
        <artifactId>spring-boot-admin-starter-client</artifactId>
        <version>${spring-boot-admin.version}</version>
      </dependency>
      <dependency>
        <groupId>org.redisson</groupId>
        <artifactId>redisson-spring-boot-starter</artifactId>
        <version>${redisson.version}</version>
      </dependency>
      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>lock4j-redisson-spring-boot-starter</artifactId>
        <version>${lock4j.version}</version>
      </dependency>
      <dependency>
        <groupId>com.aizuda</groupId>
        <artifactId>snail-job-client-starter</artifactId>
        <version>${snailjob.version}</version>
      </dependency>
      <dependency>
        <groupId>com.aizuda</groupId>
        <artifactId>snail-job-client-job-core</artifactId>
        <version>${snailjob.version}</version>
      </dependency>
      <dependency>
        <groupId>org.bouncycastle</groupId>
        <artifactId>bcprov-jdk15to18</artifactId>
        <version>${bouncycastle.version}</version>
      </dependency>
      <dependency>
        <groupId>io.github.linpeilie</groupId>
        <artifactId>mapstruct-plus-spring-boot-starter</artifactId>
        <version>${mapstruct-plus.version}</version>
      </dependency>
      <dependency>
        <groupId>org.lionsoul</groupId>
        <artifactId>ip2region</artifactId>
        <version>${ip2region.version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>fastjson</artifactId>
        <version>${fastjson.version}</version>
      </dependency>
      <dependency>
        <groupId>org.dromara</groupId>
        <artifactId>ruoyi-system</artifactId>
        <version>5.4.0</version>
      </dependency>
      <dependency>
        <groupId>org.dromara</groupId>
        <artifactId>ruoyi-job</artifactId>
        <version>5.4.0</version>
      </dependency>
      <dependency>
        <groupId>org.dromara</groupId>
        <artifactId>ruoyi-generator</artifactId>
        <version>5.4.0</version>
      </dependency>
      <dependency>
        <groupId>org.dromara</groupId>
        <artifactId>ruoyi-demo</artifactId>
        <version>5.4.0</version>
      </dependency>
      <dependency>
        <groupId>org.dromara</groupId>
        <artifactId>ruoyi-app</artifactId>
        <version>5.4.0</version>
      </dependency>
      <dependency>
        <groupId>org.dromara</groupId>
        <artifactId>ruoyi-workflow</artifactId>
        <version>5.4.0</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <repositories>
    <repository>
      <releases>
        <enabled>true</enabled>
      </releases>
      <id>public</id>
      <name>huawei nexus</name>
      <url>https://mirrors.huaweicloud.com/repository/maven/</url>
    </repository>
  </repositories>
  <pluginRepositories>
    <pluginRepository>
      <releases>
        <enabled>true</enabled>
      </releases>
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
      <id>public</id>
      <name>huawei nexus</name>
      <url>https://mirrors.huaweicloud.com/repository/maven/</url>
    </pluginRepository>
  </pluginRepositories>
  <build>
    <resources>
      <resource>
        <filtering>false</filtering>
        <directory>src/main/resources</directory>
      </resource>
      <resource>
        <filtering>true</filtering>
        <directory>src/main/resources</directory>
        <includes>
          <include>application*</include>
          <include>bootstrap*</include>
          <include>banner*</include>
        </includes>
      </resource>
    </resources>
    <plugins>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>${maven-compiler-plugin.version}</version>
        <configuration>
          <source>${java.version}</source>
          <target>${java.version}</target>
          <encoding>${project.build.sourceEncoding}</encoding>
          <annotationProcessorPaths>
            <path>
              <groupId>com.github.therapi</groupId>
              <artifactId>therapi-runtime-javadoc-scribe</artifactId>
              <version>${therapi-javadoc.version}</version>
            </path>
            <path>
              <groupId>org.projectlombok</groupId>
              <artifactId>lombok</artifactId>
              <version>${lombok.version}</version>
            </path>
            <path>
              <groupId>org.springframework.boot</groupId>
              <artifactId>spring-boot-configuration-processor</artifactId>
              <version>${spring-boot.version}</version>
            </path>
            <path>
              <groupId>io.github.linpeilie</groupId>
              <artifactId>mapstruct-plus-processor</artifactId>
              <version>${mapstruct-plus.version}</version>
            </path>
            <path>
              <groupId>org.projectlombok</groupId>
              <artifactId>lombok-mapstruct-binding</artifactId>
              <version>${mapstruct-plus.lombok.version}</version>
            </path>
          </annotationProcessorPaths>
          <compilerArgs>
            <arg>-parameters</arg>
          </compilerArgs>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-surefire-plugin</artifactId>
        <version>${maven-surefire-plugin.version}</version>
        <configuration>
          <argLine>-Dfile.encoding=UTF-8</argLine>
          <groups>${profiles.active}</groups>
          <excludedGroups>exclude</excludedGroups>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>flatten-maven-plugin</artifactId>
        <version>${flatten-maven-plugin.version}</version>
        <executions>
          <execution>
            <id>flatten</id>
            <phase>process-resources</phase>
            <goals>
              <goal>flatten</goal>
            </goals>
          </execution>
          <execution>
            <id>flatten.clean</id>
            <phase>clean</phase>
            <goals>
              <goal>clean</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <updatePomFile>true</updatePomFile>
          <flattenMode>resolveCiFriendliesOnly</flattenMode>
        </configuration>
      </plugin>
    </plugins>
  </build>
  <profiles>
    <profile>
      <id>local</id>
      <properties>
        <monitor.username>ruoyi</monitor.username>
        <logging.level>info</logging.level>
        <monitor.password>123456</monitor.password>
        <profiles.active>local</profiles.active>
      </properties>
    </profile>
    <profile>
      <id>dev</id>
      <activation>
        <activeByDefault>true</activeByDefault>
      </activation>
      <properties>
        <monitor.username>ruoyi</monitor.username>
        <logging.level>info</logging.level>
        <monitor.password>123456</monitor.password>
        <profiles.active>dev</profiles.active>
      </properties>
    </profile>
    <profile>
      <id>prod</id>
      <properties>
        <monitor.username>ruoyi</monitor.username>
        <logging.level>warn</logging.level>
        <monitor.password>123456</monitor.password>
        <profiles.active>prod</profiles.active>
      </properties>
    </profile>
  </profiles>
</project>
