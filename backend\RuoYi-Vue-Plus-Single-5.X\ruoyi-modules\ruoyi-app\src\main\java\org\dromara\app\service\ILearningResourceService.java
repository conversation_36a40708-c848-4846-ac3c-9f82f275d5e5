package org.dromara.app.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.dromara.app.domain.LearningResource;

import java.util.List;
import java.util.Map;

/**
 * 学习资源服务接口
 *
 * <AUTHOR>
 */
public interface ILearningResourceService {

    /**
     * 创建学习资源
     *
     * @param resource 学习资源
     * @return 是否成功
     */
    boolean createResource(LearningResource resource);

    /**
     * 更新学习资源
     *
     * @param resource 学习资源
     * @return 是否成功
     */
    boolean updateResource(LearningResource resource);

    /**
     * 删除学习资源
     *
     * @param resourceId 资源ID
     * @return 是否成功
     */
    boolean deleteResource(Long resourceId);

    /**
     * 获取资源详情
     *
     * @param resourceId 资源ID
     * @return 学习资源
     */
    LearningResource getResourceById(Long resourceId);

    /**
     * 分页查询资源
     *
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param skillArea 技能领域
     * @param type 资源类型
     * @param difficulty 难度等级
     * @return 分页结果
     */
    Page<LearningResource> getResourcesPage(Integer pageNum, Integer pageSize, 
                                           String skillArea, String type, String difficulty);

    /**
     * 根据技能领域和难度获取推荐资源
     *
     * @param skillArea 技能领域
     * @param difficulty 难度等级
     * @param limit 限制数量
     * @return 资源列表
     */
    List<LearningResource> getRecommendedResources(String skillArea, String difficulty, Integer limit);

    /**
     * 根据资源类型获取资源
     *
     * @param type 资源类型
     * @param limit 限制数量
     * @return 资源列表
     */
    List<LearningResource> getResourcesByType(String type, Integer limit);

    /**
     * 获取热门资源
     *
     * @param limit 限制数量
     * @return 资源列表
     */
    List<LearningResource> getPopularResources(Integer limit);

    /**
     * 获取免费资源
     *
     * @param skillArea 技能领域
     * @param limit 限制数量
     * @return 资源列表
     */
    List<LearningResource> getFreeResources(String skillArea, Integer limit);

    /**
     * 根据标签获取资源
     *
     * @param tag 标签
     * @param limit 限制数量
     * @return 资源列表
     */
    List<LearningResource> getResourcesByTag(String tag, Integer limit);

    /**
     * 搜索资源
     *
     * @param keyword 关键词
     * @param limit 限制数量
     * @return 资源列表
     */
    List<LearningResource> searchResources(String keyword, Integer limit);

    /**
     * 评估资源质量
     *
     * @param resourceId 资源ID
     * @param qualityAssessment 质量评估
     * @return 是否成功
     */
    boolean assessResourceQuality(Long resourceId, LearningResource.QualityAssessment qualityAssessment);

    /**
     * 更新资源评分
     *
     * @param resourceId 资源ID
     * @param rating 评分
     * @return 是否成功
     */
    boolean updateResourceRating(Long resourceId, Double rating);

    /**
     * 增加资源浏览次数
     *
     * @param resourceId 资源ID
     * @return 是否成功
     */
    boolean incrementViewCount(Long resourceId);

    /**
     * 增加资源学习人数
     *
     * @param resourceId 资源ID
     * @return 是否成功
     */
    boolean incrementLearnerCount(Long resourceId);

    /**
     * 增加资源完成人数
     *
     * @param resourceId 资源ID
     * @return 是否成功
     */
    boolean incrementCompletionCount(Long resourceId);

    /**
     * 获取资源统计信息
     *
     * @return 统计信息
     */
    Map<String, Object> getResourceStatistics();

    /**
     * 获取技能领域分布
     *
     * @return 技能领域分布
     */
    List<Map<String, Object>> getSkillAreaDistribution();

    /**
     * 获取资源类型分布
     *
     * @return 资源类型分布
     */
    List<Map<String, Object>> getResourceTypeDistribution();

    /**
     * 获取顶级提供者
     *
     * @param limit 限制数量
     * @return 提供者排行
     */
    List<Map<String, Object>> getTopProviders(Integer limit);

    /**
     * 批量导入资源
     *
     * @param resources 资源列表
     * @return 导入成功数量
     */
    int batchImportResources(List<LearningResource> resources);

    /**
     * 资源推荐排序
     *
     * @param resources 原始资源列表
     * @param userPreferences 用户偏好
     * @return 排序后的资源列表
     */
    List<LearningResource> sortResourcesByRecommendation(List<LearningResource> resources, 
                                                        Map<String, Object> userPreferences);

    /**
     * 获取相似资源
     *
     * @param resourceId 资源ID
     * @param limit 限制数量
     * @return 相似资源列表
     */
    List<LearningResource> getSimilarResources(Long resourceId, Integer limit);

    /**
     * 资源质量评估
     *
     * @param resourceId 资源ID
     * @return 质量评估结果
     */
    LearningResource.QualityAssessment performQualityAssessment(Long resourceId);

    /**
     * 获取资源标签云
     *
     * @param limit 限制数量
     * @return 标签统计
     */
    Map<String, Integer> getResourceTagCloud(Integer limit);

    /**
     * 获取个性化推荐资源
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 个性化推荐资源列表
     */
    List<LearningResource> getPersonalizedRecommendations(Long userId, Integer limit);

    /**
     * 根据技能差距推荐资源
     *
     * @param skillAreas 技能领域列表
     * @param difficulty 难度等级
     * @param limit 限制数量
     * @return 推荐资源列表
     */
    List<LearningResource> getResourcesBySkillGaps(List<String> skillAreas, String difficulty, Integer limit);

    /**
     * 获取协同过滤推荐资源
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 推荐资源列表
     */
    List<LearningResource> getCollaborativeFilteringRecommendations(Long userId, Integer limit);

    /**
     * 获取趋势资源
     *
     * @param days 天数
     * @param limit 限制数量
     * @return 趋势资源列表
     */
    List<LearningResource> getTrendingResources(Integer days, Integer limit);
}