package org.dromara.app.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户成长档案实体
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("user_growth_profile")
public class UserGrowthProfile extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 档案ID
     */
    @TableId(type = IdType.AUTO)
    private Long profileId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 当前阶段：new_user-新用户，beginner-初学者，intermediate-中级，advanced-高级，expert-专家
     */
    private String currentStage;

    /**
     * 加入日期
     */
    private Date joinDate;

    /**
     * 最后活跃日期
     */
    private Date lastActiveDate;

    /**
     * 总面试次数
     */
    private Integer totalInterviews;

    /**
     * 初始总体得分
     */
    private Integer initialOverallScore;

    /**
     * 初始专业知识得分
     */
    private Integer initialProfessionalKnowledge;

    /**
     * 初始逻辑思维得分
     */
    private Integer initialLogicalThinking;

    /**
     * 初始语言表达得分
     */
    private Integer initialLanguageExpression;

    /**
     * 初始抗压能力得分
     */
    private Integer initialStressResistance;

    /**
     * 初始团队协作得分
     */
    private Integer initialTeamCollaboration;

    /**
     * 初始创新能力得分
     */
    private Integer initialInnovation;

    /**
     * 当前总体得分
     */
    private Integer currentOverallScore;

    /**
     * 当前专业知识得分
     */
    private Integer currentProfessionalKnowledge;

    /**
     * 当前逻辑思维得分
     */
    private Integer currentLogicalThinking;

    /**
     * 当前语言表达得分
     */
    private Integer currentLanguageExpression;

    /**
     * 当前抗压能力得分
     */
    private Integer currentStressResistance;

    /**
     * 当前团队协作得分
     */
    private Integer currentTeamCollaboration;

    /**
     * 当前创新能力得分
     */
    private Integer currentInnovation;

    /**
     * 进步率（百分比）
     */
    private Integer improvementRate;

    /**
     * 目标职位
     */
    private String targetPosition;

    /**
     * 学习目标列表（JSON格式）
     */
    private String learningGoals;

    /**
     * 成就列表（JSON格式）
     */
    private String achievements;

    /**
     * 连续学习天数
     */
    private Integer continuousLearningDays;

    /**
     * 已完成课程数
     */
    private Integer completedCourses;
}
