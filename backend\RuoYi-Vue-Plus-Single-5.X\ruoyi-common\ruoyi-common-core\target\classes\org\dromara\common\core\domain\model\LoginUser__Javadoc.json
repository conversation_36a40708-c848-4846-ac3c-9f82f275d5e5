{"doc": "\n 登录用户身份权限\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "userId", "doc": "\n 用户ID\r\n"}, {"name": "deptId", "doc": "\n 部门ID\r\n"}, {"name": "deptCategory", "doc": "\n 部门类别编码\r\n"}, {"name": "deptName", "doc": "\n 部门名\r\n"}, {"name": "token", "doc": "\n 用户唯一标识\r\n"}, {"name": "userType", "doc": "\n 用户类型\r\n"}, {"name": "loginTime", "doc": "\n 登录时间\r\n"}, {"name": "expireTime", "doc": "\n 过期时间\r\n"}, {"name": "ipaddr", "doc": "\n 登录IP地址\r\n"}, {"name": "loginLocation", "doc": "\n 登录地点\r\n"}, {"name": "browser", "doc": "\n 浏览器类型\r\n"}, {"name": "os", "doc": "\n 操作系统\r\n"}, {"name": "menuPermission", "doc": "\n 菜单权限\r\n"}, {"name": "rolePermission", "doc": "\n 角色权限\r\n"}, {"name": "username", "doc": "\n 用户名\r\n"}, {"name": "nickname", "doc": "\n 用户昵称\r\n"}, {"name": "roles", "doc": "\n 角色对象\r\n"}, {"name": "posts", "doc": "\n 岗位对象\r\n"}, {"name": "roleId", "doc": "\n 数据权限 当前角色ID\r\n"}, {"name": "client<PERSON>ey", "doc": "\n 客户端\r\n"}, {"name": "deviceType", "doc": "\n 设备类型\r\n"}], "enumConstants": [], "methods": [{"name": "getLoginId", "paramTypes": [], "doc": "\n 获取登录id\r\n"}], "constructors": []}