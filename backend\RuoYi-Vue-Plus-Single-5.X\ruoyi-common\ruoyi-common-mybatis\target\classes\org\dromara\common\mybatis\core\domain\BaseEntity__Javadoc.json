{"doc": "\n Entity基类\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "searchValue", "doc": "\n 搜索值\r\n"}, {"name": "createDept", "doc": "\n 创建部门\r\n"}, {"name": "createBy", "doc": "\n 创建者\r\n"}, {"name": "createTime", "doc": "\n 创建时间\r\n"}, {"name": "updateBy", "doc": "\n 更新者\r\n"}, {"name": "updateTime", "doc": "\n 更新时间\r\n"}, {"name": "params", "doc": "\n 请求参数\r\n"}], "enumConstants": [], "methods": [], "constructors": []}