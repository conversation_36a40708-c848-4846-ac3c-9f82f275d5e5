package org.dromara.app.task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.PaymentOrder;
import org.dromara.app.mapper.PaymentOrderMapper;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 支付token清理定时任务
 * 定期清理过期的支付token，提高系统安全性
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PaymentTokenCleanupTask {

    private final PaymentOrderMapper paymentOrderMapper;

    /**
     * 清理过期的支付token
     * 每小时执行一次，清理过期超过1小时的token
     */
    @Scheduled(cron = "0 0 * * * ?") // 每小时执行一次
    public void cleanupExpiredTokens() {
        try {
            log.info("开始清理过期的支付token");

            // 查询过期超过1小时的token
            LocalDateTime cutoffTime = LocalDateTime.now().minusHours(1);

            LambdaQueryWrapper<PaymentOrder> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.isNotNull(PaymentOrder::getPayToken)
                .le(PaymentOrder::getPayTokenExpireTime, cutoffTime)
                .eq(PaymentOrder::getPayTokenUsed, false);

            List<PaymentOrder> expiredTokenOrders = paymentOrderMapper.selectList(queryWrapper);

            if (!expiredTokenOrders.isEmpty()) {
                // 批量更新：清空过期的token信息
                LambdaUpdateWrapper<PaymentOrder> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.in(PaymentOrder::getOrderId,
                        expiredTokenOrders.stream()
                            .map(PaymentOrder::getOrderId)
                            .toList())
                    .set(PaymentOrder::getPayToken, null)
                    .set(PaymentOrder::getPayTokenExpireTime, null)
                    .set(PaymentOrder::getPayTokenUsed, true);

                int updatedCount = paymentOrderMapper.update(null, updateWrapper);
                log.info("成功清理过期支付token，清理数量：{}", updatedCount);
            } else {
                log.info("没有需要清理的过期支付token");
            }

        } catch (Exception e) {
            log.error("清理过期支付token失败：{}", e.getMessage(), e);
        }
    }

    /**
     * 清理已完成订单的token
     * 每天凌晨2点执行一次，清理已支付订单的token
     */
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
    public void cleanupPaidOrderTokens() {
        try {
            log.info("开始清理已支付订单的token");

            // 查询已支付但token未清理的订单
            LambdaQueryWrapper<PaymentOrder> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(PaymentOrder::getStatus, "paid")
                .isNotNull(PaymentOrder::getPayToken);

            List<PaymentOrder> paidOrders = paymentOrderMapper.selectList(queryWrapper);

            if (!paidOrders.isEmpty()) {
                // 批量更新：清空已支付订单的token信息
                LambdaUpdateWrapper<PaymentOrder> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.in(PaymentOrder::getOrderId,
                        paidOrders.stream()
                            .map(PaymentOrder::getOrderId)
                            .toList())
                    .set(PaymentOrder::getPayToken, null)
                    .set(PaymentOrder::getPayTokenExpireTime, null)
                    .set(PaymentOrder::getPayTokenUsed, true);

                int updatedCount = paymentOrderMapper.update(null, updateWrapper);
                log.info("成功清理已支付订单的token，清理数量：{}", updatedCount);
            } else {
                log.info("没有需要清理的已支付订单token");
            }

        } catch (Exception e) {
            log.error("清理已支付订单token失败：{}", e.getMessage(), e);
        }
    }

    /**
     * 清理已取消订单的token
     * 每天凌晨3点执行一次，清理已取消订单的token
     */
    @Scheduled(cron = "0 0 3 * * ?") // 每天凌晨3点执行
    public void cleanupCancelledOrderTokens() {
        try {
            log.info("开始清理已取消订单的token");

            // 查询已取消但token未清理的订单
            LambdaQueryWrapper<PaymentOrder> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(PaymentOrder::getStatus, "cancelled", "expired")
                .isNotNull(PaymentOrder::getPayToken);

            List<PaymentOrder> cancelledOrders = paymentOrderMapper.selectList(queryWrapper);

            if (!cancelledOrders.isEmpty()) {
                // 批量更新：清空已取消订单的token信息
                LambdaUpdateWrapper<PaymentOrder> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.in(PaymentOrder::getOrderId,
                        cancelledOrders.stream()
                            .map(PaymentOrder::getOrderId)
                            .toList())
                    .set(PaymentOrder::getPayToken, null)
                    .set(PaymentOrder::getPayTokenExpireTime, null)
                    .set(PaymentOrder::getPayTokenUsed, true);

                int updatedCount = paymentOrderMapper.update(null, updateWrapper);
                log.info("成功清理已取消订单的token，清理数量：{}", updatedCount);
            } else {
                log.info("没有需要清理的已取消订单token");
            }

        } catch (Exception e) {
            log.error("清理已取消订单token失败：{}", e.getMessage(), e);
        }
    }
}
