package org.dromara.app.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 详细能力报告视图对象
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Schema(description = "详细能力报告视图对象")
public class DetailedAbilityReportVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 优势列表
     */
    @Schema(description = "优势列表")
    private List<String> strengths;

    /**
     * 劣势列表
     */
    @Schema(description = "劣势列表")
    private List<String> weaknesses;

    /**
     * 推荐内容
     */
    @Schema(description = "推荐内容")
    private RecommendationsVo recommendations;
}
