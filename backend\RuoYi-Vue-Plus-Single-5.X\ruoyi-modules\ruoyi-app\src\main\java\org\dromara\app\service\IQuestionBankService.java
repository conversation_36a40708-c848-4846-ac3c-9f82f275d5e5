package org.dromara.app.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.app.domain.QuestionBank;
import org.dromara.app.domain.bo.QuestionBankBo;
import org.dromara.app.domain.vo.QuestionBankVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 题库管理Service接口
 *
 * <AUTHOR>
 */
public interface IQuestionBankService extends IService<QuestionBank> {

    /**
     * 查询题库
     *
     * @param bankId 题库主键
     * @return 题库
     */
    QuestionBankVo queryById(Long bankId);

    /**
     * 查询题库列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页查询条件
     * @return 题库分页列表
     */
    TableDataInfo<QuestionBankVo> queryPageList(QuestionBankBo bo, PageQuery pageQuery);

    /**
     * 查询题库列表
     *
     * @param bo 查询条件
     * @return 题库列表
     */
    List<QuestionBankVo> queryList(QuestionBankBo bo);

    /**
     * 新增题库
     *
     * @param bo 题库信息
     * @return 新增结果
     */
    Boolean insertByBo(QuestionBankBo bo);

    /**
     * 修改题库
     *
     * @param bo 题库信息
     * @return 修改结果
     */
    Boolean updateByBo(QuestionBankBo bo);

    /**
     * 校验并批量删除题库信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 删除结果
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 批量导入题库
     *
     * @param bankList 题库列表
     * @param isUpdateSupport 是否支持更新
     * @param operName 操作人员
     * @return 导入结果信息
     */
    String importBank(List<QuestionBankVo> bankList, Boolean isUpdateSupport, String operName);

    /**
     * 导出题库列表
     *
     * @param bo 查询条件
     * @return 题库列表
     */
    List<QuestionBankVo> exportBankList(QuestionBankBo bo);

    /**
     * 更新题库状态
     *
     * @param bankId 题库ID
     * @param status 状态
     * @return 更新结果
     */
    Boolean updateStatus(Long bankId, String status);

    /**
     * 批量更新题库状态
     *
     * @param bankIds 题库ID集合
     * @param status  状态
     * @return 更新结果
     */
    Boolean batchUpdateStatus(List<Long> bankIds, String status);

    /**
     * 复制题库
     *
     * @param bankId   源题库ID
     * @param bankCode 新题库编码
     * @param title    新题库标题
     * @return 复制结果
     */
    Boolean copyBank(Long bankId, String bankCode, String title);

    /**
     * 更新题库题目总数
     *
     * @param bankId 题库ID
     * @return 更新结果
     */
    Boolean updateTotalQuestions(Long bankId);

    /**
     * 获取题库统计信息
     *
     * @param bankId 题库ID
     * @return 统计信息
     */
    QuestionBankVo getBankStatistics(Long bankId);

    /**
     * 批量设置题库排序
     *
     * @param bankIds 题库ID列表
     * @param sorts   排序值列表
     * @return 设置结果
     */
    Boolean batchSetSort(List<Long> bankIds, List<Integer> sorts);
}
