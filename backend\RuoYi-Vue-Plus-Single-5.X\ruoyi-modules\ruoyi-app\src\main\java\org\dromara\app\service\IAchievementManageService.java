package org.dromara.app.service;

import org.dromara.app.domain.Achievement;
import org.dromara.app.domain.bo.AchievementBo;
import org.dromara.app.domain.vo.AchievementVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 成就管理Service接口
 *
 * <AUTHOR>
 */
public interface IAchievementManageService {

    /**
     * 查询成就
     *
     * @param id 成就主键
     * @return 成就
     */
    AchievementVo queryById(Long id);

    /**
     * 查询成就列表
     *
     * @param pageQuery 分页查询条件
     * @return 成就集合
     */
    TableDataInfo<AchievementVo> queryPageList(PageQuery pageQuery);

    /**
     * 查询成就列表
     *
     * @param achievementBo 成就查询条件
     * @return 成就集合
     */
    List<AchievementVo> queryList(AchievementBo achievementBo);

    /**
     * 新增成就
     *
     * @param achievementBo 成就
     * @return 结果
     */
    Boolean insertByBo(AchievementBo achievementBo);

    /**
     * 修改成就
     *
     * @param achievementBo 成就
     * @return 结果
     */
    Boolean updateByBo(AchievementBo achievementBo);

    /**
     * 校验并批量删除成就信息
     *
     * @param ids     需要删除的成就主键集合
     * @param isValid 是否校验,true-删除前校验,false-不校验
     * @return 结果
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 批量更新成就状态
     *
     * @param ids      成就ID列表
     * @param isActive 是否激活
     * @return 操作结果
     */
    Boolean batchUpdateStatus(List<Long> ids, String isActive);

    /**
     * 复制成就
     *
     * @param id 成就ID
     * @return 操作结果
     */
    Boolean copyAchievement(Long id);

    /**
     * 预览成就触发条件
     *
     * @param triggerCondition 触发条件JSON
     * @param userId           测试用户ID
     * @return 预览结果
     */
    Map<String, Object> previewTriggerCondition(String triggerCondition, Long userId);

    /**
     * 测试成就规则
     *
     * @param id        成就ID
     * @param userId    测试用户ID
     * @param eventType 事件类型
     * @param eventData 事件数据
     * @return 测试结果
     */
    Map<String, Object> testAchievementRule(Long id, Long userId, String eventType, Object eventData);

    /**
     * 获取成就统计信息
     *
     * @return 统计信息
     */
    Map<String, Object> getAchievementStats();

    /**
     * 导入成就数据
     *
     * @param achievementList 成就数据列表
     * @param isUpdateSupport 是否支持更新
     * @param operName        操作用户
     * @return 导入结果
     */
    String importAchievement(List<AchievementBo> achievementList, Boolean isUpdateSupport, String operName);

    /**
     * 导出成就数据
     *
     * @param list 成就数据列表
     * @return 导出结果
     */
    List<AchievementVo> exportAchievement(List<AchievementVo> list);

}
