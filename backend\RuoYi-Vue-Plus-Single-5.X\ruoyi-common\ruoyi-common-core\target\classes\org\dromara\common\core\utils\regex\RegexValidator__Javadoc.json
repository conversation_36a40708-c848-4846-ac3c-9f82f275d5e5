{"doc": "\n 正则字段校验器\r\n 主要验证字段非空、是否为满足指定格式等\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "DICTIONARY_TYPE", "doc": "\n 字典类型必须以字母开头，且只能为（小写字母，数字，下滑线）\r\n"}, {"name": "ID_CARD_LAST_6", "doc": "\n 身份证号码（后6位）\r\n"}, {"name": "QQ_NUMBER", "doc": "\n QQ号码\r\n"}, {"name": "POSTAL_CODE", "doc": "\n 邮政编码\r\n"}, {"name": "ACCOUNT", "doc": "\n 注册账号\r\n"}, {"name": "PASSWORD", "doc": "\n 密码：包含至少8个字符，包括大写字母、小写字母、数字和特殊字符\r\n"}, {"name": "STATUS", "doc": "\n 通用状态（0表示正常，1表示停用）\r\n"}], "enumConstants": [], "methods": [{"name": "isAccount", "paramTypes": ["java.lang.CharSequence"], "doc": "\n 检查输入的账号是否匹配预定义的规则\r\n\r\n @param value 要验证的账号\r\n @return 如果账号符合规则，返回 true；否则，返回 false。\r\n"}, {"name": "validateAccount", "paramTypes": ["java.lang.CharSequence", "java.lang.String"], "doc": "\n 验证输入的账号是否符合规则，如果不符合，则抛出 ValidateException 异常\r\n\r\n @param value    要验证的账号\r\n @param errorMsg 验证失败时抛出的异常消息\r\n @param <T>      CharSequence 的子类型\r\n @return 如果验证通过，返回输入的账号\r\n @throws ValidateException 如果验证失败\r\n"}, {"name": "isStatus", "paramTypes": ["java.lang.CharSequence"], "doc": "\n 检查输入的状态是否匹配预定义的规则\r\n\r\n @param value 要验证的状态\r\n @return 如果状态符合规则，返回 true；否则，返回 false。\r\n"}, {"name": "validateStatus", "paramTypes": ["java.lang.CharSequence", "java.lang.String"], "doc": "\n 验证输入的状态是否符合规则，如果不符合，则抛出 ValidateException 异常\r\n\r\n @param value    要验证的状态\r\n @param errorMsg 验证失败时抛出的异常消息\r\n @param <T>      CharSequence 的子类型\r\n @return 如果验证通过，返回输入的状态\r\n @throws ValidateException 如果验证失败\r\n"}], "constructors": []}