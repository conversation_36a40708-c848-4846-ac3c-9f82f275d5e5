package org.dromara.app.service;

import org.dromara.app.domain.InterviewSession;
import org.dromara.app.domain.SessionQuestion;
import org.dromara.app.domain.vo.InterviewResponseVo;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 面试缓存服务接口
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
public interface IInterviewCacheService {

    /**
     * 缓存面试会话信息
     *
     * @param session 会话信息
     */
    void cacheSession(InterviewSession session);

    /**
     * 获取缓存的会话信息
     *
     * @param sessionId 会话ID
     * @return 会话信息
     */
    Optional<InterviewSession> getCachedSession(String sessionId);

    /**
     * 删除会话缓存
     *
     * @param sessionId 会话ID
     */
    void evictSession(String sessionId);

    /**
     * 缓存会话问题列表
     *
     * @param sessionId 会话ID
     * @param questions 问题列表
     */
    void cacheSessionQuestions(String sessionId, List<SessionQuestion> questions);

    /**
     * 获取缓存的会话问题列表
     *
     * @param sessionId 会话ID
     * @return 问题列表
     */
    Optional<List<SessionQuestion>> getCachedSessionQuestions(String sessionId);

    /**
     * 删除会话问题缓存
     *
     * @param sessionId 会话ID
     */
    void evictSessionQuestions(String sessionId);

    /**
     * 缓存用户当前活跃会话
     *
     * @param userId    用户ID
     * @param sessionId 会话ID
     */
    void cacheUserActiveSession(Long userId, String sessionId);

    /**
     * 获取用户当前活跃会话
     *
     * @param userId 用户ID
     * @return 会话ID
     */
    Optional<String> getUserActiveSession(Long userId);

    /**
     * 删除用户活跃会话缓存
     *
     * @param userId 用户ID
     */
    void evictUserActiveSession(Long userId);

    /**
     * 缓存面试结果
     *
     * @param sessionId 会话ID
     * @param result    面试结果
     */
    void cacheInterviewResult(String sessionId, InterviewResponseVo.InterviewResult result);

    /**
     * 获取缓存的面试结果
     *
     * @param sessionId 会话ID
     * @return 面试结果
     */
    Optional<InterviewResponseVo.InterviewResult> getCachedInterviewResult(String sessionId);

    /**
     * 删除面试结果缓存
     *
     * @param sessionId 会话ID
     */
    void evictInterviewResult(String sessionId);

    /**
     * 缓存AI评估结果
     *
     * @param questionId 问题ID
     * @param answer     答案内容
     * @param feedback   评估结果
     */
    void cacheAiEvaluation(String questionId, String answer, InterviewResponseVo.FeedbackInfo feedback);

    /**
     * 获取缓存的AI评估结果
     *
     * @param questionId 问题ID
     * @param answer     答案内容
     * @return 评估结果
     */
    Optional<InterviewResponseVo.FeedbackInfo> getCachedAiEvaluation(String questionId, String answer);

    /**
     * 缓存热门问题
     *
     * @param jobId     岗位ID
     * @param questions 问题列表
     */
    void cachePopularQuestions(Long jobId, List<InterviewResponseVo.QuestionInfo> questions);

    /**
     * 获取缓存的热门问题
     *
     * @param jobId 岗位ID
     * @return 问题列表
     */
    Optional<List<InterviewResponseVo.QuestionInfo>> getCachedPopularQuestions(Long jobId);

    /**
     * 缓存用户统计信息
     *
     * @param userId 用户ID
     * @param stats  统计信息
     */
    void cacheUserStats(Long userId, InterviewResponseVo.UserStats stats);

    /**
     * 获取缓存的用户统计信息
     *
     * @param userId 用户ID
     * @return 统计信息
     */
    Optional<InterviewResponseVo.UserStats> getCachedUserStats(Long userId);

    /**
     * 预热缓存
     *
     * @param sessionId 会话ID
     */
    void warmupCache(String sessionId);

    /**
     * 清理过期缓存
     */
    void cleanExpiredCache();

    /**
     * 清理所有缓存
     */
    void clearAllCache();

    /**
     * 获取缓存统计信息
     *
     * @return 缓存统计
     */
    Map<String, Object> getCacheStats();
}
