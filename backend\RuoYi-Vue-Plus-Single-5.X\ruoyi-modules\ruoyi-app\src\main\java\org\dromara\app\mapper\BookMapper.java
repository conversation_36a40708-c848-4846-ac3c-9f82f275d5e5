package org.dromara.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.dromara.app.domain.Book;

import java.util.List;

/**
 * 面试书籍Mapper接口
 *
 * <AUTHOR>
 */
public interface BookMapper extends BaseMapper<Book> {

    /**
     * 分页查询书籍列表（支持分类筛选和搜索）
     *
     * @param page        分页参数
     * @param category    分类筛选
     * @param searchQuery 搜索关键词
     * @param userId      用户ID（用于查询购买状态和阅读进度）
     * @return 书籍列表
     */
    Page<Book> selectBookPageWithUserInfo(Page<Book> page,
                                          @Param("category") String category,
                                          @Param("searchQuery") String searchQuery,
                                          @Param("userId") Long userId);

    /**
     * 根据ID查询书籍详情（包含用户阅读信息）
     *
     * @param id     书籍ID
     * @param userId 用户ID
     * @return 书籍详情
     */
    Book selectBookByIdWithUserInfo(@Param("id") Long id, @Param("userId") Long userId);

    /**
     * 增加书籍阅读次数
     *
     * @param bookId 书籍ID
     * @return 影响行数
     */
    int incrementReadCount(@Param("bookId") Long bookId);

    /**
     * 查询热门书籍列表
     *
     * @param limit 限制数量
     * @return 热门书籍列表
     */
    List<Book> selectHotBooks(@Param("limit") Integer limit);

    /**
     * 查询推荐书籍列表（基于用户阅读历史）
     *
     * @param userId 用户ID
     * @param limit  限制数量
     * @return 推荐书籍列表
     */
    List<Book> selectRecommendedBooks(@Param("userId") Long userId, @Param("limit") Integer limit);

    /**
     * 根据分类查询书籍数量统计
     *
     * @return 分类统计信息
     */
    List<Book> selectCategoryStats();
}
