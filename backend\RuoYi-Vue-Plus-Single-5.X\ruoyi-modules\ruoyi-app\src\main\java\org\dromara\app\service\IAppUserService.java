package org.dromara.app.service;

import org.dromara.app.domain.AppUser;
import org.dromara.app.domain.dto.AppAuthDto;
import org.dromara.app.domain.vo.AppUserInfoVo;

/**
 * 应用用户Service接口
 *
 * <AUTHOR>
 */
public interface IAppUserService {

    /**
     * 根据手机号查询用户
     */
    AppUser selectByPhone(String phone);

    /**
     * 根据邮箱查询用户
     */
    AppUser selectByEmail(String email);

    /**
     * 检查手机号是否存在
     */
    boolean checkPhoneExists(String phone);

    /**
     * 检查邮箱是否存在
     */
    boolean checkEmailExists(String email);

    /**
     * 用户注册
     */
    AppUserInfoVo register(AppAuthDto authDto);

    /**
     * 手机号登录
     */
    AppUserInfoVo loginByPhone(String phone, String code);

    /**
     * 密码登录
     */
    AppUserInfoVo loginByPassword(String phone, String password);

    /**
     * 重置密码
     */
    void resetPassword(String phone, String code, String newPassword);

    /**
     * 根据用户ID获取用户信息
     */
    AppUserInfoVo getUserInfo(Long userId);

    /**
     * 更新最后登录信息
     */
    void updateLoginInfo(Long userId, String loginIp);
}
