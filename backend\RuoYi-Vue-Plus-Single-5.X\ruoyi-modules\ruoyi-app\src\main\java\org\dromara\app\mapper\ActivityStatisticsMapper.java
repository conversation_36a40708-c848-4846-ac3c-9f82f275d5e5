package org.dromara.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.dromara.app.domain.ActivityStatistics;
import org.dromara.app.domain.enums.ActivityType;

import java.time.LocalDate;
import java.util.List;

/**
 * 用户活动统计Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Mapper
public interface ActivityStatisticsMapper extends BaseMapper<ActivityStatistics> {

    /**
     * 根据用户ID、活动类型和日期查询统计记录
     *
     * @param userId       用户ID
     * @param activityType 活动类型
     * @param statDate     统计日期
     * @return 统计记录
     */
    @Select("SELECT * FROM app_activity_statistics " +
        "WHERE user_id = #{userId} AND activity_type = #{activityType} AND stat_date = #{statDate} AND del_flag = '0'")
    ActivityStatistics selectByUserAndTypeAndDate(@Param("userId") Long userId,
                                                  @Param("activityType") ActivityType activityType,
                                                  @Param("statDate") LocalDate statDate);

    /**
     * 查询用户指定日期范围内的统计记录
     *
     * @param userId       用户ID
     * @param activityType 活动类型
     * @param startDate    开始日期
     * @param endDate      结束日期
     * @return 统计记录列表
     */
    @Select("SELECT * FROM app_activity_statistics " +
        "WHERE user_id = #{userId} " +
        "AND (#{activityType} IS NULL OR activity_type = #{activityType}) " +
        "AND stat_date >= #{startDate} AND stat_date <= #{endDate} " +
        "AND del_flag = '0' " +
        "ORDER BY stat_date DESC")
    List<ActivityStatistics> selectByUserAndDateRange(@Param("userId") Long userId,
                                                      @Param("activityType") ActivityType activityType,
                                                      @Param("startDate") LocalDate startDate,
                                                      @Param("endDate") LocalDate endDate);

    /**
     * 查询用户今日各类型活动统计
     *
     * @param userId 用户ID
     * @return 今日统计列表
     */
    @Select("SELECT * FROM app_activity_statistics " +
        "WHERE user_id = #{userId} AND stat_date = CURDATE() AND del_flag = '0'")
    List<ActivityStatistics> selectTodayStatistics(@Param("userId") Long userId);

    /**
     * 查询用户本周各类型活动统计汇总
     *
     * @param userId 用户ID
     * @return 本周统计列表
     */
    @Select("SELECT activity_type, " +
        "SUM(total_duration) as total_duration, " +
        "SUM(session_count) as session_count, " +
        "AVG(avg_duration) as avg_duration, " +
        "MAX(max_duration) as max_duration " +
        "FROM app_activity_statistics " +
        "WHERE user_id = #{userId} " +
        "AND stat_date >= DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY) " +
        "AND stat_date <= CURDATE() " +
        "AND del_flag = '0' " +
        "GROUP BY activity_type")
    List<ActivityStatistics> selectWeekStatistics(@Param("userId") Long userId);

    /**
     * 查询用户本月各类型活动统计汇总
     *
     * @param userId 用户ID
     * @return 本月统计列表
     */
    @Select("SELECT activity_type, " +
        "SUM(total_duration) as total_duration, " +
        "SUM(session_count) as session_count, " +
        "AVG(avg_duration) as avg_duration, " +
        "MAX(max_duration) as max_duration " +
        "FROM app_activity_statistics " +
        "WHERE user_id = #{userId} " +
        "AND stat_date >= DATE_FORMAT(CURDATE(), '%Y-%m-01') " +
        "AND stat_date <= CURDATE() " +
        "AND del_flag = '0' " +
        "GROUP BY activity_type")
    List<ActivityStatistics> selectMonthStatistics(@Param("userId") Long userId);

    /**
     * 更新统计记录
     *
     * @param userId       用户ID
     * @param activityType 活动类型
     * @param statDate     统计日期
     * @param duration     新增时长
     * @return 更新行数
     */
    @Update("UPDATE app_activity_statistics SET " +
        "total_duration = total_duration + #{duration}, " +
        "session_count = session_count + 1, " +
        "avg_duration = (total_duration + #{duration}) / (session_count + 1), " +
        "max_duration = GREATEST(max_duration, #{duration}), " +
        "update_time = NOW() " +
        "WHERE user_id = #{userId} AND activity_type = #{activityType} AND stat_date = #{statDate}")
    int updateStatistics(@Param("userId") Long userId,
                         @Param("activityType") ActivityType activityType,
                         @Param("statDate") LocalDate statDate,
                         @Param("duration") Long duration);

    /**
     * 删除用户指定类型的统计记录
     *
     * @param userId       用户ID
     * @param activityType 活动类型
     * @return 删除行数
     */
    @Update("UPDATE app_activity_statistics SET del_flag = '2', update_time = NOW() " +
        "WHERE user_id = #{userId} " +
        "AND (#{activityType} IS NULL OR activity_type = #{activityType}) " +
        "AND del_flag = '0'")
    int deleteUserStatistics(@Param("userId") Long userId, @Param("activityType") ActivityType activityType);
}
