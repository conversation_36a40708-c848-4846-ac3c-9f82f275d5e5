package org.dromara.app.service.impl;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.config.OllamaConfig;
import org.dromara.app.service.IEmbeddingService;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;

import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 文本嵌入服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EmbeddingServiceImpl implements IEmbeddingService {

    // 默认嵌入模型
    private static final String DEFAULT_EMBEDDING_MODEL = "mxbai-embed-large";
    private static final int DEFAULT_VECTOR_DIMENSION = 1024;
    private static final Duration REQUEST_TIMEOUT = Duration.ofSeconds(30);
    private final OllamaConfig.OllamaProperties ollamaProperties;
    private final WebClient webClient;
    private final ObjectMapper objectMapper;

    @Override
    public float[] generateEmbedding(String text) {
        if (StrUtil.isBlank(text)) {
            return new float[0];
        }

        try {
            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", getEmbeddingModel());
            requestBody.put("prompt", text);

            // 调用Ollama API
            String response = webClient.post()
                .uri(ollamaProperties.getBaseUrl() + "/api/embeddings")
                .bodyValue(requestBody)
                .retrieve()
                .bodyToMono(String.class)
                .timeout(REQUEST_TIMEOUT)
                .block();

            if (StrUtil.isBlank(response)) {
                log.error("Ollama嵌入API返回空响应");
                return new float[0];
            }

            // 解析响应
            JsonNode jsonNode = objectMapper.readTree(response);
            JsonNode embeddingNode = jsonNode.get("embedding");

            if (embeddingNode == null || !embeddingNode.isArray()) {
                log.error("无效的嵌入响应格式: {}", response);
                return new float[0];
            }

            // 转换为float数组
            float[] embedding = new float[embeddingNode.size()];
            for (int i = 0; i < embeddingNode.size(); i++) {
                embedding[i] = (float) embeddingNode.get(i).asDouble();
            }

            return embedding;

        } catch (Exception e) {
            log.error("生成文本嵌入失败: {}", text.substring(0, Math.min(text.length(), 100)), e);
            return new float[0];
        }
    }

    @Override
    public List<float[]> generateEmbeddings(List<String> texts) {
        List<float[]> embeddings = new ArrayList<>();

        for (String text : texts) {
            float[] embedding = generateEmbedding(text);
            embeddings.add(embedding);
        }

        return embeddings;
    }

    @Override
    public double calculateSimilarity(float[] vector1, float[] vector2) {
        if (vector1.length != vector2.length) {
            throw new IllegalArgumentException("向量维度不匹配");
        }

        // 计算余弦相似度
        double dotProduct = 0.0;
        double norm1 = 0.0;
        double norm2 = 0.0;

        for (int i = 0; i < vector1.length; i++) {
            dotProduct += vector1[i] * vector2[i];
            norm1 += vector1[i] * vector1[i];
            norm2 += vector2[i] * vector2[i];
        }

        if (norm1 == 0.0 || norm2 == 0.0) {
            return 0.0;
        }

        return dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2));
    }

    @Override
    public int getVectorDimension() {
        // 可以通过调用API获取实际维度，这里先返回默认值
        return DEFAULT_VECTOR_DIMENSION;
    }

    @Override
    public String getModelName() {
        return getEmbeddingModel();
    }

    @Override
    public boolean isAvailable() {
        try {
            // 测试连接
            String response = webClient.get()
                .uri(ollamaProperties.getBaseUrl() + "/api/tags")
                .retrieve()
                .bodyToMono(String.class)
                .timeout(Duration.ofSeconds(5))
                .block();

            return StrUtil.isNotBlank(response);

        } catch (Exception e) {
            log.warn("检查嵌入服务可用性失败", e);
            return false;
        }
    }

    /**
     * 获取嵌入模型名称
     */
    private String getEmbeddingModel() {
        String embeddingModel = ollamaProperties.getEmbeddingModel();
        return StrUtil.isNotBlank(embeddingModel) ? embeddingModel : DEFAULT_EMBEDDING_MODEL;
    }

    /**
     * 向量转换为字符串（用于数据库存储）
     */
    public String vectorToString(float[] vector) {
        if (vector == null || vector.length == 0) {
            return "[]";
        }

        StringBuilder sb = new StringBuilder("[");
        for (int i = 0; i < vector.length; i++) {
            if (i > 0) {
                sb.append(",");
            }
            sb.append(vector[i]);
        }
        sb.append("]");
        return sb.toString();
    }

    /**
     * 字符串转换为向量
     */
    public float[] stringToVector(String vectorStr) {
        if (StrUtil.isBlank(vectorStr)) {
            return new float[0];
        }

        try {
            JsonNode jsonNode = objectMapper.readTree(vectorStr);
            if (!jsonNode.isArray()) {
                return new float[0];
            }

            float[] vector = new float[jsonNode.size()];
            for (int i = 0; i < jsonNode.size(); i++) {
                vector[i] = (float) jsonNode.get(i).asDouble();
            }
            return vector;

        } catch (Exception e) {
            log.error("解析向量字符串失败: {}", vectorStr, e);
            return new float[0];
        }
    }
}
