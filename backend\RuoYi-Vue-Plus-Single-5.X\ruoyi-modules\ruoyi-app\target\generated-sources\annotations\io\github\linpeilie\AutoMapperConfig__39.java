package io.github.linpeilie;

import org.dromara.app.domain.FeedbackToFeedbackVoMapper;
import org.dromara.app.domain.FeedbackToFeedbackVoMapper__1;
import org.dromara.app.domain.UserResumeToUserResumeVoMapper;
import org.dromara.app.domain.UserResumeToUserResumeVoMapper__1;
import org.dromara.app.domain.VideoCommentToVideoCommentVoMapper;
import org.dromara.app.domain.VideoCommentToVideoCommentVoMapper__1;
import org.dromara.app.domain.VideoToVideoDetailVoMapper;
import org.dromara.app.domain.VideoToVideoDetailVoMapper__1;
import org.dromara.app.domain.bo.FeedbackBoToFeedbackMapper;
import org.dromara.app.domain.bo.FeedbackBoToFeedbackMapper__1;
import org.dromara.app.domain.bo.UserResumeBoToUserResumeMapper;
import org.dromara.app.domain.bo.UserResumeBoToUserResumeMapper__1;
import org.dromara.app.domain.bo.VideoCommentBoToVideoCommentMapper;
import org.dromara.app.domain.bo.VideoCommentBoToVideoCommentMapper__1;
import org.dromara.app.domain.vo.FeedbackVoToFeedbackMapper;
import org.dromara.app.domain.vo.FeedbackVoToFeedbackMapper__1;
import org.dromara.app.domain.vo.UserResumeVoToUserResumeMapper;
import org.dromara.app.domain.vo.UserResumeVoToUserResumeMapper__1;
import org.dromara.app.domain.vo.VideoCommentVoToVideoCommentMapper;
import org.dromara.app.domain.vo.VideoCommentVoToVideoCommentMapper__1;
import org.dromara.app.domain.vo.VideoDetailVoToVideoMapper;
import org.dromara.app.domain.vo.VideoDetailVoToVideoMapper__1;
import org.dromara.common.log.event.OperLogEventToSysOperLogBoMapper;
import org.dromara.common.log.event.OperLogEventToSysOperLogBoMapper__1;
import org.dromara.system.domain.SysClientToSysClientVoMapper;
import org.dromara.system.domain.SysClientToSysClientVoMapper__1;
import org.dromara.system.domain.SysConfigToSysConfigVoMapper;
import org.dromara.system.domain.SysConfigToSysConfigVoMapper__1;
import org.dromara.system.domain.SysDeptToSysDeptVoMapper;
import org.dromara.system.domain.SysDeptToSysDeptVoMapper__1;
import org.dromara.system.domain.SysDictDataToSysDictDataVoMapper;
import org.dromara.system.domain.SysDictDataToSysDictDataVoMapper__1;
import org.dromara.system.domain.SysDictTypeToSysDictTypeVoMapper;
import org.dromara.system.domain.SysDictTypeToSysDictTypeVoMapper__1;
import org.dromara.system.domain.SysLogininforToSysLogininforVoMapper;
import org.dromara.system.domain.SysLogininforToSysLogininforVoMapper__1;
import org.dromara.system.domain.SysMenuToSysMenuVoMapper;
import org.dromara.system.domain.SysMenuToSysMenuVoMapper__1;
import org.dromara.system.domain.SysNoticeToSysNoticeVoMapper;
import org.dromara.system.domain.SysNoticeToSysNoticeVoMapper__1;
import org.dromara.system.domain.SysOperLogToSysOperLogVoMapper;
import org.dromara.system.domain.SysOperLogToSysOperLogVoMapper__1;
import org.dromara.system.domain.SysOssConfigToSysOssConfigVoMapper;
import org.dromara.system.domain.SysOssConfigToSysOssConfigVoMapper__1;
import org.dromara.system.domain.SysOssToSysOssVoMapper;
import org.dromara.system.domain.SysOssToSysOssVoMapper__1;
import org.dromara.system.domain.SysPostToSysPostVoMapper;
import org.dromara.system.domain.SysPostToSysPostVoMapper__1;
import org.dromara.system.domain.SysRoleToSysRoleVoMapper;
import org.dromara.system.domain.SysRoleToSysRoleVoMapper__1;
import org.dromara.system.domain.SysSocialToSysSocialVoMapper;
import org.dromara.system.domain.SysSocialToSysSocialVoMapper__1;
import org.dromara.system.domain.SysUserToSysUserVoMapper;
import org.dromara.system.domain.SysUserToSysUserVoMapper__1;
import org.dromara.system.domain.bo.SysClientBoToSysClientMapper;
import org.dromara.system.domain.bo.SysClientBoToSysClientMapper__1;
import org.dromara.system.domain.bo.SysConfigBoToSysConfigMapper;
import org.dromara.system.domain.bo.SysConfigBoToSysConfigMapper__1;
import org.dromara.system.domain.bo.SysDeptBoToSysDeptMapper;
import org.dromara.system.domain.bo.SysDeptBoToSysDeptMapper__1;
import org.dromara.system.domain.bo.SysDictDataBoToSysDictDataMapper;
import org.dromara.system.domain.bo.SysDictDataBoToSysDictDataMapper__1;
import org.dromara.system.domain.bo.SysDictTypeBoToSysDictTypeMapper;
import org.dromara.system.domain.bo.SysDictTypeBoToSysDictTypeMapper__1;
import org.dromara.system.domain.bo.SysLogininforBoToSysLogininforMapper;
import org.dromara.system.domain.bo.SysLogininforBoToSysLogininforMapper__1;
import org.dromara.system.domain.bo.SysMenuBoToSysMenuMapper;
import org.dromara.system.domain.bo.SysMenuBoToSysMenuMapper__1;
import org.dromara.system.domain.bo.SysNoticeBoToSysNoticeMapper;
import org.dromara.system.domain.bo.SysNoticeBoToSysNoticeMapper__1;
import org.dromara.system.domain.bo.SysOperLogBoToOperLogEventMapper;
import org.dromara.system.domain.bo.SysOperLogBoToOperLogEventMapper__1;
import org.dromara.system.domain.bo.SysOperLogBoToSysOperLogMapper;
import org.dromara.system.domain.bo.SysOperLogBoToSysOperLogMapper__1;
import org.dromara.system.domain.bo.SysOssBoToSysOssMapper;
import org.dromara.system.domain.bo.SysOssBoToSysOssMapper__1;
import org.dromara.system.domain.bo.SysOssConfigBoToSysOssConfigMapper;
import org.dromara.system.domain.bo.SysOssConfigBoToSysOssConfigMapper__1;
import org.dromara.system.domain.bo.SysPostBoToSysPostMapper;
import org.dromara.system.domain.bo.SysPostBoToSysPostMapper__1;
import org.dromara.system.domain.bo.SysRoleBoToSysRoleMapper;
import org.dromara.system.domain.bo.SysRoleBoToSysRoleMapper__1;
import org.dromara.system.domain.bo.SysSocialBoToSysSocialMapper;
import org.dromara.system.domain.bo.SysSocialBoToSysSocialMapper__1;
import org.dromara.system.domain.bo.SysUserBoToSysUserMapper;
import org.dromara.system.domain.bo.SysUserBoToSysUserMapper__1;
import org.dromara.system.domain.vo.SysClientVoToSysClientMapper;
import org.dromara.system.domain.vo.SysClientVoToSysClientMapper__1;
import org.dromara.system.domain.vo.SysConfigVoToSysConfigMapper;
import org.dromara.system.domain.vo.SysConfigVoToSysConfigMapper__1;
import org.dromara.system.domain.vo.SysDeptVoToSysDeptMapper;
import org.dromara.system.domain.vo.SysDeptVoToSysDeptMapper__1;
import org.dromara.system.domain.vo.SysDictDataVoToSysDictDataMapper;
import org.dromara.system.domain.vo.SysDictDataVoToSysDictDataMapper__1;
import org.dromara.system.domain.vo.SysDictTypeVoToSysDictTypeMapper;
import org.dromara.system.domain.vo.SysDictTypeVoToSysDictTypeMapper__1;
import org.dromara.system.domain.vo.SysLogininforVoToSysLogininforMapper;
import org.dromara.system.domain.vo.SysLogininforVoToSysLogininforMapper__1;
import org.dromara.system.domain.vo.SysMenuVoToSysMenuMapper;
import org.dromara.system.domain.vo.SysMenuVoToSysMenuMapper__1;
import org.dromara.system.domain.vo.SysNoticeVoToSysNoticeMapper;
import org.dromara.system.domain.vo.SysNoticeVoToSysNoticeMapper__1;
import org.dromara.system.domain.vo.SysOperLogVoToSysOperLogMapper;
import org.dromara.system.domain.vo.SysOperLogVoToSysOperLogMapper__1;
import org.dromara.system.domain.vo.SysOssConfigVoToSysOssConfigMapper;
import org.dromara.system.domain.vo.SysOssConfigVoToSysOssConfigMapper__1;
import org.dromara.system.domain.vo.SysOssVoToSysOssMapper;
import org.dromara.system.domain.vo.SysOssVoToSysOssMapper__1;
import org.dromara.system.domain.vo.SysPostVoToSysPostMapper;
import org.dromara.system.domain.vo.SysPostVoToSysPostMapper__1;
import org.dromara.system.domain.vo.SysRoleVoToSysRoleMapper;
import org.dromara.system.domain.vo.SysRoleVoToSysRoleMapper__1;
import org.dromara.system.domain.vo.SysSocialVoToSysSocialMapper;
import org.dromara.system.domain.vo.SysSocialVoToSysSocialMapper__1;
import org.dromara.system.domain.vo.SysUserVoToSysUserMapper;
import org.dromara.system.domain.vo.SysUserVoToSysUserMapper__1;
import org.mapstruct.Builder;
import org.mapstruct.MapperConfig;
import org.mapstruct.ReportingPolicy;

@MapperConfig(
    componentModel = "spring-lazy",
    uses = {ConverterMapperAdapter__39.class, SysConfigVoToSysConfigMapper__1.class, SysDictDataVoToSysDictDataMapper.class, SysOssConfigBoToSysOssConfigMapper__1.class, SysLogininforVoToSysLogininforMapper.class, SysConfigBoToSysConfigMapper.class, VideoCommentBoToVideoCommentMapper.class, SysPostBoToSysPostMapper.class, SysOssConfigVoToSysOssConfigMapper.class, SysDictTypeToSysDictTypeVoMapper__1.class, SysDeptBoToSysDeptMapper__1.class, SysDictDataToSysDictDataVoMapper.class, VideoDetailVoToVideoMapper.class, UserResumeToUserResumeVoMapper.class, SysLogininforToSysLogininforVoMapper__1.class, SysConfigBoToSysConfigMapper__1.class, SysRoleVoToSysRoleMapper.class, SysClientVoToSysClientMapper.class, SysOssConfigBoToSysOssConfigMapper.class, VideoCommentVoToVideoCommentMapper.class, SysOssConfigToSysOssConfigVoMapper__1.class, SysDeptBoToSysDeptMapper.class, VideoCommentToVideoCommentVoMapper__1.class, SysOssBoToSysOssMapper.class, SysMenuVoToSysMenuMapper.class, SysDeptToSysDeptVoMapper__1.class, SysDictDataVoToSysDictDataMapper__1.class, UserResumeVoToUserResumeMapper__1.class, FeedbackToFeedbackVoMapper__1.class, SysDictDataBoToSysDictDataMapper__1.class, SysClientVoToSysClientMapper__1.class, FeedbackBoToFeedbackMapper__1.class, FeedbackVoToFeedbackMapper.class, SysClientBoToSysClientMapper.class, SysClientToSysClientVoMapper.class, SysDeptVoToSysDeptMapper__1.class, SysNoticeToSysNoticeVoMapper__1.class, SysLogininforVoToSysLogininforMapper__1.class, VideoToVideoDetailVoMapper.class, SysMenuVoToSysMenuMapper__1.class, SysPostVoToSysPostMapper__1.class, SysOperLogBoToOperLogEventMapper__1.class, SysConfigVoToSysConfigMapper.class, SysSocialBoToSysSocialMapper.class, SysOssVoToSysOssMapper.class, SysSocialToSysSocialVoMapper__1.class, SysDictTypeToSysDictTypeVoMapper.class, VideoCommentBoToVideoCommentMapper__1.class, UserResumeBoToUserResumeMapper__1.class, SysPostBoToSysPostMapper__1.class, SysLogininforBoToSysLogininforMapper__1.class, SysLogininforToSysLogininforVoMapper.class, SysPostVoToSysPostMapper.class, SysOssToSysOssVoMapper__1.class, SysSocialToSysSocialVoMapper.class, SysDictTypeBoToSysDictTypeMapper.class, OperLogEventToSysOperLogBoMapper.class, VideoToVideoDetailVoMapper__1.class, VideoCommentToVideoCommentVoMapper.class, SysNoticeBoToSysNoticeMapper.class, SysSocialVoToSysSocialMapper.class, SysOperLogBoToSysOperLogMapper__1.class, SysClientToSysClientVoMapper__1.class, FeedbackVoToFeedbackMapper__1.class, SysOssConfigToSysOssConfigVoMapper.class, SysDictTypeBoToSysDictTypeMapper__1.class, SysNoticeBoToSysNoticeMapper__1.class, SysRoleBoToSysRoleMapper__1.class, SysUserToSysUserVoMapper.class, SysDictTypeVoToSysDictTypeMapper.class, SysOssVoToSysOssMapper__1.class, SysMenuBoToSysMenuMapper.class, SysUserToSysUserVoMapper__1.class, SysRoleToSysRoleVoMapper__1.class, SysSocialVoToSysSocialMapper__1.class, UserResumeToUserResumeVoMapper__1.class, SysOperLogToSysOperLogVoMapper__1.class, SysSocialBoToSysSocialMapper__1.class, SysConfigToSysConfigVoMapper.class, SysOperLogBoToOperLogEventMapper.class, SysNoticeVoToSysNoticeMapper.class, SysMenuBoToSysMenuMapper__1.class, SysMenuToSysMenuVoMapper.class, FeedbackBoToFeedbackMapper.class, SysOssBoToSysOssMapper__1.class, SysOssConfigVoToSysOssConfigMapper__1.class, SysUserBoToSysUserMapper.class, OperLogEventToSysOperLogBoMapper__1.class, SysDictDataBoToSysDictDataMapper.class, SysUserVoToSysUserMapper.class, SysNoticeToSysNoticeVoMapper.class, SysDictDataToSysDictDataVoMapper__1.class, SysOssToSysOssVoMapper.class, SysOperLogToSysOperLogVoMapper.class, SysLogininforBoToSysLogininforMapper.class, VideoCommentVoToVideoCommentMapper__1.class, SysDeptVoToSysDeptMapper.class, SysRoleVoToSysRoleMapper__1.class, UserResumeBoToUserResumeMapper.class, UserResumeVoToUserResumeMapper.class, SysRoleBoToSysRoleMapper.class, SysConfigToSysConfigVoMapper__1.class, SysDictTypeVoToSysDictTypeMapper__1.class, SysRoleToSysRoleVoMapper.class, SysUserVoToSysUserMapper__1.class, SysMenuToSysMenuVoMapper__1.class, SysNoticeVoToSysNoticeMapper__1.class, SysOperLogVoToSysOperLogMapper__1.class, VideoDetailVoToVideoMapper__1.class, SysClientBoToSysClientMapper__1.class, FeedbackToFeedbackVoMapper.class, SysOperLogBoToSysOperLogMapper.class, SysOperLogVoToSysOperLogMapper.class, SysDeptToSysDeptVoMapper.class, SysPostToSysPostVoMapper__1.class, SysUserBoToSysUserMapper__1.class, SysPostToSysPostVoMapper.class},
    unmappedTargetPolicy = ReportingPolicy.IGNORE,
    builder = @Builder(buildMethod = "build", disableBuilder = true)
)
public interface AutoMapperConfig__39 {
}
