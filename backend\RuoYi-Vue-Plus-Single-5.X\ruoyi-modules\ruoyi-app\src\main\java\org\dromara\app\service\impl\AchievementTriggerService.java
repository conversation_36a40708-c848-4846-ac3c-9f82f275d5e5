package org.dromara.app.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.service.IAchievementService;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 成就触发服务，用于监听系统事件并触发成就
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class AchievementTriggerService {

    private final IAchievementService achievementService;

    /**
     * 用户登录事件处理
     *
     * @param event 登录事件
     */
    @Async
    @EventListener(UserLoginEvent.class)
    public void handleUserLogin(UserLoginEvent event) {
        String userId = event.getUserId();
        log.info("收到用户登录事件: {}", userId);

        // 记录登录事件
        Map<String, Object> eventData = new HashMap<>();
        eventData.put("loginType", event.getLoginType());
        eventData.put("ip", event.getIp());
        eventData.put("deviceInfo", event.getDeviceInfo());

        achievementService.recordEvent(userId, "user_login", eventData, 1, null, null);
    }

    /**
     * 用户完成面试事件处理
     *
     * @param event 面试完成事件
     */
    @Async
    @EventListener(InterviewCompletedEvent.class)
    public void handleInterviewCompleted(InterviewCompletedEvent event) {
        String userId = event.getUserId();
        Long interviewId = event.getInterviewId();
        log.info("收到面试完成事件: userId={}, interviewId={}", userId, interviewId);

        // 记录面试完成事件
        Map<String, Object> eventData = new HashMap<>();
        eventData.put("interviewId", interviewId);
        eventData.put("jobId", event.getJobId());
        eventData.put("score", event.getScore());
        eventData.put("duration", event.getDuration());
        eventData.put("mode", event.getMode());
        eventData.put("questionsAnswered", event.getQuestionsAnswered());

        achievementService.recordEvent(userId, "interview_completed", eventData, 1,
            interviewId.toString(), "interview");

        // 如果面试分数达到80分以上，也记录高分事件
        if (event.getScore() >= 80) {
            achievementService.recordEvent(userId, "interview_high_score", eventData, 1,
                interviewId.toString(), "interview");
        }

        // 如果是首次面试，记录首次面试事件
        if (event.isFirstInterview()) {
            achievementService.recordEvent(userId, "first_interview", eventData, 1,
                interviewId.toString(), "interview");
        }
    }

    /**
     * 用户学习事件处理
     *
     * @param event 学习事件
     */
    @Async
    @EventListener(UserLearnEvent.class)
    public void handleUserLearn(UserLearnEvent event) {
        String userId = event.getUserId();
        String learnType = event.getLearnType();
        log.info("收到用户学习事件: userId={}, learnType={}", userId, learnType);

        // 记录学习事件
        Map<String, Object> eventData = new HashMap<>();
        eventData.put("learnType", learnType);
        eventData.put("resourceId", event.getResourceId());
        eventData.put("duration", event.getDuration());
        eventData.put("progress", event.getProgress());
        eventData.put("completed", event.isCompleted());

        achievementService.recordEvent(userId, "user_learn", eventData, 1,
            event.getResourceId(), "learning_resource");

        // 如果完成了学习资源，也记录完成事件
        if (event.isCompleted()) {
            achievementService.recordEvent(userId, "learning_completed", eventData, 1,
                event.getResourceId(), "learning_resource");
        }
    }

    /**
     * 用户连续登录事件处理
     *
     * @param event 连续登录事件
     */
    @Async
    @EventListener(ConsecutiveLoginEvent.class)
    public void handleConsecutiveLogin(ConsecutiveLoginEvent event) {
        String userId = event.getUserId();
        int days = event.getDays();
        log.info("收到用户连续登录事件: userId={}, days={}", userId, days);

        // 记录连续登录事件
        Map<String, Object> eventData = new HashMap<>();
        eventData.put("days", days);

        achievementService.recordEvent(userId, "consecutive_login", eventData, days, null, null);

        // 检查特定天数的连续登录里程碑
        if (days == 7 || days == 15 || days == 30 || days == 100) {
            achievementService.recordEvent(userId, "consecutive_login_milestone", eventData, days, null, null);
        }
    }

    /**
     * 能力提升事件处理
     *
     * @param event 能力提升事件
     */
    @Async
    @EventListener(AbilityImproveEvent.class)
    public void handleAbilityImprove(AbilityImproveEvent event) {
        String userId = event.getUserId();
        String abilityType = event.getAbilityType();
        int points = event.getPoints();
        log.info("收到能力提升事件: userId={}, abilityType={}, points={}", userId, abilityType, points);

        // 记录能力提升事件
        Map<String, Object> eventData = new HashMap<>();
        eventData.put("abilityType", abilityType);
        eventData.put("oldScore", event.getOldScore());
        eventData.put("newScore", event.getNewScore());
        eventData.put("points", points);

        achievementService.recordEvent(userId, "ability_improve", eventData, points, null, null);

        // 如果能力达到了一定分数，记录能力里程碑事件
        if (event.getNewScore() >= 85) {
            achievementService.recordEvent(userId, "ability_milestone", eventData, 1, null, null);
        }

        // 如果短期内能力提升明显（20分以上），记录快速成长事件
        if (points >= 20 && event.isPeriodShort()) {
            achievementService.recordEvent(userId, "rapid_growth", eventData, 1, null, null);
        }
    }

    /**
     * 用户分享事件处理
     *
     * @param event 分享事件
     */
    @Async
    @EventListener(UserShareEvent.class)
    public void handleUserShare(UserShareEvent event) {
        String userId = event.getUserId();
        String shareType = event.getShareType();
        String targetPlatform = event.getTargetPlatform();
        log.info("收到用户分享事件: userId={}, shareType={}, targetPlatform={}", userId, shareType, targetPlatform);

        // 记录分享事件
        Map<String, Object> eventData = new HashMap<>();
        eventData.put("shareType", shareType);
        eventData.put("targetPlatform", targetPlatform);
        eventData.put("contentId", event.getContentId());

        achievementService.recordEvent(userId, "user_share", eventData, 1,
            event.getContentId(), event.getShareType());
    }

    /**
     * 简历提交事件处理
     *
     * @param event 简历提交事件
     */
    @Async
    @EventListener(ResumeSubmitEvent.class)
    public void handleResumeSubmit(ResumeSubmitEvent event) {
        String userId = event.getUserId();
        String resumeId = event.getResumeId();
        log.info("收到简历提交事件: userId={}, resumeId={}", userId, resumeId);

        // 记录简历提交事件
        Map<String, Object> eventData = new HashMap<>();
        eventData.put("resumeId", resumeId);
        eventData.put("resumeType", event.getResumeType());
        eventData.put("completeness", event.getCompleteness());

        achievementService.recordEvent(userId, "resume_submit", eventData, 1, resumeId, "resume");

        // 如果简历完整度高，记录高质量简历事件
        if (event.getCompleteness() >= 90) {
            achievementService.recordEvent(userId, "high_quality_resume", eventData, 1, resumeId, "resume");
        }
    }

    /**
     * 定义系统内可能发生的事件类
     */

    /**
     * 用户登录事件
     */
    public static class UserLoginEvent {
        private String userId;
        private String loginType;  // normal, social, etc.
        private String ip;
        private String deviceInfo;

        public UserLoginEvent(String userId, String loginType, String ip, String deviceInfo) {
            this.userId = userId;
            this.loginType = loginType;
            this.ip = ip;
            this.deviceInfo = deviceInfo;
        }

        public String getUserId() {
            return userId;
        }

        public String getLoginType() {
            return loginType;
        }

        public String getIp() {
            return ip;
        }

        public String getDeviceInfo() {
            return deviceInfo;
        }
    }

    /**
     * 面试完成事件
     */
    public static class InterviewCompletedEvent {
        private String userId;
        private Long interviewId;
        private Long jobId;
        private int score;
        private int duration;  // 单位：分钟
        private String mode;
        private int questionsAnswered;
        private boolean firstInterview;

        public InterviewCompletedEvent(String userId, Long interviewId, Long jobId, int score,
                                     int duration, String mode, int questionsAnswered,
                                     boolean firstInterview) {
            this.userId = userId;
            this.interviewId = interviewId;
            this.jobId = jobId;
            this.score = score;
            this.duration = duration;
            this.mode = mode;
            this.questionsAnswered = questionsAnswered;
            this.firstInterview = firstInterview;
        }

        public String getUserId() {
            return userId;
        }

        public Long getInterviewId() {
            return interviewId;
        }

        public Long getJobId() {
            return jobId;
        }

        public int getScore() {
            return score;
        }

        public int getDuration() {
            return duration;
        }

        public String getMode() {
            return mode;
        }

        public int getQuestionsAnswered() {
            return questionsAnswered;
        }

        public boolean isFirstInterview() {
            return firstInterview;
        }
    }

    /**
     * 用户学习事件
     */
    public static class UserLearnEvent {
        private String userId;
        private String learnType;  // article, video, quiz, etc.
        private String resourceId;
        private int duration;      // 单位：分钟
        private int progress;      // 百分比
        private boolean completed;

        public UserLearnEvent(String userId, String learnType, String resourceId, int duration,
                            int progress, boolean completed) {
            this.userId = userId;
            this.learnType = learnType;
            this.resourceId = resourceId;
            this.duration = duration;
            this.progress = progress;
            this.completed = completed;
        }

        public String getUserId() {
            return userId;
        }

        public String getLearnType() {
            return learnType;
        }

        public String getResourceId() {
            return resourceId;
        }

        public int getDuration() {
            return duration;
        }

        public int getProgress() {
            return progress;
        }

        public boolean isCompleted() {
            return completed;
        }
    }

    /**
     * 连续登录事件
     */
    public static class ConsecutiveLoginEvent {
        private String userId;
        private int days;

        public ConsecutiveLoginEvent(String userId, int days) {
            this.userId = userId;
            this.days = days;
        }

        public String getUserId() {
            return userId;
        }

        public int getDays() {
            return days;
        }
    }

    /**
     * 能力提升事件
     */
    public static class AbilityImproveEvent {
        private String userId;
        private String abilityType;  // professional, logical, communication, etc.
        private int oldScore;
        private int newScore;
        private int points;
        private boolean periodShort;  // 是否在短时间内提升（例如一个月内）

        public AbilityImproveEvent(String userId, String abilityType, int oldScore, int newScore,
                                 boolean periodShort) {
            this.userId = userId;
            this.abilityType = abilityType;
            this.oldScore = oldScore;
            this.newScore = newScore;
            this.points = newScore - oldScore;
            this.periodShort = periodShort;
        }

        public String getUserId() {
            return userId;
        }

        public String getAbilityType() {
            return abilityType;
        }

        public int getOldScore() {
            return oldScore;
        }

        public int getNewScore() {
            return newScore;
        }

        public int getPoints() {
            return points;
        }

        public boolean isPeriodShort() {
            return periodShort;
        }
    }

    /**
     * 用户分享事件
     */
    public static class UserShareEvent {
        private String userId;
        private String shareType;      // interview_result, achievement, etc.
        private String targetPlatform; // wechat, qq, weibo, etc.
        private String contentId;      // 分享内容的ID

        public UserShareEvent(String userId, String shareType, String targetPlatform, String contentId) {
            this.userId = userId;
            this.shareType = shareType;
            this.targetPlatform = targetPlatform;
            this.contentId = contentId;
        }

        public String getUserId() {
            return userId;
        }

        public String getShareType() {
            return shareType;
        }

        public String getTargetPlatform() {
            return targetPlatform;
        }

        public String getContentId() {
            return contentId;
        }
    }

    /**
     * 简历提交事件
     */
    public static class ResumeSubmitEvent {
        private String userId;
        private String resumeId;
        private String resumeType;  // pdf, word, online, etc.
        private int completeness;   // 简历完整度，百分比

        public ResumeSubmitEvent(String userId, String resumeId, String resumeType, int completeness) {
            this.userId = userId;
            this.resumeId = resumeId;
            this.resumeType = resumeType;
            this.completeness = completeness;
        }

        public String getUserId() {
            return userId;
        }

        public String getResumeId() {
            return resumeId;
        }

        public String getResumeType() {
            return resumeType;
        }

        public int getCompleteness() {
            return completeness;
        }
    }
}
