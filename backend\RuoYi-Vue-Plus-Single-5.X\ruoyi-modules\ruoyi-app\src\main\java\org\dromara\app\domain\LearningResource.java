package org.dromara.app.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.util.List;
import java.util.Map;

/**
 * 学习资源实体
 *
 * <AUTHOR>
 */
@Data
@TableName(value = "app_learning_resource", autoResultMap = true)
public class LearningResource extends BaseEntity {

    /**
     * 资源ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 资源标题
     */
    private String title;


    private String type;

    /**
     * 资源描述
     */
    private String description;

    /**
     * 资源URL
     */
    private String url;

    /**
     * 预计学习时长
     */
    private String duration;

    /**
     * 难度等级：简单, 中等, 困难
     */
    private String difficulty;

    /**
     * 技能领域
     */
    private String skillArea;

    /**
     * 资源分类
     */
    private String category;

    /**
     * 资源评分（1-5分）
     */
    private Double rating;

    /**
     * 评分人数
     */
    private Integer ratingCount;

    /**
     * 资源提供者
     */
    private String provider;

    /**
     * 是否免费
     */
    private Boolean isFree;

    /**
     * 价格（如果收费）
     */
    private String price;

    /**
     * 语言
     */
    private String language;

    /**
     * 资源状态：active-活跃, inactive-不活跃, deprecated-已废弃
     */
    private String status;

    /**
     * 资源标签
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> tags;

    /**
     * 前置条件
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> prerequisites;

    /**
     * 学习目标
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> learningObjectives;

    /**
     * 资源元数据
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> metadata;

    /**
     * 质量评估
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private QualityAssessment qualityAssessment;

    /**
     * 使用统计
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private UsageStatistics usageStatistics;

    /**
     * 质量评估
     */
    @Data
    public static class QualityAssessment {
        /**
         * 内容质量评分
         */
        private Double contentQuality;

        /**
         * 教学效果评分
         */
        private Double teachingEffectiveness;

        /**
         * 更新及时性评分
         */
        private Double timeliness;

        /**
         * 用户体验评分
         */
        private Double userExperience;

        /**
         * 综合质量评分
         */
        private Double overallQuality;

        /**
         * 评估时间
         */
        private String assessmentDate;

        /**
         * 评估备注
         */
        private String notes;
    }

    /**
     * 使用统计
     */
    @Data
    public static class UsageStatistics {
        /**
         * 浏览次数
         */
        private Long viewCount;

        /**
         * 学习人数
         */
        private Long learnerCount;

        /**
         * 完成人数
         */
        private Long completionCount;

        /**
         * 推荐次数
         */
        private Long recommendationCount;

        /**
         * 收藏次数
         */
        private Long favoriteCount;

        /**
         * 分享次数
         */
        private Long shareCount;

        /**
         * 平均学习时长
         */
        private String averageLearningTime;

        /**
         * 完成率
         */
        private Double completionRate;

        /**
         * 最后更新时间
         */
        private String lastUpdated;
    }
}
