{"doc": "\n stream 流工具类\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "filter", "paramTypes": ["java.util.Collection", "java.util.function.Predicate"], "doc": "\n 将collection过滤\r\n\r\n @param collection 需要转化的集合\r\n @param function   过滤方法\r\n @return 过滤后的list\r\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["java.util.Collection", "java.util.function.Predicate"], "doc": "\n 找到流中满足条件的第一个元素\r\n\r\n @param collection 需要查询的集合\r\n @param function   过滤方法\r\n @return 找到符合条件的第一个元素，没有则返回null\r\n"}, {"name": "findAny", "paramTypes": ["java.util.Collection", "java.util.function.Predicate"], "doc": "\n 找到流中任意一个满足条件的元素\r\n\r\n @param collection 需要查询的集合\r\n @param function   过滤方法\r\n @return 找到符合条件的任意一个元素，没有则返回null\r\n"}, {"name": "join", "paramTypes": ["java.util.Collection", "java.util.function.Function"], "doc": "\n 将collection拼接\r\n\r\n @param collection 需要转化的集合\r\n @param function   拼接方法\r\n @return 拼接后的list\r\n"}, {"name": "join", "paramTypes": ["java.util.Collection", "java.util.function.Function", "java.lang.CharSequence"], "doc": "\n 将collection拼接\r\n\r\n @param collection 需要转化的集合\r\n @param function   拼接方法\r\n @param delimiter  拼接符\r\n @return 拼接后的list\r\n"}, {"name": "sorted", "paramTypes": ["java.util.Collection", "java.util.Comparator"], "doc": "\n 将collection排序\r\n\r\n @param collection 需要转化的集合\r\n @param comparing  排序方法\r\n @return 排序后的list\r\n"}, {"name": "toIdentityMap", "paramTypes": ["java.util.Collection", "java.util.function.Function"], "doc": "\n 将collection转化为类型不变的map<br>\r\n <B>{@code Collection<V>  ---->  Map<K,V>}</B>\r\n\r\n @param collection 需要转化的集合\r\n @param key        V类型转化为K类型的lambda方法\r\n @param <V>        collection中的泛型\r\n @param <K>        map中的key类型\r\n @return 转化后的map\r\n"}, {"name": "toMap", "paramTypes": ["java.util.Collection", "java.util.function.Function", "java.util.function.Function"], "doc": "\n 将Collection转化为map(value类型与collection的泛型不同)<br>\r\n <B>{@code Collection<E> -----> Map<K,V>  }</B>\r\n\r\n @param collection 需要转化的集合\r\n @param key        E类型转化为K类型的lambda方法\r\n @param value      E类型转化为V类型的lambda方法\r\n @param <E>        collection中的泛型\r\n @param <K>        map中的key类型\r\n @param <V>        map中的value类型\r\n @return 转化后的map\r\n"}, {"name": "groupByKey", "paramTypes": ["java.util.Collection", "java.util.function.Function"], "doc": "\n 将collection按照规则(比如有相同的班级id)分类成map<br>\r\n <B>{@code Collection<E> -------> Map<K,List<E>> } </B>\r\n\r\n @param collection 需要分类的集合\r\n @param key        分类的规则\r\n @param <E>        collection中的泛型\r\n @param <K>        map中的key类型\r\n @return 分类后的map\r\n"}, {"name": "groupBy2Key", "paramTypes": ["java.util.Collection", "java.util.function.Function", "java.util.function.Function"], "doc": "\n 将collection按照两个规则(比如有相同的年级id,班级id)分类成双层map<br>\r\n <B>{@code Collection<E>  --->  Map<T,Map<U,List<E>>> } </B>\r\n\r\n @param collection 需要分类的集合\r\n @param key1       第一个分类的规则\r\n @param key2       第二个分类的规则\r\n @param <E>        集合元素类型\r\n @param <K>        第一个map中的key类型\r\n @param <U>        第二个map中的key类型\r\n @return 分类后的map\r\n"}, {"name": "group2Map", "paramTypes": ["java.util.Collection", "java.util.function.Function", "java.util.function.Function"], "doc": "\n 将collection按照两个规则(比如有相同的年级id,班级id)分类成双层map<br>\r\n <B>{@code Collection<E>  --->  Map<T,Map<U,E>> } </B>\r\n\r\n @param collection 需要分类的集合\r\n @param key1       第一个分类的规则\r\n @param key2       第二个分类的规则\r\n @param <T>        第一个map中的key类型\r\n @param <U>        第二个map中的key类型\r\n @param <E>        collection中的泛型\r\n @return 分类后的map\r\n"}, {"name": "toList", "paramTypes": ["java.util.Collection", "java.util.function.Function"], "doc": "\n 将collection转化为List集合，但是两者的泛型不同<br>\r\n <B>{@code Collection<E>  ------>  List<T> } </B>\r\n\r\n @param collection 需要转化的集合\r\n @param function   collection中的泛型转化为list泛型的lambda表达式\r\n @param <E>        collection中的泛型\r\n @param <T>        List中的泛型\r\n @return 转化后的list\r\n"}, {"name": "toSet", "paramTypes": ["java.util.Collection", "java.util.function.Function"], "doc": "\n 将collection转化为Set集合，但是两者的泛型不同<br>\r\n <B>{@code Collection<E>  ------>  Set<T> } </B>\r\n\r\n @param collection 需要转化的集合\r\n @param function   collection中的泛型转化为set泛型的lambda表达式\r\n @param <E>        collection中的泛型\r\n @param <T>        Set中的泛型\r\n @return 转化后的Set\r\n"}, {"name": "merge", "paramTypes": ["java.util.Map", "java.util.Map", "java.util.function.BiFunction"], "doc": "\n 合并两个相同key类型的map\r\n\r\n @param map1  第一个需要合并的 map\r\n @param map2  第二个需要合并的 map\r\n @param merge 合并的lambda，将key  value1 value2合并成最终的类型,注意value可能为空的情况\r\n @param <K>   map中的key类型\r\n @param <X>   第一个 map的value类型\r\n @param <Y>   第二个 map的value类型\r\n @param <V>   最终map的value类型\r\n @return 合并后的map\r\n"}], "constructors": []}