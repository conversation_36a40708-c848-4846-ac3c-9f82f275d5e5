{"doc": "\n 数据权限过滤\r\n\r\n <AUTHOR>\r\n @version 3.5.0\r\n", "fields": [{"name": "dataPermissionCacheMap", "doc": "\n 类名称与注解的映射关系缓存(由于aop无法拦截mybatis接口类上的注解 只能通过启动预扫描的方式进行)\r\n"}, {"name": "parser", "doc": "\n spel 解析器\r\n"}, {"name": "beanResolver", "doc": "\n bean解析器 用于处理 spel 表达式中对 bean 的调用\r\n"}], "enumConstants": [], "methods": [{"name": "getSqlSegment", "paramTypes": ["net.sf.jsqlparser.expression.Expression", "java.lang.String", "boolean"], "doc": "\n 获取数据过滤条件的 SQL 片段\r\n\r\n @param where             原始的查询条件表达式\r\n @param mappedStatementId Mapper 方法的 ID\r\n @param isSelect          是否为查询语句\r\n @return 数据过滤条件的 SQL 片段\r\n"}, {"name": "buildDataFilter", "paramTypes": ["org.dromara.common.mybatis.annotation.DataPermission", "boolean"], "doc": "\n 构建数据过滤条件的 SQL 语句\r\n\r\n @param dataPermission 数据权限注解\r\n @param isSelect       标志当前操作是否为查询操作，查询操作和更新或删除操作在处理过滤条件时会有不同的处理方式\r\n @return 构建的数据过滤条件的 SQL 语句\r\n @throws ServiceException 如果角色的数据范围异常或者 key 与 value 的长度不匹配，则抛出 ServiceException 异常\r\n"}, {"name": "scanMapperClasses", "paramTypes": ["java.lang.String"], "doc": "\n 扫描指定包下的 Mapper 类，并查找其中带有特定注解的方法或类\r\n\r\n @param mapperPackage Mapper 类所在的包路径\r\n"}, {"name": "findAnnotation", "paramTypes": ["java.lang.Class"], "doc": "\n 在指定的类中查找特定的注解 DataPermission，并将带有这个注解的方法或类存储到 dataPermissionCacheMap 中\r\n\r\n @param clazz 要查找的类\r\n"}, {"name": "getDataPermission", "paramTypes": ["java.lang.String"], "doc": "\n 根据映射语句 ID 或类名获取对应的 DataPermission 注解对象\r\n\r\n @param mapperId 映射语句 ID\r\n @return DataPermission 注解对象，如果不存在则返回 null\r\n"}, {"name": "invalid", "paramTypes": ["java.lang.String"], "doc": "\n 检查给定的映射语句 ID 是否有效，即是否能够找到对应的 DataPermission 注解对象\r\n\r\n @param mapperId 映射语句 ID\r\n @return 如果找到对应的 DataPermission 注解对象，则返回 false；否则返回 true\r\n"}], "constructors": [{"name": "<init>", "paramTypes": ["java.lang.String"], "doc": "\n 构造方法，扫描指定包下的 Mapper 类并初始化缓存\r\n\r\n @param mapperPackage Mapper 类所在的包路径\r\n"}]}