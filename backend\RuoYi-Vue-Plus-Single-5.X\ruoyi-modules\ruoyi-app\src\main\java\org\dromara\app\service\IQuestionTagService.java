package org.dromara.app.service;

import org.dromara.app.domain.QuestionTag;

import java.util.List;

/**
 * 问题标签Service接口
 *
 * <AUTHOR> Assistant
 * @date 2025-07-19
 */
public interface IQuestionTagService {

    /**
     * 根据分类查询标签
     *
     * @param category 标签分类
     * @return 标签列表
     */
    List<QuestionTag> selectByCategory(String category);

    /**
     * 查询热门标签
     *
     * @param limit 限制数量
     * @return 热门标签列表
     */
    List<QuestionTag> selectHotTags(Integer limit);

    /**
     * 增加标签使用次数
     *
     * @param tagName 标签名称
     * @return 是否成功
     */
    boolean incrementUsageCount(String tagName);

    /**
     * 根据名称查询标签
     *
     * @param names 标签名称列表
     * @return 标签列表
     */
    List<QuestionTag> selectByNames(List<String> names);

    /**
     * 获取所有技术领域的标签
     *
     * @return 按分类分组的标签
     */
    TagsByCategory getAllTagsByCategory();

    /**
     * 按分类分组的标签结果
     */
    class TagsByCategory {
        private List<QuestionTag> aiTags;
        private List<QuestionTag> bigDataTags;
        private List<QuestionTag> iotTags;
        private List<QuestionTag> smartSystemTags;
        private List<QuestionTag> generalTags;

        // getters and setters
        public List<QuestionTag> getAiTags() {
            return aiTags;
        }

        public void setAiTags(List<QuestionTag> aiTags) {
            this.aiTags = aiTags;
        }

        public List<QuestionTag> getBigDataTags() {
            return bigDataTags;
        }

        public void setBigDataTags(List<QuestionTag> bigDataTags) {
            this.bigDataTags = bigDataTags;
        }

        public List<QuestionTag> getIotTags() {
            return iotTags;
        }

        public void setIotTags(List<QuestionTag> iotTags) {
            this.iotTags = iotTags;
        }

        public List<QuestionTag> getSmartSystemTags() {
            return smartSystemTags;
        }

        public void setSmartSystemTags(List<QuestionTag> smartSystemTags) {
            this.smartSystemTags = smartSystemTags;
        }

        public List<QuestionTag> getGeneralTags() {
            return generalTags;
        }

        public void setGeneralTags(List<QuestionTag> generalTags) {
            this.generalTags = generalTags;
        }
    }

}