package org.dromara.app.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.util.Map;

/**
 * 工具调用记录对象 app_tool_call
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_tool_call")
public class ToolCall extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 调用记录ID
     */
    @TableId(value = "id")
    private String id;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 消息ID
     */
    private String messageId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 工具ID
     */
    private String toolId;

    /**
     * 工具名称
     */
    private String toolName;

    /**
     * 调用参数（JSON格式）
     */
    private String parameters;

    /**
     * 调用结果（JSON格式）
     */
    private String result;

    /**
     * 调用状态：0-调用中，1-成功，2-失败
     */
    private Integer status;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 执行时间（毫秒）
     */
    private Long executionTime;

    /**
     * 调用开始时间
     */
    private Long startTime;

    /**
     * 调用结束时间
     */
    private Long endTime;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 调用来源：user/system/auto
     */
    private String source;

    /**
     * 调用上下文（JSON格式）
     */
    private String context;

    /**
     * 参数对象（不存储到数据库）
     */
    @TableField(exist = false)
    private Map<String, Object> parametersObject;

    /**
     * 结果对象（不存储到数据库）
     */
    @TableField(exist = false)
    private ToolCallResult resultObject;

    /**
     * 上下文对象（不存储到数据库）
     */
    @TableField(exist = false)
    private CallContext contextObject;

    /**
     * 工具调用结果内部类
     */
    @Data
    @Builder
    @RequiredArgsConstructor
    @AllArgsConstructor
    @EqualsAndHashCode(callSuper = false)
    public static class ToolCallResult {
        private boolean success;
        private Object data;
        private String message;
        private String type; // text/json/file/image
        private Map<String, Object> metadata;
    }

    /**
     * 调用上下文内部类
     */
    @Data
    public static class CallContext {
        private String userQuery; // 用户原始查询
        private String aiReasoning; // AI推理过程
        private String triggerReason; // 触发原因
        private Map<String, Object> environment; // 环境变量
        private String previousSteps; // 前置步骤
    }
}
