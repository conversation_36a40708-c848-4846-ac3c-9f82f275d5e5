package org.dromara.app.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 异步处理配置
 * 配置多模态数据分析的异步处理线程池
 *
 * <AUTHOR>
 * @date 2025-01-19
 */
@Slf4j
@Configuration
@EnableAsync
public class AsyncConfig {

    @Value("${app.multimodal.max-concurrent-analysis:5}")
    private int maxConcurrentAnalysis;

    @Value("${app.multimodal.analysis-timeout:300}")
    private int analysisTimeoutSeconds;

    /**
     * 分析任务执行器
     * 用于执行多模态分析任务
     */
    @Bean("analysisTaskExecutor")
    public Executor analysisTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 核心线程数
        executor.setCorePoolSize(maxConcurrentAnalysis);
        
        // 最大线程数
        executor.setMaxPoolSize(maxConcurrentAnalysis * 2);
        
        // 队列容量
        executor.setQueueCapacity(100);
        
        // 线程名前缀
        executor.setThreadNamePrefix("analysis-task-");
        
        // 线程空闲时间（秒）
        executor.setKeepAliveSeconds(60);
        
        // 拒绝策略：由调用线程执行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        
        // 等待任务完成后关闭
        executor.setWaitForTasksToCompleteOnShutdown(true);
        
        // 等待时间
        executor.setAwaitTerminationSeconds(30);
        
        executor.initialize();
        
        log.info("分析任务执行器已初始化，核心线程数: {}, 最大线程数: {}, 队列容量: {}", 
            maxConcurrentAnalysis, maxConcurrentAnalysis * 2, 100);
        
        return executor;
    }

    /**
     * 报告生成执行器
     * 用于异步生成报告
     */
    @Bean("reportGenerationExecutor")
    public Executor reportGenerationExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 核心线程数
        executor.setCorePoolSize(2);
        
        // 最大线程数
        executor.setMaxPoolSize(5);
        
        // 队列容量
        executor.setQueueCapacity(50);
        
        // 线程名前缀
        executor.setThreadNamePrefix("report-gen-");
        
        // 线程空闲时间（秒）
        executor.setKeepAliveSeconds(60);
        
        // 拒绝策略：由调用线程执行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        
        // 等待任务完成后关闭
        executor.setWaitForTasksToCompleteOnShutdown(true);
        
        // 等待时间
        executor.setAwaitTerminationSeconds(30);
        
        executor.initialize();
        
        log.info("报告生成执行器已初始化");
        
        return executor;
    }

    /**
     * 缓存优化执行器
     * 用于异步执行缓存优化任务
     */
    @Bean("cacheOptimizationExecutor")
    public Executor cacheOptimizationExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 核心线程数
        executor.setCorePoolSize(1);
        
        // 最大线程数
        executor.setMaxPoolSize(3);
        
        // 队列容量
        executor.setQueueCapacity(20);
        
        // 线程名前缀
        executor.setThreadNamePrefix("cache-opt-");
        
        // 线程空闲时间（秒）
        executor.setKeepAliveSeconds(60);
        
        // 拒绝策略：丢弃最旧的任务
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.DiscardOldestPolicy());
        
        // 等待任务完成后关闭
        executor.setWaitForTasksToCompleteOnShutdown(true);
        
        // 等待时间
        executor.setAwaitTerminationSeconds(15);
        
        executor.initialize();
        
        log.info("缓存优化执行器已初始化");
        
        return executor;
    }

    /**
     * 数据清理执行器
     * 用于异步执行数据清理任务
     */
    @Bean("dataCleanupExecutor")
    public Executor dataCleanupExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 核心线程数
        executor.setCorePoolSize(1);
        
        // 最大线程数
        executor.setMaxPoolSize(2);
        
        // 队列容量
        executor.setQueueCapacity(10);
        
        // 线程名前缀
        executor.setThreadNamePrefix("data-cleanup-");
        
        // 线程空闲时间（秒）
        executor.setKeepAliveSeconds(120);
        
        // 拒绝策略：丢弃任务
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.DiscardPolicy());
        
        // 等待任务完成后关闭
        executor.setWaitForTasksToCompleteOnShutdown(true);
        
        // 等待时间
        executor.setAwaitTerminationSeconds(60);
        
        executor.initialize();
        
        log.info("数据清理执行器已初始化");
        
        return executor;
    }
}