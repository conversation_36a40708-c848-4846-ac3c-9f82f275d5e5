package org.dromara.app.service;

import org.dromara.app.domain.dto.AppUserProfileDto;
import org.dromara.app.domain.vo.AppUserProfileVo;

/**
 * 应用用户信息Service接口
 *
 * <AUTHOR>
 */
public interface IAppUserProfileService {

    /**
     * 获取用户个人信息详情
     */
    AppUserProfileVo getUserProfile(Long userId);

    /**
     * 更新用户个人信息
     */
    boolean updateUserProfile(Long userId, AppUserProfileDto profileDto);

}
