package org.dromara.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.dromara.app.domain.ChatMessage;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 聊天消息Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface ChatMessageMapper extends BaseMapper<ChatMessage> {

    /**
     * 分页查询会话消息列表
     *
     * @param page      分页对象
     * @param sessionId 会话ID
     * @param userId    用户ID
     * @return 消息分页结果
     */
    Page<ChatMessage> selectSessionMessages(Page<ChatMessage> page,
                                            @Param("sessionId") String sessionId,
                                            @Param("userId") Long userId);

    /**
     * 查询会话的最新N条消息（用于构建上下文）
     *
     * @param sessionId 会话ID
     * @param userId    用户ID
     * @param limit     消息数量限制
     * @return 消息列表
     */
    @Select("SELECT * FROM app_chat_message " +
        "WHERE session_id = #{sessionId} AND user_id = #{userId} AND del_flag = '0' " +
        "ORDER BY create_time DESC LIMIT #{limit}")
    List<ChatMessage> selectRecentMessages(@Param("sessionId") String sessionId,
                                           @Param("userId") Long userId,
                                           @Param("limit") Integer limit);

    /**
     * 查询会话的第一条用户消息（用于生成标题）
     *
     * @param sessionId 会话ID
     * @param userId    用户ID
     * @return 第一条用户消息
     */
    @Select("SELECT * FROM app_chat_message " +
        "WHERE session_id = #{sessionId} AND user_id = #{userId} AND role = 'user' " +
        "AND del_flag = '0' ORDER BY create_time ASC LIMIT 1")
    ChatMessage selectFirstUserMessage(@Param("sessionId") String sessionId, @Param("userId") Long userId);

    /**
     * 统计会话消息数量
     *
     * @param sessionId 会话ID
     * @param userId    用户ID
     * @return 消息数量
     */
    @Select("SELECT COUNT(*) FROM app_chat_message " +
        "WHERE session_id = #{sessionId} AND user_id = #{userId} AND del_flag = '0'")
    Integer countSessionMessages(@Param("sessionId") String sessionId, @Param("userId") Long userId);

    /**
     * 清空会话消息（软删除）
     *
     * @param sessionId 会话ID
     * @param userId    用户ID
     * @return 影响行数
     */
    @Update("UPDATE app_chat_message SET del_flag = '1', update_time = NOW() " +
        "WHERE session_id = #{sessionId} AND user_id = #{userId}")
    int deleteSessionMessages(@Param("sessionId") String sessionId, @Param("userId") Long userId);

    /**
     * 更新消息状态
     *
     * @param messageId 消息ID
     * @param status    消息状态
     * @return 影响行数
     */
    @Update("UPDATE app_chat_message SET status = #{status}, update_time = NOW() WHERE id = #{messageId}")
    int updateMessageStatus(@Param("messageId") String messageId, @Param("status") Integer status);

    /**
     * 标记消息为已读
     *
     * @param messageIds 消息ID列表
     * @param userId     用户ID
     * @return 影响行数
     */
    @Update("<script>" +
        "UPDATE app_chat_message SET is_read = 1, update_time = NOW() " +
        "WHERE user_id = #{userId} AND id IN " +
        "<foreach collection='messageIds' item='id' open='(' close=')' separator=','>" +
        "#{id}" +
        "</foreach>" +
        "</script>")
    int markMessagesAsRead(@Param("messageIds") List<String> messageIds, @Param("userId") Long userId);

    /**
     * 查询用户未读消息数量
     *
     * @param userId 用户ID
     * @return 未读消息数量
     */
    @Select("SELECT COUNT(*) FROM app_chat_message " +
        "WHERE user_id = #{userId} AND role = 'assistant' AND is_read = 0 AND del_flag = '0'")
    Integer countUnreadMessages(@Param("userId") Long userId);

    /**
     * 根据消息类型统计数量
     *
     * @param userId      用户ID
     * @param messageType 消息类型
     * @return 消息数量
     */
    @Select("SELECT COUNT(*) FROM app_chat_message " +
        "WHERE user_id = #{userId} AND message_type = #{messageType} AND del_flag = '0'")
    Integer countMessagesByType(@Param("userId") Long userId, @Param("messageType") String messageType);

    /**
     * 批量插入消息
     *
     * @param messages 消息列表
     * @return 影响行数
     */
    int batchInsertMessages(@Param("messages") List<ChatMessage> messages);

    /**
     * 搜索消息内容
     *
     * @param userId    用户ID
     * @param sessionId 会话ID（可选）
     * @param keyword   搜索关键词
     * @param startTime 开始时间（可选）
     * @param endTime   结束时间（可选）
     * @return 消息列表
     */
    List<ChatMessage> searchMessages(@Param("userId") Long userId,
                                     @Param("sessionId") String sessionId,
                                     @Param("keyword") String keyword,
                                     @Param("startTime") LocalDateTime startTime,
                                     @Param("endTime") LocalDateTime endTime);

    /**
     * 获取用户消息统计信息
     *
     * @param userId    用户ID
     * @param startTime 开始时间（可选）
     * @param endTime   结束时间（可选）
     * @return 统计信息
     */
    Map<String, Object> getMessageStats(@Param("userId") Long userId,
                                        @Param("startTime") LocalDateTime startTime,
                                        @Param("endTime") LocalDateTime endTime);

    /**
     * 获取会话中的最后一条消息
     *
     * @param sessionId 会话ID
     * @param userId    用户ID
     * @return 最后一条消息
     */
    @Select("SELECT * FROM app_chat_message " +
        "WHERE session_id = #{sessionId} AND user_id = #{userId} AND del_flag = '0' " +
        "ORDER BY create_time DESC LIMIT 1")
    ChatMessage selectLastMessage(@Param("sessionId") String sessionId, @Param("userId") Long userId);

    /**
     * 更新消息错误信息
     *
     * @param messageId    消息ID
     * @param errorMessage 错误信息
     * @return 影响行数
     */
    @Update("UPDATE app_chat_message SET status = 2, error_message = #{errorMessage}, update_time = NOW() " +
        "WHERE id = #{messageId}")
    int updateMessageError(@Param("messageId") String messageId, @Param("errorMessage") String errorMessage);

    /**
     * 根据父消息ID查询子消息
     *
     * @param parentMessageId 父消息ID
     * @return 子消息列表
     */
    @Select("SELECT * FROM app_chat_message " +
        "WHERE parent_message_id = #{parentMessageId} AND del_flag = '0' " +
        "ORDER BY create_time ASC")
    List<ChatMessage> selectChildMessages(@Param("parentMessageId") String parentMessageId);

    /**
     * 统计用户消息总数
     *
     * @param userId 用户ID
     * @return 消息总数
     */
    @Select("SELECT COUNT(*) FROM app_chat_message WHERE user_id = #{userId} AND del_flag = '0'")
    Integer countUserMessages(@Param("userId") Long userId);

    /**
     * 统计今日消息数
     *
     * @param userId 用户ID
     * @return 今日消息数
     */
    @Select("SELECT COUNT(*) FROM app_chat_message " +
        "WHERE user_id = #{userId} AND del_flag = '0' " +
        "AND DATE(create_time) = CURDATE()")
    Integer countTodayMessages(@Param("userId") Long userId);
}
