package org.dromara.app.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.util.List;

/**
 * AI代理对象 app_agent
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_agent")
public class Agent extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 代理ID
     */
    @TableId(value = "id")
    private String id;

    /**
     * 代理名称
     */
    private String name;

    /**
     * 代理描述
     */
    private String description;

    /**
     * 代理图标
     */
    private String icon;

    /**
     * 代理颜色
     */
    private String color;

    /**
     * 代理类型：general/interviewer/resume_analyzer/skill_assessor/career_advisor/mock_interviewer/learning_guide
     */
    private String agentType;

    /**
     * 系统提示词
     */
    private String systemPrompt;

    /**
     * 模型配置（JSON格式）
     */
    private String modelConfig;

    /**
     * 代理能力列表（JSON格式）
     */
    private String capabilities;

    /**
     * 快速操作列表（JSON格式）
     */
    private String quickActions;

    /**
     * 是否启用：0-禁用，1-启用
     */
    private Boolean enabled;

    /**
     * 排序序号
     */
    private Integer sortOrder;

    /**
     * 使用次数
     */
    private Long usageCount;

    /**
     * 平均评分
     */
    private Double averageRating;

    /**
     * 代理配置扩展（JSON格式）
     */
    private String extendConfig;

    /**
     * 能力列表（不存储到数据库，用于返回给前端）
     */
    @TableField(exist = false)
    private List<String> capabilityList;

    /**
     * 快速操作列表（不存储到数据库，用于返回给前端）
     */
    @TableField(exist = false)
    private List<QuickAction> quickActionList;

    /**
     * 模型配置对象（不存储到数据库）
     */
    @TableField(exist = false)
    private ModelConfig modelConfigObject;

    /**
     * delFlag
     */
    private String delFlag;

    /**
     * 快速操作内部类
     */
    @Data
    public static class QuickAction {
        private String id;
        private String title;
        private String description;
        private String icon;
        private String prompt;
        private String category;
    }

    /**
     * 模型配置内部类
     */
    @Data
    public static class ModelConfig {
        private String primaryModel; // 主要模型（如 llama3.1:8b）
        private String fallbackModel; // 备用模型
        private Double temperature;
        private Integer maxTokens;
        private Double topP;
        private String provider; // ollama/openai/rjb-sias等
        private Boolean enableStreaming;
        private Integer timeoutSeconds;
    }
}
