2025-07-24 20:06:06 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatMessageMapper.searchMessages] is ignored, because it exists, maybe from xml file
2025-07-24 20:06:06 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatMessageMapper.getMessageStats] is ignored, because it exists, maybe from xml file
2025-07-24 20:06:06 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatMessageMapper.selectRecentMessages] is ignored, because it exists, maybe from xml file
2025-07-24 20:06:06 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatMessageMapper.countSessionMessages] is ignored, because it exists, maybe from xml file
2025-07-24 20:06:06 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.countUserSessions] is ignored, because it exists, maybe from xml file
2025-07-24 20:06:06 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.getPopularAgentTypes] is ignored, because it exists, maybe from xml file
2025-07-24 20:06:06 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.selectRecentSessions] is ignored, because it exists, maybe from xml file
2025-07-24 20:06:06 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.selectActiveSessions] is ignored, because it exists, maybe from xml file
2025-07-24 20:06:06 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.selectUserSessionsByAgent] is ignored, because it exists, maybe from xml file
2025-07-24 20:06:06 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.selectSessionStats] is ignored, because it exists, maybe from xml file
2025-07-24 20:06:06 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ToolCallMapper.getUserToolStats] is ignored, because it exists, maybe from xml file
2025-07-24 20:09:54 [main] ERROR o.s.a.r.l.SimpleMessageListenerContainer - Consumer failed to start in 60000 milliseconds; does the task executor have enough threads to support the container concurrency?
2025-07-24 20:12:00 [main] ERROR o.s.a.r.l.SimpleMessageListenerContainer - Consumer failed to start in 60000 milliseconds; does the task executor have enough threads to support the container concurrency?
2025-07-24 20:12:01 [main] ERROR o.s.b.c.p.m.PropertiesMigrationListener - 
The use of configuration keys that are no longer supported was found in the environment:

Property source 'Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/' (document #10)':
	Key: xunfei.spark.api-key
		Line: 421
		Reason: none
	Key: xunfei.spark.api-secret
		Line: 422
		Reason: none

Property source 'Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' (document #19)':
	Key: xunfei.spark.api-key
		Line: 872
		Reason: none
	Key: xunfei.spark.api-secret
		Line: 873
		Reason: none


Please refer to the release notes or reference guide for potential alternatives.

