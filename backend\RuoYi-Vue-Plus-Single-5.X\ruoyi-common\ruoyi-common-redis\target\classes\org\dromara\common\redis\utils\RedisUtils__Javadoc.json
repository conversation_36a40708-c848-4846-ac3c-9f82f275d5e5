{"doc": "\n redis 工具类\r\n\r\n <AUTHOR>\r\n @version 3.1.0 新增\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "rateLimiter", "paramTypes": ["java.lang.String", "org.redisson.api.RateType", "int", "int"], "doc": "\n 限流\r\n\r\n @param key          限流key\r\n @param rateType     限流类型\r\n @param rate         速率\r\n @param rateInterval 速率间隔\r\n @return -1 表示失败\r\n"}, {"name": "rateLimiter", "paramTypes": ["java.lang.String", "org.redisson.api.RateType", "int", "int", "int"], "doc": "\n 限流\r\n\r\n @param key          限流key\r\n @param rateType     限流类型\r\n @param rate         速率\r\n @param rateInterval 速率间隔\r\n @param timeout      超时时间\r\n @return -1 表示失败\r\n"}, {"name": "getClient", "paramTypes": [], "doc": "\n 获取客户端实例\r\n"}, {"name": "publish", "paramTypes": ["java.lang.String", "java.lang.Object", "java.util.function.Consumer"], "doc": "\n 发布通道消息\r\n\r\n @param channelKey 通道key\r\n @param msg        发送数据\r\n @param consumer   自定义处理\r\n"}, {"name": "publish", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": "\n 发布消息到指定的频道\r\n\r\n @param channelKey 通道key\r\n @param msg        发送数据\r\n"}, {"name": "subscribe", "paramTypes": ["java.lang.String", "java.lang.Class", "java.util.function.Consumer"], "doc": "\n 订阅通道接收消息\r\n\r\n @param channelKey 通道key\r\n @param clazz      消息类型\r\n @param consumer   自定义处理\r\n"}, {"name": "setCacheObject", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": "\n 缓存基本的对象，Integer、String、实体类等\r\n\r\n @param key   缓存的键值\r\n @param value 缓存的值\r\n"}, {"name": "setCacheObject", "paramTypes": ["java.lang.String", "java.lang.Object", "boolean"], "doc": "\n 缓存基本的对象，保留当前对象 TTL 有效期\r\n\r\n @param key       缓存的键值\r\n @param value     缓存的值\r\n @param isSaveTtl 是否保留TTL有效期(例如: set之前ttl剩余90 set之后还是为90)\r\n @since Redis 6.X 以上使用 setAndKeepTTL 兼容 5.X 方案\r\n"}, {"name": "setCacheObject", "paramTypes": ["java.lang.String", "java.lang.Object", "java.time.Duration"], "doc": "\n 缓存基本的对象，Integer、String、实体类等\r\n\r\n @param key      缓存的键值\r\n @param value    缓存的值\r\n @param duration 时间\r\n"}, {"name": "setObjectIfAbsent", "paramTypes": ["java.lang.String", "java.lang.Object", "java.time.Duration"], "doc": "\n 如果不存在则设置 并返回 true 如果存在则返回 false\r\n\r\n @param key   缓存的键值\r\n @param value 缓存的值\r\n @return set成功或失败\r\n"}, {"name": "setObjectIfExists", "paramTypes": ["java.lang.String", "java.lang.Object", "java.time.Duration"], "doc": "\n 如果存在则设置 并返回 true 如果存在则返回 false\r\n\r\n @param key   缓存的键值\r\n @param value 缓存的值\r\n @return set成功或失败\r\n"}, {"name": "addObjectListener", "paramTypes": ["java.lang.String", "org.redisson.api.ObjectListener"], "doc": "\n 注册对象监听器\r\n <p>\r\n key 监听器需开启 `notify-keyspace-events` 等 redis 相关配置\r\n\r\n @param key      缓存的键值\r\n @param listener 监听器配置\r\n"}, {"name": "expire", "paramTypes": ["java.lang.String", "long"], "doc": "\n 设置有效时间\r\n\r\n @param key     Redis键\r\n @param timeout 超时时间\r\n @return true=设置成功；false=设置失败\r\n"}, {"name": "expire", "paramTypes": ["java.lang.String", "java.time.Duration"], "doc": "\n 设置有效时间\r\n\r\n @param key      Redis键\r\n @param duration 超时时间\r\n @return true=设置成功；false=设置失败\r\n"}, {"name": "getCacheObject", "paramTypes": ["java.lang.String"], "doc": "\n 获得缓存的基本对象。\r\n\r\n @param key 缓存键值\r\n @return 缓存键值对应的数据\r\n"}, {"name": "getTimeToLive", "paramTypes": ["java.lang.String"], "doc": "\n 获得key剩余存活时间\r\n\r\n @param key 缓存键值\r\n @return 剩余存活时间\r\n"}, {"name": "deleteObject", "paramTypes": ["java.lang.String"], "doc": "\n 删除单个对象\r\n\r\n @param key 缓存的键值\r\n"}, {"name": "deleteObject", "paramTypes": ["java.util.Collection"], "doc": "\n 删除集合对象\r\n\r\n @param collection 多个对象\r\n"}, {"name": "isExistsObject", "paramTypes": ["java.lang.String"], "doc": "\n 检查缓存对象是否存在\r\n\r\n @param key 缓存的键值\r\n"}, {"name": "setCacheList", "paramTypes": ["java.lang.String", "java.util.List"], "doc": "\n 缓存List数据\r\n\r\n @param key      缓存的键值\r\n @param dataList 待缓存的List数据\r\n @return 缓存的对象\r\n"}, {"name": "addCacheList", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": "\n 追加缓存List数据\r\n\r\n @param key  缓存的键值\r\n @param data 待缓存的数据\r\n @return 缓存的对象\r\n"}, {"name": "addListListener", "paramTypes": ["java.lang.String", "org.redisson.api.ObjectListener"], "doc": "\n 注册List监听器\r\n <p>\r\n key 监听器需开启 `notify-keyspace-events` 等 redis 相关配置\r\n\r\n @param key      缓存的键值\r\n @param listener 监听器配置\r\n"}, {"name": "getCacheList", "paramTypes": ["java.lang.String"], "doc": "\n 获得缓存的list对象\r\n\r\n @param key 缓存的键值\r\n @return 缓存键值对应的数据\r\n"}, {"name": "getCacheListRange", "paramTypes": ["java.lang.String", "int", "int"], "doc": "\n 获得缓存的list对象(范围)\r\n\r\n @param key  缓存的键值\r\n @param form 起始下标\r\n @param to   截止下标\r\n @return 缓存键值对应的数据\r\n"}, {"name": "setCacheSet", "paramTypes": ["java.lang.String", "java.util.Set"], "doc": "\n 缓存Set\r\n\r\n @param key     缓存键值\r\n @param dataSet 缓存的数据\r\n @return 缓存数据的对象\r\n"}, {"name": "addCacheSet", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": "\n 追加缓存Set数据\r\n\r\n @param key  缓存的键值\r\n @param data 待缓存的数据\r\n @return 缓存的对象\r\n"}, {"name": "addSetListener", "paramTypes": ["java.lang.String", "org.redisson.api.ObjectListener"], "doc": "\n 注册Set监听器\r\n <p>\r\n key 监听器需开启 `notify-keyspace-events` 等 redis 相关配置\r\n\r\n @param key      缓存的键值\r\n @param listener 监听器配置\r\n"}, {"name": "getCacheSet", "paramTypes": ["java.lang.String"], "doc": "\n 获得缓存的set\r\n\r\n @param key 缓存的key\r\n @return set对象\r\n"}, {"name": "setCacheMap", "paramTypes": ["java.lang.String", "java.util.Map"], "doc": "\n 缓存Map\r\n\r\n @param key     缓存的键值\r\n @param dataMap 缓存的数据\r\n"}, {"name": "addMapListener", "paramTypes": ["java.lang.String", "org.redisson.api.ObjectListener"], "doc": "\n 注册Map监听器\r\n <p>\r\n key 监听器需开启 `notify-keyspace-events` 等 redis 相关配置\r\n\r\n @param key      缓存的键值\r\n @param listener 监听器配置\r\n"}, {"name": "getCacheMap", "paramTypes": ["java.lang.String"], "doc": "\n 获得缓存的Map\r\n\r\n @param key 缓存的键值\r\n @return map对象\r\n"}, {"name": "getCacheMapKeySet", "paramTypes": ["java.lang.String"], "doc": "\n 获得缓存Map的key列表\r\n\r\n @param key 缓存的键值\r\n @return key列表\r\n"}, {"name": "setCacheMapValue", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Object"], "doc": "\n 往Hash中存入数据\r\n\r\n @param key   Redis键\r\n @param hKey  Hash键\r\n @param value 值\r\n"}, {"name": "getCacheMapValue", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 获取Hash中的数据\r\n\r\n @param key  Redis键\r\n @param hKey Hash键\r\n @return Hash中的对象\r\n"}, {"name": "delCacheMapValue", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 删除Hash中的数据\r\n\r\n @param key  Redis键\r\n @param hKey Hash键\r\n @return Hash中的对象\r\n"}, {"name": "delMultiCacheMapValue", "paramTypes": ["java.lang.String", "java.util.Set"], "doc": "\n 删除Hash中的数据\r\n\r\n @param key   Redis键\r\n @param hKeys Hash键\r\n"}, {"name": "getMultiCacheMapValue", "paramTypes": ["java.lang.String", "java.util.Set"], "doc": "\n 获取多个Hash中的数据\r\n\r\n @param key   Redis键\r\n @param hKeys Hash键集合\r\n @return Hash对象集合\r\n"}, {"name": "setAtomicValue", "paramTypes": ["java.lang.String", "long"], "doc": "\n 设置原子值\r\n\r\n @param key   Redis键\r\n @param value 值\r\n"}, {"name": "getAtomicValue", "paramTypes": ["java.lang.String"], "doc": "\n 获取原子值\r\n\r\n @param key Redis键\r\n @return 当前值\r\n"}, {"name": "incrAtomicValue", "paramTypes": ["java.lang.String"], "doc": "\n 递增原子值\r\n\r\n @param key Redis键\r\n @return 当前值\r\n"}, {"name": "decrAtomicValue", "paramTypes": ["java.lang.String"], "doc": "\n 递减原子值\r\n\r\n @param key Redis键\r\n @return 当前值\r\n"}, {"name": "keys", "paramTypes": ["java.lang.String"], "doc": "\n 获得缓存的基本对象列表\r\n <p>\r\n limit-设置扫描的限制数量(默认为0,查询全部)\r\n pattern-设置键的匹配模式(默认为null)\r\n chunkSize-设置每次扫描的块大小(默认为0,本方法设置为1000)\r\n type-设置键的类型(默认为null,查询全部类型)\r\n </P>\r\n\r\n @param pattern 字符串前缀\r\n @return 对象列表\r\n @see KeysScanOptions\r\n"}, {"name": "keys", "paramTypes": ["org.redisson.api.options.KeysScanOptions"], "doc": "\n 通过扫描参数获取缓存的基本对象列表\r\n\r\n @param keysScanOptions 扫描参数\r\n                        <p>\r\n                        limit-设置扫描的限制数量(默认为0,查询全部)\r\n                        pattern-设置键的匹配模式(默认为null)\r\n                        chunkSize-设置每次扫描的块大小(默认为0)\r\n                        type-设置键的类型(默认为null,查询全部类型)\r\n                        </P>\r\n @see KeysScanOptions\r\n"}, {"name": "deleteKeys", "paramTypes": ["java.lang.String"], "doc": "\n 删除缓存的基本对象列表\r\n\r\n @param pattern 字符串前缀\r\n"}, {"name": "<PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.String"], "doc": "\n 检查redis中是否存在key\r\n\r\n @param key 键\r\n"}], "constructors": []}