package org.dromara.app.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Date;

/**
 * 书籍章节对象 book_chapters (MongoDB)
 *
 * <AUTHOR>
 */
@Data
@Document(collection = "book_chapters")
public class BookChapter {

    /**
     * 章节ID（MongoDB ObjectId）
     */
    @Id
    private String id;

    /**
     * 书籍ID
     */
    @Field("book_id")
    private Long bookId;

    /**
     * 章节标题
     */
    @Field("title")
    private String title;

    /**
     * 章节内容（Markdown格式）
     */
    @Field("content")
    private String content;

    /**
     * 章节排序序号
     */
    @Field("chapter_order")
    private Integer chapterOrder;

    /**
     * 字数统计
     */
    @Field("word_count")
    private Integer wordCount;

    /**
     * 预估阅读时间（分钟）
     */
    @Field("read_time")
    private Integer readTime;

    /**
     * 是否已解锁：0-未解锁，1-已解锁
     */
    @Field("is_unlocked")
    private Boolean isUnlocked;

    /**
     * 是否为试读章节：0-否，1-是
     */
    @Field("is_preview")
    private Boolean isPreview;

    /**
     * 章节状态：0-草稿，1-已发布
     */
    @Field("status")
    private Boolean status;

    /**
     * 创建时间
     */
    @Field("create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @Field("update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 创建者
     */
    @Field("create_by")
    private String createBy;

    /**
     * 更新者
     */
    @Field("update_by")
    private String updateBy;

    /**
     * 是否已完成（用户维度，不存储到数据库）
     */
    private transient Boolean isCompleted;
}
