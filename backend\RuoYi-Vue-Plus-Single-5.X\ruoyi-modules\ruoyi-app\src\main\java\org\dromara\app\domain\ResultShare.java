package org.dromara.app.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 结果分享记录对象 app_result_share
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@Data
@TableName("app_result_share")
public class ResultShare implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 分享记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 结果ID
     */
    private String resultId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 分享平台（wechat,qq,weibo,link）
     */
    private String platform;

    /**
     * 分享链接
     */
    private String shareUrl;

    /**
     * 分享内容
     */
    private String content;

    /**
     * 查看次数
     */
    private Integer viewCount;

    /**
     * 过期时间
     */
    private LocalDateTime expiresAt;

    /**
     * 状态（active,expired,disabled）
     */
    private String status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

}
