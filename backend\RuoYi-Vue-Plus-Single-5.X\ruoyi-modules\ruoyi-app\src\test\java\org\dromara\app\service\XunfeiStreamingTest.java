package org.dromara.app.service;

import org.dromara.app.domain.dto.ChatRequestDto;
import org.dromara.app.service.impl.LangChain4jChatServiceImpl;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 讯飞流式服务测试
 *
 * <AUTHOR>
 */
@SpringBootTest
@ActiveProfiles("dev")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class XunfeiStreamingTest {

    @Autowired
    private LangChain4jChatServiceImpl chatService;
    
    private static final Long TEST_USER_ID = 1L;
    private static final int TIMEOUT_SECONDS = 60; // 增加超时时间

    @BeforeEach
    void setUp() {
        assertNotNull(chatService, "ChatService should be autowired");
    }

    /**
     * 测试流式响应是否真正逐步返回
     */
    @Test
    @Order(1)
    @DisplayName("测试真正的流式响应")
    void testRealStreamingResponse() throws InterruptedException {
        // Given
        ChatRequestDto request = new ChatRequestDto();
        request.setMessage("请写一首关于春天的诗，要求至少4句话");
        request.setAgentType("general");
        
        CountDownLatch latch = new CountDownLatch(1);
        AtomicInteger tokenCount = new AtomicInteger(0);
        AtomicReference<String> fullResponse = new AtomicReference<>("");
        AtomicReference<String> errorMessage = new AtomicReference<>(null);
        
        // When
        SseEmitter emitter = chatService.sendMessageStream(request, TEST_USER_ID);
        assertNotNull(emitter, "SseEmitter should not be null");
        
        // 监听SSE事件
        emitter.onCompletion(() -> {
            System.out.println("=== 流式响应完成 ===");
            System.out.println("总token数量: " + tokenCount.get());
            System.out.println("完整响应: " + fullResponse.get());
            latch.countDown();
        });
        
        emitter.onError((throwable) -> {
            errorMessage.set(throwable.getMessage());
            System.err.println("流式响应错误: " + throwable.getMessage());
            latch.countDown();
        });
        
        // Then
        boolean completed = latch.await(TIMEOUT_SECONDS, TimeUnit.SECONDS);
        assertTrue(completed, "Test should complete within timeout");
        
        if (errorMessage.get() != null) {
            fail("Streaming failed with error: " + errorMessage.get());
        }
        
        // 验证是否收到了多个token（真正的流式响应）
        assertTrue(tokenCount.get() > 1, "Should receive multiple tokens in streaming response, got: " + tokenCount.get());
        
        // 验证完整响应不为空
        assertNotNull(fullResponse.get(), "Full response should not be null");
        assertFalse(fullResponse.get().trim().isEmpty(), "Full response should not be empty");
        
        System.out.println("✅ 流式响应测试通过，收到 " + tokenCount.get() + " 个token");
    }

    /**
     * 测试不同Agent类型的流式响应
     */
    @Test
    @Order(2)
    @DisplayName("测试面试官Agent的流式响应")
    void testInterviewerStreamingResponse() throws InterruptedException {
        // Given
        ChatRequestDto request = new ChatRequestDto();
        request.setMessage("我想应聘Java开发工程师，请给我3个面试问题");
        request.setAgentType("interviewer");
        
        CountDownLatch latch = new CountDownLatch(1);
        AtomicInteger tokenCount = new AtomicInteger(0);
        AtomicReference<String> errorMessage = new AtomicReference<>(null);
        
        // When
        SseEmitter emitter = chatService.sendMessageStream(request, TEST_USER_ID);
        assertNotNull(emitter, "SseEmitter should not be null");
        
        emitter.onCompletion(() -> {
            System.out.println("=== 面试官流式响应完成 ===");
            System.out.println("总token数量: " + tokenCount.get());
            latch.countDown();
        });
        
        emitter.onError((throwable) -> {
            errorMessage.set(throwable.getMessage());
            System.err.println("面试官流式响应错误: " + throwable.getMessage());
            latch.countDown();
        });
        
        // Then
        boolean completed = latch.await(TIMEOUT_SECONDS, TimeUnit.SECONDS);
        assertTrue(completed, "Interviewer test should complete within timeout");
        
        if (errorMessage.get() != null) {
            fail("Interviewer streaming failed with error: " + errorMessage.get());
        }
        
        assertTrue(tokenCount.get() > 1, "Should receive multiple tokens for interviewer response");
        System.out.println("✅ 面试官流式响应测试通过，收到 " + tokenCount.get() + " 个token");
    }

    /**
     * 测试上下文记忆的流式响应
     */
    @Test
    @Order(3)
    @DisplayName("测试上下文记忆的流式响应")
    void testContextMemoryStreamingResponse() throws InterruptedException {
        String sessionId = "test-streaming-session-" + System.currentTimeMillis();
        
        // 第一轮对话
        ChatRequestDto request1 = new ChatRequestDto();
        request1.setMessage("我的名字是张三，我是一名Java开发工程师");
        request1.setAgentType("general");
        request1.setSessionId(sessionId);
        
        CountDownLatch latch1 = new CountDownLatch(1);
        AtomicReference<String> errorMessage1 = new AtomicReference<>(null);
        
        SseEmitter emitter1 = chatService.sendMessageStream(request1, TEST_USER_ID);
        emitter1.onCompletion(() -> latch1.countDown());
        emitter1.onError((throwable) -> {
            errorMessage1.set(throwable.getMessage());
            latch1.countDown();
        });
        
        boolean firstCompleted = latch1.await(TIMEOUT_SECONDS, TimeUnit.SECONDS);
        assertTrue(firstCompleted, "First conversation should complete");
        assertNull(errorMessage1.get(), "First conversation should not have errors");
        
        // 等待保存完成
        Thread.sleep(2000);
        
        // 第二轮对话，测试上下文记忆
        ChatRequestDto request2 = new ChatRequestDto();
        request2.setMessage("你还记得我的名字和职业吗？");
        request2.setAgentType("general");
        request2.setSessionId(sessionId);
        
        CountDownLatch latch2 = new CountDownLatch(1);
        AtomicInteger tokenCount = new AtomicInteger(0);
        AtomicReference<String> errorMessage2 = new AtomicReference<>(null);
        
        SseEmitter emitter2 = chatService.sendMessageStream(request2, TEST_USER_ID);
        emitter2.onCompletion(() -> {
            System.out.println("=== 上下文记忆流式响应完成 ===");
            System.out.println("总token数量: " + tokenCount.get());
            latch2.countDown();
        });
        emitter2.onError((throwable) -> {
            errorMessage2.set(throwable.getMessage());
            latch2.countDown();
        });
        
        boolean secondCompleted = latch2.await(TIMEOUT_SECONDS, TimeUnit.SECONDS);
        assertTrue(secondCompleted, "Second conversation should complete");
        assertNull(errorMessage2.get(), "Second conversation should not have errors");
        
        assertTrue(tokenCount.get() > 1, "Should receive multiple tokens for context memory response");
        System.out.println("✅ 上下文记忆流式响应测试通过，收到 " + tokenCount.get() + " 个token");
    }

    @AfterEach
    void tearDown() {
        // 等待一下确保资源清理
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
}
