package org.dromara.app.service;

import org.dromara.app.service.impl.FileServiceImpl;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * 文件服务接口
 *
 * <AUTHOR>
 */
public interface IFileService {

    /**
     * 上传聊天附件
     *
     * @param file   文件
     * @param type   文件类型：image/file/voice
     * @param userId 用户ID
     * @return 上传结果
     */
    FileUploadResult uploadChatFile(MultipartFile file, String type, Long userId);

    /**
     * 语音转文字
     *
     * @param audioFile 音频文件
     * @param userId    用户ID
     * @return 转换结果
     */
    SpeechToTextResult speechToText(MultipartFile audioFile, Long userId);

    /**
     * 删除文件
     *
     * @param fileUrl 文件URL
     * @param userId  用户ID
     * @return 是否成功
     */
    boolean deleteFile(String fileUrl, Long userId);

    /**
     * 获取文件信息
     *
     * @param fileUrl 文件URL
     * @param userId  用户ID
     * @return 文件信息
     */
    FileServiceImpl.FileInfo getFileInfo(String fileUrl, Long userId);

    /**
     * 批量删除文件
     *
     * @param fileUrls 文件URL列表
     * @param userId   用户ID
     * @return 删除结果
     */
    Map<String, Boolean> batchDeleteFiles(java.util.List<String> fileUrls, Long userId);

    /**
     * 获取用户文件使用统计
     *
     * @param userId 用户ID
     * @return 统计信息
     */
    Map<String, Object> getFileUsageStats(Long userId);

    // ========== 内部类定义 ==========

    /**
     * 文件上传结果
     */
    class FileUploadResult {
        private boolean success;
        private String fileUrl;
        private String fileName;
        private Long fileSize;
        private String mimeType;
        private String errorMessage;
        private Map<String, Object> metadata;

        public FileUploadResult() {
        }

        public FileUploadResult(boolean success, String fileUrl) {
            this.success = success;
            this.fileUrl = fileUrl;
        }

        public static FileUploadResult success(String fileUrl, String fileName, Long fileSize, String mimeType) {
            FileUploadResult result = new FileUploadResult(true, fileUrl);
            result.setFileName(fileName);
            result.setFileSize(fileSize);
            result.setMimeType(mimeType);
            return result;
        }

        public static FileUploadResult success(String fileUrl, String fileName, Long fileSize, String mimeType, Map<String, Object> metadata) {
            FileUploadResult result = new FileUploadResult(true, fileUrl);
            result.setFileName(fileName);
            result.setFileSize(fileSize);
            result.setMimeType(mimeType);
            result.setMetadata(metadata);
            return result;
        }

        public static FileUploadResult error(String errorMessage) {
            FileUploadResult result = new FileUploadResult(false, null);
            result.setErrorMessage(errorMessage);
            return result;
        }

        // getters and setters
        public boolean isSuccess() {
            return success;
        }

        public void setSuccess(boolean success) {
            this.success = success;
        }

        public String getFileUrl() {
            return fileUrl;
        }

        public void setFileUrl(String fileUrl) {
            this.fileUrl = fileUrl;
        }

        public String getFileName() {
            return fileName;
        }

        public void setFileName(String fileName) {
            this.fileName = fileName;
        }

        public Long getFileSize() {
            return fileSize;
        }

        public void setFileSize(Long fileSize) {
            this.fileSize = fileSize;
        }

        public String getMimeType() {
            return mimeType;
        }

        public void setMimeType(String mimeType) {
            this.mimeType = mimeType;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }

        public Map<String, Object> getMetadata() {
            return metadata;
        }

        public void setMetadata(Map<String, Object> metadata) {
            this.metadata = metadata;
        }
    }

    /**
     * 语音转文字结果
     */
    class SpeechToTextResult {
        private boolean success;
        private String text;
        private String language;
        private Double confidence;
        private Long duration;
        private String errorMessage;
        private Map<String, Object> metadata;

        public SpeechToTextResult() {
        }

        public SpeechToTextResult(boolean success, String text) {
            this.success = success;
            this.text = text;
        }

        public static SpeechToTextResult success(String text, String language, Double confidence, Long duration) {
            SpeechToTextResult result = new SpeechToTextResult(true, text);
            result.setLanguage(language);
            result.setConfidence(confidence);
            result.setDuration(duration);
            return result;
        }

        public static SpeechToTextResult error(String errorMessage) {
            SpeechToTextResult result = new SpeechToTextResult(false, null);
            result.setErrorMessage(errorMessage);
            return result;
        }

        // getters and setters
        public boolean isSuccess() {
            return success;
        }

        public void setSuccess(boolean success) {
            this.success = success;
        }

        public String getText() {
            return text;
        }

        public void setText(String text) {
            this.text = text;
        }

        public String getLanguage() {
            return language;
        }

        public void setLanguage(String language) {
            this.language = language;
        }

        public Double getConfidence() {
            return confidence;
        }

        public void setConfidence(Double confidence) {
            this.confidence = confidence;
        }

        public Long getDuration() {
            return duration;
        }

        public void setDuration(Long duration) {
            this.duration = duration;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }

        public Map<String, Object> getMetadata() {
            return metadata;
        }

        public void setMetadata(Map<String, Object> metadata) {
            this.metadata = metadata;
        }
    }

    /**
     * 文件信息
     */
    class FileInfo {
        private String fileName;
        private Long fileSize;
        private String mimeType;
        private String fileUrl;
        private Long uploadTime;
        private String uploader;
        private Map<String, Object> metadata;

        public FileInfo() {
        }

        // getters and setters
        public String getFileName() {
            return fileName;
        }

        public void setFileName(String fileName) {
            this.fileName = fileName;
        }

        public Long getFileSize() {
            return fileSize;
        }

        public void setFileSize(Long fileSize) {
            this.fileSize = fileSize;
        }

        public String getMimeType() {
            return mimeType;
        }

        public void setMimeType(String mimeType) {
            this.mimeType = mimeType;
        }

        public String getFileUrl() {
            return fileUrl;
        }

        public void setFileUrl(String fileUrl) {
            this.fileUrl = fileUrl;
        }

        public Long getUploadTime() {
            return uploadTime;
        }

        public void setUploadTime(Long uploadTime) {
            this.uploadTime = uploadTime;
        }

        public String getUploader() {
            return uploader;
        }

        public void setUploader(String uploader) {
            this.uploader = uploader;
        }

        public Map<String, Object> getMetadata() {
            return metadata;
        }

        public void setMetadata(Map<String, Object> metadata) {
            this.metadata = metadata;
        }
    }
}
