package org.dromara.app.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.app.domain.enums.ActivityType;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 用户活动会话记录对象 app_activity_session
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "app_activity_session", autoResultMap = true)
public class ActivitySession extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 会话ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 会话唯一标识符
     */
    private String sessionId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 活动类型
     */
    private ActivityType activityType;

    /**
     * 活动对象ID(如题目ID、课程ID等)
     */
    private String activityId;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 分类ID(如题库ID、课程分类ID等)
     */
    private String categoryId;

    /**
     * 分类名称
     */
    private String categoryName;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 持续时长(毫秒)
     */
    private Long duration;

    /**
     * 是否活跃(1:活跃 0:已结束)
     */
    private Boolean isActive;

    /**
     * 额外元数据(JSON格式)
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> metadata;

    /**
     * 计算持续时长
     * 如果会话还在进行中，计算到当前时间的时长
     * 如果会话已结束，返回记录的时长
     *
     * @return 持续时长(毫秒)
     */
    public Long calculateDuration() {
        if (startTime == null) {
            return 0L;
        }

        if (endTime != null) {
            // 会话已结束，返回记录的时长
            return duration != null ? duration : 0L;
        } else if (Boolean.TRUE.equals(isActive)) {
            // 会话进行中，计算到当前时间的时长
            return java.time.Duration.between(startTime, LocalDateTime.now()).toMillis();
        } else {
            // 会话已暂停，返回记录的时长
            return duration != null ? duration : 0L;
        }
    }

    /**
     * 检查会话是否有效
     *
     * @return 是否有效
     */
    public boolean isValid() {
        return sessionId != null && !sessionId.trim().isEmpty()
            && userId != null && userId > 0
            && activityType != null
            && startTime != null;
    }

    /**
     * 结束会话
     */
    public void endSession() {
        if (Boolean.TRUE.equals(isActive)) {
            this.endTime = LocalDateTime.now();
            this.duration = calculateDuration();
            this.isActive = false;
        }
    }

    /**
     * 暂停会话
     */
    public void pauseSession() {
        if (Boolean.TRUE.equals(isActive)) {
            this.duration = calculateDuration();
            this.isActive = false;
        }
    }

    /**
     * 恢复会话
     */
    public void resumeSession() {
        if (Boolean.FALSE.equals(isActive)) {
            this.startTime = LocalDateTime.now();
            this.endTime = null;
            this.isActive = true;
        }
    }
}
