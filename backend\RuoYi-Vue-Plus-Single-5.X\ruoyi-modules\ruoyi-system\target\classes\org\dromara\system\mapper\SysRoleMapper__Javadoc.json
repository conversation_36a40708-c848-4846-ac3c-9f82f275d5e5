{"doc": "\n 角色表 数据层\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectPageRoleList", "paramTypes": ["com.baomidou.mybatisplus.extension.plugins.pagination.Page", "com.baomidou.mybatisplus.core.conditions.Wrapper"], "doc": "\n 分页查询角色列表\r\n\r\n @param page         分页对象\r\n @param queryWrapper 查询条件\r\n @return 包含角色信息的分页结果\r\n"}, {"name": "selectRoleList", "paramTypes": ["com.baomidou.mybatisplus.core.conditions.Wrapper"], "doc": "\n 根据条件分页查询角色数据\r\n\r\n @param queryWrapper 查询条件\r\n @return 角色数据集合信息\r\n"}, {"name": "selectRoleById", "paramTypes": ["java.lang.Long"], "doc": "\n 根据角色ID查询角色信息\r\n\r\n @param roleId 角色ID\r\n @return 对应的角色信息\r\n"}, {"name": "selectRolePermissionByUserId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据用户ID查询角色\r\n\r\n @param userId 用户ID\r\n @return 角色列表\r\n"}, {"name": "selectRolesByUserId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据用户ID查询角色\r\n\r\n @param userId 用户ID\r\n @return 角色列表\r\n"}], "constructors": []}