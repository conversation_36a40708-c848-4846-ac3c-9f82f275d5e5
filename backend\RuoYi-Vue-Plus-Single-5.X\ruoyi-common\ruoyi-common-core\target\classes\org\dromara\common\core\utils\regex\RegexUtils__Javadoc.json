{"doc": "\n 正则相关工具类\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "extractFromString", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 从输入字符串中提取匹配的部分，如果没有匹配则返回默认值\r\n\r\n @param input        要提取的输入字符串\r\n @param regex        用于匹配的正则表达式，可以使用 {@link RegexConstants} 中定义的常量\r\n @param defaultInput 如果没有匹配时返回的默认值\r\n @return 如果找到匹配的部分，则返回匹配的部分，否则返回默认值\r\n"}], "constructors": []}