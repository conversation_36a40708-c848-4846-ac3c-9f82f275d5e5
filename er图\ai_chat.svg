<?xml version="1.0" encoding="UTF-8"?>
<svg width="900" height="700" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .entity { fill: white; stroke: black; stroke-width: 2; }
      .attribute { fill: white; stroke: black; stroke-width: 1; }
      .relationship { fill: white; stroke: black; stroke-width: 2; }
      .text { font-family: Arial, sans-serif; font-size: 12px; text-anchor: middle; dominant-baseline: middle; }
      .line { stroke: black; stroke-width: 1; fill: none; }
      .cardinality { font-family: Arial, sans-serif; font-size: 10px; text-anchor: middle; }
    </style>
  </defs>
  
  <!-- 用户实体 -->
  <rect x="50" y="250" width="100" height="60" class="entity"/>
  <text x="100" y="280" class="text">用户</text>
  
  <!-- 用户属性 -->
  <ellipse cx="100" cy="180" rx="30" ry="20" class="attribute"/>
  <text x="100" y="180" class="text">用户ID</text>
  <line x1="100" y1="200" x2="100" y2="250" class="line"/>
  
  <ellipse cx="30" cy="220" rx="25" ry="20" class="attribute"/>
  <text x="30" y="220" class="text">姓名</text>
  <line x1="45" y1="235" x2="70" y2="250" class="line"/>
  
  <ellipse cx="30" cy="340" rx="25" ry="20" class="attribute"/>
  <text x="30" y="340" class="text">邮箱</text>
  <line x1="45" y1="325" x2="70" y2="310" class="line"/>
  
  <ellipse cx="100" cy="370" rx="30" ry="20" class="attribute"/>
  <text x="100" y="370" class="text">联系方式</text>
  <line x1="100" y1="350" x2="100" y2="310" class="line"/>
  
  <!-- 聊天会话实体 -->
  <rect x="350" y="250" width="120" height="60" class="entity"/>
  <text x="410" y="280" class="text">聊天会话</text>
  
  <!-- 聊天会话属性 -->
  <ellipse cx="300" cy="180" rx="30" ry="20" class="attribute"/>
  <text x="300" y="180" class="text">会话ID</text>
  <line x1="315" y1="195" x2="370" y2="250" class="line"/>
  
  <ellipse cx="410" cy="160" rx="30" ry="20" class="attribute"/>
  <text x="410" y="160" class="text">会话标题</text>
  <line x1="410" y1="180" x2="410" y2="250" class="line"/>
  
  <ellipse cx="520" cy="180" rx="35" ry="20" class="attribute"/>
  <text x="520" y="180" class="text">Agent类型</text>
  <line x1="505" y1="195" x2="450" y2="250" class="line"/>
  
  <ellipse cx="280" cy="350" rx="30" ry="20" class="attribute"/>
  <text x="280" y="350" class="text">消息总数</text>
  <line x1="295" y1="335" x2="360" y2="310" class="line"/>

  <ellipse cx="410" cy="370" rx="35" ry="20" class="attribute"/>
  <text x="410" y="370" class="text">最后活跃时间</text>
  <line x1="410" y1="350" x2="410" y2="310" class="line"/>
  
  <ellipse cx="520" cy="380" rx="30" ry="20" class="attribute"/>
  <text x="520" y="380" class="text">会话状态</text>
  <line x1="505" y1="365" x2="450" y2="310" class="line"/>
  
  <!-- 对话消息实体 -->
  <rect x="650" y="250" width="120" height="60" class="entity"/>
  <text x="710" y="280" class="text">对话消息</text>
  
  <!-- 对话消息属性 -->
  <ellipse cx="600" cy="180" rx="30" ry="20" class="attribute"/>
  <text x="600" y="180" class="text">消息ID</text>
  <line x1="615" y1="195" x2="670" y2="250" class="line"/>
  
  <ellipse cx="710" cy="160" rx="30" ry="20" class="attribute"/>
  <text x="710" y="160" class="text">消息内容</text>
  <line x1="710" y1="180" x2="710" y2="250" class="line"/>
  
  <ellipse cx="820" cy="180" rx="30" ry="20" class="attribute"/>
  <text x="820" y="180" class="text">发送者</text>
  <line x1="805" y1="195" x2="750" y2="250" class="line"/>
  
  <ellipse cx="600" cy="380" rx="30" ry="20" class="attribute"/>
  <text x="600" y="380" class="text">时间戳</text>
  <line x1="615" y1="365" x2="670" y2="310" class="line"/>
  
  <ellipse cx="710" cy="400" rx="30" ry="20" class="attribute"/>
  <text x="710" y="400" class="text">消息类型</text>
  <line x1="710" y1="380" x2="710" y2="310" class="line"/>
  
  <ellipse cx="820" cy="380" rx="30" ry="20" class="attribute"/>
  <text x="820" y="380" class="text">状态</text>
  <line x1="805" y1="365" x2="750" y2="310" class="line"/>
  
  <!-- AI工具实体 -->
  <rect x="350" y="500" width="120" height="60" class="entity"/>
  <text x="410" y="530" class="text">AI工具</text>
  
  <!-- AI工具属性 -->
  <ellipse cx="280" cy="470" rx="30" ry="20" class="attribute"/>
  <text x="280" y="470" class="text">工具ID</text>
  <line x1="295" y1="485" x2="350" y2="520" class="line"/>

  <ellipse cx="410" cy="450" rx="30" ry="20" class="attribute"/>
  <text x="410" y="450" class="text">工具名称</text>
  <line x1="410" y1="470" x2="410" y2="500" class="line"/>
  
  <ellipse cx="520" cy="450" rx="30" ry="20" class="attribute"/>
  <text x="520" y="450" class="text">工具描述</text>
  <line x1="505" y1="465" x2="450" y2="500" class="line"/>
  
  <ellipse cx="300" cy="610" rx="30" ry="20" class="attribute"/>
  <text x="300" y="610" class="text">功能定义</text>
  <line x1="315" y1="595" x2="370" y2="560" class="line"/>
  
  <ellipse cx="410" cy="630" rx="30" ry="20" class="attribute"/>
  <text x="410" y="630" class="text">参数模式</text>
  <line x1="410" y1="610" x2="410" y2="560" class="line"/>
  
  <ellipse cx="520" cy="610" rx="30" ry="20" class="attribute"/>
  <text x="520" y="610" class="text">启用状态</text>
  <line x1="505" y1="595" x2="450" y2="560" class="line"/>
  
  <!-- 关系：创建会话 -->
  <polygon points="200,250 230,270 200,290 170,270" class="relationship"/>
  <text x="200" y="270" class="text">创建</text>
  <line x1="150" y1="280" x2="170" y2="270" class="line"/>
  <line x1="230" y1="270" x2="350" y2="280" class="line"/>
  <text x="160" y="290" class="cardinality">1</text>
  <text x="330" y="290" class="cardinality">n</text>
  
  <!-- 关系：包含消息 -->
  <polygon points="550,250 580,270 550,290 520,270" class="relationship"/>
  <text x="550" y="270" class="text">包含</text>
  <line x1="470" y1="280" x2="520" y2="270" class="line"/>
  <line x1="580" y1="270" x2="650" y2="280" class="line"/>
  <text x="490" y="290" class="cardinality">1</text>
  <text x="630" y="290" class="cardinality">n</text>
  
  <!-- 关系：使用工具 -->
  <polygon points="330,380 360,400 330,420 300,400" class="relationship"/>
  <text x="330" y="400" class="text">使用</text>
  <line x1="410" y1="310" x2="360" y2="380" class="line"/>
  <line x1="330" y1="420" x2="380" y2="500" class="line"/>
  <text x="380" y="350" class="cardinality">1</text>
  <text x="350" y="480" class="cardinality">n</text>
</svg>
