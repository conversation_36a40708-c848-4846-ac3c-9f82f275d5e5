package org.dromara.app.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 推荐响应VO
 * 用于返回推荐的学习资源列表
 *
 * <AUTHOR>
 * @date 2025-07-27
 */
@Data
public class RecommendationResponseVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 推荐资源列表
     */
    private List<RecommendationItemVo> items;

    /**
     * 总记录数
     */
    private Long total;

    /**
     * 当前页码
     */
    private Integer pageNum;

    /**
     * 每页大小
     */
    private Integer pageSize;

    /**
     * 推荐算法版本
     */
    private String algorithmVersion;

    /**
     * 推荐生成时间
     */
    private LocalDateTime generatedAt;

    /**
     * 推荐项VO
     */
    @Data
    public static class RecommendationItemVo implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 资源ID
         */
        private Long id;

        /**
         * 资源标题
         */
        private String title;

        /**
         * 资源描述
         */
        private String description;

        /**
         * 资源类型（video/question-bank/book）
         */
        private String type;

        /**
         * 难度等级
         */
        private String difficulty;

        /**
         * 目标能力
         */
        private String target;

        /**
         * 目标能力代码
         */
        private String targetCapability;

        /**
         * 预期提升点数
         */
        private Integer improvementPoints;

        /**
         * 推荐优先级（high/medium/low）
         */
        private String priority;

        /**
         * 推荐优先级分数
         */
        private Double priorityScore;

        /**
         * 分类
         */
        private String category;

        /**
         * 标签列表
         */
        private List<String> tags;

        /**
         * 评分
         */
        private BigDecimal rating;

        /**
         * 封面图片
         */
        private String cover;

        /**
         * 是否已收藏
         */
        private Boolean isBookmarked;

        /**
         * 推荐原因
         */
        private String recommendReason;

        // 视频特有字段
        /**
         * 时长
         */
        private String duration;

        /**
         * 讲师
         */
        private String instructor;

        /**
         * 学习人数
         */
        private Integer studentCount;

        /**
         * 价格
         */
        private BigDecimal price;

        /**
         * 原价
         */
        private BigDecimal originalPrice;

        // 题库特有字段
        /**
         * 题目数量
         */
        private Integer questionCount;

        /**
         * 已完成数量
         */
        private Integer completedCount;

        /**
         * 完成进度
         */
        private BigDecimal progress;

        /**
         * 预估时间
         */
        private String estimatedTime;

        // 书籍特有字段
        /**
         * 作者
         */
        private String author;

        /**
         * 页数
         */
        private Integer pageCount;

        /**
         * 阅读进度
         */
        private BigDecimal readingProgress;

        /**
         * 出版社
         */
        private String publisher;

        /**
         * 阅读人数
         */
        private Integer readerCount;

        /**
         * 创建时间
         */
        private LocalDateTime createTime;

        /**
         * 更新时间
         */
        private LocalDateTime updateTime;
    }
}
