package org.dromara.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.dromara.app.domain.SearchKeyword;

import java.util.List;

/**
 * 搜索关键词统计Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Mapper
public interface SearchKeywordMapper extends BaseMapper<SearchKeyword> {

    /**
     * 查询热门搜索关键词
     *
     * @param limit 限制数量
     * @return 热门关键词列表
     */
    @Select({
        "SELECT keyword FROM app_search_keyword ",
        "ORDER BY search_count DESC, last_search_time DESC ",
        "LIMIT #{limit}"
    })
    List<String> selectHotKeywords(@Param("limit") Integer limit);

    /**
     * 根据关键词模糊查询搜索建议
     *
     * @param keyword 关键词
     * @param limit   限制数量
     * @return 搜索建议列表
     */
    @Select({
        "SELECT keyword FROM app_search_keyword ",
        "WHERE keyword LIKE CONCAT('%', #{keyword}, '%') ",
        "ORDER BY search_count DESC, last_search_time DESC ",
        "LIMIT #{limit}"
    })
    List<String> selectSuggestionsByKeyword(@Param("keyword") String keyword, @Param("limit") Integer limit);

}
