{"doc": "\n MongoDB配置类\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "mongoDatabaseFactory", "paramTypes": ["com.mongodb.client.MongoClient"], "doc": "\n MongoDB数据库工厂\r\n"}, {"name": "mongoTemplate", "paramTypes": ["org.springframework.data.mongodb.MongoDatabaseFactory"], "doc": "\n MongoDB模板\r\n"}, {"name": "transactionManager", "paramTypes": ["org.springframework.data.mongodb.MongoDatabaseFactory"], "doc": "\n MongoDB事务管理器\r\n"}], "constructors": []}