package org.dromara.app.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

/**
 * 文档分块对象 app_document_chunk
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_document_chunk")
public class DocumentChunk extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 分块ID
     */
    @TableId(value = "id")
    private String id;

    /**
     * 文档ID
     */
    private String documentId;

    /**
     * 知识库ID
     */
    private Long knowledgeBaseId;

    /**
     * 分块序号
     */
    private Integer chunkIndex;

    /**
     * 分块内容
     */
    private String content;

    /**
     * 分块标题（可选）
     */
    private String title;

    /**
     * 内容长度
     */
    private Integer contentLength;

    /**
     * 向量数据（JSON格式存储浮点数组）
     */
    private String vector;

    /**
     * 向量维度
     */
    private Integer vectorDimension;

    /**
     * 嵌入模型名称
     */
    private String embeddingModel;

    /**
     * 分块类型：text/code/table/image_caption
     */
    private String chunkType;

    /**
     * 开始位置（在原文档中的字符位置）
     */
    private Integer startPosition;

    /**
     * 结束位置
     */
    private Integer endPosition;

    /**
     * 分块元数据（JSON格式）
     */
    private String metadata;

    /**
     * 分块权重
     */
    private Double weight;

    /**
     * 是否启用：0-禁用，1-启用
     */
    private Boolean enabled;

    /**
     * 哈希值（用于去重）
     */
    private String contentHash;

    /**
     * 向量化时间
     */
    private Long vectorizedAt;

    /**
     * 分块元数据对象（不存储到数据库）
     */
    @TableField(exist = false)
    private ChunkMetadata metadataObject;

    /**
     * 向量数组（不存储到数据库，用于计算）
     */
    @TableField(exist = false)
    private float[] vectorArray;

    /**
     * 相似度分数（搜索时使用）
     */
    @TableField(exist = false)
    private Double similarityScore;

    /**
     * 重排序分数（搜索时使用）
     */
    @TableField(exist = false)
    private Double rerankScore;

    /**
     * 分块元数据内部类
     */
    @Data
    public static class ChunkMetadata {
        private String section; // 所属章节
        private String subsection; // 所属子章节
        private Integer pageNumber; // 页码
        private Integer lineNumber; // 行号
        private String language; // 语言
        private String contentType; // 内容类型
        private Double confidence; // 置信度
        private String extractMethod; // 提取方法
        private Long processingTime; // 处理时间
    }
}
