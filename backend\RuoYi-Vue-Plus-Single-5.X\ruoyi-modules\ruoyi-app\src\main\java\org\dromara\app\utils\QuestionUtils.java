package org.dromara.app.utils;

import org.dromara.app.domain.enums.QuestionDifficultyEnum;
import org.dromara.app.domain.enums.QuestionTypeEnum;

/**
 * 题目工具类
 *
 * <AUTHOR>
 */
public class QuestionUtils {

    /**
     * 将难度代码转换为中文描述
     *
     * @param difficulty 难度代码（1-简单 2-中等 3-困难）
     * @return 难度描述
     */
    public static String getDifficultyDescription(Integer difficulty) {
        return QuestionDifficultyEnum.getDescriptionByCode(difficulty);
    }

    /**
     * 将难度描述转换为代码
     *
     * @param difficultyDescription 难度描述
     * @return 难度代码
     */
    public static Integer getDifficultyCode(String difficultyDescription) {
        return QuestionDifficultyEnum.getCodeByDescription(difficultyDescription);
    }

    /**
     * 根据代码获取难度枚举实例
     *
     * @param difficulty 难度代码
     * @return 难度枚举实例
     */
    public static QuestionDifficultyEnum getDifficultyEnum(Integer difficulty) {
        return QuestionDifficultyEnum.getByCode(difficulty);
    }

    /**
     * 获取题目类型描述
     *
     * @param typeCode 类型代码
     * @return 类型描述
     */
    public static String getTypeDescription(Integer typeCode) {
        return QuestionTypeEnum.getDescriptionByCode(typeCode);
    }

    /**
     * 获取题目类型代码
     *
     * @param typeDescription 类型描述
     * @return 类型代码
     */
    public static Integer getTypeCode(String typeDescription) {
        return QuestionTypeEnum.getCodeByDescription(typeDescription);
    }

    /**
     * 根据代码获取题目类型枚举实例
     *
     * @param typeCode 类型代码
     * @return 类型枚举实例
     */
    public static QuestionTypeEnum getTypeEnum(Integer typeCode) {
        return QuestionTypeEnum.getByCode(typeCode);
    }

    /**
     * 根据难度和题目类型计算预计完成时间
     *
     * @param difficulty 难度代码（1-简单 2-中等 3-困难）
     * @param typeCode   题目类型代码
     * @return 预计时间（分钟）
     */
    public static Integer calculateEstimatedTime(Integer difficulty, Integer typeCode) {
        int baseTime = 5; // 基础时间5分钟

        // 根据难度调整时间（使用枚举）
        QuestionDifficultyEnum difficultyEnum = QuestionDifficultyEnum.getByCode(difficulty);
        if (difficultyEnum != null) {
            switch (difficultyEnum) {
                case EASY:
                    baseTime += 0;
                    break;
                case MEDIUM:
                    baseTime += 5;
                    break;
                case HARD:
                    baseTime += 10;
                    break;
                default:
                    baseTime += 3;
            }
        }

        // 根据题目类型调整时间（使用枚举）
        QuestionTypeEnum typeEnum = QuestionTypeEnum.getByCode(typeCode);
        if (typeEnum != null) {
            switch (typeEnum) {
                case PROGRAMMING:
                    baseTime += 15;
                    break;
                case SHORT_ANSWER:
                    baseTime += 10;
                    break;
                case MULTIPLE_CHOICE:
                    baseTime += 3;
                    break;
                case SINGLE_CHOICE:
                case TRUE_FALSE:
                    baseTime += 0;
                    break;
                default:
                    baseTime += 2;
            }
        }

        return baseTime;
    }

    /**
     * 验证难度代码是否有效
     *
     * @param difficulty 难度代码
     * @return 是否有效
     */
    public static boolean isValidDifficulty(Integer difficulty) {
        return QuestionDifficultyEnum.getByCode(difficulty) != null;
    }

    /**
     * 验证题目类型代码是否有效
     *
     * @param typeCode 类型代码
     * @return 是否有效
     */
    public static boolean isValidType(Integer typeCode) {
        return QuestionTypeEnum.getByCode(typeCode) != null;
    }

    /**
     * 获取所有难度选项
     *
     * @return 难度枚举数组
     */
    public static QuestionDifficultyEnum[] getAllDifficulties() {
        return QuestionDifficultyEnum.values();
    }

    /**
     * 获取所有题目类型选项
     *
     * @return 类型枚举数组
     */
    public static QuestionTypeEnum[] getAllTypes() {
        return QuestionTypeEnum.values();
    }
}
