package org.dromara.app.service.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.service.IInterviewAnalysisService;
import org.dromara.app.service.IXunfeiService;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;

/**
 * 面试分析服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class InterviewAnalysisServiceImpl implements IInterviewAnalysisService {

    private final IXunfeiService xunfeiService;
    private final Random random = new Random();

    @Override
    public JSONObject analyzeEmotion(String imageData, Integer questionId) {
        try {

            // 方案1: 如果讯飞有图像情感分析API，直接调用
            // 方案2: 使用星火大模型的多模态能力分析图像
            // 方案3: 暂时使用模拟数据，后续接入真实API

            // 这里先使用模拟数据，实际项目中需要调用讯飞的图像分析API
            JSONObject result = generateMockEmotionData();

            // TODO: 实际实现
            // String prompt = buildEmotionAnalysisPrompt(questionId);
            // Map<String, Object> context = new HashMap<>();
            // context.put("image_data", imageData);
            // String analysisResult = xunfeiService.sparkChat(prompt, context);
            // result = parseEmotionResult(analysisResult);

            return result;

        } catch (Exception e) {
            log.error("表情分析失败", e);
            return createErrorResult("表情分析失败: " + e.getMessage());
        }
    }

    @Override
    public JSONObject analyzeSpeech(String audioData, Integer questionId) {
        try {

            // 1. 使用讯飞语音识别API将音频转为文本
            // 注意：这里需要将base64音频数据转换为MultipartFile或字节数组
            // var speechResult = xunfeiService.speechRecognition(audioFile);

            // 2. 对识别出的文本进行情感分析
            // var emotionResult = xunfeiService.emotionAnalysis(speechResult.getText());

            // 3. 分析语音特征（语速、音调等）
            JSONObject result = new JSONObject();
            result.put("text", "这是识别出的文本内容"); // 实际应该是speechResult.getText()
            result.put("emotion", "neutral"); // 实际应该是emotionResult.getEmotion()
            result.put("confidence", 0.85);
            result.put("speechRate", "normal"); // 语速分析
            result.put("volume", "medium"); // 音量分析

            return result;

        } catch (Exception e) {
            log.error("语音分析失败", e);
            return createErrorResult("语音分析失败: " + e.getMessage());
        }
    }

    @Override
    public JSONObject generateEmotionSuggestion(JSONObject emotionResult, Integer questionId) {
        try {

            JSONObject emotions = emotionResult.getJSONObject("emotions");
            if (emotions == null) {
                return null;
            }

            // 分析主要情绪
            String primaryEmotion = findPrimaryEmotion(emotions);

            // 根据情绪生成建议
            String suggestionText = generateEmotionSuggestionText(primaryEmotion, questionId);

            if (suggestionText != null) {
                JSONObject suggestion = new JSONObject();
                suggestion.put("id", System.currentTimeMillis() + "_emotion");
                suggestion.put("type", "emotion");
                suggestion.put("level", determineEmotionSuggestionLevel(primaryEmotion));
                suggestion.put("icon", "fa fa-smile");
                suggestion.put("title", "情绪建议");
                suggestion.put("message", suggestionText);
                suggestion.put("action", "调整状态");

                return suggestion;
            }

            return null;

        } catch (Exception e) {
            log.error("生成情绪建议失败", e);
            return null;
        }
    }

    @Override
    public JSONObject generateSpeechSuggestion(JSONObject speechResult, Integer questionId) {
        try {

            String text = speechResult.getStr("text");
            String speechRate = speechResult.getStr("speechRate");

            // 使用星火大模型分析回答内容并生成建议
            String prompt = buildSpeechSuggestionPrompt(text, speechRate, questionId);
            Map<String, Object> context = new HashMap<>();
            context.put("question_id", questionId);
            context.put("speech_text", text);
            context.put("speech_rate", speechRate);

            String aiSuggestion = xunfeiService.sparkChat(prompt, context);

            // 解析AI生成的建议
            JSONObject suggestion = parseSuggestionFromAI(aiSuggestion, "speech");

            log.info("语音建议生成完成: {}", suggestion);
            return suggestion;

        } catch (Exception e) {
            log.error("生成语音建议失败", e);
            return null;
        }
    }

    @Override
    public JSONObject generateComprehensiveSuggestion(JSONObject context) {
        try {
            log.info("生成综合建议");

            // 使用星火大模型基于完整上下文生成综合建议
            String prompt = buildComprehensiveSuggestionPrompt(context);
            String aiSuggestion = xunfeiService.sparkChat(prompt, new HashMap<>());

            return parseSuggestionFromAI(aiSuggestion, "comprehensive");

        } catch (Exception e) {
            log.error("生成综合建议失败", e);
            return null;
        }
    }

    /**
     * 生成模拟情绪数据（用于测试）
     */
    private JSONObject generateMockEmotionData() {
        JSONObject result = new JSONObject();
        JSONObject emotions = new JSONObject();

        // 生成随机情绪数据
        emotions.put("happy", random.nextInt(30) + 10);
        emotions.put("neutral", random.nextInt(40) + 30);
        emotions.put("sad", random.nextInt(20));
        emotions.put("angry", random.nextInt(10));
        emotions.put("surprised", random.nextInt(15));
        emotions.put("fear", random.nextInt(10));
        emotions.put("disgusted", random.nextInt(5));

        // 找出主要情绪
        String primaryEmotion = findPrimaryEmotion(emotions);
        int primaryValue = emotions.getInt(primaryEmotion);

        JSONObject primary = new JSONObject();
        primary.put("type", primaryEmotion);
        primary.put("value", primaryValue);

        result.put("emotions", emotions);
        result.put("primary", primary);

        return result;
    }

    /**
     * 找出主要情绪
     */
    private String findPrimaryEmotion(JSONObject emotions) {
        String primaryEmotion = "neutral";
        int maxValue = 0;

        for (String emotion : emotions.keySet()) {
            int value = emotions.getInt(emotion);
            if (value > maxValue) {
                maxValue = value;
                primaryEmotion = emotion;
            }
        }

        return primaryEmotion;
    }

    /**
     * 根据情绪生成建议文本
     */
    private String generateEmotionSuggestionText(String emotion, Integer questionId) {
        return switch (emotion) {
            case "sad" -> "检测到您情绪较低，建议深呼吸放松，保持积极心态";
            case "angry" -> "检测到您情绪激动，建议稍作停顿，平复心情后继续";
            case "fear" -> "检测到您有些紧张，这很正常，试着放慢语速，保持自信";
            case "surprised" -> "您看起来有些意外，可以花几秒钟整理思路再回答";
            default -> null; // 正常情绪不需要建议
        };
    }

    /**
     * 确定情绪建议的优先级
     */
    private String determineEmotionSuggestionLevel(String emotion) {
        return switch (emotion) {
            case "angry", "fear" -> "high";
            case "sad", "surprised" -> "medium";
            default -> "low";
        };
    }

    /**
     * 构建语音建议的prompt
     */
    private String buildSpeechSuggestionPrompt(String text, String speechRate, Integer questionId) {
        return String.format(
            "作为面试专家，请分析以下面试回答并给出改进建议：\n" +
            "问题编号：%d\n" +
            "回答内容：%s\n" +
            "语速：%s\n" +
            "请从内容完整性、逻辑清晰度、关键词使用等方面给出具体建议。" +
            "请以JSON格式返回，包含type、level、title、message、action字段。",
            questionId, text, speechRate
        );
    }

    /**
     * 构建综合建议的prompt
     */
    private String buildComprehensiveSuggestionPrompt(JSONObject context) {
        return "基于面试者的整体表现，包括语音、情绪、回答内容等，给出综合性的面试建议。" +
               "请以JSON格式返回建议内容。";
    }

    /**
     * 从AI响应中解析建议
     */
    private JSONObject parseSuggestionFromAI(String aiResponse, String type) {
        try {
            // 尝试解析JSON响应
            JSONObject parsed = JSONUtil.parseObj(aiResponse);
            if (parsed.containsKey("message")) {
                parsed.put("id", System.currentTimeMillis() + "_" + type);
                parsed.put("type", type);
                if (!parsed.containsKey("level")) {
                    parsed.put("level", "medium");
                }
                if (!parsed.containsKey("icon")) {
                    parsed.put("icon", "fa fa-lightbulb");
                }
                return parsed;
            }
        } catch (Exception e) {
            log.warn("解析AI响应失败，使用原始文本: {}", e.getMessage());
        }

        // 如果解析失败，创建简单的建议对象
        JSONObject suggestion = new JSONObject();
        suggestion.put("id", System.currentTimeMillis() + "_" + type);
        suggestion.put("type", type);
        suggestion.put("level", "medium");
        suggestion.put("icon", "fa fa-lightbulb");
        suggestion.put("title", "AI建议");
        suggestion.put("message", aiResponse);
        suggestion.put("action", "查看详情");

        return suggestion;
    }

    /**
     * 创建错误结果
     */
    private JSONObject createErrorResult(String message) {
        JSONObject result = new JSONObject();
        result.put("error", true);
        result.put("message", message);
        return result;
    }
}
