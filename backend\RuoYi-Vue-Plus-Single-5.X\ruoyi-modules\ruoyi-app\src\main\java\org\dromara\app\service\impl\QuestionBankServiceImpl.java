package org.dromara.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.QuestionBank;
import org.dromara.app.domain.bo.QuestionBankBo;
import org.dromara.app.domain.vo.QuestionBankVo;
import org.dromara.app.mapper.QuestionBankMapper;
import org.dromara.app.mapper.QuestionMapper;
import org.dromara.app.service.IQuestionBankService;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 题库管理Service业务层处理
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class QuestionBankServiceImpl extends ServiceImpl<QuestionBankMapper, QuestionBank> implements IQuestionBankService {

    private final QuestionBankMapper baseMapper;
    private final QuestionMapper questionMapper;

    /**
     * 查询题库
     */
    @Override
    public QuestionBankVo queryById(Long bankId) {
        return baseMapper.selectVoById(bankId);
    }

    /**
     * 查询题库列表
     */
    @Override
    public TableDataInfo<QuestionBankVo> queryPageList(QuestionBankBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<QuestionBank> lqw = buildQueryWrapper(bo);
        Page<QuestionBankVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询题库列表
     */
    @Override
    public List<QuestionBankVo> queryList(QuestionBankBo bo) {
        LambdaQueryWrapper<QuestionBank> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<QuestionBank> buildQueryWrapper(QuestionBankBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<QuestionBank> lqw = Wrappers.lambdaQueryWrapper();

        lqw.like(StringUtils.isNotBlank(bo.getBankCode()), QuestionBank::getBankCode, bo.getBankCode());
        lqw.like(StringUtils.isNotBlank(bo.getTitle()), QuestionBank::getTitle, bo.getTitle());
        lqw.like(StringUtils.isNotBlank(bo.getDescription()), QuestionBank::getDescription, bo.getDescription());
        lqw.eq(bo.getMajorId() != null, QuestionBank::getMajorId, bo.getMajorId());
        lqw.eq(bo.getDifficulty() != null, QuestionBank::getDifficulty, bo.getDifficulty());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), QuestionBank::getStatus, bo.getStatus());

        // 批量查询条件
        lqw.in(bo.getStatusArray() != null && bo.getStatusArray().length > 0, QuestionBank::getStatus, (Object[]) bo.getStatusArray());
        lqw.in(bo.getDifficultyArray() != null && bo.getDifficultyArray().length > 0, QuestionBank::getDifficulty, (Object[]) bo.getDifficultyArray());

        // 关键词搜索
        if (StringUtils.isNotBlank(bo.getKeyword())) {
            lqw.and(wrapper -> wrapper
                .like(QuestionBank::getTitle, bo.getKeyword())
                .or()
                .like(QuestionBank::getDescription, bo.getKeyword())
                .or()
                .like(QuestionBank::getBankCode, bo.getKeyword())
            );
        }

        // 时间范围查询
        lqw.ge(StringUtils.isNotBlank(bo.getCreateTimeStart()), QuestionBank::getCreateTime, bo.getCreateTimeStart());
        lqw.le(StringUtils.isNotBlank(bo.getCreateTimeEnd()), QuestionBank::getCreateTime, bo.getCreateTimeEnd());

        // 排序
        if (StringUtils.isNotBlank(bo.getOrderByColumn())) {
            String orderBy = bo.getOrderByColumn();
            boolean isAsc = "asc".equals(bo.getIsAsc());

            switch (orderBy) {
                case "bankCode" -> lqw.orderBy(true, isAsc, QuestionBank::getBankCode);
                case "title" -> lqw.orderBy(true, isAsc, QuestionBank::getTitle);
                case "difficulty" -> lqw.orderBy(true, isAsc, QuestionBank::getDifficulty);
                case "totalQuestions" -> lqw.orderBy(true, isAsc, QuestionBank::getTotalQuestions);
                case "practiceCount" -> lqw.orderBy(true, isAsc, QuestionBank::getPracticeCount);
                case "createTime" -> lqw.orderBy(true, isAsc, QuestionBank::getCreateTime);
                case "updateTime" -> lqw.orderBy(true, isAsc, QuestionBank::getUpdateTime);
                case "sort" -> lqw.orderBy(true, isAsc, QuestionBank::getSort);
                default -> lqw.orderByDesc(QuestionBank::getCreateTime);
            }
        } else {
            lqw.orderByAsc(QuestionBank::getSort).orderByDesc(QuestionBank::getCreateTime);
        }

        return lqw;
    }

    /**
     * 新增题库
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(QuestionBankBo bo) {
        QuestionBank add = BeanUtil.toBean(bo, QuestionBank.class);
        validEntityBeforeSave(add);

        // 检查编码是否重复
        if (existsBankCode(add.getBankCode(), null)) {
            throw new RuntimeException("题库编码已存在");
        }

        // 设置默认值
        if (add.getTotalQuestions() == null) {
            add.setTotalQuestions(0);
        }
        if (add.getPracticeCount() == null) {
            add.setPracticeCount(0);
        }
        if (add.getStatus() == null) {
            add.setStatus("0");
        }
        if (add.getSort() == null) {
            add.setSort(0);
        }

        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setBankId(add.getBankId());
        }
        return flag;
    }

    /**
     * 修改题库
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(QuestionBankBo bo) {
        QuestionBank update = BeanUtil.toBean(bo, QuestionBank.class);
        validEntityBeforeSave(update);

        // 检查编码是否重复
        if (existsBankCode(update.getBankCode(), update.getBankId())) {
            throw new RuntimeException("题库编码已存在");
        }

        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(QuestionBank entity) {
        if (StringUtils.isBlank(entity.getBankCode())) {
            throw new RuntimeException("题库编码不能为空");
        }
        if (StringUtils.isBlank(entity.getTitle())) {
            throw new RuntimeException("题库标题不能为空");
        }
    }

    /**
     * 检查题库编码是否存在
     */
    private boolean existsBankCode(String bankCode, Long excludeId) {
        LambdaQueryWrapper<QuestionBank> lqw = Wrappers.lambdaQueryWrapper();
        lqw.eq(QuestionBank::getBankCode, bankCode);
        if (excludeId != null) {
            lqw.ne(QuestionBank::getBankId, excludeId);
        }
        return baseMapper.exists(lqw);
    }

    /**
     * 批量删除题库
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 检查是否有题目引用
            for (Long id : ids) {
                long questionCount = questionMapper.selectCount(
                    Wrappers.lambdaQueryWrapper(org.dromara.app.domain.Question.class)
                        .eq(org.dromara.app.domain.Question::getBankId, id)
                        .eq(org.dromara.app.domain.Question::getStatus, "0")
                );
                if (questionCount > 0) {
                    QuestionBankVo bank = baseMapper.selectVoById(id);
                    throw new RuntimeException(String.format("题库【%s】下存在%d个题目，无法删除",
                        bank != null ? bank.getTitle() : id.toString(), questionCount));
                }
            }
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 批量导入题库
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importBank(List<QuestionBankVo> bankList, Boolean isUpdateSupport, String operName) {
        if (ObjectUtil.isNull(bankList) || bankList.isEmpty()) {
            throw new RuntimeException("导入题库数据不能为空！");
        }

        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        for (QuestionBankVo bankVo : bankList) {
            try {
                // 验证是否存在这个题库
                QuestionBank existBank = baseMapper.selectOne(
                    Wrappers.lambdaQueryWrapper(QuestionBank.class)
                        .eq(QuestionBank::getBankCode, bankVo.getBankCode())
                );

                if (ObjectUtil.isNull(existBank)) {
                    // 新增
                    QuestionBank bank = BeanUtil.toBean(bankVo, QuestionBank.class);
                    validEntityBeforeSave(bank);

                    // 设置默认值
                    if (bank.getTotalQuestions() == null) bank.setTotalQuestions(0);
                    if (bank.getPracticeCount() == null) bank.setPracticeCount(0);
                    if (bank.getStatus() == null) bank.setStatus("0");
                    if (bank.getSort() == null) bank.setSort(0);

                    baseMapper.insert(bank);
                    successNum++;
                    successMsg.append("<br/>").append(successNum).append("、题库 ").append(bank.getTitle()).append(" 导入成功");
                } else if (isUpdateSupport) {
                    // 更新
                    QuestionBank bank = BeanUtil.toBean(bankVo, QuestionBank.class);
                    bank.setBankId(existBank.getBankId());
                    validEntityBeforeSave(bank);
                    baseMapper.updateById(bank);
                    successNum++;
                    successMsg.append("<br/>").append(successNum).append("、题库 ").append(bank.getTitle()).append(" 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>").append(failureNum).append("、题库 ").append(bankVo.getTitle()).append(" 已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、题库 " + bankVo.getTitle() + " 导入失败：";
                failureMsg.append(msg).append(e.getMessage());
                log.error(msg, e);
            }
        }

        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new RuntimeException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 导出题库列表
     */
    @Override
    public List<QuestionBankVo> exportBankList(QuestionBankBo bo) {
        LambdaQueryWrapper<QuestionBank> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 更新题库状态
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateStatus(Long bankId, String status) {
        QuestionBank bank = new QuestionBank();
        bank.setBankId(bankId);
        bank.setStatus(status);
        return baseMapper.updateById(bank) > 0;
    }

    /**
     * 批量更新题库状态
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchUpdateStatus(List<Long> bankIds, String status) {
        if (bankIds == null || bankIds.isEmpty()) {
            return false;
        }

        for (Long bankId : bankIds) {
            QuestionBank bank = new QuestionBank();
            bank.setBankId(bankId);
            bank.setStatus(status);
            baseMapper.updateById(bank);
        }
        return true;
    }

    /**
     * 复制题库
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean copyBank(Long bankId, String bankCode, String title) {
        QuestionBank sourceBank = baseMapper.selectById(bankId);
        if (sourceBank == null) {
            throw new RuntimeException("源题库不存在");
        }

        // 检查新编码是否重复
        if (existsBankCode(bankCode, null)) {
            throw new RuntimeException("题库编码已存在");
        }

        // 创建新题库
        QuestionBank newBank = new QuestionBank();
        BeanUtil.copyProperties(sourceBank, newBank, "bankId", "createTime", "updateTime", "createBy", "updateBy");
        newBank.setBankCode(bankCode);
        newBank.setTitle(title);
        newBank.setTotalQuestions(0); // 复制时重置题目数量
        newBank.setPracticeCount(0); // 重置练习次数

        return baseMapper.insert(newBank) > 0;
    }

    /**
     * 更新题库题目总数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateTotalQuestions(Long bankId) {
        long questionCount = questionMapper.selectCount(
            Wrappers.lambdaQueryWrapper(org.dromara.app.domain.Question.class)
                .eq(org.dromara.app.domain.Question::getBankId, bankId)
                .eq(org.dromara.app.domain.Question::getStatus, "0")
        );

        QuestionBank bank = new QuestionBank();
        bank.setBankId(bankId);
        bank.setTotalQuestions((int) questionCount);

        return baseMapper.updateById(bank) > 0;
    }

    /**
     * 获取题库统计信息
     */
    @Override
    public QuestionBankVo getBankStatistics(Long bankId) {
        QuestionBankVo bankVo = baseMapper.selectVoById(bankId);
        if (bankVo == null) {
            return null;
        }

        // 获取实时题目数量
        long questionCount = questionMapper.selectCount(
            Wrappers.lambdaQueryWrapper(org.dromara.app.domain.Question.class)
                .eq(org.dromara.app.domain.Question::getBankId, bankId)
                .eq(org.dromara.app.domain.Question::getStatus, "0")
        );

        bankVo.setTotalQuestions((int) questionCount);
        return bankVo;
    }

    /**
     * 批量设置题库排序
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchSetSort(List<Long> bankIds, List<Integer> sorts) {
        if (bankIds == null || sorts == null || bankIds.size() != sorts.size()) {
            throw new RuntimeException("参数错误");
        }

        for (int i = 0; i < bankIds.size(); i++) {
            QuestionBank bank = new QuestionBank();
            bank.setBankId(bankIds.get(i));
            bank.setSort(sorts.get(i));
            baseMapper.updateById(bank);
        }

        return true;
    }
}
