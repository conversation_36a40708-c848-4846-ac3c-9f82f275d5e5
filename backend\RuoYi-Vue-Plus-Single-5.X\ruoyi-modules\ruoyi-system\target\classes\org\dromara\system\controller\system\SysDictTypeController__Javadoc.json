{"doc": "\n 数据字典信息\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.system.domain.bo.SysDictTypeBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询字典类型列表\r\n"}, {"name": "export", "paramTypes": ["org.dromara.system.domain.bo.SysDictTypeBo", "jakarta.servlet.http.HttpServletResponse"], "doc": "\n 导出字典类型列表\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": "\n 查询字典类型详细\r\n\r\n @param dictId 字典ID\r\n"}, {"name": "add", "paramTypes": ["org.dromara.system.domain.bo.SysDictTypeBo"], "doc": "\n 新增字典类型\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.system.domain.bo.SysDictTypeBo"], "doc": "\n 修改字典类型\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": "\n 删除字典类型\r\n\r\n @param dictIds 字典ID串\r\n"}, {"name": "refreshCache", "paramTypes": [], "doc": "\n 刷新字典缓存\r\n"}, {"name": "optionselect", "paramTypes": [], "doc": "\n 获取字典选择框列表\r\n"}], "constructors": []}