{"doc": "\n MongoDB常量\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "DEFAULT_DATABASE", "doc": "\n 默认数据库名称\r\n"}, {"name": "ID_FIELD", "doc": "\n ID字段名\r\n"}, {"name": "CREATE_TIME_FIELD", "doc": "\n 创建时间字段名\r\n"}, {"name": "UPDATE_TIME_FIELD", "doc": "\n 更新时间字段名\r\n"}, {"name": "CREATE_BY_FIELD", "doc": "\n 创建者字段名\r\n"}, {"name": "UPDATE_BY_FIELD", "doc": "\n 更新者字段名\r\n"}, {"name": "VERSION_FIELD", "doc": "\n 版本字段名\r\n"}, {"name": "DEL_FLAG_FIELD", "doc": "\n 删除标志字段名\r\n"}, {"name": "DEL_FLAG_NORMAL", "doc": "\n 未删除标志\r\n"}, {"name": "DEL_FLAG_DELETED", "doc": "\n 已删除标志\r\n"}, {"name": "DEFAULT_PAGE_SIZE", "doc": "\n 默认分页大小\r\n"}, {"name": "MAX_PAGE_SIZE", "doc": "\n 最大分页大小\r\n"}], "enumConstants": [], "methods": [], "constructors": []}