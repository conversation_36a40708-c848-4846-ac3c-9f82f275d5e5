package org.dromara.app.domain.vo;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 缓存统计信息视图对象
 *
 * <AUTHOR>
 * @date 2025-01-19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CacheStatisticsVo {

    /**
     * 总缓存数量
     */
    private Long totalCacheCount;

    /**
     * 缓存命中次数
     */
    private Long hitCount;

    /**
     * 缓存未命中次数
     */
    private Long missCount;

    /**
     * 缓存命中率（百分比）
     */
    private Double hitRate;

    /**
     * 缓存总大小（字节）
     */
    private Long totalCacheSize;

    /**
     * 已使用缓存大小（字节）
     */
    private Long usedCacheSize;

    /**
     * 缓存使用率（百分比）
     */
    private Double cacheUsageRate;

    /**
     * 过期缓存数量
     */
    private Long expiredCacheCount;

    /**
     * 热点缓存数量
     */
    private Long hotCacheCount;

    /**
     * 冷缓存数量
     */
    private Long coldCacheCount;

    /**
     * 平均缓存访问时间（毫秒）
     */
    private Double averageAccessTime;

    /**
     * 缓存写入次数
     */
    private Long writeCount;

    /**
     * 缓存删除次数
     */
    private Long deleteCount;

    /**
     * 缓存更新次数
     */
    private Long updateCount;

    /**
     * 缓存驱逐次数
     */
    private Long evictionCount;

    /**
     * 最后统计时间
     */
    private LocalDateTime lastStatisticsTime;

    /**
     * 缓存分类统计
     */
    private Map<String, Long> categoryStatistics;

    /**
     * 缓存大小分布
     */
    private Map<String, Long> sizeDistribution;

    /**
     * 缓存访问频率分布
     */
    private Map<String, Long> accessFrequencyDistribution;

    /**
     * 内存使用情况
     */
    private String memoryUsage;

    /**
     * 缓存健康状态：healthy/warning/critical
     */
    private String healthStatus;

    /**
     * 性能指标
     */
    private Map<String, Object> performanceMetrics;
}