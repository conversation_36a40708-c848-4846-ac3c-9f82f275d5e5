package org.dromara.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.dromara.app.domain.InterviewHistory;

import java.util.List;

/**
 * 面试历史记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@Mapper
public interface InterviewHistoryMapper extends BaseMapper<InterviewHistory> {

    /**
     * 根据用户ID查询历史记录列表
     *
     * @param userId 用户ID
     * @param limit  限制数量
     * @return 历史记录列表
     */
    List<InterviewHistory> selectByUserId(@Param("userId") Long userId, @Param("limit") Integer limit);

    /**
     * 根据用户ID和结果ID查询历史记录
     *
     * @param userId   用户ID
     * @param resultId 结果ID
     * @return 历史记录
     */
    InterviewHistory selectByUserIdAndResultId(@Param("userId") Long userId, @Param("resultId") String resultId);

    /**
     * 查询用户收藏的历史记录
     *
     * @param userId 用户ID
     * @return 历史记录列表
     */
    List<InterviewHistory> selectFavoritesByUserId(@Param("userId") Long userId);

    /**
     * 根据标签查询历史记录
     *
     * @param userId 用户ID
     * @param tags   标签列表
     * @return 历史记录列表
     */
    List<InterviewHistory> selectByUserIdAndTags(@Param("userId") Long userId, @Param("tags") List<String> tags);

    /**
     * 更新收藏状态
     *
     * @param id         历史记录ID
     * @param isFavorite 是否收藏
     * @return 更新数量
     */
    int updateFavoriteStatus(@Param("id") Long id, @Param("isFavorite") Boolean isFavorite);

    /**
     * 根据用户ID删除历史记录
     *
     * @param userId 用户ID
     * @return 删除数量
     */
    int deleteByUserId(@Param("userId") Long userId);

    /**
     * 查询用户历史记录数量
     *
     * @param userId 用户ID
     * @return 记录数量
     */
    Integer selectCountByUserId(@Param("userId") Long userId);

}
