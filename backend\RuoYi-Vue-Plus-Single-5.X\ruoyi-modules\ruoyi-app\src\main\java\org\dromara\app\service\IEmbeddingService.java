package org.dromara.app.service;

import java.util.List;

/**
 * 文本嵌入服务接口
 *
 * <AUTHOR>
 */
public interface IEmbeddingService {

    /**
     * 生成文本向量
     *
     * @param text 文本内容
     * @return 向量数组
     */
    float[] generateEmbedding(String text);

    /**
     * 批量生成文本向量
     *
     * @param texts 文本列表
     * @return 向量列表
     */
    List<float[]> generateEmbeddings(List<String> texts);

    /**
     * 计算向量相似度
     *
     * @param vector1 向量1
     * @param vector2 向量2
     * @return 相似度分数 (0-1)
     */
    double calculateSimilarity(float[] vector1, float[] vector2);

    /**
     * 获取向量维度
     *
     * @return 向量维度
     */
    int getVectorDimension();

    /**
     * 获取嵌入模型名称
     *
     * @return 模型名称
     */
    String getModelName();

    /**
     * 检查嵌入服务是否可用
     *
     * @return 是否可用
     */
    boolean isAvailable();
}
