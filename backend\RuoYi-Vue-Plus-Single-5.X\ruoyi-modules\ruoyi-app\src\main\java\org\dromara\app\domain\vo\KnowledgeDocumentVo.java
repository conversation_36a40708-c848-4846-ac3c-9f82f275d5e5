package org.dromara.app.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.app.domain.KnowledgeDocument;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 知识库文档视图对象 app_knowledge_document
 *
 * <AUTHOR>
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = KnowledgeDocument.class)
public class KnowledgeDocumentVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 文档ID
     */
    @ExcelProperty(value = "文档ID")
    private Long id;

    /**
     * 知识库ID
     */
    @ExcelProperty(value = "知识库ID")
    private Long knowledgeBaseId;

    /**
     * 文档标题
     */
    @ExcelProperty(value = "文档标题")
    private String title;

    /**
     * 文档内容
     */
    private String content;

    /**
     * 文档类型 (text/pdf/word/markdown/etc.)
     */
    @ExcelProperty(value = "文档类型")
    private String docType;

    /**
     * 文档来源 (upload/url/api/etc.)
     */
    @ExcelProperty(value = "文档来源")
    private String source;

    /**
     * 原始文件名
     */
    @ExcelProperty(value = "原始文件名")
    private String originalFilename;

    /**
     * 文件路径
     */
    private String filePath;

    /**
     * 文件大小 (字节)
     */
    @ExcelProperty(value = "文件大小")
    private Long fileSize;

    /**
     * 文档状态 (0=处理中 1=已完成 2=失败)
     */
    @ExcelProperty(value = "文档状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=处理中,1=已完成,2=失败")
    private Integer status;

    /**
     * 处理状态 (0=未处理 1=已向量化 2=已索引)
     */
    @ExcelProperty(value = "处理状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=未处理,1=已向量化,2=已索引")
    private Integer processStatus;

    /**
     * 向量数量
     */
    @ExcelProperty(value = "向量数量")
    private Long vectorCount;

    /**
     * 文档摘要
     */
    @ExcelProperty(value = "文档摘要")
    private String summary;

    /**
     * 文档标签 (JSON数组)
     */
    private String tags;

    /**
     * 文档元数据 (JSON格式)
     */
    private String metadata;

    /**
     * 处理配置 (JSON格式)
     */
    private String processConfig;

    /**
     * 错误信息
     */
    @ExcelProperty(value = "错误信息")
    private String errorMessage;

    /**
     * 最后处理时间
     */
    @ExcelProperty(value = "最后处理时间")
    private LocalDateTime lastProcessTime;

    /**
     * 排序字段
     */
    @ExcelProperty(value = "排序")
    private Integer sortOrder;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 创建部门
     */
    private Long createDept;

    /**
     * 创建者
     */
    @ExcelProperty(value = "创建者")
    private Long createBy;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @ExcelProperty(value = "更新者")
    private Long updateBy;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    // ========== 扩展字段 ==========

    /**
     * 知识库名称
     */
    @ExcelProperty(value = "知识库名称")
    private String knowledgeBaseName;

    /**
     * 文档类型名称（用于显示）
     */
    private String docTypeName;

    /**
     * 文档来源名称（用于显示）
     */
    private String sourceName;

    /**
     * 状态名称（用于显示）
     */
    private String statusName;

    /**
     * 处理状态名称（用于显示）
     */
    private String processStatusName;

    /**
     * 创建者名称
     */
    private String createByName;

    /**
     * 更新者名称
     */
    private String updateByName;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 文件大小（格式化显示）
     */
    private String fileSizeFormatted;

    /**
     * 处理进度百分比
     */
    private Double processProgress;

    /**
     * 平均向量相似度
     */
    private Double avgSimilarity;
}
