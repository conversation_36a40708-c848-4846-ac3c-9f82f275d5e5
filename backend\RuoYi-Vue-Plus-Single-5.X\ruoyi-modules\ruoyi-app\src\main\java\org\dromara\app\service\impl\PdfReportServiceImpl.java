package org.dromara.app.service.impl;

import com.itextpdf.kernel.colors.Color;
import com.itextpdf.kernel.colors.ColorConstants;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.element.Cell;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.HorizontalAlignment;
import com.itextpdf.layout.properties.TextAlignment;
import com.itextpdf.layout.properties.UnitValue;
import com.itextpdf.layout.properties.VerticalAlignment;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
// 移除冲突的POI imports，因为我们使用iText的Table和Color
import org.dromara.app.domain.InterviewReport;
import org.dromara.app.service.IChartGenerationService;
import org.dromara.app.service.IPdfReportService;
import org.dromara.common.core.exception.ServiceException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * PDF报告生成服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PdfReportServiceImpl implements IPdfReportService {

    @Value("${app.file.upload.path:/data/reports}")
    private String reportPath;

    @Value("${app.report.template.path:/templates/report}")
    private String templatePath;

    // 颜色常量
    private static final Color PRIMARY_COLOR = new DeviceRgb(0, 102, 204);
    private static final Color SECONDARY_COLOR = new DeviceRgb(102, 102, 102);
    private static final Color SUCCESS_COLOR = new DeviceRgb(0, 153, 0);
    private static final Color WARNING_COLOR = new DeviceRgb(255, 153, 0);
    private static final Color DANGER_COLOR = new DeviceRgb(220, 53, 69);


    /**
     * 生成报告内容
     */
    private void generateReportContent(Document document, InterviewReport report,
                                     PdfFont titleFont, PdfFont normalFont) {

        // 1. 生成封面
        generateCoverPage(document, report, titleFont, normalFont);

        // 2. 添加新页面
        document.add(new AreaBreak());

        // 3. 生成总览页
        generateOverviewPage(document, report, titleFont, normalFont);

        // 4. 添加新页面
        document.add(new AreaBreak());

        // 5. 生成能力评估页
        generateCapabilityAssessmentPage(document, report, titleFont, normalFont);

        // 6. 添加新页面
        document.add(new AreaBreak());

        // 7. 生成改进建议页
        generateImprovementSuggestionsPage(document, report, titleFont, normalFont);

        // 8. 添加新页面
        document.add(new AreaBreak());

        // 9. 生成学习路径页
        generateLearningPathPage(document, report, titleFont, normalFont);
    }

    /**
     * 生成封面页
     */
    private void generateCoverPage(Document document, InterviewReport report,
                                 PdfFont titleFont, PdfFont normalFont) {

        // 主标题
        Paragraph mainTitle = new Paragraph("面试评估报告")
            .setFont(titleFont)
            .setFontSize(28)
            .setFontColor(PRIMARY_COLOR)
            .setTextAlignment(TextAlignment.CENTER)
            .setMarginTop(100);
        document.add(mainTitle);

        // 报告标题
        Paragraph reportTitle = new Paragraph(report.getTitle())
            .setFont(titleFont)
            .setFontSize(20)
            .setFontColor(SECONDARY_COLOR)
            .setTextAlignment(TextAlignment.CENTER)
            .setMarginTop(30);
        document.add(reportTitle);

        // 总分显示
        Paragraph scoreDisplay = new Paragraph(String.valueOf(report.getTotalScore()))
            .setFont(titleFont)
            .setFontSize(48)
            .setFontColor(PRIMARY_COLOR)
            .setTextAlignment(TextAlignment.CENTER)
            .setMarginTop(80);
        document.add(scoreDisplay);

        // 等级显示
        String levelText = getLevelText(report.getLevel());
        Paragraph levelDisplay = new Paragraph(levelText)
            .setFont(normalFont)
            .setFontSize(18)
            .setFontColor(SECONDARY_COLOR)
            .setTextAlignment(TextAlignment.CENTER)
            .setMarginTop(20);
        document.add(levelDisplay);

        // 基本信息表格
        Table infoTable = new Table(2);
        infoTable.setWidth(UnitValue.createPercentValue(60));
        infoTable.setHorizontalAlignment(HorizontalAlignment.CENTER);
        infoTable.setMarginTop(100);

        addInfoRow(infoTable, "应聘岗位", report.getJobPosition(), normalFont);
        addInfoRow(infoTable, "百分位排名", report.getPercentile() + "%", normalFont);
        addInfoRow(infoTable, "生成日期", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")), normalFont);

        document.add(infoTable);

        // 页脚
        Paragraph footer = new Paragraph("智能面试评测系统出品")
            .setFont(normalFont)
            .setFontSize(12)
            .setFontColor(SECONDARY_COLOR)
            .setTextAlignment(TextAlignment.CENTER)
            .setFixedPosition(0, 50, 595);
        document.add(footer);
    }

    /**
     * 生成总览页
     */
    private void generateOverviewPage(Document document, InterviewReport report,
                                    PdfFont titleFont, PdfFont normalFont) {

        // 页面标题
        Paragraph pageTitle = new Paragraph("面试总体评估")
            .setFont(titleFont)
            .setFontSize(22)
            .setFontColor(PRIMARY_COLOR)
            .setMarginBottom(30);
        document.add(pageTitle);

        // 总体反馈
        if (report.getOverallFeedback() != null && !report.getOverallFeedback().isEmpty()) {
            Paragraph feedbackTitle = new Paragraph("总体反馈")
                .setFont(titleFont)
                .setFontSize(16)
                .setFontColor(SECONDARY_COLOR)
                .setMarginBottom(10);
            document.add(feedbackTitle);

            Paragraph feedbackContent = new Paragraph(report.getOverallFeedback())
                .setFont(normalFont)
                .setFontSize(12)
                .setMarginBottom(30);
            document.add(feedbackContent);
        }

        // 优势和劣势对比表格
        Table comparisonTable = new Table(2);
        comparisonTable.setWidth(UnitValue.createPercentValue(100));

        // 表头
        Cell strengthHeader = new Cell()
            .add(new Paragraph("主要优势").setFont(titleFont).setFontSize(14).setFontColor(SUCCESS_COLOR))
            .setBackgroundColor(new DeviceRgb(240, 248, 255))
            .setTextAlignment(TextAlignment.CENTER)
            .setPadding(10);

        Cell weaknessHeader = new Cell()
            .add(new Paragraph("需要改进").setFont(titleFont).setFontSize(14).setFontColor(WARNING_COLOR))
            .setBackgroundColor(new DeviceRgb(255, 248, 240))
            .setTextAlignment(TextAlignment.CENTER)
            .setPadding(10);

        comparisonTable.addHeaderCell(strengthHeader);
        comparisonTable.addHeaderCell(weaknessHeader);

        // 内容
        StringBuilder strengthsText = new StringBuilder();
        if (report.getStrengths() != null) {
            for (String strength : report.getStrengths()) {
                strengthsText.append("• ").append(strength).append("\n");
            }
        }

        StringBuilder weaknessesText = new StringBuilder();
        if (report.getWeaknesses() != null) {
            for (String weakness : report.getWeaknesses()) {
                weaknessesText.append("• ").append(weakness).append("\n");
            }
        }

        Cell strengthCell = new Cell()
            .add(new Paragraph(strengthsText.toString()).setFont(normalFont).setFontSize(11))
            .setPadding(15)
            .setVerticalAlignment(VerticalAlignment.TOP);

        Cell weaknessCell = new Cell()
            .add(new Paragraph(weaknessesText.toString()).setFont(normalFont).setFontSize(11))
            .setPadding(15)
            .setVerticalAlignment(VerticalAlignment.TOP);

        comparisonTable.addCell(strengthCell);
        comparisonTable.addCell(weaknessCell);

        document.add(comparisonTable);
    }

    /**
     * 生成能力评估页
     */
    private void generateCapabilityAssessmentPage(Document document, InterviewReport report,
                                                PdfFont titleFont, PdfFont normalFont) {

        // 页面标题
        Paragraph pageTitle = new Paragraph("能力维度评估")
            .setFont(titleFont)
            .setFontSize(22)
            .setFontColor(PRIMARY_COLOR)
            .setMarginBottom(30);
        document.add(pageTitle);

        // 维度评分表格
        if (report.getDimensionScores() != null && !report.getDimensionScores().isEmpty()) {
            Table dimensionTable = new Table(4);
            dimensionTable.setWidth(UnitValue.createPercentValue(100));

            // 表头
            addTableHeader(dimensionTable, "能力维度", titleFont);
            addTableHeader(dimensionTable, "得分", titleFont);
            addTableHeader(dimensionTable, "百分位", titleFont);
            addTableHeader(dimensionTable, "评价", titleFont);

            // 数据行
            for (org.dromara.app.domain.DimensionScore score : report.getDimensionScores()) {
                addTableCell(dimensionTable, score.getDimension(), normalFont);
                addTableCell(dimensionTable, score.getScore() + "/" + score.getMaxScore(), normalFont);
                addTableCell(dimensionTable, score.getPercentile() + "%", normalFont);
                addTableCell(dimensionTable, score.getDescription(), normalFont);
            }

            document.add(dimensionTable);
        }

        // 雷达图说明
        Paragraph radarNote = new Paragraph("注：雷达图显示了您在各个能力维度上的表现，外圈代表满分，您的得分用蓝色区域表示。")
            .setFont(normalFont)
            .setFontSize(10)
            .setFontColor(SECONDARY_COLOR)
            .setMarginTop(20);
        document.add(radarNote);
    }

    /**
     * 生成改进建议页
     */
    private void generateImprovementSuggestionsPage(Document document, InterviewReport report,
                                                  PdfFont titleFont, PdfFont normalFont) {

        // 页面标题
        Paragraph pageTitle = new Paragraph("改进建议")
            .setFont(titleFont)
            .setFontSize(22)
            .setFontColor(PRIMARY_COLOR)
            .setMarginBottom(30);
        document.add(pageTitle);

        if (report.getImprovementSuggestions() != null && !report.getImprovementSuggestions().isEmpty()) {
            int index = 1;
            for (InterviewReport.ImprovementSuggestion suggestion : report.getImprovementSuggestions()) {
                // 建议标题
                Paragraph suggestionTitle = new Paragraph(index + ". " + suggestion.getTitle())
                    .setFont(titleFont)
                    .setFontSize(14)
                    .setFontColor(PRIMARY_COLOR)
                    .setMarginBottom(5);
                document.add(suggestionTitle);

                // 建议描述
                Paragraph suggestionDesc = new Paragraph(suggestion.getDescription())
                    .setFont(normalFont)
                    .setFontSize(11)
                    .setMarginBottom(10);
                document.add(suggestionDesc);

                // 行动项
                if (suggestion.getActionItems() != null && !suggestion.getActionItems().isEmpty()) {
                    Paragraph actionTitle = new Paragraph("具体行动:")
                        .setFont(normalFont)
                        .setFontSize(11)
                        .setBold()
                        .setMarginBottom(5);
                    document.add(actionTitle);

                    for (String action : suggestion.getActionItems()) {
                        Paragraph actionItem = new Paragraph("• " + action)
                            .setFont(normalFont)
                            .setFontSize(10)
                            .setMarginLeft(20)
                            .setMarginBottom(3);
                        document.add(actionItem);
                    }
                }

                // 优先级和预计时间
                Table infoTable = new Table(3);
                infoTable.setWidth(UnitValue.createPercentValue(60));
                infoTable.setMarginTop(10);
                infoTable.setMarginBottom(20);

                addInfoCell(infoTable, "优先级", suggestion.getPriority(), normalFont, getPriorityColor(suggestion.getPriority()));
                addInfoCell(infoTable, "预计时间", suggestion.getEstimatedTime() + "天", normalFont, SECONDARY_COLOR);
                addInfoCell(infoTable, "难度", suggestion.getDifficulty(), normalFont, SECONDARY_COLOR);

                document.add(infoTable);

                index++;
            }
        }
    }

    /**
     * 生成学习路径页
     */
    private void generateLearningPathPage(Document document, InterviewReport report,
                                        PdfFont titleFont, PdfFont normalFont) {

        // 页面标题
        Paragraph pageTitle = new Paragraph("学习路径推荐")
            .setFont(titleFont)
            .setFontSize(22)
            .setFontColor(PRIMARY_COLOR)
            .setMarginBottom(30);
        document.add(pageTitle);

        if (report.getLearningPaths() != null && !report.getLearningPaths().isEmpty()) {
            int index = 1;
            for (InterviewReport.LearningPathRecommendation path : report.getLearningPaths()) {
                // 路径标题
                Paragraph pathTitle = new Paragraph(index + ". " + path.getTitle())
                    .setFont(titleFont)
                    .setFontSize(16)
                    .setFontColor(PRIMARY_COLOR)
                    .setMarginBottom(5);
                document.add(pathTitle);

                // 路径描述
                Paragraph pathDesc = new Paragraph(path.getDescription())
                    .setFont(normalFont)
                    .setFontSize(11)
                    .setMarginBottom(10);
                document.add(pathDesc);

                // 基本信息
                Table pathInfoTable = new Table(3);
                pathInfoTable.setWidth(UnitValue.createPercentValue(80));
                pathInfoTable.setMarginBottom(15);

                addInfoCell(pathInfoTable, "预计时长", path.getEstimatedHours() + "小时", normalFont, SECONDARY_COLOR);
                addInfoCell(pathInfoTable, "难度等级", path.getDifficulty(), normalFont, SECONDARY_COLOR);
                addInfoCell(pathInfoTable, "优先级", String.valueOf(path.getPriority()), normalFont, PRIMARY_COLOR);

                document.add(pathInfoTable);

                // 学习资源
                if (path.getResources() != null && !path.getResources().isEmpty()) {
                    Paragraph resourceTitle = new Paragraph("推荐资源:")
                        .setFont(normalFont)
                        .setFontSize(12)
                        .setBold()
                        .setMarginBottom(5);
                    document.add(resourceTitle);

                    for (InterviewReport.LearningResource resource : path.getResources()) {
                        Paragraph resourceItem = new Paragraph("• " + resource.getTitle() + " (" + resource.getType() + ")")
                            .setFont(normalFont)
                            .setFontSize(10)
                            .setMarginLeft(20)
                            .setMarginBottom(2);
                        document.add(resourceItem);

                        if (resource.getDescription() != null && !resource.getDescription().isEmpty()) {
                            Paragraph resourceDesc = new Paragraph("  " + resource.getDescription())
                                .setFont(normalFont)
                                .setFontSize(9)
                                .setFontColor(SECONDARY_COLOR)
                                .setMarginLeft(25)
                                .setMarginBottom(3);
                            document.add(resourceDesc);
                        }
                    }
                }

                // 学习里程碑
                if (path.getMilestones() != null && !path.getMilestones().isEmpty()) {
                    Paragraph milestoneTitle = new Paragraph("学习里程碑:")
                        .setFont(normalFont)
                        .setFontSize(12)
                        .setBold()
                        .setMarginTop(10)
                        .setMarginBottom(5);
                    document.add(milestoneTitle);

                    for (String milestone : path.getMilestones()) {
                        Paragraph milestoneItem = new Paragraph("✓ " + milestone)
                            .setFont(normalFont)
                            .setFontSize(10)
                            .setMarginLeft(20)
                            .setMarginBottom(3);
                        document.add(milestoneItem);
                    }
                }

                document.add(new Paragraph("\n"));
                index++;
            }
        }
    }

    // ========== 辅助方法 ==========

    // 重复的getLevelText方法已删除，使用下面更完整的实现

    /**
     * 添加信息行到表格
     */
    private void addInfoRow(Table table, String label, String value, PdfFont font) {
        Cell labelCell = new Cell()
            .add(new Paragraph(label).setFont(font).setFontSize(12))
            .setPadding(8)
            .setBackgroundColor(new DeviceRgb(248, 249, 250));

        Cell valueCell = new Cell()
            .add(new Paragraph(value).setFont(font).setFontSize(12))
            .setPadding(8);

        table.addCell(labelCell);
        table.addCell(valueCell);
    }

    /**
     * 添加表格头部
     */
    private void addTableHeader(Table table, String text, PdfFont font) {
        Cell headerCell = new Cell()
            .add(new Paragraph(text).setFont(font).setFontSize(12).setBold())
            .setBackgroundColor(PRIMARY_COLOR)
            .setFontColor(com.itextpdf.kernel.colors.ColorConstants.WHITE)
            .setTextAlignment(TextAlignment.CENTER)
            .setPadding(8);
        table.addHeaderCell(headerCell);
    }

    /**
     * 添加表格单元格
     */
    private void addTableCell(Table table, String text, PdfFont font) {
        Cell cell = new Cell()
            .add(new Paragraph(text).setFont(font).setFontSize(10))
            .setPadding(6)
            .setTextAlignment(TextAlignment.CENTER);
        table.addCell(cell);
    }

    /**
     * 添加信息单元格
     */
    private void addInfoCell(Table table, String label, String value, PdfFont font, Color color) {
        Cell cell = new Cell()
            .add(new Paragraph(label + ": " + value).setFont(font).setFontSize(10).setFontColor(color))
            .setPadding(5)
            .setTextAlignment(TextAlignment.CENTER);
        table.addCell(cell);
    }

    /**
     * 获取优先级颜色
     */
    private Color getPriorityColor(String priority) {
        if (priority == null) return SECONDARY_COLOR;
        switch (priority) {
            case "高": return DANGER_COLOR;
            case "中": return WARNING_COLOR;
            case "低": return SUCCESS_COLOR;
            default: return SECONDARY_COLOR;
        }
    }

    // 重复的颜色定义已删除，使用下面的定义

    @Override
    public String generatePdfReport(InterviewReport report) {
        try {
            log.info("开始生成PDF报告，报告ID: {}", report.getId());

            // 创建报告目录
            Path reportDir = Paths.get(reportPath);
            if (!Files.exists(reportDir)) {
                Files.createDirectories(reportDir);
            }

            // 生成文件名
            String fileName = String.format("interview_report_%s_%s.pdf",
                report.getId(), System.currentTimeMillis());
            Path filePath = reportDir.resolve(fileName);

            // 生成PDF内容
            byte[] pdfBytes = generatePdfReportBytes(report);

            // 写入文件
            Files.write(filePath, pdfBytes);

            log.info("PDF报告生成成功，文件路径: {}", filePath.toString());
            return filePath.toString();

        } catch (Exception e) {
            log.error("生成PDF报告失败", e);
            throw new ServiceException("生成PDF报告失败: " + e.getMessage());
        }
    }

    @Override
    public InputStream generatePdfReportStream(InterviewReport report) {
        try {
            byte[] pdfBytes = generatePdfReportBytes(report);
            return new ByteArrayInputStream(pdfBytes);
        } catch (Exception e) {
            log.error("生成PDF报告流失败", e);
            throw new ServiceException("生成PDF报告流失败: " + e.getMessage());
        }
    }

    @Override
    public byte[] generatePdfReportBytes(InterviewReport report) {
        try {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            PdfWriter writer = new PdfWriter(baos);
            PdfDocument pdfDoc = new PdfDocument(writer);
            Document document = new Document(pdfDoc, PageSize.A4);

            // 设置页边距
            document.setMargins(50, 50, 50, 50);

            // 加载中文字体
            PdfFont chineseFont = loadChineseFont();
            PdfFont boldFont = loadBoldChineseFont();

            // 生成报告内容
            addCoverPage(document, report, boldFont, chineseFont);
            addOverviewPage(document, report, boldFont, chineseFont);
            addDimensionAnalysisPage(document, report, boldFont, chineseFont);
            addImprovementSuggestionsPage(document, report, boldFont, chineseFont);
            addLearningPathPage(document, report, boldFont, chineseFont);
            addAppendixPage(document, report, boldFont, chineseFont);

            // 添加页脚
            addPageFooter(document, chineseFont);

            document.close();

            return baos.toByteArray();

        } catch (Exception e) {
            log.error("生成PDF报告字节数组失败", e);
            throw new ServiceException("生成PDF报告失败: " + e.getMessage());
        }
    }

    @Override
    public boolean isPdfFileExists(String filePath) {
        return Files.exists(Paths.get(filePath));
    }

    @Override
    public boolean deletePdfFile(String filePath) {
        try {
            return Files.deleteIfExists(Paths.get(filePath));
        } catch (IOException e) {
            log.error("删除PDF文件失败: {}", filePath, e);
            return false;
        }
    }

    @Override
    public long getPdfFileSize(String filePath) {
        try {
            return Files.size(Paths.get(filePath));
        } catch (IOException e) {
            log.error("获取PDF文件大小失败: {}", filePath, e);
            return 0;
        }
    }

    // ========== 私有方法 ==========

    /**
     * 加载中文字体
     */
    private PdfFont loadChineseFont() {
        try {
            // 尝试加载系统中文字体
            String[] fontPaths = {
                "C:/Windows/Fonts/simsun.ttc,0",  // Windows 宋体
                "/System/Library/Fonts/PingFang.ttc,0",  // macOS 苹方
                "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",  // Linux
                "fonts/NotoSansCJK-Regular.ttc,0"  // 备用字体
            };

            for (String fontPath : fontPaths) {
                try {
                    return PdfFontFactory.createFont(fontPath, "Identity-H");
                } catch (Exception e) {
                    // 继续尝试下一个字体
                }
            }

            // 如果都失败了，使用默认字体
            return PdfFontFactory.createFont();

        } catch (Exception e) {
            log.warn("加载中文字体失败，使用默认字体", e);
            try {
                return PdfFontFactory.createFont();
            } catch (Exception ex) {
                throw new ServiceException("无法加载字体");
            }
        }
    }

    /**
     * 加载粗体中文字体
     */
    private PdfFont loadBoldChineseFont() {
        try {
            String[] fontPaths = {
                "C:/Windows/Fonts/simhei.ttf",  // Windows 黑体
                "/System/Library/Fonts/PingFang.ttc,1",  // macOS 苹方粗体
                "/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf",  // Linux
                "fonts/NotoSansCJK-Bold.ttc,0"  // 备用粗体字体
            };

            for (String fontPath : fontPaths) {
                try {
                    return PdfFontFactory.createFont(fontPath, "Identity-H");
                } catch (Exception e) {
                    // 继续尝试下一个字体
                }
            }

            // 如果都失败了，返回普通字体
            return loadChineseFont();

        } catch (Exception e) {
            log.warn("加载粗体中文字体失败，使用普通字体", e);
            return loadChineseFont();
        }
    }

    /**
     * 添加封面页
     */
    private void addCoverPage(Document document, InterviewReport report, PdfFont boldFont, PdfFont normalFont) {
        // 主标题
        Paragraph title = new Paragraph("面试评估报告")
            .setFont(boldFont)
            .setFontSize(28)
            .setFontColor(PRIMARY_COLOR)
            .setTextAlignment(TextAlignment.CENTER)
            .setMarginTop(100);
        document.add(title);

        // 副标题
        Paragraph subtitle = new Paragraph(report.getTitle())
            .setFont(boldFont)
            .setFontSize(18)
            .setFontColor(SECONDARY_COLOR)
            .setTextAlignment(TextAlignment.CENTER)
            .setMarginTop(20);
        document.add(subtitle);

        // 分数展示
        Paragraph scoreDisplay = new Paragraph(String.valueOf(report.getTotalScore()))
            .setFont(boldFont)
            .setFontSize(48)
            .setFontColor(getScoreColor(report.getTotalScore()))
            .setTextAlignment(TextAlignment.CENTER)
            .setMarginTop(80);
        document.add(scoreDisplay);

        // 等级展示
        Paragraph levelDisplay = new Paragraph(getLevelText(report.getLevel()))
            .setFont(boldFont)
            .setFontSize(20)
            .setFontColor(SECONDARY_COLOR)
            .setTextAlignment(TextAlignment.CENTER)
            .setMarginTop(10);
        document.add(levelDisplay);

        // 基本信息表格
        Table infoTable = new Table(2);
        infoTable.setWidth(UnitValue.createPercentValue(60));
        infoTable.setHorizontalAlignment(HorizontalAlignment.CENTER);
        infoTable.setMarginTop(60);

        addTableRow(infoTable, "应聘岗位", report.getJobPosition(), normalFont);
        addTableRow(infoTable, "综合评分", report.getTotalScore() + "分", normalFont);
        addTableRow(infoTable, "能力等级", getLevelText(report.getLevel()), normalFont);
        addTableRow(infoTable, "百分位排名", "超过" + report.getPercentile() + "%的候选人", normalFont);
        addTableRow(infoTable, "生成时间", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy年MM月dd日")), normalFont);

        document.add(infoTable);

        // 页脚信息
        Paragraph footer = new Paragraph("智能面试评测系统")
            .setFont(normalFont)
            .setFontSize(12)
            .setFontColor(SECONDARY_COLOR)
            .setTextAlignment(TextAlignment.CENTER)
            .setFixedPosition(50, 50, 500);
        document.add(footer);

        // 新页
        document.add(new AreaBreak());
    }

    /**
     * 添加总览页
     */
    private void addOverviewPage(Document document, InterviewReport report, PdfFont boldFont, PdfFont normalFont) {
        // 页面标题
        Paragraph pageTitle = new Paragraph("面试总体评估")
            .setFont(boldFont)
            .setFontSize(20)
            .setFontColor(PRIMARY_COLOR)
            .setMarginBottom(20);
        document.add(pageTitle);

        // 总体反馈
        if (report.getOverallFeedback() != null && !report.getOverallFeedback().isEmpty()) {
            Paragraph feedbackTitle = new Paragraph("总体反馈")
                .setFont(boldFont)
                .setFontSize(16)
                .setFontColor(SECONDARY_COLOR)
                .setMarginTop(20)
                .setMarginBottom(10);
            document.add(feedbackTitle);

            Paragraph feedback = new Paragraph(report.getOverallFeedback())
                .setFont(normalFont)
                .setFontSize(12)
                .setMarginBottom(20);
            document.add(feedback);
        }

        // 优势和劣势对比
        Table comparisonTable = new Table(2);
        comparisonTable.setWidth(UnitValue.createPercentValue(100));

        // 优势列
        Cell strengthsCell = new Cell();
        strengthsCell.add(new Paragraph("主要优势")
            .setFont(boldFont)
            .setFontSize(14)
            .setFontColor(SUCCESS_COLOR));

        if (report.getStrengths() != null) {
            for (String strength : report.getStrengths()) {
                strengthsCell.add(new Paragraph("• " + strength)
                    .setFont(normalFont)
                    .setFontSize(11)
                    .setMarginTop(5));
            }
        }

        // 劣势列
        Cell weaknessesCell = new Cell();
        weaknessesCell.add(new Paragraph("需要改进")
            .setFont(boldFont)
            .setFontSize(14)
            .setFontColor(WARNING_COLOR));

        if (report.getWeaknesses() != null) {
            for (String weakness : report.getWeaknesses()) {
                weaknessesCell.add(new Paragraph("• " + weakness)
                    .setFont(normalFont)
                    .setFontSize(11)
                    .setMarginTop(5));
            }
        }

        comparisonTable.addCell(strengthsCell);
        comparisonTable.addCell(weaknessesCell);
        document.add(comparisonTable);

        // 维度评分概览
        if (report.getDimensionScores() != null && !report.getDimensionScores().isEmpty()) {
            Paragraph dimensionTitle = new Paragraph("能力维度评分")
                .setFont(boldFont)
                .setFontSize(16)
                .setFontColor(SECONDARY_COLOR)
                .setMarginTop(30)
                .setMarginBottom(15);
            document.add(dimensionTitle);

            Table dimensionTable = new Table(3);
            dimensionTable.setWidth(UnitValue.createPercentValue(100));

            // 表头
            dimensionTable.addHeaderCell(createHeaderCell("能力维度", boldFont));
            dimensionTable.addHeaderCell(createHeaderCell("得分", boldFont));
            dimensionTable.addHeaderCell(createHeaderCell("评价", boldFont));

            // 数据行
            for (org.dromara.app.domain.DimensionScore score : report.getDimensionScores()) {
                dimensionTable.addCell(new Cell().add(new Paragraph(score.getDimension()).setFont(normalFont).setFontSize(11)));

                Cell scoreCell = new Cell().add(new Paragraph(score.getScore() + "/" + score.getMaxScore())
                    .setFont(normalFont)
                    .setFontSize(11)
                    .setFontColor(getScoreColor(score.getScore())));
                dimensionTable.addCell(scoreCell);

                dimensionTable.addCell(new Cell().add(new Paragraph(getScoreLevel(score.getScore()))
                    .setFont(normalFont)
                    .setFontSize(11)));
            }

            document.add(dimensionTable);
        }

        // 新页
        document.add(new AreaBreak());
    }

    /**
     * 添加维度分析页
     */
    private void addDimensionAnalysisPage(Document document, InterviewReport report, PdfFont boldFont, PdfFont normalFont) {
        // 页面标题
        Paragraph pageTitle = new Paragraph("详细能力分析")
            .setFont(boldFont)
            .setFontSize(20)
            .setFontColor(PRIMARY_COLOR)
            .setMarginBottom(20);
        document.add(pageTitle);

        if (report.getDimensionScores() != null && !report.getDimensionScores().isEmpty()) {
            for (org.dromara.app.domain.DimensionScore score : report.getDimensionScores()) {
                // 维度标题
                Paragraph dimensionTitle = new Paragraph(score.getDimension())
                    .setFont(boldFont)
                    .setFontSize(16)
                    .setFontColor(SECONDARY_COLOR)
                    .setMarginTop(20)
                    .setMarginBottom(10);
                document.add(dimensionTitle);

                // 分数和等级
                Table scoreTable = new Table(4);
                scoreTable.setWidth(UnitValue.createPercentValue(100));

                scoreTable.addCell(createInfoCell("得分", score.getScore() + "/" + score.getMaxScore(), normalFont));
                scoreTable.addCell(createInfoCell("等级", getScoreLevel(score.getScore()), normalFont));
                scoreTable.addCell(createInfoCell("百分位", score.getPercentile() + "%", normalFont));
                scoreTable.addCell(createInfoCell("评价", score.getDescription(), normalFont));

                document.add(scoreTable);

                // 详细分析
                if (score.getDetailedAnalysis() != null && !score.getDetailedAnalysis().isEmpty()) {
                    Paragraph analysis = new Paragraph(score.getDetailedAnalysis())
                        .setFont(normalFont)
                        .setFontSize(11)
                        .setMarginTop(10)
                        .setMarginBottom(15);
                    document.add(analysis);
                }
            }
        }

        // 新页
        document.add(new AreaBreak());
    }

    /**
     * 添加改进建议页
     */
    private void addImprovementSuggestionsPage(Document document, InterviewReport report, PdfFont boldFont, PdfFont normalFont) {
        // 页面标题
        Paragraph pageTitle = new Paragraph("改进建议")
            .setFont(boldFont)
            .setFontSize(20)
            .setFontColor(PRIMARY_COLOR)
            .setMarginBottom(20);
        document.add(pageTitle);

        if (report.getImprovementSuggestions() != null && !report.getImprovementSuggestions().isEmpty()) {
            int index = 1;
            for (InterviewReport.ImprovementSuggestion suggestion : report.getImprovementSuggestions()) {
                // 建议标题
                Paragraph suggestionTitle = new Paragraph(index + ". " + suggestion.getTitle())
                    .setFont(boldFont)
                    .setFontSize(14)
                    .setFontColor(SECONDARY_COLOR)
                    .setMarginTop(20)
                    .setMarginBottom(10);
                document.add(suggestionTitle);

                // 建议描述
                Paragraph description = new Paragraph(suggestion.getDescription())
                    .setFont(normalFont)
                    .setFontSize(12)
                    .setMarginBottom(10);
                document.add(description);

                // 行动项
                if (suggestion.getActionItems() != null && !suggestion.getActionItems().isEmpty()) {
                    Paragraph actionTitle = new Paragraph("具体行动:")
                        .setFont(boldFont)
                        .setFontSize(12)
                        .setMarginBottom(5);
                    document.add(actionTitle);

                    for (String action : suggestion.getActionItems()) {
                        Paragraph actionItem = new Paragraph("• " + action)
                            .setFont(normalFont)
                            .setFontSize(11)
                            .setMarginLeft(20)
                            .setMarginBottom(3);
                        document.add(actionItem);
                    }
                }

                // 优先级和预计时间
                Table suggestionInfo = new Table(3);
                suggestionInfo.setWidth(UnitValue.createPercentValue(60));
                suggestionInfo.setMarginTop(10);

                suggestionInfo.addCell(createInfoCell("优先级", suggestion.getPriority(), normalFont));
                suggestionInfo.addCell(createInfoCell("预计时间", suggestion.getEstimatedTime() + "天", normalFont));
                suggestionInfo.addCell(createInfoCell("难度", suggestion.getDifficulty(), normalFont));

                document.add(suggestionInfo);

                index++;
            }
        }

        // 新页
        document.add(new AreaBreak());
    }

    /**
     * 添加学习路径页
     */
    private void addLearningPathPage(Document document, InterviewReport report, PdfFont boldFont, PdfFont normalFont) {
        // 页面标题
        Paragraph pageTitle = new Paragraph("个性化学习路径")
            .setFont(boldFont)
            .setFontSize(20)
            .setFontColor(PRIMARY_COLOR)
            .setMarginBottom(20);
        document.add(pageTitle);

        if (report.getLearningPaths() != null && !report.getLearningPaths().isEmpty()) {
            int index = 1;
            for (InterviewReport.LearningPathRecommendation path : report.getLearningPaths()) {
                // 路径标题
                Paragraph pathTitle = new Paragraph(index + ". " + path.getTitle())
                    .setFont(boldFont)
                    .setFontSize(16)
                    .setFontColor(SECONDARY_COLOR)
                    .setMarginTop(25)
                    .setMarginBottom(10);
                document.add(pathTitle);

                // 路径描述
                Paragraph description = new Paragraph(path.getDescription())
                    .setFont(normalFont)
                    .setFontSize(12)
                    .setMarginBottom(15);
                document.add(description);

                // 路径信息
                Table pathInfo = new Table(3);
                pathInfo.setWidth(UnitValue.createPercentValue(80));
                pathInfo.setMarginBottom(15);

                pathInfo.addCell(createInfoCell("预计时长", path.getEstimatedHours() + "小时", normalFont));
                pathInfo.addCell(createInfoCell("难度等级", path.getDifficulty(), normalFont));
                pathInfo.addCell(createInfoCell("优先级", String.valueOf(path.getPriority()), normalFont));

                document.add(pathInfo);

                // 学习资源
                if (path.getResources() != null && !path.getResources().isEmpty()) {
                    Paragraph resourceTitle = new Paragraph("推荐资源:")
                        .setFont(boldFont)
                        .setFontSize(12)
                        .setMarginBottom(8);
                    document.add(resourceTitle);

                    for (InterviewReport.LearningResource resource : path.getResources()) {
                        Paragraph resourceItem = new Paragraph("• " + resource.getTitle() + " (" + resource.getType() + ")")
                            .setFont(normalFont)
                            .setFontSize(11)
                            .setMarginLeft(20)
                            .setMarginBottom(3);
                        document.add(resourceItem);

                        if (resource.getDescription() != null && !resource.getDescription().isEmpty()) {
                            Paragraph resourceDesc = new Paragraph("  " + resource.getDescription())
                                .setFont(normalFont)
                                .setFontSize(10)
                                .setFontColor(SECONDARY_COLOR)
                                .setMarginLeft(25)
                                .setMarginBottom(5);
                            document.add(resourceDesc);
                        }
                    }
                }

                // 学习里程碑
                if (path.getMilestones() != null && !path.getMilestones().isEmpty()) {
                    Paragraph milestoneTitle = new Paragraph("学习里程碑:")
                        .setFont(boldFont)
                        .setFontSize(12)
                        .setMarginTop(10)
                        .setMarginBottom(8);
                    document.add(milestoneTitle);

                    for (String milestone : path.getMilestones()) {
                        Paragraph milestoneItem = new Paragraph("✓ " + milestone)
                            .setFont(normalFont)
                            .setFontSize(11)
                            .setMarginLeft(20)
                            .setMarginBottom(3);
                        document.add(milestoneItem);
                    }
                }

                index++;
            }
        }

        // 新页
        document.add(new AreaBreak());
    }

    /**
     * 添加附录页
     */
    private void addAppendixPage(Document document, InterviewReport report, PdfFont boldFont, PdfFont normalFont) {
        // 页面标题
        Paragraph pageTitle = new Paragraph("附录")
            .setFont(boldFont)
            .setFontSize(20)
            .setFontColor(PRIMARY_COLOR)
            .setMarginBottom(20);
        document.add(pageTitle);

        // 评分标准说明
        Paragraph standardTitle = new Paragraph("评分标准说明")
            .setFont(boldFont)
            .setFontSize(16)
            .setFontColor(SECONDARY_COLOR)
            .setMarginBottom(15);
        document.add(standardTitle);

        Table standardTable = new Table(2);
        standardTable.setWidth(UnitValue.createPercentValue(80));

        standardTable.addHeaderCell(createHeaderCell("分数区间", boldFont));
        standardTable.addHeaderCell(createHeaderCell("等级评价", boldFont));

        standardTable.addCell(createTableCell("90-100分", normalFont));
        standardTable.addCell(createTableCell("优秀 - 表现卓越，各方面能力突出", normalFont));

        standardTable.addCell(createTableCell("80-89分", normalFont));
        standardTable.addCell(createTableCell("良好 - 表现良好，大部分能力达标", normalFont));

        standardTable.addCell(createTableCell("70-79分", normalFont));
        standardTable.addCell(createTableCell("中等 - 表现一般，部分能力需要提升", normalFont));

        standardTable.addCell(createTableCell("60-69分", normalFont));
        standardTable.addCell(createTableCell("及格 - 基本达标，多数能力需要改进", normalFont));

        standardTable.addCell(createTableCell("60分以下", normalFont));
        standardTable.addCell(createTableCell("不及格 - 表现不佳，需要全面提升", normalFont));

        document.add(standardTable);

        // 报告说明
        Paragraph disclaimerTitle = new Paragraph("报告说明")
            .setFont(boldFont)
            .setFontSize(16)
            .setFontColor(SECONDARY_COLOR)
            .setMarginTop(30)
            .setMarginBottom(15);
        document.add(disclaimerTitle);

        String disclaimerText = "1. 本报告基于AI智能分析生成，评估结果仅供参考。\n" +
                               "2. 面试表现受多种因素影响，建议结合实际情况综合判断。\n" +
                               "3. 改进建议和学习路径为个性化推荐，可根据个人情况调整。\n" +
                               "4. 如有疑问，请咨询专业的职业规划师或面试指导老师。";

        Paragraph disclaimer = new Paragraph(disclaimerText)
            .setFont(normalFont)
            .setFontSize(11)
            .setMarginBottom(20);
        document.add(disclaimer);

        // 联系信息
        Paragraph contactTitle = new Paragraph("技术支持")
            .setFont(boldFont)
            .setFontSize(16)
            .setFontColor(SECONDARY_COLOR)
            .setMarginTop(20)
            .setMarginBottom(15);
        document.add(contactTitle);

        Paragraph contact = new Paragraph("智能面试评测系统\n技术支持邮箱: <EMAIL>")
            .setFont(normalFont)
            .setFontSize(11);
        document.add(contact);
    }

    /**
     * 添加页脚
     */
    private void addPageFooter(Document document, PdfFont font) {
        // 这里可以添加页脚逻辑，如页码等
        // 由于iText的页脚处理比较复杂，这里简化处理
    }

    // ========== 辅助方法 ==========

    /**
     * 添加表格行
     */
    private void addTableRow(Table table, String label, String value, PdfFont font) {
        table.addCell(new Cell().add(new Paragraph(label).setFont(font).setFontSize(12)));
        table.addCell(new Cell().add(new Paragraph(value).setFont(font).setFontSize(12)));
    }

    /**
     * 创建表头单元格
     */
    private Cell createHeaderCell(String text, PdfFont font) {
        return new Cell().add(new Paragraph(text)
            .setFont(font)
            .setFontSize(12)
            .setFontColor(ColorConstants.WHITE))
            .setBackgroundColor(PRIMARY_COLOR);
    }

    /**
     * 创建表格单元格
     */
    private Cell createTableCell(String text, PdfFont font) {
        return new Cell().add(new Paragraph(text).setFont(font).setFontSize(11));
    }

    /**
     * 创建信息单元格
     */
    private Cell createInfoCell(String label, String value, PdfFont font) {
        return new Cell().add(new Paragraph(label + ": " + value).setFont(font).setFontSize(11));
    }

    /**
     * 根据分数获取颜色
     */
    private Color getScoreColor(Integer score) {
        if (score == null) return SECONDARY_COLOR;
        if (score >= 90) return SUCCESS_COLOR;
        if (score >= 80) return PRIMARY_COLOR;
        if (score >= 70) return WARNING_COLOR;
        return DANGER_COLOR;
    }

    /**
     * 获取等级文本
     */
    private String getLevelText(String level) {
        if (level == null) return "未知";
        switch (level.toLowerCase()) {
            case "excellent": return "优秀";
            case "good": return "良好";
            case "average": return "中等";
            case "poor": return "较差";
            default: return "未知";
        }
    }

    /**
     * 根据分数获取等级
     */
    private String getScoreLevel(Integer score) {
        if (score == null) return "未知";
        if (score >= 90) return "优秀";
        if (score >= 80) return "良好";
        if (score >= 70) return "中等";
        if (score >= 60) return "及格";
        return "不及格";
    }
}
