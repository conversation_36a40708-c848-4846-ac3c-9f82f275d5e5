package org.dromara.app.service;

import dev.langchain4j.memory.chat.MessageWindowChatMemory;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 聊天内存管理器
 * 负责管理聊天会话的内存，防止内存泄露
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ChatMemoryManager {

    /**
     * 内存过期时间（小时）
     */
    private static final int MEMORY_EXPIRE_HOURS = 2;
    /**
     * 最大内存数量
     */
    private static final int MAX_MEMORY_COUNT = 1000;
    /**
     * 清理任务执行间隔（分钟）
     */
    private static final int CLEANUP_INTERVAL_MINUTES = 30;
    /**
     * 内存缓存，存储会话ID到内存的映射
     */
    private final Map<String, MemoryWrapper> memoryCache = new ConcurrentHashMap<>();
    /**
     * 定时清理任务执行器
     */
    private ScheduledExecutorService scheduler;

    @PostConstruct
    public void init() {
        scheduler = Executors.newScheduledThreadPool(1, r -> {
            Thread thread = new Thread(r, "ChatMemoryCleanup");
            thread.setDaemon(true);
            return thread;
        });

        // 定期清理过期内存
        scheduler.scheduleAtFixedRate(
            this::cleanExpiredMemory,
            CLEANUP_INTERVAL_MINUTES,
            CLEANUP_INTERVAL_MINUTES,
            TimeUnit.MINUTES
        );

        log.info("聊天内存管理器初始化完成，清理间隔: {} 分钟", CLEANUP_INTERVAL_MINUTES);
    }

    @PreDestroy
    public void destroy() {
        if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdown();
            try {
                if (!scheduler.awaitTermination(10, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        memoryCache.clear();
        log.info("聊天内存管理器已销毁");
    }

    /**
     * 获取或创建会话内存
     *
     * @param sessionId 会话ID
     * @return 聊天内存
     */
    public MessageWindowChatMemory getOrCreateMemory(String sessionId) {
        return getOrCreateMemory(sessionId, 20);
    }

    /**
     * 获取或创建会话内存
     *
     * @param sessionId   会话ID
     * @param maxMessages 最大消息数
     * @return 聊天内存
     */
    public MessageWindowChatMemory getOrCreateMemory(String sessionId, int maxMessages) {
        MemoryWrapper wrapper = memoryCache.computeIfAbsent(sessionId, k -> {
            log.debug("创建新的聊天内存: sessionId={}, maxMessages={}", sessionId, maxMessages);
            return new MemoryWrapper(MessageWindowChatMemory.withMaxMessages(maxMessages));
        });

        // 更新最后访问时间
        wrapper.updateLastAccess();

        // 检查内存数量是否超限
        if (memoryCache.size() > MAX_MEMORY_COUNT) {
            cleanOldestMemory();
        }

        return wrapper.getMemory();
    }

    /**
     * 移除会话内存
     *
     * @param sessionId 会话ID
     */
    public void removeMemory(String sessionId) {
        MemoryWrapper removed = memoryCache.remove(sessionId);
        if (removed != null) {
            log.debug("移除聊天内存: sessionId={}", sessionId);
        }
    }

    /**
     * 清理过期内存
     */
    private void cleanExpiredMemory() {
        LocalDateTime expireTime = LocalDateTime.now().minusHours(MEMORY_EXPIRE_HOURS);
        int cleanedCount = 0;

        var iterator = memoryCache.entrySet().iterator();
        while (iterator.hasNext()) {
            var entry = iterator.next();
            if (entry.getValue().getLastAccess().isBefore(expireTime)) {
                iterator.remove();
                cleanedCount++;
            }
        }

        if (cleanedCount > 0) {
            log.info("清理过期聊天内存: {} 个，剩余: {} 个", cleanedCount, memoryCache.size());
        }
    }

    /**
     * 清理最旧的内存（当内存数量超限时）
     */
    private void cleanOldestMemory() {
        if (memoryCache.isEmpty()) {
            return;
        }

        String oldestSessionId = null;
        LocalDateTime oldestTime = LocalDateTime.now();

        for (Map.Entry<String, MemoryWrapper> entry : memoryCache.entrySet()) {
            if (entry.getValue().getLastAccess().isBefore(oldestTime)) {
                oldestTime = entry.getValue().getLastAccess();
                oldestSessionId = entry.getKey();
            }
        }

        if (oldestSessionId != null) {
            memoryCache.remove(oldestSessionId);
            log.debug("清理最旧的聊天内存: sessionId={}", oldestSessionId);
        }
    }

    /**
     * 获取内存统计信息
     *
     * @return 统计信息
     */
    public MemoryStats getStats() {
        return new MemoryStats(
            memoryCache.size(),
            MAX_MEMORY_COUNT,
            MEMORY_EXPIRE_HOURS
        );
    }

    /**
     * 内存包装器，包含内存对象和最后访问时间
     */
    private static class MemoryWrapper {
        private final MessageWindowChatMemory memory;
        private LocalDateTime lastAccess;

        public MemoryWrapper(MessageWindowChatMemory memory) {
            this.memory = memory;
            this.lastAccess = LocalDateTime.now();
        }

        public MessageWindowChatMemory getMemory() {
            return memory;
        }

        public LocalDateTime getLastAccess() {
            return lastAccess;
        }

        public void updateLastAccess() {
            this.lastAccess = LocalDateTime.now();
        }
    }

    /**
     * 内存统计信息
     */
    public static class MemoryStats {
        private final int currentCount;
        private final int maxCount;
        private final int expireHours;

        public MemoryStats(int currentCount, int maxCount, int expireHours) {
            this.currentCount = currentCount;
            this.maxCount = maxCount;
            this.expireHours = expireHours;
        }

        public int getCurrentCount() {
            return currentCount;
        }

        public int getMaxCount() {
            return maxCount;
        }

        public int getExpireHours() {
            return expireHours;
        }

        @Override
        public String toString() {
            return String.format("MemoryStats{current=%d, max=%d, expireHours=%d}",
                currentCount, maxCount, expireHours);
        }
    }
}
