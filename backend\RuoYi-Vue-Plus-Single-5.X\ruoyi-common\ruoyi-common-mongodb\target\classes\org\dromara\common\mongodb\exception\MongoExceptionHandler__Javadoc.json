{"doc": "\n MongoDB异常处理器\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "handleMongoException", "paramTypes": ["com.mongodb.MongoException"], "doc": "\n MongoDB通用异常\r\n"}, {"name": "handleMongoTimeoutException", "paramTypes": ["com.mongodb.MongoTimeoutException"], "doc": "\n MongoDB超时异常\r\n"}, {"name": "handleMongoWriteException", "paramTypes": ["com.mongodb.MongoWriteException"], "doc": "\n MongoDB写入异常\r\n"}, {"name": "handleDataAccessException", "paramTypes": ["org.springframework.dao.DataAccessException"], "doc": "\n 数据访问异常\r\n"}, {"name": "handleDuplicateKeyException", "paramTypes": ["org.springframework.dao.DuplicateKeyException"], "doc": "\n 重复键异常\r\n"}], "constructors": []}