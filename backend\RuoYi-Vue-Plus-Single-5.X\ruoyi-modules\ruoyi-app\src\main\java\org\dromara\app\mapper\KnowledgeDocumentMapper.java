package org.dromara.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.dromara.app.domain.KnowledgeDocument;

import java.util.List;

/**
 * 知识库文档Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface KnowledgeDocumentMapper extends BaseMapper<KnowledgeDocument> {

    /**
     * 根据知识库ID查询文档列表
     *
     * @param knowledgeBaseId 知识库ID
     * @return 文档列表
     */
    @Select("SELECT * FROM app_knowledge_document WHERE knowledge_base_id = #{knowledgeBaseId} AND del_flag = 0 ORDER BY sort_order ASC, create_time DESC")
    List<KnowledgeDocument> selectByKnowledgeBaseId(@Param("knowledgeBaseId") Long knowledgeBaseId);

    /**
     * 根据状态查询文档列表
     *
     * @param status 状态
     * @return 文档列表
     */
    @Select("SELECT * FROM app_knowledge_document WHERE status = #{status} AND del_flag = 0 ORDER BY create_time ASC")
    List<KnowledgeDocument> selectByStatus(@Param("status") Integer status);

    /**
     * 根据处理状态查询文档列表
     *
     * @param processStatus 处理状态
     * @return 文档列表
     */
    @Select("SELECT * FROM app_knowledge_document WHERE process_status = #{processStatus} AND del_flag = 0 ORDER BY create_time ASC")
    List<KnowledgeDocument> selectByProcessStatus(@Param("processStatus") Integer processStatus);

    /**
     * 更新文档状态
     *
     * @param id           文档ID
     * @param status       状态
     * @param errorMessage 错误信息
     * @return 更新结果
     */
    @Update("UPDATE app_knowledge_document SET status = #{status}, error_message = #{errorMessage} WHERE id = #{id}")
    int updateStatus(@Param("id") Long id, @Param("status") Integer status, @Param("errorMessage") String errorMessage);

    /**
     * 更新处理状态
     *
     * @param id            文档ID
     * @param processStatus 处理状态
     * @return 更新结果
     */
    @Update("UPDATE app_knowledge_document SET process_status = #{processStatus} WHERE id = #{id}")
    int updateProcessStatus(@Param("id") Long id, @Param("processStatus") Integer processStatus);

    /**
     * 更新向量数量
     *
     * @param id          文档ID
     * @param vectorCount 向量数量
     * @return 更新结果
     */
    @Update("UPDATE app_knowledge_document SET vector_count = #{vectorCount} WHERE id = #{id}")
    int updateVectorCount(@Param("id") Long id, @Param("vectorCount") Long vectorCount);

    /**
     * 统计知识库文档数量
     *
     * @param knowledgeBaseId 知识库ID
     * @return 文档数量
     */
    @Select("SELECT COUNT(*) FROM app_knowledge_document WHERE knowledge_base_id = #{knowledgeBaseId} AND del_flag = 0")
    long countByKnowledgeBaseId(@Param("knowledgeBaseId") Long knowledgeBaseId);

    /**
     * 根据文档类型查询文档
     *
     * @param docType 文档类型
     * @return 文档列表
     */
    @Select("SELECT * FROM app_knowledge_document WHERE doc_type = #{docType} AND del_flag = 0 ORDER BY create_time DESC")
    List<KnowledgeDocument> selectByDocType(@Param("docType") String docType);

    /**
     * 批量更新文档状态
     *
     * @param ids    文档ID列表
     * @param status 状态
     * @return 更新结果
     */
    int updateStatusByIds(@Param("ids") List<Long> ids, @Param("status") Integer status);
}
