package org.dromara.app.task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.config.InterviewConfig;
import org.dromara.app.domain.InterviewSession;
import org.dromara.app.mapper.InterviewSessionMapper;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 面试会话清理定时任务
 *
 * <AUTHOR> Assistant
 * @date 2025-01-17
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "app.interview.session", name = "cleanup-enabled", havingValue = "true", matchIfMissing = true)
public class InterviewSessionCleanupTask {

    private final InterviewSessionMapper interviewSessionMapper;
    private final InterviewConfig interviewConfig;

    /**
     * 清理过期的面试会话
     * 每30分钟执行一次
     */
    @Scheduled(fixedRateString = "#{${app.interview.session.cleanup-interval-minutes:30} * 60 * 1000}")
    public void cleanupExpiredSessions() {
        log.info("开始清理过期的面试会话");

        try {
            // 查询过期的会话
            LambdaQueryWrapper<InterviewSession> wrapper = new LambdaQueryWrapper<>();
            wrapper.lt(InterviewSession::getExpiresAt, LocalDateTime.now())
                .eq(InterviewSession::getDelFlag, "0")
                .in(InterviewSession::getStatus, "created", "started", "paused");

            List<InterviewSession> expiredSessions = interviewSessionMapper.selectList(wrapper);

            if (expiredSessions.isEmpty()) {
                log.info("没有找到过期的面试会话");
                return;
            }

            // 批量更新过期会话状态
            int cleanupCount = 0;
            for (InterviewSession session : expiredSessions) {
                session.setStatus("expired");
                session.setEndTime(LocalDateTime.now());
                interviewSessionMapper.updateById(session);
                cleanupCount++;
            }

            log.info("成功清理了 {} 个过期的面试会话", cleanupCount);

        } catch (Exception e) {
            log.error("清理过期面试会话时发生错误", e);
        }
    }

    /**
     * 清理长时间未使用的会话记录
     * 每天凌晨2点执行
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanupOldSessions() {
        log.info("开始清理长时间未使用的会话记录");

        try {
            // 删除30天前的会话记录
            LocalDateTime cutoffTime = LocalDateTime.now().minusDays(30);

            LambdaQueryWrapper<InterviewSession> wrapper = new LambdaQueryWrapper<>();
            wrapper.lt(InterviewSession::getCreateTime, cutoffTime)
                .eq(InterviewSession::getDelFlag, "0");

            List<InterviewSession> oldSessions = interviewSessionMapper.selectList(wrapper);

            if (oldSessions.isEmpty()) {
                log.info("没有找到需要清理的旧会话记录");
                return;
            }

            // 软删除旧会话记录
            int deleteCount = 0;
            for (InterviewSession session : oldSessions) {
                session.setDelFlag("1");
                interviewSessionMapper.updateById(session);
                deleteCount++;
            }

            log.info("成功清理了 {} 个旧的会话记录", deleteCount);

        } catch (Exception e) {
            log.error("清理旧会话记录时发生错误", e);
        }
    }

    /**
     * 统计会话使用情况
     * 每小时执行一次
     */
    @Scheduled(fixedRate = 3600000) // 1小时
    public void reportSessionStatistics() {
        try {
            // 统计各状态的会话数量
            LambdaQueryWrapper<InterviewSession> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(InterviewSession::getDelFlag, "0");

            List<InterviewSession> allSessions = interviewSessionMapper.selectList(wrapper);

            long createdCount = allSessions.stream().filter(s -> "created".equals(s.getStatus())).count();
            long startedCount = allSessions.stream().filter(s -> "started".equals(s.getStatus())).count();
            long pausedCount = allSessions.stream().filter(s -> "paused".equals(s.getStatus())).count();
            long completedCount = allSessions.stream().filter(s -> "completed".equals(s.getStatus())).count();
            long expiredCount = allSessions.stream().filter(s -> "expired".equals(s.getStatus())).count();

            log.info("面试会话统计 - 总数: {}, 已创建: {}, 进行中: {}, 暂停: {}, 已完成: {}, 已过期: {}",
                allSessions.size(), createdCount, startedCount, pausedCount, completedCount, expiredCount);

        } catch (Exception e) {
            log.error("统计会话使用情况时发生错误", e);
        }
    }

    /**
     * 检查系统健康状态
     * 每5分钟执行一次
     */
    @Scheduled(fixedRate = 300000) // 5分钟
    public void healthCheck() {
        try {
            // 检查是否有长时间运行的会话
            LocalDateTime longRunningThreshold = LocalDateTime.now().minusHours(4);

            LambdaQueryWrapper<InterviewSession> wrapper = new LambdaQueryWrapper<>();
            wrapper.lt(InterviewSession::getStartTime, longRunningThreshold)
                .eq(InterviewSession::getStatus, "started")
                .eq(InterviewSession::getDelFlag, "0");

            List<InterviewSession> longRunningSessions = interviewSessionMapper.selectList(wrapper);

            if (!longRunningSessions.isEmpty()) {
                log.warn("发现 {} 个长时间运行的面试会话，可能需要人工干预", longRunningSessions.size());

                // 可以在这里添加告警逻辑，比如发送邮件或消息通知
                for (InterviewSession session : longRunningSessions) {
                    log.warn("长时间运行的会话: sessionId={}, userId={}, startTime={}",
                        session.getId(), session.getUserId(), session.getStartTime());
                }
            }

        } catch (Exception e) {
            log.error("健康检查时发生错误", e);
        }
    }

}
