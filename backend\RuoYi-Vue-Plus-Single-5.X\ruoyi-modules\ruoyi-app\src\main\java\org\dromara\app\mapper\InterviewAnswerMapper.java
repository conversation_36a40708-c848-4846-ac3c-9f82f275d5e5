package org.dromara.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.dromara.app.domain.InterviewAnswer;

import java.util.List;

/**
 * 面试答案Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Mapper
public interface InterviewAnswerMapper extends BaseMapper<InterviewAnswer> {

    /**
     * 根据会话ID查询所有答案
     *
     * @param sessionId 会话ID
     * @return 答案列表
     */
    List<InterviewAnswer> selectBySessionId(@Param("sessionId") String sessionId);

    /**
     * 根据会话ID和问题ID查询答案
     *
     * @param sessionId  会话ID
     * @param questionId 问题ID
     * @return 答案
     */
    InterviewAnswer selectBySessionIdAndQuestionId(@Param("sessionId") String sessionId, 
                                                   @Param("questionId") String questionId);

    /**
     * 根据用户ID查询最近的答案
     *
     * @param userId 用户ID
     * @param limit  限制数量
     * @return 答案列表
     */
    List<InterviewAnswer> selectRecentByUserId(@Param("userId") Long userId, @Param("limit") Integer limit);

    /**
     * 统计会话已回答问题数量
     *
     * @param sessionId 会话ID
     * @return 已回答数量
     */
    Integer countAnsweredBySessionId(@Param("sessionId") String sessionId);

    /**
     * 统计会话跳过问题数量
     *
     * @param sessionId 会话ID
     * @return 跳过数量
     */
    Integer countSkippedBySessionId(@Param("sessionId") String sessionId);

    /**
     * 查询会话的平均分数
     *
     * @param sessionId 会话ID
     * @return 平均分数
     */
    Double selectAvgScoreBySessionId(@Param("sessionId") String sessionId);

    /**
     * 根据状态查询答案列表
     *
     * @param sessionId 会话ID
     * @param status    状态
     * @return 答案列表
     */
    List<InterviewAnswer> selectBySessionIdAndStatus(@Param("sessionId") String sessionId, 
                                                     @Param("status") String status);
}
