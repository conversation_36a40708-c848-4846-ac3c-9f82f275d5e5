{"doc": "\n 微信支付相关常量\r\n", "fields": [{"name": "TRADE_STATE_SUCCESS", "doc": "\n 微信支付交易状态 - 支付成功\r\n"}, {"name": "TRADE_STATE_REFUND", "doc": "\n 微信支付交易状态 - 转入退款\r\n"}, {"name": "TRADE_STATE_NOTPAY", "doc": "\n 微信支付交易状态 - 未支付\r\n"}, {"name": "TRADE_STATE_CLOSED", "doc": "\n 微信支付交易状态 - 已关闭\r\n"}, {"name": "TRADE_STATE_REVOKED", "doc": "\n 微信支付交易状态 - 已撤销（付款码支付）\r\n"}, {"name": "TRADE_STATE_USERPAYING", "doc": "\n 微信支付交易状态 - 用户支付中（付款码支付）\r\n"}, {"name": "TRADE_STATE_PAYERROR", "doc": "\n 微信支付交易状态 - 支付失败（其他原因，如银行返回失败）\r\n"}], "enumConstants": [], "methods": [], "constructors": []}