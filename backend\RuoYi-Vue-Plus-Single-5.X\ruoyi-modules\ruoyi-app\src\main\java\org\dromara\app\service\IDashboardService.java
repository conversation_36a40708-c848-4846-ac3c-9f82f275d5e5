package org.dromara.app.service;

import org.dromara.app.domain.vo.DashboardSummaryVO;
import org.dromara.app.domain.vo.SmartTaskVO;
import org.dromara.app.domain.vo.StudyStatsVO;
import org.dromara.app.domain.vo.UserAbilitiesVO;

import java.util.List;
import java.util.Map;

/**
 * 首页仪表盘服务接口
 *
 * <AUTHOR>
 */
public interface IDashboardService {

    /**
     * 获取首页仪表盘汇总数据
     *
     * @param userId 用户ID
     * @return 仪表盘汇总数据
     */
    DashboardSummaryVO getDashboardSummary(Long userId);

    /**
     * 获取用户能力评估数据
     *
     * @param userId 用户ID
     * @return 用户能力评估数据
     */
    UserAbilitiesVO getUserAbilities(Long userId);

    /**
     * 获取学习统计数据
     *
     * @param userId 用户ID
     * @return 学习统计数据
     */
    StudyStatsVO getStudyStats(Long userId);

    /**
     * 获取智能推荐任务
     *
     * @param userId 用户ID
     * @param limit  任务数量限制
     * @param type   任务类型
     * @return 智能推荐任务列表
     */
    List<SmartTaskVO> getSmartTasks(Long userId, Integer limit, String type);

    /**
     * 获取最近面试记录
     *
     * @param userId 用户ID
     * @param limit  记录数量限制
     * @param page   页码
     * @return 最近面试记录列表
     */
    List<Map<String, Object>> getRecentInterviews(Long userId, Integer limit, Integer page);

    /**
     * 更新用户目标岗位
     *
     * @param userId         用户ID
     * @param targetPosition 目标岗位
     * @return 更新结果
     */
    boolean updateTargetPosition(Long userId, String targetPosition);

    /**
     * 标记任务为已完成
     *
     * @param userId 用户ID
     * @param taskId 任务ID
     * @return 更新结果
     */
    boolean completeTask(Long userId, Long taskId);

    /**
     * 获取首页所有数据（聚合接口）
     *
     * @param userId 用户ID
     * @return 所有数据
     */
    Map<String, Object> getDashboardData(Long userId);
}
