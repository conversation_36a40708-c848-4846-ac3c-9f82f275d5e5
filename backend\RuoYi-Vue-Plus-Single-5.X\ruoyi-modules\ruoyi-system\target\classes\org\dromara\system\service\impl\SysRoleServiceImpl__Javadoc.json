{"doc": "\n 角色 业务层处理\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectRoleList", "paramTypes": ["org.dromara.system.domain.bo.SysRoleBo"], "doc": "\n 根据条件分页查询角色数据\r\n\r\n @param role 角色信息\r\n @return 角色数据集合信息\r\n"}, {"name": "selectRolesByUserId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据用户ID查询角色\r\n\r\n @param userId 用户ID\r\n @return 角色列表\r\n"}, {"name": "selectRolesAuthByUserId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据用户ID查询角色列表(包含被授权状态)\r\n\r\n @param userId 用户ID\r\n @return 角色列表\r\n"}, {"name": "selectRolePermissionByUserId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据用户ID查询权限\r\n\r\n @param userId 用户ID\r\n @return 权限列表\r\n"}, {"name": "selectRoleAll", "paramTypes": [], "doc": "\n 查询所有角色\r\n\r\n @return 角色列表\r\n"}, {"name": "selectRoleListByUserId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据用户ID获取角色选择框列表\r\n\r\n @param userId 用户ID\r\n @return 选中角色ID列表\r\n"}, {"name": "selectRoleById", "paramTypes": ["java.lang.Long"], "doc": "\n 通过角色ID查询角色\r\n\r\n @param roleId 角色ID\r\n @return 角色对象信息\r\n"}, {"name": "selectRoleByIds", "paramTypes": ["java.util.List"], "doc": "\n 通过角色ID串查询角色\r\n\r\n @param roleIds 角色ID串\r\n @return 角色列表信息\r\n"}, {"name": "checkRoleNameUnique", "paramTypes": ["org.dromara.system.domain.bo.SysRoleBo"], "doc": "\n 校验角色名称是否唯一\r\n\r\n @param role 角色信息\r\n @return 结果\r\n"}, {"name": "checkRoleKeyUnique", "paramTypes": ["org.dromara.system.domain.bo.SysRoleBo"], "doc": "\n 校验角色权限是否唯一\r\n\r\n @param role 角色信息\r\n @return 结果\r\n"}, {"name": "checkRoleAllowed", "paramTypes": ["org.dromara.system.domain.bo.SysRoleBo"], "doc": "\n 校验角色是否允许操作\r\n\r\n @param role 角色信息\r\n"}, {"name": "checkRoleDataScope", "paramTypes": ["java.lang.Long"], "doc": "\n 校验角色是否有数据权限\r\n\r\n @param roleId 角色id\r\n"}, {"name": "countUserRoleByRoleId", "paramTypes": ["java.lang.Long"], "doc": "\n 通过角色ID查询角色使用数量\r\n\r\n @param roleId 角色ID\r\n @return 结果\r\n"}, {"name": "insertRole", "paramTypes": ["org.dromara.system.domain.bo.SysRoleBo"], "doc": "\n 新增保存角色信息\r\n\r\n @param bo 角色信息\r\n @return 结果\r\n"}, {"name": "updateRole", "paramTypes": ["org.dromara.system.domain.bo.SysRoleBo"], "doc": "\n 修改保存角色信息\r\n\r\n @param bo 角色信息\r\n @return 结果\r\n"}, {"name": "updateRoleStatus", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 修改角色状态\r\n\r\n @param roleId 角色ID\r\n @param status 角色状态\r\n @return 结果\r\n"}, {"name": "authDataScope", "paramTypes": ["org.dromara.system.domain.bo.SysRoleBo"], "doc": "\n 修改数据权限信息\r\n\r\n @param bo 角色信息\r\n @return 结果\r\n"}, {"name": "insertRoleMenu", "paramTypes": ["org.dromara.system.domain.bo.SysRoleBo"], "doc": "\n 新增角色菜单信息\r\n\r\n @param role 角色对象\r\n"}, {"name": "insertRoleDept", "paramTypes": ["org.dromara.system.domain.bo.SysRoleBo"], "doc": "\n 新增角色部门信息(数据权限)\r\n\r\n @param role 角色对象\r\n"}, {"name": "deleteRoleById", "paramTypes": ["java.lang.Long"], "doc": "\n 通过角色ID删除角色\r\n\r\n @param roleId 角色ID\r\n @return 结果\r\n"}, {"name": "deleteRoleByIds", "paramTypes": ["java.lang.Long[]"], "doc": "\n 批量删除角色信息\r\n\r\n @param roleIds 需要删除的角色ID\r\n @return 结果\r\n"}, {"name": "deleteAuthUser", "paramTypes": ["org.dromara.system.domain.SysUserRole"], "doc": "\n 取消授权用户角色\r\n\r\n @param userRole 用户和角色关联信息\r\n @return 结果\r\n"}, {"name": "deleteAuthUsers", "paramTypes": ["java.lang.Long", "java.lang.Long[]"], "doc": "\n 批量取消授权用户角色\r\n\r\n @param roleId  角色ID\r\n @param userIds 需要取消授权的用户数据ID\r\n @return 结果\r\n"}, {"name": "insertAuthUsers", "paramTypes": ["java.lang.Long", "java.lang.Long[]"], "doc": "\n 批量选择授权用户角色\r\n\r\n @param roleId  角色ID\r\n @param userIds 需要授权的用户数据ID\r\n @return 结果\r\n"}], "constructors": []}