<?xml version="1.0" encoding="UTF-8"?>
<svg width="900" height="700" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .entity { fill: white; stroke: black; stroke-width: 2; }
      .attribute { fill: white; stroke: black; stroke-width: 1; }
      .relationship { fill: white; stroke: black; stroke-width: 2; }
      .text { font-family: Arial, sans-serif; font-size: 12px; text-anchor: middle; dominant-baseline: middle; }
      .line { stroke: black; stroke-width: 1; fill: none; }
      .cardinality { font-family: Arial, sans-serif; font-size: 10px; text-anchor: middle; }
    </style>
  </defs>
  
  <!-- 用户实体 -->
  <rect x="50" y="250" width="100" height="60" class="entity"/>
  <text x="100" y="280" class="text">用户</text>
  
  <!-- 用户属性 -->
  <ellipse cx="100" cy="180" rx="30" ry="20" class="attribute"/>
  <text x="100" y="180" class="text">用户ID</text>
  <line x1="100" y1="200" x2="100" y2="250" class="line"/>
  
  <ellipse cx="30" cy="220" rx="25" ry="20" class="attribute"/>
  <text x="30" y="220" class="text">姓名</text>
  <line x1="45" y1="235" x2="70" y2="250" class="line"/>
  
  <ellipse cx="30" cy="340" rx="25" ry="20" class="attribute"/>
  <text x="30" y="340" class="text">等级</text>
  <line x1="45" y1="325" x2="70" y2="310" class="line"/>
  
  <ellipse cx="100" cy="370" rx="30" ry="20" class="attribute"/>
  <text x="100" y="370" class="text">经验值</text>
  <line x1="100" y1="350" x2="100" y2="310" class="line"/>
  
  <!-- 学习进度实体 -->
  <rect x="350" y="250" width="120" height="60" class="entity"/>
  <text x="410" y="280" class="text">学习进度</text>
  
  <!-- 学习进度属性 -->
  <ellipse cx="300" cy="150" rx="30" ry="20" class="attribute"/>
  <text x="300" y="150" class="text">进度ID</text>
  <line x1="315" y1="165" x2="370" y2="250" class="line"/>
  
  <ellipse cx="410" cy="130" rx="30" ry="20" class="attribute"/>
  <text x="410" y="130" class="text">学习路径</text>
  <line x1="410" y1="150" x2="410" y2="250" class="line"/>
  
  <ellipse cx="520" cy="150" rx="30" ry="20" class="attribute"/>
  <text x="520" y="150" class="text">完成度</text>
  <line x1="505" y1="165" x2="450" y2="250" class="line"/>
  
  <ellipse cx="250" cy="200" rx="30" ry="20" class="attribute"/>
  <text x="250" y="200" class="text">开始时间</text>
  <line x1="265" y1="215" x2="350" y2="260" class="line"/>
  
  <ellipse cx="570" cy="200" rx="30" ry="20" class="attribute"/>
  <text x="570" y="200" class="text">预计完成</text>
  <line x1="555" y1="215" x2="470" y2="260" class="line"/>
  
  <ellipse cx="250" cy="360" rx="30" ry="20" class="attribute"/>
  <text x="250" y="360" class="text">学习时长</text>
  <line x1="265" y1="345" x2="350" y2="300" class="line"/>
  
  <ellipse cx="350" cy="380" rx="25" ry="20" class="attribute"/>
  <text x="350" y="380" class="text">状态</text>
  <line x1="365" y1="365" x2="390" y2="310" class="line"/>
  
  <ellipse cx="520" cy="340" rx="30" ry="20" class="attribute"/>
  <text x="520" y="340" class="text">当前章节</text>
  <line x1="505" y1="325" x2="460" y2="310" class="line"/>

  <!-- 学习资源实体 -->
  <rect x="650" y="100" width="120" height="60" class="entity"/>
  <text x="710" y="130" class="text">学习资源</text>

  <!-- 学习资源属性 -->
  <ellipse cx="600" cy="50" rx="30" ry="20" class="attribute"/>
  <text x="600" y="50" class="text">资源ID</text>
  <line x1="615" y1="65" x2="670" y2="100" class="line"/>

  <ellipse cx="710" cy="30" rx="30" ry="20" class="attribute"/>
  <text x="710" y="30" class="text">资源名称</text>
  <line x1="710" y1="50" x2="710" y2="100" class="line"/>

  <ellipse cx="820" cy="50" rx="30" ry="20" class="attribute"/>
  <text x="820" y="50" class="text">资源类型</text>
  <line x1="805" y1="65" x2="750" y2="100" class="line"/>

  <ellipse cx="600" cy="200" rx="25" ry="20" class="attribute"/>
  <text x="600" y="200" class="text">难度</text>
  <line x1="615" y1="185" x2="680" y2="160" class="line"/>

  <ellipse cx="820" cy="200" rx="25" ry="20" class="attribute"/>
  <text x="820" y="200" class="text">时长</text>
  <line x1="805" y1="185" x2="750" y2="160" class="line"/>
  
  <!-- 成就系统实体 -->
  <rect x="650" y="400" width="120" height="60" class="entity"/>
  <text x="710" y="430" class="text">成就系统</text>
  
  <!-- 成就系统属性 -->
  <ellipse cx="580" cy="370" rx="30" ry="20" class="attribute"/>
  <text x="580" y="370" class="text">成就ID</text>
  <line x1="595" y1="385" x2="650" y2="410" class="line"/>

  <ellipse cx="710" cy="350" rx="30" ry="20" class="attribute"/>
  <text x="710" y="350" class="text">成就名称</text>
  <line x1="710" y1="370" x2="710" y2="400" class="line"/>

  <ellipse cx="840" cy="370" rx="30" ry="20" class="attribute"/>
  <text x="840" y="370" class="text">成就类型</text>
  <line x1="825" y1="385" x2="770" y2="410" class="line"/>
  
  <ellipse cx="600" cy="510" rx="30" ry="20" class="attribute"/>
  <text x="600" y="510" class="text">获得条件</text>
  <line x1="615" y1="495" x2="670" y2="460" class="line"/>
  
  <ellipse cx="710" cy="530" rx="30" ry="20" class="attribute"/>
  <text x="710" y="530" class="text">奖励积分</text>
  <line x1="710" y1="510" x2="710" y2="460" class="line"/>
  
  <ellipse cx="820" cy="510" rx="30" ry="20" class="attribute"/>
  <text x="820" y="510" class="text">获得时间</text>
  <line x1="805" y1="495" x2="750" y2="460" class="line"/>
  
  <!-- 学习计划实体 -->
  <rect x="350" y="500" width="120" height="60" class="entity"/>
  <text x="410" y="530" class="text">学习计划</text>
  
  <!-- 学习计划属性 -->
  <ellipse cx="280" cy="470" rx="30" ry="20" class="attribute"/>
  <text x="280" y="470" class="text">计划ID</text>
  <line x1="295" y1="485" x2="350" y2="510" class="line"/>

  <ellipse cx="330" cy="450" rx="30" ry="20" class="attribute"/>
  <text x="330" y="450" class="text">计划名称</text>
  <line x1="345" y1="465" x2="380" y2="500" class="line"/>

  <ellipse cx="540" cy="470" rx="30" ry="20" class="attribute"/>
  <text x="540" y="470" class="text">目标技能</text>
  <line x1="525" y1="485" x2="470" y2="510" class="line"/>
  
  <ellipse cx="300" cy="610" rx="30" ry="20" class="attribute"/>
  <text x="300" y="610" class="text">计划周期</text>
  <line x1="315" y1="595" x2="370" y2="560" class="line"/>
  
  <ellipse cx="410" cy="630" rx="30" ry="20" class="attribute"/>
  <text x="410" y="630" class="text">优先级</text>
  <line x1="410" y1="610" x2="410" y2="560" class="line"/>
  
  <ellipse cx="520" cy="610" rx="30" ry="20" class="attribute"/>
  <text x="520" y="610" class="text">创建时间</text>
  <line x1="505" y1="595" x2="450" y2="560" class="line"/>
  
  <!-- 关系：拥有进度 -->
  <polygon points="200,250 230,270 200,290 170,270" class="relationship"/>
  <text x="200" y="270" class="text">拥有</text>
  <line x1="150" y1="280" x2="170" y2="270" class="line"/>
  <line x1="230" y1="270" x2="350" y2="280" class="line"/>
  <text x="160" y="290" class="cardinality">1</text>
  <text x="330" y="290" class="cardinality">n</text>
  
  <!-- 关系：使用资源 -->
  <polygon points="530,160 560,180 530,200 500,180" class="relationship"/>
  <text x="530" y="180" class="text">使用</text>
  <line x1="470" y1="250" x2="500" y2="180" class="line"/>
  <line x1="560" y1="180" x2="650" y2="130" class="line"/>
  <text x="480" y="220" class="cardinality">n</text>
  <text x="620" y="150" class="cardinality">n</text>
  
  <!-- 关系：获得成就 -->
  <polygon points="200,350 230,370 200,390 170,370" class="relationship"/>
  <text x="200" y="370" class="text">获得</text>
  <line x1="150" y1="300" x2="170" y2="370" class="line"/>
  <line x1="230" y1="370" x2="650" y2="420" class="line"/>
  <text x="160" y="320" class="cardinality">1</text>
  <text x="630" y="410" class="cardinality">n</text>

  <!-- 关系：制定计划 -->
  <polygon points="200,450 230,470 200,490 170,470" class="relationship"/>
  <text x="200" y="470" class="text">制定</text>
  <line x1="150" y1="310" x2="170" y2="470" class="line"/>
  <line x1="230" y1="470" x2="350" y2="520" class="line"/>
  <text x="160" y="420" class="cardinality">1</text>
  <text x="330" y="500" class="cardinality">n</text>
  
  <!-- 关系：执行计划 -->
  <polygon points="450,380 480,400 450,420 420,400" class="relationship"/>
  <text x="450" y="400" class="text">执行</text>
  <line x1="430" y1="310" x2="440" y2="380" class="line"/>
  <line x1="460" y1="420" x2="430" y2="500" class="line"/>
  <text x="440" y="350" class="cardinality">n</text>
  <text x="440" y="480" class="cardinality">1</text>
</svg>
