package org.dromara.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.dromara.app.domain.UserAssessmentResult;

import java.util.List;

/**
 * 用户评估结果Mapper接口
 *
 * <AUTHOR>
 */
public interface UserAssessmentResultMapper extends BaseMapper<UserAssessmentResult> {

    /**
     * 根据记录ID查询评估结果列表
     *
     * @param recordId 记录ID
     * @return 评估结果列表
     */
    List<UserAssessmentResult> selectResultsByRecordId(@Param("recordId") Long recordId);

    /**
     * 根据记录ID和类别查询评估结果列表
     *
     * @param recordId 记录ID
     * @param category 类别
     * @return 评估结果列表
     */
    List<UserAssessmentResult> selectResultsByRecordIdAndCategory(@Param("recordId") Long recordId,
                                                                  @Param("category") String category);

    /**
     * 根据记录ID查询各类别的平均分
     *
     * @param recordId 记录ID
     * @return 各类别平均分
     */
    List<UserAssessmentResult> selectCategoryAverageScoresByRecordId(@Param("recordId") Long recordId);

    /**
     * 批量插入评估结果
     *
     * @param results 评估结果列表
     * @return 插入数量
     */
    int insertBatch(@Param("results") List<UserAssessmentResult> results);

    /**
     * 根据记录ID删除评估结果
     *
     * @param recordId 记录ID
     * @return 删除数量
     */
    int deleteByRecordId(@Param("recordId") Long recordId);

    /**
     * 根据用户ID查询所有评估结果
     *
     * @param userId 用户ID
     * @return 评估结果列表
     */
    List<UserAssessmentResult> selectResultsByUserId(@Param("userId") Long userId);
}
