{"doc": "\n 参数配置视图对象 sys_config\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "configId", "doc": "\n 参数主键\r\n"}, {"name": "config<PERSON><PERSON>", "doc": "\n 参数名称\r\n"}, {"name": "config<PERSON><PERSON>", "doc": "\n 参数键名\r\n"}, {"name": "config<PERSON><PERSON><PERSON>", "doc": "\n 参数键值\r\n"}, {"name": "configType", "doc": "\n 系统内置（Y是 N否）\r\n"}, {"name": "remark", "doc": "\n 备注\r\n"}, {"name": "createTime", "doc": "\n 创建时间\r\n"}], "enumConstants": [], "methods": [], "constructors": []}