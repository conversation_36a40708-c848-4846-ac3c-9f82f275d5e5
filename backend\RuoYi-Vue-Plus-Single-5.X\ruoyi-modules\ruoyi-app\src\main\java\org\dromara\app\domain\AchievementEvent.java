package org.dromara.app.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.time.LocalDateTime;

/**
 * 成就事件实体
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_achievement_event")
public class AchievementEvent extends BaseEntity {

    /**
     * 事件ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 事件类型
     */
    private String eventType;

    /**
     * 事件数据（JSON格式）
     */
    private String eventData;

    /**
     * 事件时间
     */
    private LocalDateTime eventTime;

    /**
     * 关联对象ID
     */
    private String relatedObjectId;

    /**
     * 关联对象类型
     */
    private String relatedObjectType;

    /**
     * 事件值（计数类事件）
     */
    private Integer eventValue;

    /**
     * 事件来源
     */
    private String eventSource;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 客户端信息
     */
    private String clientInfo;

    /**
     * 处理状态（0=未处理，1=已处理，2=处理失败）
     */
    private Integer processStatus;

    /**
     * 处理时间
     */
    private LocalDateTime processTime;

    /**
     * 处理结果（JSON格式）
     */
    private String processResult;

    /**
     * 触发的成就IDs（用逗号分隔）
     */
    private String triggeredAchievements;
}
