package org.dromara.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.dromara.app.domain.BookReadingRecord;

import java.util.List;

/**
 * 书籍阅读记录Mapper接口
 *
 * <AUTHOR>
 */
public interface BookReadingRecordMapper extends BaseMapper<BookReadingRecord> {

    /**
     * 根据用户ID和书籍ID查询阅读记录
     *
     * @param userId 用户ID
     * @param bookId 书籍ID
     * @return 阅读记录
     */
    BookReadingRecord selectByUserIdAndBookId(@Param("userId") Long userId, @Param("bookId") Long bookId);

    /**
     * 查询用户的阅读历史（分页）
     *
     * @param page   分页参数
     * @param userId 用户ID
     * @return 阅读记录列表
     */
    Page<BookReadingRecord> selectUserReadingHistory(Page<BookReadingRecord> page, @Param("userId") Long userId);

    /**
     * 查询用户最近阅读的书籍
     *
     * @param userId 用户ID
     * @param limit  限制数量
     * @return 阅读记录列表
     */
    List<BookReadingRecord> selectRecentReading(@Param("userId") Long userId, @Param("limit") Integer limit);

    /**
     * 统计用户阅读数据
     *
     * @param userId 用户ID
     * @return 阅读统计数据
     */
    BookReadingRecord selectUserReadingStats(@Param("userId") Long userId);

    /**
     * 更新或插入阅读记录
     *
     * @param record 阅读记录
     * @return 影响行数
     */
    boolean insertOrUpdate(BookReadingRecord record);
}
