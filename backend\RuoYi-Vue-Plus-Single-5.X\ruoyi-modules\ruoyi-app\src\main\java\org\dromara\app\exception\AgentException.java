package org.dromara.app.exception;

import org.dromara.app.domain.enums.ErrorCode;

/**
 * Agent相关异常
 *
 * <AUTHOR>
 */
public class AgentException extends BaseBusinessException {

    private final String agentType;

    public AgentException(ErrorCode errorCode) {
        super(errorCode);
        this.agentType = null;
    }

    public AgentException(ErrorCode errorCode, String agentType) {
        super(errorCode);
        this.agentType = agentType;
    }

    public AgentException(ErrorCode errorCode, String agentType, String message) {
        super(errorCode, message);
        this.agentType = agentType;
    }

    public AgentException(ErrorCode errorCode, String agentType, Throwable cause) {
        super(errorCode, cause);
        this.agentType = agentType;
    }

    public static AgentException notFound(String agentType) {
        return new AgentException(ErrorCode.AGENT_NOT_FOUND, agentType);
    }

    public static AgentException disabled(String agentType) {
        return new AgentException(ErrorCode.AGENT_DISABLED, agentType);
    }

    public static AgentException serviceUnavailable() {
        return new AgentException(ErrorCode.AGENT_SERVICE_UNAVAILABLE);
    }

    public static AgentException configError(String agentType, Throwable cause) {
        return new AgentException(ErrorCode.AGENT_CONFIG_ERROR, agentType, cause);
    }

    public String getAgentType() {
        return agentType;
    }
}
