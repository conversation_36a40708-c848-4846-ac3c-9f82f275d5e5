{"doc": "\n LangChain4j配置类\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "openAiChatModel", "paramTypes": [], "doc": "\n OpenAI聊天模型\r\n"}, {"name": "dashscopeChatModel", "paramTypes": [], "doc": "\n 阿里云通义千问聊天模型\r\n"}, {"name": "openAiStreamingChatModel", "paramTypes": [], "doc": "\n OpenAI流式聊天模型\r\n"}, {"name": "dashscopeStreamingChatModel", "paramTypes": [], "doc": "\n 阿里云通义千问流式聊天模型\r\n"}, {"name": "defaultChatModel", "paramTypes": [], "doc": "\n 默认聊天模型\r\n"}, {"name": "defaultStreamingChatModel", "paramTypes": [], "doc": "\n 默认流式聊天模型\r\n"}], "constructors": []}