package org.dromara.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.dromara.app.domain.InterviewResult;

import java.util.List;

/**
 * 面试结果Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@Mapper
public interface InterviewResultMapper extends BaseMapper<InterviewResult> {

    /**
     * 根据会话ID查询面试结果
     *
     * @param sessionId 会话ID
     * @return 面试结果
     */
    InterviewResult selectBySessionId(@Param("sessionId") String sessionId);

    /**
     * 根据用户ID查询面试结果列表
     *
     * @param userId 用户ID
     * @param limit  限制数量
     * @return 面试结果列表
     */
    List<InterviewResult> selectByUserId(@Param("userId") Long userId, @Param("limit") Integer limit);

    /**
     * 根据用户ID和状态查询面试结果列表
     *
     * @param userId 用户ID
     * @param status 状态
     * @return 面试结果列表
     */
    List<InterviewResult> selectByUserIdAndStatus(@Param("userId") Long userId, @Param("status") String status);

    /**
     * 根据岗位ID查询面试结果统计
     *
     * @param jobId 岗位ID
     * @return 统计数据
     */
    List<InterviewResult> selectStatsByJobId(@Param("jobId") Long jobId);

    /**
     * 查询用户最近的面试结果
     *
     * @param userId 用户ID
     * @param days   天数
     * @return 面试结果列表
     */
    List<InterviewResult> selectRecentByUserId(@Param("userId") Long userId, @Param("days") Integer days);

    /**
     * 根据分数范围查询面试结果
     *
     * @param minScore 最低分数
     * @param maxScore 最高分数
     * @return 面试结果列表
     */
    List<InterviewResult> selectByScoreRange(@Param("minScore") Integer minScore, @Param("maxScore") Integer maxScore);

    /**
     * 查询用户平均分数
     *
     * @param userId 用户ID
     * @return 平均分数
     */
    Double selectAvgScoreByUserId(@Param("userId") Long userId);

    /**
     * 查询用户面试次数
     *
     * @param userId 用户ID
     * @return 面试次数
     */
    Integer selectCountByUserId(@Param("userId") Long userId);

    /**
     * 分页查询用户面试历史记录（包含岗位和分类信息）
     *
     * @param page     分页参数
     * @param userId   用户ID
     * @param category 分类名称（可选）
     * @param status   状态（可选）
     * @return 面试结果列表
     */
    IPage<InterviewResult> selectHistoryPageWithJobInfo(Page<InterviewResult> page,
                                                       @Param("userId") Long userId,
                                                       @Param("category") String category,
                                                       @Param("status") String status);

}
