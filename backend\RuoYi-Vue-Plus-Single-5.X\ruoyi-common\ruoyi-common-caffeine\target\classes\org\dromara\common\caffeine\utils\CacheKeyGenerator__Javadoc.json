{"doc": "\n 缓存键生成器\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "generate", "paramTypes": ["java.lang.reflect.Method", "java.lang.Object[]"], "doc": "\n 生成缓存键\r\n\r\n @param method 方法\r\n @param args   参数\r\n @return 缓存键\r\n"}, {"name": "getArgString", "paramTypes": ["java.lang.Object"], "doc": "\n 获取参数字符串表示\r\n"}, {"name": "generateSimpleKey", "paramTypes": ["java.lang.Object[]"], "doc": "\n 简单键生成（仅使用参数）\r\n\r\n @param args 参数\r\n @return 缓存键\r\n"}], "constructors": []}