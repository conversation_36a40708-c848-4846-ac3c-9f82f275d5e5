package org.dromara.app.service;

import org.dromara.app.domain.KnowledgeBase;
import org.dromara.app.domain.KnowledgeDocument;
import org.dromara.app.domain.VectorEmbedding;
import org.dromara.app.domain.bo.KnowledgeBaseBo;
import org.dromara.app.domain.bo.KnowledgeDocumentBo;
import org.dromara.app.domain.vo.KnowledgeBaseVo;
import org.dromara.app.domain.vo.KnowledgeDocumentVo;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.List;

/**
 * RAG知识库服务接口
 *
 * <AUTHOR>
 */
public interface IRagService {

    // ========== 知识库分页查询方法 ==========

    /**
     * 分页查询知识库列表
     *
     * @param bo 查询条件
     * @return 分页结果
     */
    TableDataInfo<KnowledgeBaseVo> queryPageList(KnowledgeBaseBo bo);

    /**
     * 查询知识库列表
     *
     * @param bo 查询条件
     * @return 知识库列表
     */
    List<KnowledgeBaseVo> queryList(KnowledgeBaseBo bo);

    /**
     * 根据ID查询知识库详情
     *
     * @param id 知识库ID
     * @return 知识库详情
     */
    KnowledgeBaseVo queryById(Long id);

    // ========== 文档分页查询方法 ==========

    /**
     * 分页查询知识库文档列表
     *
     * @param bo 查询条件
     * @return 分页结果
     */
    TableDataInfo<KnowledgeDocumentVo> queryDocumentPageList(KnowledgeDocumentBo bo);

    /**
     * 查询知识库文档列表
     *
     * @param bo 查询条件
     * @return 文档列表
     */
    List<KnowledgeDocumentVo> queryDocumentList(KnowledgeDocumentBo bo);

    /**
     * 根据ID查询文档详情
     *
     * @param id 文档ID
     * @return 文档详情
     */
    KnowledgeDocumentVo queryDocumentById(Long id);

    // ========== 原有方法 ==========

    /**
     * 创建知识库
     *
     * @param knowledgeBase 知识库信息
     * @return 创建结果
     */
    boolean createKnowledgeBase(KnowledgeBase knowledgeBase);

    /**
     * 添加文档到知识库
     *
     * @param document 文档信息
     * @return 添加结果
     */
    boolean addDocument(KnowledgeDocument document);

    /**
     * 处理文档（分块、向量化）
     *
     * @param documentId 文档ID
     * @return 处理结果
     */
    boolean processDocument(Long documentId);

    /**
     * 搜索相关知识
     *
     * @param knowledgeBaseId 知识库ID
     * @param query           查询文本
     * @param topK            返回数量
     * @return 相关文档列表
     */
    List<VectorEmbedding> searchKnowledge(Long knowledgeBaseId, String query, Integer topK);

    /**
     * 混合搜索（向量 + 关键词）
     *
     * @param knowledgeBaseId 知识库ID
     * @param query           查询文本
     * @param topK            返回数量
     * @return 相关文档列表
     */
    List<VectorEmbedding> hybridSearch(Long knowledgeBaseId, String query, Integer topK);

    /**
     * 获取知识库列表
     *
     * @return 知识库列表
     */
    List<KnowledgeBase> getKnowledgeBases();

    /**
     * 获取知识库详情
     *
     * @param knowledgeBaseId 知识库ID
     * @return 知识库信息
     */
    KnowledgeBase getKnowledgeBase(Long knowledgeBaseId);

    /**
     * 获取知识库文档列表
     *
     * @param knowledgeBaseId 知识库ID
     * @return 文档列表
     */
    List<KnowledgeDocument> getDocuments(Long knowledgeBaseId);

    /**
     * 删除文档
     *
     * @param documentId 文档ID
     * @return 删除结果
     */
    boolean deleteDocument(Long documentId);

    /**
     * 删除知识库
     *
     * @param knowledgeBaseId 知识库ID
     * @return 删除结果
     */
    boolean deleteKnowledgeBase(Long knowledgeBaseId);

    /**
     * 更新知识库
     *
     * @param knowledgeBase 知识库信息
     * @return 更新结果
     */
    boolean updateKnowledgeBase(KnowledgeBase knowledgeBase);

    /**
     * 重建知识库索引
     *
     * @param knowledgeBaseId 知识库ID
     * @return 重建结果
     */
    boolean rebuildIndex(Long knowledgeBaseId);

    /**
     * 获取知识库统计信息
     *
     * @param knowledgeBaseId 知识库ID
     * @return 统计信息
     */
    KnowledgeBaseStats getKnowledgeBaseStats(Long knowledgeBaseId);

    /**
     * 知识库统计信息
     */
    class KnowledgeBaseStats {
        private Long documentCount;
        private Long vectorCount;
        private Long totalSize;
        private String lastUpdateTime;

        // getters and setters
        public Long getDocumentCount() {
            return documentCount;
        }

        public void setDocumentCount(Long documentCount) {
            this.documentCount = documentCount;
        }

        public Long getVectorCount() {
            return vectorCount;
        }

        public void setVectorCount(Long vectorCount) {
            this.vectorCount = vectorCount;
        }

        public Long getTotalSize() {
            return totalSize;
        }

        public void setTotalSize(Long totalSize) {
            this.totalSize = totalSize;
        }

        public String getLastUpdateTime() {
            return lastUpdateTime;
        }

        public void setLastUpdateTime(String lastUpdateTime) {
            this.lastUpdateTime = lastUpdateTime;
        }
    }
}
