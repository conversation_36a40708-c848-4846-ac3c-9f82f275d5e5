package org.dromara.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.dromara.app.domain.ImprovementPlan;

import java.util.List;

/**
 * 提升计划Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@Mapper
public interface ImprovementPlanMapper extends BaseMapper<ImprovementPlan> {

    /**
     * 根据结果ID查询提升计划
     *
     * @param resultId 结果ID
     * @return 提升计划
     */
    ImprovementPlan selectByResultId(@Param("resultId") String resultId);

    /**
     * 根据用户ID查询提升计划列表
     *
     * @param userId 用户ID
     * @return 提升计划列表
     */
    List<ImprovementPlan> selectByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID和状态查询提升计划列表
     *
     * @param userId 用户ID
     * @param status 状态
     * @return 提升计划列表
     */
    List<ImprovementPlan> selectByUserIdAndStatus(@Param("userId") Long userId, @Param("status") String status);

    /**
     * 根据结果ID删除提升计划
     *
     * @param resultId 结果ID
     * @return 删除数量
     */
    int deleteByResultId(@Param("resultId") String resultId);

    /**
     * 更新计划进度
     *
     * @param id       计划ID
     * @param progress 进度
     * @return 更新数量
     */
    int updateProgress(@Param("id") Long id, @Param("progress") Integer progress);

    /**
     * 查询用户活跃的提升计划
     *
     * @param userId 用户ID
     * @return 提升计划列表
     */
    List<ImprovementPlan> selectActiveByUserId(@Param("userId") Long userId);

    /**
     * 查询即将到期的提升计划
     *
     * @param userId 用户ID
     * @param days   天数
     * @return 提升计划列表
     */
    List<ImprovementPlan> selectExpiringPlans(@Param("userId") Long userId, @Param("days") Integer days);

}
