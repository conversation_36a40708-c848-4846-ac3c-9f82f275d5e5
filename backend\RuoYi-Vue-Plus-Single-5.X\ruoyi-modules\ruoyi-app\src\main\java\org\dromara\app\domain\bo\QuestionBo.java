package org.dromara.app.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.app.domain.Question;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

/**
 * 题目业务对象 question
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = Question.class, reverseConvertGenerate = false)
public class QuestionBo extends BaseEntity {

    /**
     * 题目ID
     */
    @NotNull(message = "题目ID不能为空", groups = { EditGroup.class })
    private Long questionId;

    /**
     * 题库ID
     */
    @NotNull(message = "题库ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long bankId;

    /**
     * 题目编码
     */
    @NotBlank(message = "题目编码不能为空", groups = { AddGroup.class, EditGroup.class })
    @Size(max = 50, message = "题目编码长度不能超过50个字符")
    private String questionCode;

    /**
     * 题目标题
     */
    @NotBlank(message = "题目标题不能为空", groups = { AddGroup.class, EditGroup.class })
    @Size(max = 200, message = "题目标题长度不能超过200个字符")
    private String title;

    /**
     * 题目描述
     */
    @Size(max = 1000, message = "题目描述长度不能超过1000个字符")
    private String description;

    /**
     * 题目内容
     */
    @NotBlank(message = "题目内容不能为空", groups = { AddGroup.class, EditGroup.class })
    private String content;

    /**
     * 参考答案
     */
    private String answer;

    /**
     * 答案解析
     */
    private String analysis;

    /**
     * 难度（1-简单 2-中等 3-困难）
     */
    @NotNull(message = "难度不能为空", groups = { AddGroup.class, EditGroup.class })
    @Min(value = 1, message = "难度等级最小为1")
    @Max(value = 3, message = "难度等级最大为3")
    private Integer difficulty;

    /**
     * 分类
     */
    @NotBlank(message = "分类不能为空", groups = { AddGroup.class, EditGroup.class })
    @Size(max = 50, message = "分类长度不能超过50个字符")
    private String category;

    /**
     * 题目类型（1-单选题 2-多选题 3-判断题 4-简答题 5-编程题）
     */
    @NotNull(message = "题目类型不能为空", groups = { AddGroup.class, EditGroup.class })
    @Min(value = 1, message = "题目类型最小为1")
    @Max(value = 5, message = "题目类型最大为5")
    private Integer type;

    /**
     * 练习次数
     */
    @Min(value = 0, message = "练习次数不能为负数")
    private Integer practiceCount;

    /**
     * 正确率
     */
    @Min(value = 0, message = "正确率不能为负数")
    @Max(value = 100, message = "正确率不能超过100")
    private Integer correctRate;

    /**
     * 通过率（百分比）
     */
    @Min(value = 0, message = "通过率不能为负数")
    @Max(value = 100, message = "通过率不能超过100")
    private Double acceptanceRate;

    /**
     * 评论数
     */
    @Min(value = 0, message = "评论数不能为负数")
    private Integer commentCount;

    /**
     * 标签（JSON格式）
     */
    private String tags;

    /**
     * 排序
     */
    @Min(value = 0, message = "排序值不能为负数")
    private Integer sort;

    /**
     * 状态（0正常 1停用）
     */
    @Pattern(regexp = "^[01]$", message = "状态只能是0或1")
    private String status;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;

    /**
     * 查询关键词（用于搜索）
     */
    private String keyword;

    /**
     * 题库名称（用于查询）
     */
    private String bankName;

    /**
     * 状态数组（用于批量查询）
     */
    private String[] statusArray;

    /**
     * 难度数组（用于批量查询）
     */
    private Integer[] difficultyArray;

    /**
     * 类型数组（用于批量查询）
     */
    private Integer[] typeArray;

    /**
     * 分类数组（用于批量查询）
     */
    private String[] categoryArray;

    /**
     * 创建时间范围查询-开始时间
     */
    private String createTimeStart;

    /**
     * 创建时间范围查询-结束时间
     */
    private String createTimeEnd;

    /**
     * 排序字段
     */
    private String orderByColumn;

    /**
     * 排序方向
     */
    private String isAsc;
}
