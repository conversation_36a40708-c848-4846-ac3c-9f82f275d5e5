<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.app.mapper.SessionQuestionMapper">

    <resultMap type="org.dromara.app.domain.SessionQuestion" id="SessionQuestionResult">
        <id property="id" column="id"/>
        <result property="sessionId" column="session_id"/>
        <result property="questionId" column="question_id"/>
        <result property="content" column="content"/>
        <result property="type" column="type"/>
        <result property="difficulty" column="difficulty"/>
        <result property="category" column="category"/>
        <result property="subcategory" column="subcategory"/>
        <result property="timeLimit" column="time_limit"/>
        <result property="questionOrder" column="question_order"/>
        <result property="tags" column="tags" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="status" column="status"/>
        <result property="answerPoints" column="answer_points" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="hints" column="hints" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="scoringCriteria" column="scoring_criteria"/>
        <result property="isCustom" column="is_custom"/>
        <result property="source" column="source"/>
        <result property="metadata" column="metadata"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

    <sql id="selectSessionQuestionVo">
        SELECT id, session_id, question_id, content, type, difficulty, category, subcategory,
               time_limit, question_order, tags, status, answer_points, hints, scoring_criteria,
               is_custom, source, metadata, create_time, update_time, create_by, update_by
        FROM app_session_question
    </sql>

    <select id="selectBySessionIdOrderByOrder" parameterType="String" resultMap="SessionQuestionResult">
        <include refid="selectSessionQuestionVo"/>
        WHERE session_id = #{sessionId}
        ORDER BY question_order ASC
    </select>

    <select id="selectBySessionIdAndQuestionId" resultMap="SessionQuestionResult">
        <include refid="selectSessionQuestionVo"/>
        WHERE session_id = #{sessionId} AND question_id = #{questionId}
        LIMIT 1
    </select>

    <select id="selectBySessionIdAndOrder" resultMap="SessionQuestionResult">
        <include refid="selectSessionQuestionVo"/>
        WHERE session_id = #{sessionId} AND question_order = #{questionOrder}
        LIMIT 1
    </select>

    <select id="selectNextQuestion" resultMap="SessionQuestionResult">
        <include refid="selectSessionQuestionVo"/>
        WHERE session_id = #{sessionId} AND question_order > #{currentOrder}
        ORDER BY question_order ASC
        LIMIT 1
    </select>

    <select id="selectCurrentQuestion" parameterType="String" resultMap="SessionQuestionResult">
        <include refid="selectSessionQuestionVo"/>
        WHERE session_id = #{sessionId} AND status = 'current'
        LIMIT 1
    </select>

    <select id="countBySessionId" parameterType="String" resultType="Integer">
        SELECT COUNT(*)
        FROM app_session_question
        WHERE session_id = #{sessionId}
    </select>

    <select id="countBySessionIdAndStatus" resultType="Integer">
        SELECT COUNT(*)
        FROM app_session_question
        WHERE session_id = #{sessionId} AND status = #{status}
    </select>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO app_session_question (
            id, session_id, question_id, content, type, difficulty, category, subcategory,
            time_limit, question_order, tags, status, answer_points, hints, scoring_criteria,
            is_custom, source, metadata, create_time, create_by
        ) VALUES
        <foreach collection="questions" item="item" separator=",">
            (
                #{item.id}, #{item.sessionId}, #{item.questionId}, #{item.content}, #{item.type},
                #{item.difficulty}, #{item.category}, #{item.subcategory}, #{item.timeLimit},
                #{item.questionOrder}, #{item.tags,typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler},
                #{item.status}, #{item.answerPoints,typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler},
                #{item.hints,typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler},
                #{item.scoringCriteria}, #{item.isCustom}, #{item.source}, #{item.metadata},
                #{item.createTime}, #{item.createBy}
            )
        </foreach>
    </insert>

    <update id="updateStatusBySessionIdAndQuestionId">
        UPDATE app_session_question
        SET status = #{status}, update_time = NOW()
        WHERE session_id = #{sessionId} AND question_id = #{questionId}
    </update>

</mapper>
