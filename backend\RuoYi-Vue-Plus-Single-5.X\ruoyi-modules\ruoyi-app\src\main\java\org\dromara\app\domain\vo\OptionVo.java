package org.dromara.app.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 问题选项视图对象
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "问题选项视图对象")
public class OptionVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 选项ID
     */
    @Schema(description = "选项ID")
    private String id;

    /**
     * 选项文本
     */
    @Schema(description = "选项文本")
    private String text;

    /**
     * 选项分数
     */
    @Schema(description = "选项分数")
    private Integer score;
}
