package org.dromara.app.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.dromara.common.translation.annotation.Translation;
import org.dromara.common.translation.constant.TransConstant;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 成就视图对象 app_achievement
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "成就视图对象")
@ExcelIgnoreUnannotated
public class AchievementVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 成就代码(唯一标识)
     */
    @Schema(description = "成就代码")
    @ExcelProperty(value = "成就代码")
    private String achievementCode;

    /**
     * 成就名称
     */
    @Schema(description = "成就名称")
    @ExcelProperty(value = "成就名称")
    private String achievementName;

    /**
     * 成就描述
     */
    @Schema(description = "成就描述")
    @ExcelProperty(value = "成就描述")
    private String achievementDesc;

    /**
     * 成就图标URL
     */
    @Schema(description = "成就图标URL")
    @ExcelProperty(value = "成就图标URL")
    private String achievementIcon;

    /**
     * 成就类型
     */
    @Schema(description = "成就类型")
    @ExcelProperty(value = "成就类型")
    private String achievementType;

    /**
     * 触发条件(JSON格式)
     */
    @Schema(description = "触发条件")
    @ExcelProperty(value = "触发条件")
    private String triggerCondition;

    /**
     * 奖励积分
     */
    @Schema(description = "奖励积分")
    @ExcelProperty(value = "奖励积分")
    private Integer rewardPoints;

    /**
     * 是否激活(0否 1是)
     */
    @Schema(description = "是否激活")
    @ExcelProperty(value = "是否激活")
    private String isActive;

    /**
     * 排序
     */
    @Schema(description = "排序")
    @ExcelProperty(value = "排序")
    private Integer sortOrder;

    /**
     * 创建者
     */
    @Schema(description = "创建者")
    @Translation(type = TransConstant.USER_ID_TO_NAME, mapper = "createBy")
    private String createByName;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @ExcelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @ExcelProperty(value = "备注")
    private String remark;

}
