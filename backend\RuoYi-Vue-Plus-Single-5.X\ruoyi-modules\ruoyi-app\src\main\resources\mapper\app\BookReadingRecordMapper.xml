<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.app.mapper.BookReadingRecordMapper">

    <resultMap type="org.dromara.app.domain.BookReadingRecord" id="BookReadingRecordResult">
        <id property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="bookId" column="book_id"/>
        <result property="currentChapterId" column="current_chapter_id"/>
        <result property="currentChapterIndex" column="current_chapter_index"/>
        <result property="readingProgress" column="reading_progress"/>
        <result property="totalReadingTime" column="total_reading_time"/>
        <result property="lastReadingTime" column="last_reading_time"/>
        <result property="readingSettings" column="reading_settings"/>
        <result property="completedChapters" column="completed_chapters"/>
        <result property="isFinished" column="is_finished"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <resultMap type="org.dromara.app.domain.BookReadingRecord" id="BookReadingRecordWithBookResult"
               extends="BookReadingRecordResult">
        <association property="book" javaType="org.dromara.app.domain.Book">
            <id property="id" column="book_id"/>
            <result property="title" column="book_title"/>
            <result property="author" column="book_author"/>
            <result property="cover" column="book_cover"/>
            <result property="category" column="book_category"/>
            <result property="rating" column="book_rating"/>
            <result property="chapters" column="book_chapters"/>
            <result property="difficulty" column="book_difficulty"/>
            <result property="isFree" column="book_is_free"/>
        </association>
    </resultMap>

    <!-- 根据用户ID和书籍ID查询阅读记录 -->
    <select id="selectByUserIdAndBookId" resultMap="BookReadingRecordResult">
        SELECT *
        FROM app_book_reading_record
        WHERE user_id = #{userId}
          AND book_id = #{bookId}
    </select>

    <!-- 查询用户的阅读历史（分页） -->
    <select id="selectUserReadingHistory" resultMap="BookReadingRecordWithBookResult">
        SELECT r.*,
               b.title      as book_title,
               b.author     as book_author,
               b.cover      as book_cover,
               b.category   as book_category,
               b.rating     as book_rating,
               b.chapters   as book_chapters,
               b.difficulty as book_difficulty,
               b.is_free    as book_is_free
        FROM app_book_reading_record r
                 INNER JOIN app_book b ON r.book_id = b.id
        WHERE r.user_id = #{userId}
          AND b.del_flag = '0'
        ORDER BY r.last_reading_time DESC, r.update_time DESC
    </select>

    <!-- 查询用户最近阅读的书籍 -->
    <select id="selectRecentReading" resultMap="BookReadingRecordWithBookResult">
        SELECT r.*,
               b.title      as book_title,
               b.author     as book_author,
               b.cover      as book_cover,
               b.category   as book_category,
               b.rating     as book_rating,
               b.chapters   as book_chapters,
               b.difficulty as book_difficulty,
               b.is_free    as book_is_free
        FROM app_book_reading_record r
                 INNER JOIN app_book b ON r.book_id = b.id
        WHERE r.user_id = #{userId}
          AND b.del_flag = '0'
          AND b.status = 1
          AND r.last_reading_time IS NOT NULL
        ORDER BY r.last_reading_time DESC
        LIMIT #{limit}
    </select>

    <!-- 统计用户阅读数据 -->
    <select id="selectUserReadingStats" resultMap="BookReadingRecordResult">
        SELECT COUNT(*)                                         as total_reading_time,    -- 复用字段存储总书籍数
               SUM(CASE WHEN is_finished = 1 THEN 1 ELSE 0 END) as current_chapter_index, -- 复用字段存储已读完数
               SUM(COALESCE(total_reading_time, 0))             as reading_progress       -- 复用字段存储总阅读时长
        FROM app_book_reading_record r
                 INNER JOIN app_book b ON r.book_id = b.id
        WHERE r.user_id = #{userId}
          AND b.del_flag = '0'
    </select>

    <!-- 更新或插入阅读记录 -->
    <insert id="insertOrUpdate" parameterType="org.dromara.app.domain.BookReadingRecord">
        INSERT INTO app_book_reading_record (user_id, book_id, current_chapter_id, current_chapter_index,
                                             reading_progress, total_reading_time, last_reading_time,
                                             reading_settings, completed_chapters, is_finished,
                                             create_time, update_time)
        VALUES (#{userId}, #{bookId}, #{currentChapterId}, #{currentChapterIndex},
                #{readingProgress}, #{totalReadingTime}, #{lastReadingTime},
                #{readingSettings}, #{completedChapters}, #{isFinished},
                NOW(), NOW())
        ON DUPLICATE KEY UPDATE current_chapter_id    = VALUES(current_chapter_id),
                                current_chapter_index = VALUES(current_chapter_index),
                                reading_progress      = VALUES(reading_progress),
                                total_reading_time    = VALUES(total_reading_time),
                                last_reading_time     = VALUES(last_reading_time),
                                reading_settings      = VALUES(reading_settings),
                                completed_chapters    = VALUES(completed_chapters),
                                is_finished           = VALUES(is_finished),
                                update_time           = NOW()
    </insert>

</mapper>
