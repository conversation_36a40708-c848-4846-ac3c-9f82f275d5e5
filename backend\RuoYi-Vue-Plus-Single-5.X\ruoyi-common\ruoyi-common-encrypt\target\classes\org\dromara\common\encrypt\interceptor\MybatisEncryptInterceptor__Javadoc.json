{"doc": "\n 入参加密拦截器\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "encrypt<PERSON><PERSON>ler", "paramTypes": ["java.lang.Object"], "doc": "\n 加密对象\r\n\r\n @param sourceObject 待加密对象\r\n"}, {"name": "encryptField", "paramTypes": ["java.lang.String", "java.lang.reflect.Field"], "doc": "\n 字段值进行加密。通过字段的批注注册新的加密算法\r\n\r\n @param value 待加密的值\r\n @param field 待加密字段\r\n @return 加密后结果\r\n"}], "constructors": []}