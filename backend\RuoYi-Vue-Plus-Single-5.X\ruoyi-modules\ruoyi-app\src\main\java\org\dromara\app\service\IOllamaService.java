package org.dromara.app.service;

import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.List;
import java.util.Map;

/**
 * Ollama服务接口
 *
 * <AUTHOR>
 */
public interface IOllamaService {

    /**
     * 发送聊天消息（同步）
     *
     * @param model       模型名称
     * @param messages    消息列表
     * @param temperature 温度参数
     * @param maxTokens   最大token数
     * @return 响应结果
     */
    OllamaResponse chat(String model, List<ChatMessage> messages, Double temperature, Integer maxTokens);

    /**
     * 发送聊天消息（流式）
     *
     * @param model       模型名称
     * @param messages    消息列表
     * @param temperature 温度参数
     * @param maxTokens   最大token数
     * @param sseEmitter  SSE发射器
     */
    void chatStream(String model, List<ChatMessage> messages, Double temperature, Integer maxTokens, SseEmitter sseEmitter);

    /**
     * 获取可用模型列表
     *
     * @return 模型列表
     */
    List<String> getAvailableModels();

    /**
     * 检查模型是否可用
     *
     * @param modelName 模型名称
     * @return 是否可用
     */
    boolean isModelAvailable(String modelName);

    /**
     * 获取模型信息
     *
     * @param modelName 模型名称
     * @return 模型信息
     */
    ModelInfo getModelInfo(String modelName);

    /**
     * 检查Ollama服务状态
     *
     * @return 服务状态
     */
    ServiceStatus checkServiceStatus();

    /**
     * 聊天消息内部类
     */
    class ChatMessage {
        private String role; // system/user/assistant
        private String content;

        public ChatMessage(String role, String content) {
            this.role = role;
            this.content = content;
        }

        // getters and setters
        public String getRole() {
            return role;
        }

        public void setRole(String role) {
            this.role = role;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }
    }

    /**
     * Ollama响应内部类
     */
    class OllamaResponse {
        private boolean success;
        private String message;
        private String error;
        private Map<String, Object> metadata;

        public OllamaResponse(boolean success, String message) {
            this.success = success;
            this.message = message;
        }

        public OllamaResponse(boolean success, String message, String error) {
            this.success = success;
            this.message = message;
            this.error = error;
        }

        // getters and setters
        public boolean isSuccess() {
            return success;
        }

        public void setSuccess(boolean success) {
            this.success = success;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public String getError() {
            return error;
        }

        public void setError(String error) {
            this.error = error;
        }

        public Map<String, Object> getMetadata() {
            return metadata;
        }

        public void setMetadata(Map<String, Object> metadata) {
            this.metadata = metadata;
        }
    }

    /**
     * 模型信息内部类
     */
    class ModelInfo {
        private String name;
        private String description;
        private Long size;
        private Map<String, Object> parameters;
        private String family;
        private String format;

        public ModelInfo() {
        }

        // getters and setters
        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public Long getSize() {
            return size;
        }

        public void setSize(Long size) {
            this.size = size;
        }

        public Map<String, Object> getParameters() {
            return parameters;
        }

        public void setParameters(Map<String, Object> parameters) {
            this.parameters = parameters;
        }

        public String getFamily() {
            return family;
        }

        public void setFamily(String family) {
            this.family = family;
        }

        public String getFormat() {
            return format;
        }

        public void setFormat(String format) {
            this.format = format;
        }
    }

    /**
     * 服务状态内部类
     */
    class ServiceStatus {
        private boolean available;
        private String version;
        private List<String> models;
        private String baseUrl;
        private Long responseTime;
        private String errorMessage;

        public ServiceStatus() {
        }

        // getters and setters
        public boolean isAvailable() {
            return available;
        }

        public void setAvailable(boolean available) {
            this.available = available;
        }

        public String getVersion() {
            return version;
        }

        public void setVersion(String version) {
            this.version = version;
        }

        public List<String> getModels() {
            return models;
        }

        public void setModels(List<String> models) {
            this.models = models;
        }

        public String getBaseUrl() {
            return baseUrl;
        }

        public void setBaseUrl(String baseUrl) {
            this.baseUrl = baseUrl;
        }

        public Long getResponseTime() {
            return responseTime;
        }

        public void setResponseTime(Long responseTime) {
            this.responseTime = responseTime;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }
    }
}
