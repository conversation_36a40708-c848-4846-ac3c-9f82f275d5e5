package org.dromara.app.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.app.domain.QuestionBank;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

/**
 * 题库业务对象 question_bank
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = QuestionBank.class, reverseConvertGenerate = false)
public class QuestionBankBo extends BaseEntity {

    /**
     * 题库ID
     */
    @NotNull(message = "题库ID不能为空", groups = { EditGroup.class })
    private Long bankId;

    /**
     * 题库编码
     */
    @NotBlank(message = "题库编码不能为空", groups = { AddGroup.class, EditGroup.class })
    @Size(max = 50, message = "题库编码长度不能超过50个字符")
    private String bankCode;

    /**
     * 题库标题
     */
    @NotBlank(message = "题库标题不能为空", groups = { AddGroup.class, EditGroup.class })
    @Size(max = 100, message = "题库标题长度不能超过100个字符")
    private String title;

    /**
     * 题库描述
     */
    @Size(max = 500, message = "题库描述长度不能超过500个字符")
    private String description;

    /**
     * 专业ID
     */
    private Long majorId;

    /**
     * 题库图标
     */
    @Size(max = 200, message = "题库图标URL长度不能超过200个字符")
    private String icon;

    /**
     * 题库颜色
     */
    @Size(max = 20, message = "题库颜色长度不能超过20个字符")
    private String color;

    /**
     * 难度（1-简单 2-中等 3-困难）
     */
    @Min(value = 1, message = "难度等级最小为1")
    @Max(value = 3, message = "难度等级最大为3")
    private Integer difficulty;

    /**
     * 题目总数
     */
    @Min(value = 0, message = "题目总数不能为负数")
    private Integer totalQuestions;

    /**
     * 练习次数
     */
    @Min(value = 0, message = "练习次数不能为负数")
    private Integer practiceCount;

    /**
     * 分类标签（JSON格式）
     */
    private String categories;

    /**
     * 排序
     */
    @Min(value = 0, message = "排序值不能为负数")
    private Integer sort;

    /**
     * 状态（0正常 1停用）
     */
    @Pattern(regexp = "^[01]$", message = "状态只能是0或1")
    private String status;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;

    /**
     * 查询关键词（用于搜索）
     */
    private String keyword;

    /**
     * 专业名称（用于查询）
     */
    private String majorName;

    /**
     * 状态数组（用于批量查询）
     */
    private String[] statusArray;

    /**
     * 难度数组（用于批量查询）
     */
    private Integer[] difficultyArray;

    /**
     * 创建时间范围查询-开始时间
     */
    private String createTimeStart;

    /**
     * 创建时间范围查询-结束时间
     */
    private String createTimeEnd;

    /**
     * 排序字段
     */
    private String orderByColumn;

    /**
     * 排序方向
     */
    private String isAsc;
}
