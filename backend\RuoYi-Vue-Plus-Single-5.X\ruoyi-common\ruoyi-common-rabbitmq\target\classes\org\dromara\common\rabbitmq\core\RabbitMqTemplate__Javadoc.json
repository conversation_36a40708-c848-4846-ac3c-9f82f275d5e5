{"doc": "\n RabbitMQ 模板封装\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "send", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": "\n 发送消息到默认交换机\r\n\r\n @param routingKey 路由键\r\n @param message    消息内容\r\n"}, {"name": "send", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Object"], "doc": "\n 发送消息到指定交换机\r\n\r\n @param exchange   交换机名称\r\n @param routingKey 路由键\r\n @param message    消息内容\r\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Object", "long"], "doc": "\n 发送延迟消息\r\n\r\n @param exchange   交换机名称\r\n @param routingKey 路由键\r\n @param message    消息内容\r\n @param delayTime  延迟时间（毫秒）\r\n"}, {"name": "sendAndReceive", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Object"], "doc": "\n 发送消息并接收响应（RPC模式）\r\n\r\n @param exchange   交换机名称\r\n @param routingKey 路由键\r\n @param message    消息内容\r\n @return 响应结果\r\n"}, {"name": "sendToDead", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Object"], "doc": "\n 发送消息到死信队列\r\n\r\n @param deadExchange   死信交换机\r\n @param deadRoutingKey 死信路由键\r\n @param message        消息内容\r\n"}, {"name": "receive", "paramTypes": ["java.lang.String"], "doc": "\n 接收并转换消息\r\n\r\n @param queueName 队列名称\r\n @return 消息内容\r\n"}, {"name": "receive", "paramTypes": ["java.lang.String", "long"], "doc": "\n 接收并转换消息（带超时）\r\n\r\n @param queueName 队列名称\r\n @param timeout   超时时间（毫秒）\r\n @return 消息内容\r\n"}, {"name": "createTextMessage", "paramTypes": ["java.lang.String"], "doc": "\n 创建文本消息\r\n\r\n @param content 消息内容\r\n @return Message对象\r\n"}], "constructors": []}