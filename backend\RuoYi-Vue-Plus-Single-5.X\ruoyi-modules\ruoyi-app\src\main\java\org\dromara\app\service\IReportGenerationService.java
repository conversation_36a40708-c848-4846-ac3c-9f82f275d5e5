package org.dromara.app.service;

import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * 报告生成服务接口
 *
 * <AUTHOR>
 */
public interface IReportGenerationService {

    /**
     * 生成面试报告
     *
     * @param sessionId 面试会话ID
     * @return 报告数据
     */
    InterviewReportData generateInterviewReport(String sessionId);

    /**
     * 生成PDF报告
     *
     * @param sessionId 面试会话ID
     * @return PDF文件路径
     */
    String generatePdfReport(String sessionId);

    /**
     * 生成雷达图数据
     *
     * @param dimensionScores 维度评分列表
     * @return 雷达图数据
     */
    RadarChartData generateRadarChartData(List<IMultimodalAnalysisService.DimensionScore> dimensionScores);

    /**
     * 生成改进建议
     *
     * @param weaknesses 弱点列表
     * @param dimensionScores 维度评分列表
     * @return 改进建议列表
     */
    List<ImprovementSuggestion> generateImprovementSuggestions(List<String> weaknesses, List<IMultimodalAnalysisService.DimensionScore> dimensionScores);

    /**
     * 生成学习路径推荐
     *
     * @param sessionId 会话ID
     * @param reportData 报告数据
     * @return 学习路径推荐
     */
    List<LearningPathRecommendation> generateLearningPathRecommendations(String sessionId,
                                                                         InterviewReportData reportData);

    /**
     * 面试报告数据
     */
    @Setter
    @Getter
    class InterviewReportData {
        // Getters and Setters
        private String sessionId;
        private BasicInfo basicInfo;
        private int overallScore;
        private String rank;
        private int percentile;
        private RadarChartData radarChartData;
        private List<DimensionScore> dimensionScores;
        private List<String> strengths;
        private List<String> weaknesses;
        private List<ImprovementSuggestion> improvementSuggestions;
        private List<LearningPathRecommendation> learningPaths;
        private String overallFeedback;
        private Map<String, Object> comparisonData;
        private long generateTime;

    }

    /**
     * 基本信息
     */
    @Setter
    @Getter
    class BasicInfo {
        // Getters and Setters
        private String candidateName;
        private String jobPosition;
        private String company;
        private String interviewDate;
        private String duration;
        private int totalQuestions;
        private int answeredQuestions;

    }

    /**
     * 雷达图数据
     */
    @Setter
    @Getter
    class RadarChartData {
        // Getters and Setters
        private List<String> dimensions;
        private List<Integer> scores;
        private List<Integer> maxScores;
        private List<Integer> industryAverages;
        private Map<String, Object> chartConfig;

    }

    /**
     * 维度评分
     */
    @Setter
    @Getter
    class DimensionScore {
        // Getters and Setters
        private String dimension;
        private int score;
        private int maxScore;
        private int percentile;
        private String description;
        private List<String> strengths;
        private List<String> weaknesses;
        private List<String> recommendations;

    }

    /**
     * 改进建议
     */
    @Setter
    @Getter
    class ImprovementSuggestion {
        // Getters and Setters
        private String title;
        private String description;
        private List<String> actionItems;
        private String priority;
        private int estimatedTime;
        private String difficulty;

    }

    /**
     * 学习路径推荐
     */
    @Getter
    class LearningPathRecommendation {
        // Getters and Setters
        private String title;
        private String description;
        private int estimatedHours;
        private String difficulty;
        private int priority;
        private List<LearningResource> resources;
        private List<String> milestones;

        public void setTitle(String title) { this.title = title; }

        public void setDescription(String description) { this.description = description; }

        public void setEstimatedHours(int estimatedHours) { this.estimatedHours = estimatedHours; }

        public void setDifficulty(String difficulty) { this.difficulty = difficulty; }

        public void setPriority(int priority) { this.priority = priority; }

        public void setResources(List<LearningResource> resources) { this.resources = resources; }

        public void setMilestones(List<String> milestones) { this.milestones = milestones; }
    }

    /**
     * 学习资源
     */
    @Setter
    @Getter
    class LearningResource {
        // Getters and Setters
        private String title;
        private String type;
        private String description;
        private String url;
        private String duration;
        private String difficulty;

    }
}
