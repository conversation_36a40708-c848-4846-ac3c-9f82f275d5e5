{"doc": "\n 流程任务监听\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "flowCode", "doc": "\n 流程定义编码\r\n"}, {"name": "nodeType", "doc": "\n 节点类型（0开始节点 1中间节点 2结束节点 3互斥网关 4并行网关）\r\n"}, {"name": "nodeCode", "doc": "\n 流程节点编码\r\n"}, {"name": "nodeName", "doc": "\n 流程节点名称\r\n"}, {"name": "taskId", "doc": "\n 任务id\r\n"}, {"name": "businessId", "doc": "\n 业务id\r\n"}, {"name": "status", "doc": "\n 流程状态\r\n"}], "enumConstants": [], "methods": [], "constructors": []}