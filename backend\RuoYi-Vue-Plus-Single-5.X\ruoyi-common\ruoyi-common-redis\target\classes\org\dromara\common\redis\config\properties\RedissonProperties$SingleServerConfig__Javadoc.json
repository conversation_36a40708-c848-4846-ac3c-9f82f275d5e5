{"doc": "", "fields": [{"name": "clientName", "doc": "\n 客户端名称\r\n"}, {"name": "connectionMinimumIdleSize", "doc": "\n 最小空闲连接数\r\n"}, {"name": "connectionPoolSize", "doc": "\n 连接池大小\r\n"}, {"name": "idleConnectionTimeout", "doc": "\n 连接空闲超时，单位：毫秒\r\n"}, {"name": "timeout", "doc": "\n 命令等待超时，单位：毫秒\r\n"}, {"name": "subscriptionConnectionPoolSize", "doc": "\n 发布和订阅连接池大小\r\n"}], "enumConstants": [], "methods": [], "constructors": []}