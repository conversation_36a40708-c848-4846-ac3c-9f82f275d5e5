{"doc": "\n Excel 导入监听\r\n\r\n <AUTHOR>\r\n <AUTHOR>\r\n", "fields": [{"name": "isValidate", "doc": "\n 是否Validator检验，默认为是\r\n"}, {"name": "headMap", "doc": "\n excel 表头数据\r\n"}, {"name": "excelResult", "doc": "\n 导入回执\r\n"}], "enumConstants": [], "methods": [{"name": "onException", "paramTypes": ["java.lang.Exception", "cn.idev.excel.context.AnalysisContext"], "doc": "\n 处理异常\r\n\r\n @param exception ExcelDataConvertException\r\n @param context   Excel 上下文\r\n"}], "constructors": []}