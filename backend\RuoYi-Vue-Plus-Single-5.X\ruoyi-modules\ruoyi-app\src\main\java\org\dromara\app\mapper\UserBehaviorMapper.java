package org.dromara.app.mapper;

import org.apache.ibatis.annotations.Param;
import org.dromara.app.domain.UserBehavior;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.Date;
import java.util.List;

/**
 * 用户行为记录Mapper接口
 *
 * <AUTHOR>
 */
public interface UserBehaviorMapper extends BaseMapperPlus<UserBehavior, UserBehavior> {

    /**
     * 根据用户ID和行为类型查询行为记录
     *
     * @param userId       用户ID
     * @param behaviorType 行为类型
     * @return 行为记录列表
     */
    List<UserBehavior> selectByUserIdAndBehaviorType(@Param("userId") Long userId, 
                                                     @Param("behaviorType") String behaviorType);

    /**
     * 根据用户ID和时间范围查询行为记录
     *
     * @param userId    用户ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 行为记录列表
     */
    List<UserBehavior> selectByUserIdAndTimeRange(@Param("userId") Long userId,
                                                  @Param("startTime") Date startTime,
                                                  @Param("endTime") Date endTime);

    /**
     * 统计用户某种行为的次数
     *
     * @param userId       用户ID
     * @param behaviorType 行为类型
     * @return 行为次数
     */
    Long countByUserIdAndBehaviorType(@Param("userId") Long userId, 
                                      @Param("behaviorType") String behaviorType);

    /**
     * 统计用户某种行为在指定时间范围内的次数
     *
     * @param userId       用户ID
     * @param behaviorType 行为类型
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @return 行为次数
     */
    Long countByUserIdAndBehaviorTypeAndTimeRange(@Param("userId") Long userId,
                                                  @Param("behaviorType") String behaviorType,
                                                  @Param("startTime") Date startTime,
                                                  @Param("endTime") Date endTime);

    /**
     * 查询用户连续登录天数
     *
     * @param userId 用户ID
     * @return 连续登录天数
     */
    Integer selectConsecutiveLoginDays(@Param("userId") Long userId);

    /**
     * 查询用户累计学习时长（分钟）
     *
     * @param userId 用户ID
     * @return 累计学习时长
     */
    Long selectTotalStudyMinutes(@Param("userId") Long userId);

    /**
     * 批量插入用户行为记录
     *
     * @param behaviors 行为记录列表
     * @return 插入数量
     */
    int batchInsert(@Param("behaviors") List<UserBehavior> behaviors);

}
