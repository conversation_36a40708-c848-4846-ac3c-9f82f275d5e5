package org.dromara.app.service;

import org.dromara.app.domain.vo.RecommendationResponseVo;

import java.util.Map;

/**
 * 学习推荐服务接口
 * 基于用户能力评估和学习历史，提供个性化的学习资源推荐
 *
 * <AUTHOR>
 * @date 2025-07-27
 */
public interface ILearningRecommendService {

    /**
     * 获取推荐视频列表
     * 基于用户能力短板和学习偏好推荐视频课程
     *
     * @param userId      用户ID
     * @param pageNum     页码
     * @param pageSize    每页大小
     * @param searchQuery 搜索关键词
     * @return 推荐视频列表
     */
    RecommendationResponseVo getRecommendedVideos(Long userId, Integer pageNum, Integer pageSize, String searchQuery);

    /**
     * 获取推荐题库列表
     * 基于用户能力短板和练习历史推荐题库
     *
     * @param userId      用户ID
     * @param pageNum     页码
     * @param pageSize    每页大小
     * @param searchQuery 搜索关键词
     * @return 推荐题库列表
     */
    RecommendationResponseVo getRecommendedQuestionBanks(Long userId, Integer pageNum, Integer pageSize, String searchQuery);

    /**
     * 获取推荐书籍列表
     * 基于用户能力短板和阅读偏好推荐书籍
     *
     * @param userId      用户ID
     * @param pageNum     页码
     * @param pageSize    每页大小
     * @param searchQuery 搜索关键词
     * @return 推荐书籍列表
     */
    RecommendationResponseVo getRecommendedBooks(Long userId, Integer pageNum, Integer pageSize, String searchQuery);

    /**
     * 获取用户能力评估数据
     * 用于前端显示用户当前能力状况和薄弱环节
     *
     * @param userId 用户ID
     * @return 用户能力评估数据
     */
    Map<String, Object> getUserCapabilities(Long userId);

    /**
     * 获取推荐统计信息
     * 包括推荐总数、各类型推荐数量等统计信息
     *
     * @param userId 用户ID
     * @return 推荐统计信息
     */
    Map<String, Object> getRecommendationStatistics(Long userId);

    /**
     * 刷新用户推荐
     * 基于用户最新的学习行为和能力评估重新计算推荐
     *
     * @param userId 用户ID
     */
    void refreshUserRecommendations(Long userId);

    /**
     * 记录用户对推荐内容的反馈
     * 用于优化推荐算法
     *
     * @param userId       用户ID
     * @param resourceType 资源类型
     * @param resourceId   资源ID
     * @param action       用户行为
     */
    void recordRecommendationFeedback(Long userId, String resourceType, Long resourceId, String action);

    /**
     * 计算用户能力短板
     * 基于面试历史和学习记录分析用户薄弱环节
     *
     * @param userId 用户ID
     * @return 能力短板列表
     */
    Map<String, Double> calculateUserWeaknesses(Long userId);

    /**
     * 计算资源推荐优先级
     * 基于用户能力短板、资源质量、学习历史等因素计算推荐优先级
     *
     * @param userId       用户ID
     * @param resourceType 资源类型
     * @param resourceId   资源ID
     * @return 推荐优先级分数
     */
    Double calculateRecommendationPriority(Long userId, String resourceType, Long resourceId);

    /**
     * 获取个性化推荐算法配置
     * 根据用户学习偏好和历史行为调整推荐算法参数
     *
     * @param userId 用户ID
     * @return 算法配置参数
     */
    Map<String, Object> getPersonalizedAlgorithmConfig(Long userId);
}
