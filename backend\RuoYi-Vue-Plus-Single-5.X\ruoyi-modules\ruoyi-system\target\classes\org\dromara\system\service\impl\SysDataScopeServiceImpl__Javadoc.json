{"doc": "\n 数据权限 实现\r\n <p>\r\n 注意: 此Service内不允许调用标注`数据权限`注解的方法\r\n 例如: deptMapper.selectList 此 selectList 方法标注了`数据权限`注解 会出现循环解析的问题\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "getRoleCustom", "paramTypes": ["java.lang.Long"], "doc": "\n 获取角色自定义权限\r\n\r\n @param roleId 角色Id\r\n @return 部门Id组\r\n"}, {"name": "getDeptAndChild", "paramTypes": ["java.lang.Long"], "doc": "\n 获取部门及以下权限\r\n\r\n @param deptId 部门Id\r\n @return 部门Id组\r\n"}], "constructors": []}