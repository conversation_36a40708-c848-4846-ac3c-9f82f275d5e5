# 项目记忆中枢 (Memory Bank) v1.0

## 项目概述
**项目名称**: SmartInterview - 智能面试系统
**创建时间**: 2025-01-18
**项目类型**: 全栈Web应用 (前后端分离架构)

## 技术架构

### 后端技术栈
- **框架**: RuoYi-Vue-Plus-Single-5.X (基于Spring Boot的单体架构)
- **位置**: `./backend/RuoYi-Vue-Plus-Single-5.X/`
- **特点**: 企业级快速开发框架，包含权限管理、代码生成等功能
- **模块结构**:
  - `ruoyi-admin`: 主应用模块
  - `ruoyi-common`: 通用组件库 (包含chat、sse、websocket等AI相关组件)
  - `ruoyi-modules`: 业务模块 (包含chat、system、workflow等)
  - `ruoyi-extend`: 扩展模块 (包含mcp-server等)

### 前端技术栈
- **主前端**: Plus-UI (位于 `./front/plus-ui/`) - 目前为空目录
- **移动端**: Unibest (位于 `./front/unibest-main/`) - 基于UniApp的跨平台应用
- **技术**: Vue.js + UniApp + TypeScript
- **特点**: 支持Web、小程序、APP多端部署

## 核心业务模块

### 1. 用户管理模块 (User Management)
- **ER图**: `./er图/user_management.svg`
- **功能**: 用户注册、登录、权限管理

### 2. 面试模块 (Interview)
- **ER图**: `./er图/interview.svg`
- **功能**: 面试流程管理、题目管理、面试记录

### 3. AI聊天模块 (AI Chat)
- **ER图**: `./er图/ai_chat.svg`
- **功能**: AI对话、智能问答

### 4. 学习模块 (Learning)
- **ER图**: `./er图/learning.svg`
- **功能**: 学习资源管理、进度跟踪

### 5. 技能评估模块 (Skill Assessment)
- **ER图**: `./er图/skill_assessment.svg`
- **功能**: 技能测试、能力评估

## 项目文档

### 核心文档
- **项目缘起**: `./doc/项目缘起文档.md`
- **AI Agent实现计划**: `./doc/ai-agent-implementation-plan.md`
- **前端页面文档**: `./doc/前端页面文档/`
- **原型设计**: `./doc/原型/`

### 测试文档
- **测试跟踪表**: `./SmartInterview_测试项目跟踪表.xlsx`
- **详细测试报告**: `./SmartInterview_详细测试报告.html`

### 可视化文档
- **系统架构图**: `./system-architecture-green.html`
- **项目PPT**: `./智能可视化报告系统PPT.html`

## AI Agent架构设计

### 7个核心AI Agent
1. **面试官AI Agent** (InterviewerAgent) - 智能问题生成、动态追问
2. **简历分析AI Agent** (ResumeAnalyzerAgent) - 简历解析、技能匹配
3. **技能评估AI Agent** (SkillAssessorAgent) - 技术能力测试、编程评估
4. **职业顾问AI Agent** (CareerAdvisorAgent) - 职业规划、行业分析
5. **模拟面试AI Agent** (MockInterviewerAgent) - 真实面试模拟
6. **反馈分析AI Agent** (FeedbackAnalyzerAgent) - 表现分析、改进建议
7. **学习指导AI Agent** (LearningGuideAgent) - 学习计划、资源推荐

### 技术实现方案
- **后端**: Spring Boot + MyBatis-Plus + Redis + MySQL
- **AI服务**: Python Flask/FastAPI + Transformers + BERT/GPT
- **实时通信**: WebSocket + SSE流式响应
- **微服务架构**: AI Gateway + Agent Core + NLP Processing + Knowledge Base

## 项目状态
- **开发阶段**: 开发中 (已有基础框架和AI实现计划)
- **测试状态**: 已有测试文档和报告
- **部署状态**: 待确认
- **前端状态**: Plus-UI为空，Unibest已有基础结构

## 关键决策记录
1. 采用前后端分离架构
2. 后端选择RuoYi框架以快速搭建企业级应用
3. 前端支持Web和移动端双平台 (UniApp技术栈)
4. 集成7个专业AI Agent作为核心竞争力
5. 采用微服务架构支持AI功能扩展
6. 使用SSE和WebSocket实现实时交互

## 项目特色功能
- **多Agent协同**: 7个专业AI Agent协同工作
- **实时交互**: SSE流式响应，即时反馈
- **多端支持**: Web + 小程序 + APP
- **个性化服务**: 基于用户画像的定制化方案
- **科学评估**: 标准化技能测试和量化分析

## 项目实现状态

### 后端实现状态 (80%完成度)
- ✅ **基础框架**: RuoYi-Vue-Plus-Single-5.X 完整搭建
- ✅ **Chat模块**: 完整的聊天功能，支持SSE流式响应
- ✅ **MCP Server**: Spring AI工具调用框架，支持AI Agent
- ✅ **用户认证**: Sa-Token认证体系
- ⚠️ **AI Agent**: 基础框架已有，7个专业Agent待完善
- ❓ **数据库**: 表结构待确认

### 前端实现状态 (90%完成度)
- ✅ **UniApp框架**: 完整的跨平台应用结构
- ✅ **页面完整**: 所有核心功能页面已实现
- ✅ **UI组件**: wot-design-uni组件库集成
- ✅ **核心功能**: AI聊天、面试、学习、评估模块页面
- ✅ **用户系统**: 登录、注册、个人中心完整
- ❓ **API集成**: 前后端接口对接状态待确认

### 核心功能模块
1. **AI聊天模块** ✅ - 页面和后端服务已实现
2. **面试模块** ✅ - 完整页面流程 (选择、房间、结果等)
3. **学习模块** ✅ - 题库、练习、视频、资源等页面
4. **技能评估** ✅ - 初始评估和结果页面
5. **用户管理** ✅ - 完整的用户中心功能

## 技术亮点
- **Spring AI集成**: 使用最新的Spring AI框架
- **SSE流式响应**: 实时AI对话体验
- **UniApp多端**: 一套代码多端部署
- **TailwindCSS**: 现代化CSS框架
- **TypeScript**: 类型安全的前端开发

## 项目文档产出
- **需求文档**: SmartInterview智能面试系统需求文档.html
  - 深度分析大学生学习需求
  - 详细介绍6个AI智能体功能
  - 包含多种图表和数据可视化
  - 符合学术文档格式规范
  - 突出AI智能体协同工作特色

---
*最后更新: 2025-01-18*
