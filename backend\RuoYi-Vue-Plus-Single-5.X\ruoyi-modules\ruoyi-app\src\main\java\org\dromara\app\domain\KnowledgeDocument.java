package org.dromara.app.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.time.LocalDateTime;

/**
 * 知识库文档实体
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_knowledge_document")
public class KnowledgeDocument extends BaseEntity {

    /**
     * 文档ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 知识库ID
     */
    private Long knowledgeBaseId;

    /**
     * 文档标题
     */
    private String title;

    /**
     * 文档内容
     */
    private String content;

    /**
     * 文档类型 (text/pdf/word/markdown/etc.)
     */
    private String docType;

    /**
     * 文档来源 (upload/url/api/etc.)
     */
    private String source;

    /**
     * 原始文件名
     */
    private String originalFilename;

    /**
     * 文件路径
     */
    private String filePath;

    /**
     * 文件大小 (字节)
     */
    private Long fileSize;

    /**
     * 文档状态 (0=处理中 1=已完成 2=失败)
     */
    private Integer status;

    /**
     * 处理状态 (0=未处理 1=已向量化 2=已索引)
     */
    private Integer processStatus;

    /**
     * 向量数量
     */
    private Long vectorCount;

    /**
     * 文档摘要
     */
    private String summary;

    /**
     * 文档标签 (JSON数组)
     */
    private String tags;

    /**
     * 文档元数据 (JSON格式)
     */
    private String metadata;

    /**
     * 处理配置 (JSON格式)
     */
    private String processConfig;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 最后处理时间
     */
    private LocalDateTime lastProcessTime;

    /**
     * 排序字段
     */
    private Integer sortOrder;

    /**
     * 备注
     */
    private String remark;
}
