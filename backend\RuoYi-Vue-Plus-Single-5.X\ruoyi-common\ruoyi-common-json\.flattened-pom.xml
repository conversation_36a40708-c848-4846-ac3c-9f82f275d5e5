<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.dromara</groupId>
    <artifactId>ruoyi-common</artifactId>
    <version>5.4.0</version>
  </parent>
  <groupId>org.dromara</groupId>
  <artifactId>ruoyi-common-json</artifactId>
  <version>5.4.0</version>
  <description>ruoyi-common-json 序列化模块</description>
  <dependencies>
    <dependency>
      <groupId>org.dromara</groupId>
      <artifactId>ruoyi-common-core</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.datatype</groupId>
      <artifactId>jackson-datatype-jsr310</artifactId>
    </dependency>
  </dependencies>
</project>
