{"doc": "\n 通用 用户服务\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectUserNameById", "paramTypes": ["java.lang.Long"], "doc": "\n 通过用户ID查询用户账户\r\n\r\n @param userId 用户ID\r\n @return 用户账户\r\n"}, {"name": "selectNicknameById", "paramTypes": ["java.lang.Long"], "doc": "\n 通过用户ID查询用户账户\r\n\r\n @param userId 用户ID\r\n @return 用户名称\r\n"}, {"name": "selectNicknameByIds", "paramTypes": ["java.lang.String"], "doc": "\n 通过用户ID查询用户账户\r\n\r\n @param userIds 用户ID 多个用逗号隔开\r\n @return 用户名称\r\n"}, {"name": "selectPhonenumberById", "paramTypes": ["java.lang.Long"], "doc": "\n 通过用户ID查询用户手机号\r\n\r\n @param userId 用户id\r\n @return 用户手机号\r\n"}, {"name": "selectEmailById", "paramTypes": ["java.lang.Long"], "doc": "\n 通过用户ID查询用户邮箱\r\n\r\n @param userId 用户id\r\n @return 用户邮箱\r\n"}, {"name": "selectListByIds", "paramTypes": ["java.util.List"], "doc": "\n 通过用户ID查询用户列表\r\n\r\n @param userIds 用户ids\r\n @return 用户列表\r\n"}, {"name": "selectUserIdsByRoleIds", "paramTypes": ["java.util.List"], "doc": "\n 通过角色ID查询用户ID\r\n\r\n @param roleIds 角色ids\r\n @return 用户ids\r\n"}, {"name": "selectUsersByRoleIds", "paramTypes": ["java.util.List"], "doc": "\n 通过角色ID查询用户\r\n\r\n @param roleIds 角色ids\r\n @return 用户\r\n"}, {"name": "selectUsersByDeptIds", "paramTypes": ["java.util.List"], "doc": "\n 通过部门ID查询用户\r\n\r\n @param deptIds 部门ids\r\n @return 用户\r\n"}, {"name": "selectUsersByPostIds", "paramTypes": ["java.util.List"], "doc": "\n 通过岗位ID查询用户\r\n\r\n @param postIds 岗位ids\r\n @return 用户\r\n"}, {"name": "selectUserNamesByIds", "paramTypes": ["java.util.List"], "doc": "\n 根据用户 ID 列表查询用户名称映射关系\r\n\r\n @param userIds 用户 ID 列表\r\n @return Map，其中 key 为用户 ID，value 为对应的用户名称\r\n"}, {"name": "selectRoleNamesByIds", "paramTypes": ["java.util.List"], "doc": "\n 根据角色 ID 列表查询角色名称映射关系\r\n\r\n @param roleIds 角色 ID 列表\r\n @return Map，其中 key 为角色 ID，value 为对应的角色名称\r\n"}, {"name": "selectDeptNamesByIds", "paramTypes": ["java.util.List"], "doc": "\n 根据部门 ID 列表查询部门名称映射关系\r\n\r\n @param deptIds 部门 ID 列表\r\n @return Map，其中 key 为部门 ID，value 为对应的部门名称\r\n"}, {"name": "selectPostNamesByIds", "paramTypes": ["java.util.List"], "doc": "\n 根据岗位 ID 列表查询岗位名称映射关系\r\n\r\n @param postIds 岗位 ID 列表\r\n @return Map，其中 key 为岗位 ID，value 为对应的岗位名称\r\n"}], "constructors": []}