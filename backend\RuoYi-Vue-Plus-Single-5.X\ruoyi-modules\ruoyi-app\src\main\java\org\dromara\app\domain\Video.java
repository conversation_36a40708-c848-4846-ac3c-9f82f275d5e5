package org.dromara.app.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 视频课程对象 app_video
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_video")
public class Video extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 视频ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 视频标题
     */
    private String title;

    /**
     * 视频简介
     */
    private String description;

    /**
     * 讲师名称
     */
    private String instructor;

    /**
     * 讲师头像
     */
    private String instructorAvatar;

    /**
     * 讲师ID
     */
    private Long instructorId;

    /**
     * 视频时长
     */
    private String duration;

    /**
     * 缩略图
     */
    private String thumbnail;

    /**
     * 分类(interview/tech/algorithm/project/communication)
     */
    private String category;

    /**
     * 难度(入门/进阶/高级)
     */
    private String difficulty;

    /**
     * 评分
     */
    private BigDecimal rating;

    /**
     * 学习人数
     */
    private Integer studentCount;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 是否免费(0-否 1-是)
     */
    private Integer free;

    /**
     * 视频地址
     */
    private String videoUrl;

    /**
     * 播放次数
     */
    private Integer viewCount;

    /**
     * 点赞数
     */
    private Integer likeCount;

    /**
     * 收藏数
     */
    private Integer collectCount;

    /**
     * 分享数
     */
    private Integer shareCount;

    /**
     * 标签(JSON数组)
     */
    private String tags;

    /**
     * 发布时间
     */
    private LocalDateTime publishTime;

    /**
     * 状态(0-下架 1-上架)
     */
    private Integer status;
}
