package org.dromara.app.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.app.domain.Question;
import org.dromara.app.domain.bo.QuestionBo;
import org.dromara.app.domain.vo.QuestionVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 题目管理Service接口
 *
 * <AUTHOR>
 */
public interface IQuestionService extends IService<Question> {

    /**
     * 查询题目
     *
     * @param questionId 题目主键
     * @return 题目
     */
    QuestionVo queryById(Long questionId);

    /**
     * 查询题目列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页查询条件
     * @return 题目分页列表
     */
    TableDataInfo<QuestionVo> queryPageList(QuestionBo bo, PageQuery pageQuery);

    /**
     * 查询题目列表
     *
     * @param bo 查询条件
     * @return 题目列表
     */
    List<QuestionVo> queryList(QuestionBo bo);

    /**
     * 新增题目
     *
     * @param bo 题目信息
     * @return 新增结果
     */
    Boolean insertByBo(QuestionBo bo);

    /**
     * 修改题目
     *
     * @param bo 题目信息
     * @return 修改结果
     */
    Boolean updateByBo(QuestionBo bo);

    /**
     * 校验并批量删除题目信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 删除结果
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 批量导入题目
     *
     * @param questionList 题目列表
     * @param bankId 题库ID
     * @param isUpdateSupport 是否支持更新
     * @param operName 操作人员
     * @return 导入结果信息
     */
    String importQuestion(List<QuestionVo> questionList, Long bankId, Boolean isUpdateSupport, String operName);

    /**
     * 导出题目列表
     *
     * @param bo 查询条件
     * @return 题目列表
     */
    List<QuestionVo> exportQuestionList(QuestionBo bo);

    /**
     * 更新题目状态
     *
     * @param questionId 题目ID
     * @param status 状态
     * @return 更新结果
     */
    Boolean updateStatus(Long questionId, String status);

    /**
     * 批量更新题目状态
     *
     * @param questionIds 题目ID集合
     * @param status  状态
     * @return 更新结果
     */
    Boolean batchUpdateStatus(List<Long> questionIds, String status);

    /**
     * 更新题目难度
     *
     * @param questionId 题目ID
     * @param difficulty 难度
     * @return 是否成功
     */
    Boolean updateDifficulty(Long questionId, String difficulty);

    /**
     * 批量更新题目难度
     *
     * @param questionIds 题目ID列表
     * @param difficulty 难度
     * @return 是否成功
     */
    Boolean batchUpdateDifficulty(List<Long> questionIds, String difficulty);

    /**
     * 批量移动题目到其他题库
     *
     * @param questionIds 题目ID集合
     * @param targetBankId 目标题库ID
     * @return 移动结果
     */
    Boolean batchMoveToBank(List<Long> questionIds, Long targetBankId);

    /**
     * 移动题目到其他题库
     *
     * @param questionId 题目ID
     * @param targetBankId 目标题库ID
     * @return 是否成功
     */
    Boolean moveQuestion(Long questionId, Long targetBankId);

    /**
     * 批量移动题目到其他题库
     *
     * @param questionIds 题目ID列表
     * @param targetBankId 目标题库ID
     * @return 是否成功
     */
    Boolean batchMoveQuestions(List<Long> questionIds, Long targetBankId);

    /**
     * 复制题目
     *
     * @param questionId 源题目ID
     * @param bankId 目标题库ID
     * @return 复制结果
     */
    Boolean copyQuestion(Long questionId, Long bankId);

    /**
     * 批量复制题目
     *
     * @param questionIds 题目ID集合
     * @param targetBankId 目标题库ID
     * @return 复制结果
     */
    Boolean batchCopyQuestion(List<Long> questionIds, Long targetBankId);

    /**
     * 批量复制题目
     *
     * @param questionIds 题目ID列表
     * @param targetBankId 目标题库ID
     * @return 是否成功
     */
    Boolean batchCopyQuestions(List<Long> questionIds, Long targetBankId);

    /**
     * 批量设置题目排序
     *
     * @param questionIds 题目ID列表
     * @param sorts 排序列表
     * @return 是否成功
     */
    Boolean batchSetSort(List<Long> questionIds, List<Integer> sorts);

    /**
     * 获取题目统计信息
     *
     * @param questionId 题目ID
     * @return 统计信息
     */
    Map<String, Object> getQuestionStatistics(Long questionId);

    /**
     * 获取题目类型统计
     *
     * @param bankId 题库ID（可选）
     * @return 统计结果
     */
    Map<String, Long> getTypeStats(Long bankId);

    /**
     * 获取题目难度统计
     *
     * @param bankId 题库ID（可选）
     * @return 统计结果
     */
    Map<String, Long> getDifficultyStats(Long bankId);

    /**
     * 随机抽题
     *
     * @param bankId 题库ID
     * @param count 数量
     * @param questionType 题目类型（可选）
     * @param difficulty 难度（可选）
     * @return 题目列表
     */
    List<QuestionVo> getRandomQuestions(Long bankId, Integer count, String questionType, String difficulty);

    /**
     * 获取题目标签统计
     *
     * @param bankId 题库ID（可选）
     * @return 统计结果
     */
    Map<String, Long> getTagStats(Long bankId);

    /**
     * 批量设置题目标签
     *
     * @param questionIds 题目ID集合
     * @param tags 标签列表
     * @return 设置结果
     */
    Boolean batchSetTags(List<Long> questionIds, List<String> tags);

    /**
     * 批量设置题目难度
     *
     * @param questionIds 题目ID集合
     * @param difficulty 难度
     * @return 设置结果
     */
    Boolean batchSetDifficulty(List<Long> questionIds, Integer difficulty);

    /**
     * 批量设置题目分类
     *
     * @param questionIds 题目ID集合
     * @param category 分类
     * @return 设置结果
     */
    Boolean batchSetCategory(List<Long> questionIds, String category);

    /**
     * 更新题目练习统计
     *
     * @param questionId 题目ID
     * @param isCorrect 是否答对
     * @return 更新结果
     */
    Boolean updatePracticeStatistics(Long questionId, Boolean isCorrect);

    /**
     * 获取题库下的题目统计
     *
     * @param bankId 题库ID
     * @return 统计信息
     */
    Map<String, Object> getBankQuestionStatistics(Long bankId);

    /**
     * 题目AI评分
     *
     * @param questionId 题目ID
     * @return 是否成功
     */
    Boolean aiScoreQuestion(Long questionId);

    /**
     * 批量AI评分
     *
     * @param questionIds 题目ID列表
     * @return 是否成功
     */
    Boolean batchAiScoreQuestions(List<Long> questionIds);

    /**
     * 根据题库ID查询题目列表
     *
     * @param bankId 题库ID
     * @param bo 查询条件
     * @param pageQuery 分页条件
     * @return 题目列表
     */
    TableDataInfo<QuestionVo> queryByBankId(Long bankId, QuestionBo bo, PageQuery pageQuery);
}
