{"doc": "\n Caffeine缓存专用的SpEL表达式解析工具类\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "parseExpression", "paramTypes": ["java.lang.String", "java.lang.Object", "java.lang.reflect.Method", "java.lang.Object[]"], "doc": "\n 解析SpEL表达式并返回字符串结果\r\n\r\n @param expression SpEL表达式\r\n @param method     方法\r\n @param args       参数\r\n @return 解析结果\r\n"}, {"name": "parseExpression", "paramTypes": ["java.lang.String", "java.lang.Object", "java.lang.reflect.Method", "java.lang.Object[]", "java.lang.Object"], "doc": "\n 解析SpEL表达式并返回字符串结果\r\n\r\n @param expression SpEL表达式\r\n @param method     方法\r\n @param args       参数\r\n @param result     方法返回值\r\n @return 解析结果\r\n"}, {"name": "parseCondition", "paramTypes": ["java.lang.String", "java.lang.Object", "java.lang.reflect.Method", "java.lang.Object[]", "java.lang.Object"], "doc": "\n 解析条件表达式并返回布尔结果\r\n\r\n @param condition 条件表达式\r\n @param method    方法\r\n @param args      参数\r\n @param result    方法返回值（可为null）\r\n @return 条件结果\r\n"}, {"name": "createEvaluationContext", "paramTypes": ["java.lang.Object", "java.lang.reflect.Method", "java.lang.Object[]", "java.lang.Object"], "doc": "\n 创建SpEL评估上下文\r\n\r\n @param method 方法\r\n @param args   参数\r\n @param result 返回值\r\n @return 评估上下文\r\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "paramTypes": [], "doc": "\n 获取原始的SpEL表达式解析器\r\n\r\n @return SpEL表达式解析器\r\n"}, {"name": "parseExpression", "paramTypes": ["java.lang.String"], "doc": "\n 解析表达式字符串\r\n\r\n @param expressionString 表达式字符串\r\n @return 表达式对象\r\n @throws ParseException 解析异常\r\n"}, {"name": "parseExpression", "paramTypes": ["java.lang.String", "org.springframework.expression.ParserContext"], "doc": "\n 解析表达式字符串（带上下文）\r\n\r\n @param expressionString 表达式字符串\r\n @param context          解析上下文\r\n @return 表达式对象\r\n @throws ParseException 解析异常\r\n"}], "constructors": []}