package org.dromara.app.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 音频指标对象 app_audio_metrics
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@Data
@TableName("app_audio_metrics")
public class AudioMetrics implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 音频指标ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 结果ID
     */
    private String resultId;

    /**
     * 清晰度
     */
    private Integer clarity;

    /**
     * 流利度
     */
    private Integer fluency;

    /**
     * 自信度
     */
    private Integer confidence;

    /**
     * 语速
     */
    private Integer pace;

    /**
     * 总体评分
     */
    private Integer overall;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

}
