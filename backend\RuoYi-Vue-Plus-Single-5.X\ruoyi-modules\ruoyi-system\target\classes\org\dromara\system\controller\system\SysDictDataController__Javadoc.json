{"doc": "\n 数据字典信息\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.system.domain.bo.SysDictDataBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询字典数据列表\r\n"}, {"name": "export", "paramTypes": ["org.dromara.system.domain.bo.SysDictDataBo", "jakarta.servlet.http.HttpServletResponse"], "doc": "\n 导出字典数据列表\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": "\n 查询字典数据详细\r\n\r\n @param dictCode 字典code\r\n"}, {"name": "dictType", "paramTypes": ["java.lang.String"], "doc": "\n 根据字典类型查询字典数据信息\r\n\r\n @param dictType 字典类型\r\n"}, {"name": "add", "paramTypes": ["org.dromara.system.domain.bo.SysDictDataBo"], "doc": "\n 新增字典类型\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.system.domain.bo.SysDictDataBo"], "doc": "\n 修改保存字典类型\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": "\n 删除字典类型\r\n\r\n @param dictCodes 字典code串\r\n"}], "constructors": []}