package org.dromara.app.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 题目评论VO
 *
 * <AUTHOR>
 */
@Data
public class QuestionCommentVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 评论ID
     */
    private String id;

    /**
     * 题目ID
     */
    private String questionId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 用户昵称
     */
    private String author;

    /**
     * 用户头像
     */
    private String avatar;

    /**
     * 评论内容
     */
    private String content;

    /**
     * 点赞数
     */
    private Integer likes;

    /**
     * 回复数
     */
    private Integer replyCount;

    /**
     * 父评论ID
     */
    private String parentId;

    /**
     * 回复列表
     */
    private List<QuestionCommentVO> replies;

    /**
     * 发表时间
     */
    private String time;

    /**
     * 是否点赞(true:已点赞,false:未点赞)
     */
    private Boolean liked;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;

}
