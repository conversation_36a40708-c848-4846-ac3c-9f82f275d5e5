{"doc": "\n Caffeine缓存配置属性\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "enabled", "doc": "\n 是否开启\r\n"}, {"name": "defaultCacheName", "doc": "\n 默认缓存名称\r\n"}, {"name": "maximumSize", "doc": "\n 缓存最大条目数\r\n"}, {"name": "expireAfterWrite", "doc": "\n 写入后过期时间（秒）\r\n"}, {"name": "expireAfterAccess", "doc": "\n 访问后过期时间（秒）\r\n"}, {"name": "refreshAfterWrite", "doc": "\n 刷新时间（秒）\r\n"}, {"name": "initialCapacity", "doc": "\n 初始容量\r\n"}, {"name": "recordStats", "doc": "\n 是否启用统计\r\n"}, {"name": "weakKeys", "doc": "\n 是否启用软引用\r\n"}, {"name": "weakValues", "doc": "\n 是否启用弱引用值\r\n"}, {"name": "softValues", "doc": "\n 是否启用软引用值\r\n"}, {"name": "debugEnabled", "doc": "\n 是否启用调试日志\r\n"}], "enumConstants": [], "methods": [], "constructors": []}