package org.dromara.app.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.dromara.app.domain.Job;
import org.dromara.app.domain.bo.JobQueryBo;
import org.dromara.app.domain.vo.JobVo;

import java.util.List;

/**
 * 岗位信息Service接口
 *
 * <AUTHOR> Assistant
 * @date 2025-07-19
 */
public interface IJobService {

    /**
     * 分页查询岗位列表
     *
     * @param page    分页参数
     * @param queryBo 查询条件
     * @return 岗位列表
     */
    Page<JobVo> selectJobPageWithFavorite(Page<JobVo> page, JobQueryBo queryBo);

    /**
     * 根据技术领域查询岗位
     *
     * @param technicalDomain 技术领域
     * @param level          岗位级别
     * @param limit          限制数量
     * @return 岗位列表
     */
    List<JobVo> selectByTechnicalDomain(String technicalDomain, String level, Integer limit);

    /**
     * 查询推荐岗位
     *
     * @param limit  限制数量
     * @param userId 用户ID
     * @return 推荐岗位列表
     */
    List<JobVo> selectRecommendedJobs(Integer limit, Long userId);

    /**
     * 查询热门岗位
     *
     * @param limit  限制数量
     * @param userId 用户ID
     * @return 热门岗位列表
     */
    List<JobVo> selectHotJobs(Integer limit, Long userId);

    /**
     * 根据ID查询岗位详情
     *
     * @param jobId  岗位ID
     * @param userId 用户ID
     * @return 岗位详情
     */
    JobVo selectJobDetail(Long jobId, Long userId);

    /**
     * 增加浏览次数
     *
     * @param jobId 岗位ID
     * @return 是否成功
     */
    boolean incrementViewCount(Long jobId);

    /**
     * 更新收藏次数
     *
     * @param jobId     岗位ID
     * @param increment 增量（1或-1）
     * @return 是否成功
     */
    boolean updateFavoriteCount(Long jobId, Integer increment);

    /**
     * 查询技术领域统计
     *
     * @return 统计信息
     */
    List<TechnicalDomainStats> getTechnicalDomainStats();

    /**
     * 技术领域统计信息
     */
    class TechnicalDomainStats {
        private String technicalDomain;
        private Integer jobCount;
        private Double avgDifficulty;
        private Double avgPassRate;
        private Integer totalViews;
        private Integer totalFavorites;

        // getters and setters
        public String getTechnicalDomain() {
            return technicalDomain;
        }

        public void setTechnicalDomain(String technicalDomain) {
            this.technicalDomain = technicalDomain;
        }

        public Integer getJobCount() {
            return jobCount;
        }

        public void setJobCount(Integer jobCount) {
            this.jobCount = jobCount;
        }

        public Double getAvgDifficulty() {
            return avgDifficulty;
        }

        public void setAvgDifficulty(Double avgDifficulty) {
            this.avgDifficulty = avgDifficulty;
        }

        public Double getAvgPassRate() {
            return avgPassRate;
        }

        public void setAvgPassRate(Double avgPassRate) {
            this.avgPassRate = avgPassRate;
        }

        public Integer getTotalViews() {
            return totalViews;
        }

        public void setTotalViews(Integer totalViews) {
            this.totalViews = totalViews;
        }

        public Integer getTotalFavorites() {
            return totalFavorites;
        }

        public void setTotalFavorites(Integer totalFavorites) {
            this.totalFavorites = totalFavorites;
        }
    }

}
