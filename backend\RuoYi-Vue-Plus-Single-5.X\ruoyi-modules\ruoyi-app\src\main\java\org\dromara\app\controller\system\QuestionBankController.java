package org.dromara.app.controller.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.app.domain.bo.QuestionBankBo;
import org.dromara.app.domain.vo.QuestionBankVo;
import org.dromara.app.service.IQuestionBankService;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 题库管理
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/question-bank")
public class QuestionBankController extends BaseController {

    private final IQuestionBankService questionBankService;

    /**
     * 查询题库列表
     */
    @SaCheckPermission("system:questionbank:list")
    @GetMapping("/list")
    public TableDataInfo<QuestionBankVo> list(QuestionBankBo bo, PageQuery pageQuery) {
        return questionBankService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出题库列表
     */
    @SaCheckPermission("system:questionbank:export")
    @Log(title = "题库", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(QuestionBankBo bo, HttpServletResponse response) {
        List<QuestionBankVo> list = questionBankService.exportBankList(bo);
        ExcelUtil.exportExcel(list, "题库", QuestionBankVo.class, response);
    }

    /**
     * 获取题库详细信息
     */
    @SaCheckPermission("system:questionbank:query")
    @GetMapping("/{bankId}")
    public R<QuestionBankVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long bankId) {
        return R.ok(questionBankService.queryById(bankId));
    }

    /**
     * 新增题库
     */
    @SaCheckPermission("system:questionbank:add")
    @Log(title = "题库", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping
    public R<Void> add(@Validated(AddGroup.class) @RequestBody QuestionBankBo bo) {
        return toAjax(questionBankService.insertByBo(bo));
    }

    /**
     * 修改题库
     */
    @SaCheckPermission("system:questionbank:edit")
    @Log(title = "题库", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody QuestionBankBo bo) {
        return toAjax(questionBankService.updateByBo(bo));
    }

    /**
     * 删除题库
     */
    @SaCheckPermission("system:questionbank:remove")
    @Log(title = "题库", businessType = BusinessType.DELETE)
    @DeleteMapping("/{bankIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] bankIds) {
        return toAjax(questionBankService.deleteWithValidByIds(List.of(bankIds), true));
    }

    /**
     * 更新题库状态
     */
    @SaCheckPermission("system:questionbank:edit")
    @Log(title = "题库", businessType = BusinessType.UPDATE)
    @PutMapping("/updateStatus")
    public R<Void> updateStatus(@RequestBody Map<String, Object> params) {
        Long bankId = Long.valueOf(params.get("bankId").toString());
        String status = params.get("status").toString();
        return toAjax(questionBankService.updateStatus(bankId, status));
    }

    /**
     * 批量更新题库状态
     */
    @SaCheckPermission("system:questionbank:edit")
    @Log(title = "题库", businessType = BusinessType.UPDATE)
    @PutMapping("/batchUpdateStatus")
    public R<Void> batchUpdateStatus(@RequestBody Map<String, Object> params) {
        @SuppressWarnings("unchecked")
        List<Long> bankIds = (List<Long>) params.get("bankIds");
        String status = params.get("status").toString();
        return toAjax(questionBankService.batchUpdateStatus(bankIds, status));
    }

    /**
     * 复制题库
     */
    @SaCheckPermission("system:questionbank:add")
    @Log(title = "题库", businessType = BusinessType.INSERT)
    @PostMapping("/copy")
    public R<Void> copyBank(@RequestBody Map<String, Object> params) {
        Long bankId = Long.valueOf(params.get("bankId").toString());
        String bankCode = params.get("bankCode").toString();
        String title = params.get("title").toString();
        return toAjax(questionBankService.copyBank(bankId, bankCode, title));
    }

    /**
     * 更新题库题目总数
     */
    @SaCheckPermission("system:questionbank:edit")
    @Log(title = "题库", businessType = BusinessType.UPDATE)
    @PutMapping("/updateTotalQuestions/{bankId}")
    public R<Void> updateTotalQuestions(@PathVariable Long bankId) {
        return toAjax(questionBankService.updateTotalQuestions(bankId));
    }

    /**
     * 获取题库统计信息
     */
    @SaCheckPermission("system:questionbank:query")
    @GetMapping("/statistics/{bankId}")
    public R<QuestionBankVo> getBankStatistics(@PathVariable Long bankId) {
        return R.ok(questionBankService.getBankStatistics(bankId));
    }

    /**
     * 批量设置题库排序
     */
    @SaCheckPermission("system:questionbank:edit")
    @Log(title = "题库", businessType = BusinessType.UPDATE)
    @PutMapping("/batchSetSort")
    public R<Void> batchSetSort(@RequestBody Map<String, Object> params) {
        @SuppressWarnings("unchecked")
        List<Long> bankIds = (List<Long>) params.get("bankIds");
        @SuppressWarnings("unchecked")
        List<Integer> sorts = (List<Integer>) params.get("sorts");
        return toAjax(questionBankService.batchSetSort(bankIds, sorts));
    }

    /**
     * 导入题库数据
     */
    @SaCheckPermission("system:questionbank:import")
    @Log(title = "题库", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public R<Void> importData(MultipartFile file, boolean updateSupport) throws Exception {
        List<QuestionBankVo> bankList = ExcelUtil.importExcel(file.getInputStream(), QuestionBankVo.class);
        String operName = "admin"; // 暂时使用固定值
        String message = questionBankService.importBank(bankList, updateSupport, operName);
        return R.ok(message);
    }

    /**
     * 获取导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil.exportExcel(List.of(), "题库数据", QuestionBankVo.class, response);
    }

    /**
     * 获取题库下拉选项
     */
    @SaCheckPermission("system:questionbank:list")
    @GetMapping("/options")
    public R<List<QuestionBankVo>> getOptions() {
        QuestionBankBo bo = new QuestionBankBo();
        bo.setStatus("0"); // 只查询正常状态的题库
        List<QuestionBankVo> list = questionBankService.queryList(bo);
        return R.ok(list);
    }

    /**
     * 根据专业ID获取题库列表
     */
    @SaCheckPermission("system:questionbank:list")
    @GetMapping("/listByMajor/{majorId}")
    public R<List<QuestionBankVo>> listByMajor(@PathVariable Long majorId) {
        QuestionBankBo bo = new QuestionBankBo();
        bo.setMajorId(majorId);
        bo.setStatus("0");
        List<QuestionBankVo> list = questionBankService.queryList(bo);
        return R.ok(list);
    }

    /**
     * 题库编码重复校验
     */
    @GetMapping("/checkBankCodeUnique")
    public R<Boolean> checkBankCodeUnique(String bankCode, Long bankId) {
        QuestionBankBo bo = new QuestionBankBo();
        bo.setBankCode(bankCode);
        List<QuestionBankVo> list = questionBankService.queryList(bo);

        if (list.isEmpty()) {
            return R.ok(true);
        }

        if (bankId != null && list.size() == 1 && list.get(0).getId().equals(bankId)) {
            return R.ok(true);
        }

        return R.ok(false);
    }

    /**
     * 获取题库分类统计
     */
    @SaCheckPermission("system:questionbank:list")
    @GetMapping("/categoryStats")
    public R<Map<String, Long>> getCategoryStats() {
        // 这里可以实现具体的分类统计逻辑
        return R.ok(Map.of());
    }

    /**
     * 获取题库难度分布统计
     */
    @SaCheckPermission("system:questionbank:list")
    @GetMapping("/difficultyStats")
    public R<Map<String, Long>> getDifficultyStats() {
        // 这里可以实现具体的难度统计逻辑
        return R.ok(Map.of());
    }
}
