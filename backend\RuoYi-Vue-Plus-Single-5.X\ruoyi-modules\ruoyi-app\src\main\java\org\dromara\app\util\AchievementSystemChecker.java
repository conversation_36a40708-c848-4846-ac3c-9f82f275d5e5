package org.dromara.app.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 成就系统完整性检查工具
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class AchievementSystemChecker {

    /**
     * 检查成就系统完整性
     *
     * @return 检查结果
     */
    public Map<String, Object> checkSystemIntegrity() {
        Map<String, Object> result = new HashMap<>();
        List<String> issues = new ArrayList<>();
        List<String> success = new ArrayList<>();

        // 检查核心类是否存在
        checkCoreClasses(issues, success);

        // 检查服务接口
        checkServiceInterfaces(issues, success);

        // 检查Mapper接口
        checkMapperInterfaces(issues, success);

        // 检查控制器
        checkControllers(issues, success);

        // 检查配置文件
        checkConfigurations(issues, success);

        result.put("success", success);
        result.put("issues", issues);
        result.put("totalChecks", success.size() + issues.size());
        result.put("successCount", success.size());
        result.put("issueCount", issues.size());
        result.put("status", issues.isEmpty() ? "HEALTHY" : "ISSUES_FOUND");

        return result;
    }

    private void checkCoreClasses(List<String> issues, List<String> success) {
        // 检查实体类
        checkClass("org.dromara.app.domain.Achievement", issues, success, "Achievement实体类");
        checkClass("org.dromara.app.domain.UserAchievement", issues, success, "UserAchievement实体类");
        checkClass("org.dromara.app.domain.UserBehavior", issues, success, "UserBehavior实体类");

        // 检查VO类
        checkClass("org.dromara.app.domain.vo.AchievementVo", issues, success, "AchievementVo视图对象");
        checkClass("org.dromara.app.domain.vo.UserAchievementVo", issues, success, "UserAchievementVo视图对象");
        checkClass("org.dromara.app.domain.vo.AchievementStatsVo", issues, success, "AchievementStatsVo统计对象");
        checkClass("org.dromara.app.domain.vo.BadgeVo", issues, success, "BadgeVo徽章对象");

        // 检查BO类
        checkClass("org.dromara.app.domain.bo.AchievementBo", issues, success, "AchievementBo业务对象");
        checkClass("org.dromara.app.domain.bo.UserAchievementBo", issues, success, "UserAchievementBo业务对象");
        checkClass("org.dromara.app.domain.bo.UserBehaviorBo", issues, success, "UserBehaviorBo业务对象");

        // 检查DTO类
        checkClass("org.dromara.app.domain.dto.TrackEventDto", issues, success, "TrackEventDto传输对象");
    }

    private void checkServiceInterfaces(List<String> issues, List<String> success) {
        // 检查服务接口
        checkClass("org.dromara.app.service.IAchievementService", issues, success, "IAchievementService接口");
        checkClass("org.dromara.app.service.IAchievementManageService", issues, success, "IAchievementManageService接口");
        checkClass("org.dromara.app.service.IUserBehaviorService", issues, success, "IUserBehaviorService接口");
        checkClass("org.dromara.app.service.IAchievementNotificationService", issues, success, "IAchievementNotificationService接口");

        // 检查服务实现类
        checkClass("org.dromara.app.service.impl.AchievementServiceImpl", issues, success, "AchievementServiceImpl实现类");
        checkClass("org.dromara.app.service.impl.AchievementManageServiceImpl", issues, success, "AchievementManageServiceImpl实现类");
        checkClass("org.dromara.app.service.impl.UserBehaviorServiceImpl", issues, success, "UserBehaviorServiceImpl实现类");
        checkClass("org.dromara.app.service.impl.AchievementNotificationServiceImpl", issues, success, "AchievementNotificationServiceImpl实现类");
    }

    private void checkMapperInterfaces(List<String> issues, List<String> success) {
        // 检查Mapper接口
        checkClass("org.dromara.app.mapper.AchievementMapper", issues, success, "AchievementMapper接口");
        checkClass("org.dromara.app.mapper.UserAchievementMapper", issues, success, "UserAchievementMapper接口");
        checkClass("org.dromara.app.mapper.UserBehaviorMapper", issues, success, "UserBehaviorMapper接口");
        checkClass("org.dromara.app.mapper.AchievementEventMapper", issues, success, "AchievementEventMapper接口");
        checkClass("org.dromara.app.mapper.UserBadgeMapper", issues, success, "UserBadgeMapper接口");
    }

    private void checkControllers(List<String> issues, List<String> success) {
        // 检查控制器
        checkClass("org.dromara.app.controller.TrackController", issues, success, "TrackController埋点控制器");
        checkClass("org.dromara.app.controller.achievement.AchievementManageController", issues, success, "AchievementManageController管理控制器");
        checkClass("org.dromara.app.controller.achievement.UserAchievementController", issues, success, "UserAchievementController用户成就控制器");
    }

    private void checkConfigurations(List<String> issues, List<String> success) {
        // 检查配置类
        checkClass("org.dromara.app.config.AchievementRabbitMqConfig", issues, success, "RabbitMQ配置");
        checkClass("org.dromara.app.engine.AchievementRuleEngine", issues, success, "成就规则引擎");
        checkClass("org.dromara.app.listener.AchievementMessageListener", issues, success, "消息监听器");
    }

    private void checkClass(String className, List<String> issues, List<String> success, String description) {
        try {
            Class.forName(className);
            success.add("✅ " + description + " - " + className);
        } catch (ClassNotFoundException e) {
            issues.add("❌ " + description + " 缺失 - " + className);
        }
    }

    /**
     * 检查数据库表是否存在
     *
     * @return 检查结果
     */
    public Map<String, Object> checkDatabaseTables() {
        Map<String, Object> result = new HashMap<>();
        List<String> requiredTables = List.of(
            "app_achievement",
            "app_user_achievement",
            "app_user_behavior",
            "app_achievement_event",
            "app_user_badge"
        );

        result.put("requiredTables", requiredTables);
        result.put("message", "请确保以下数据库表已创建");

        return result;
    }

    /**
     * 检查RabbitMQ配置
     *
     * @return 检查结果
     */
    public Map<String, Object> checkRabbitMQConfig() {
        Map<String, Object> result = new HashMap<>();
        List<String> requiredQueues = List.of(
            "achievement.check.queue",
            "achievement.notification.queue"
        );

        List<String> requiredExchanges = List.of(
            "achievement.topic.exchange"
        );

        result.put("requiredQueues", requiredQueues);
        result.put("requiredExchanges", requiredExchanges);
        result.put("message", "请确保RabbitMQ服务运行正常，并且相关队列和交换机已创建");

        return result;
    }

    /**
     * 生成系统健康报告
     *
     * @return 健康报告
     */
    public Map<String, Object> generateHealthReport() {
        Map<String, Object> report = new HashMap<>();

        // 系统完整性检查
        Map<String, Object> integrityCheck = checkSystemIntegrity();
        report.put("integrityCheck", integrityCheck);

        // 数据库检查
        Map<String, Object> databaseCheck = checkDatabaseTables();
        report.put("databaseCheck", databaseCheck);

        // RabbitMQ检查
        Map<String, Object> rabbitMQCheck = checkRabbitMQConfig();
        report.put("rabbitMQCheck", rabbitMQCheck);

        // 总体状态
        boolean isHealthy = "HEALTHY".equals(integrityCheck.get("status"));
        report.put("overallStatus", isHealthy ? "HEALTHY" : "NEEDS_ATTENTION");
        report.put("timestamp", System.currentTimeMillis());

        return report;
    }

    /**
     * 打印系统状态报告
     */
    public void printSystemStatus() {
        Map<String, Object> report = generateHealthReport();

        log.info("=== 成就系统状态报告 ===");
        log.info("总体状态: {}", report.get("overallStatus"));

        @SuppressWarnings("unchecked")
        Map<String, Object> integrityCheck = (Map<String, Object>) report.get("integrityCheck");
        log.info("完整性检查: 成功 {}/{}", integrityCheck.get("successCount"), integrityCheck.get("totalChecks"));

        @SuppressWarnings("unchecked")
        List<String> issues = (List<String>) integrityCheck.get("issues");
        if (!issues.isEmpty()) {
            log.warn("发现问题:");
            issues.forEach(log::warn);
        }

        @SuppressWarnings("unchecked")
        List<String> success = (List<String>) integrityCheck.get("success");
        log.info("检查通过的组件数量: {}", success.size());

        log.info("=== 报告结束 ===");
    }

}
