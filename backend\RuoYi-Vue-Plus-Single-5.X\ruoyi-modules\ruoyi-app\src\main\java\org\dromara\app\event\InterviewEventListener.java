package org.dromara.app.event;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.event.InterviewEvents.*;
import org.dromara.app.service.IInterviewStatsService;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 面试事件监听器
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class InterviewEventListener {

    private final IInterviewStatsService statsService;

    /**
     * 监听面试开始事件
     */
    @Async
    @EventListener
    public void handleInterviewStarted(InterviewStartedEvent event) {
        log.info("面试开始事件: sessionId={}, userId={}, jobId={}",
                event.getSessionId(), event.getUserId(), event.getJobId());

        try {
            Map<String, Object> eventData = new HashMap<>();
            eventData.put("userId", event.getUserId());
            eventData.put("jobId", event.getJobId());
            eventData.put("mode", event.getMode());
            eventData.put("questionCount", event.getQuestionCount());
            eventData.put("timestamp", LocalDateTime.now());

            statsService.recordInterviewEvent(event.getSessionId(), "interview_started", eventData);
        } catch (Exception e) {
            log.error("处理面试开始事件失败", e);
        }
    }

    /**
     * 监听问题回答事件
     */
    @Async
    @EventListener
    public void handleQuestionAnswered(QuestionAnsweredEvent event) {
        log.info("问题回答事件: sessionId={}, questionId={}, score={}",
                event.getSessionId(), event.getQuestionId(), event.getScore());

        try {
            Map<String, Object> eventData = new HashMap<>();
            eventData.put("questionId", event.getQuestionId());
            eventData.put("questionType", event.getQuestionType());
            eventData.put("score", event.getScore());
            eventData.put("duration", event.getDuration());
            eventData.put("hasAudio", event.getHasAudio());
            eventData.put("hasVideo", event.getHasVideo());
            eventData.put("timestamp", LocalDateTime.now());

            statsService.recordInterviewEvent(event.getSessionId(), "question_answered", eventData);
        } catch (Exception e) {
            log.error("处理问题回答事件失败", e);
        }
    }

    /**
     * 监听问题跳过事件
     */
    @Async
    @EventListener
    public void handleQuestionSkipped(QuestionSkippedEvent event) {
        log.info("问题跳过事件: sessionId={}, questionId={}, reason={}",
                event.getSessionId(), event.getQuestionId(), event.getReason());

        try {
            Map<String, Object> eventData = new HashMap<>();
            eventData.put("questionId", event.getQuestionId());
            eventData.put("questionType", event.getQuestionType());
            eventData.put("reason", event.getReason());
            eventData.put("timestamp", LocalDateTime.now());

            statsService.recordInterviewEvent(event.getSessionId(), "question_skipped", eventData);
        } catch (Exception e) {
            log.error("处理问题跳过事件失败", e);
        }
    }

    /**
     * 监听面试完成事件
     */
    @Async
    @EventListener
    public void handleInterviewCompleted(InterviewCompletedEvent event) {
        log.info("面试完成事件: sessionId={}, totalScore={}, completionRate={}",
                event.getSessionId(), event.getTotalScore(), event.getCompletionRate());

        try {
            Map<String, Object> eventData = new HashMap<>();
            eventData.put("totalScore", event.getTotalScore());
            eventData.put("completionRate", event.getCompletionRate());
            eventData.put("answeredCount", event.getAnsweredCount());
            eventData.put("skippedCount", event.getSkippedCount());
            eventData.put("totalDuration", event.getTotalDuration());
            eventData.put("endReason", event.getEndReason());
            eventData.put("timestamp", LocalDateTime.now());

            statsService.recordInterviewEvent(event.getSessionId(), "interview_completed", eventData);
        } catch (Exception e) {
            log.error("处理面试完成事件失败", e);
        }
    }

    /**
     * 监听AI评估事件
     */
    @Async
    @EventListener
    public void handleAiEvaluation(AiEvaluationEvent event) {
        log.info("AI评估事件: sessionId={}, questionId={}, evaluationTime={}",
                event.getSessionId(), event.getQuestionId(), event.getEvaluationTime());

        try {
            Map<String, Object> eventData = new HashMap<>();
            eventData.put("questionId", event.getQuestionId());
            eventData.put("evaluationTime", event.getEvaluationTime());
            eventData.put("score", event.getScore());
            eventData.put("model", event.getModel());
            eventData.put("success", event.getSuccess());
            eventData.put("errorMessage", event.getErrorMessage());
            eventData.put("timestamp", event.getEventTimestamp());

            statsService.recordInterviewEvent(event.getSessionId(), "ai_evaluation", eventData);
        } catch (Exception e) {
            log.error("处理AI评估事件失败", e);
        }
    }

    /**
     * 监听系统错误事件
     */
    @Async
    @EventListener
    public void handleSystemError(SystemErrorEvent event) {
        log.error("系统错误事件: sessionId={}, errorType={}, message={}",
                event.getSessionId(), event.getErrorType(), event.getMessage());

        try {
            Map<String, Object> eventData = new HashMap<>();
            eventData.put("errorType", event.getErrorType());
            eventData.put("message", event.getMessage());
            eventData.put("stackTrace", event.getStackTrace());
            eventData.put("userId", event.getUserId());
            eventData.put("timestamp", event.getEventTimestamp());

            statsService.recordInterviewEvent(event.getSessionId(), "system_error", eventData);
        } catch (Exception e) {
            log.error("处理系统错误事件失败", e);
        }
    }
}
