{"doc": "\n JavaMail 配置属性\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "enabled", "doc": "\n 过滤开关\r\n"}, {"name": "host", "doc": "\n SMTP服务器域名\r\n"}, {"name": "port", "doc": "\n SMTP服务端口\r\n"}, {"name": "auth", "doc": "\n 是否需要用户名密码验证\r\n"}, {"name": "user", "doc": "\n 用户名\r\n"}, {"name": "pass", "doc": "\n 密码\r\n"}, {"name": "from", "doc": "\n 发送方，遵循RFC-822标准<br>\r\n 发件人可以是以下形式：\r\n\r\n <pre>\r\n 1. <EMAIL>\r\n 2.  name &lt;<EMAIL>&gt;\r\n </pre>\r\n"}, {"name": "starttlsEnable", "doc": "\n 使用 STARTTLS安全连接，STARTTLS是对纯文本通信协议的扩展。它将纯文本连接升级为加密连接（TLS或SSL）， 而不是使用一个单独的加密通信端口。\r\n"}, {"name": "sslEnable", "doc": "\n 使用 SSL安全连接\r\n"}, {"name": "timeout", "doc": "\n SMTP超时时长，单位毫秒，缺省值不超时\r\n"}, {"name": "connectionTimeout", "doc": "\n Socket连接超时值，单位毫秒，缺省值不超时\r\n"}], "enumConstants": [], "methods": [], "constructors": []}