<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.workflow.mapper.FlwCategoryMapper">

    <select id="countCategoryById" resultType="Long">
        select count(*)
        from flow_category
        where del_flag = '0'
          and category_id = #{categoryId}
    </select>

</mapper>
