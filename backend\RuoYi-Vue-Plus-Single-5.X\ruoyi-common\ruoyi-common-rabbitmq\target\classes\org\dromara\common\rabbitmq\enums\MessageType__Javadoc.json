{"doc": "\n 消息类型枚举\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "code", "doc": "\n 类型编码\r\n"}, {"name": "desc", "doc": "\n 类型描述\r\n"}], "enumConstants": [{"name": "NORMAL", "doc": "\n 普通消息\r\n"}, {"name": "DELAY", "doc": "\n 延迟消息\r\n"}, {"name": "BROADCAST", "doc": "\n 广播消息\r\n"}, {"name": "RPC", "doc": "\n RPC消息\r\n"}, {"name": "TRANSACTION", "doc": "\n 事务消息\r\n"}], "methods": [{"name": "getByCode", "paramTypes": ["java.lang.String"], "doc": "\n 根据编码获取枚举\r\n\r\n @param code 编码\r\n @return 枚举对象\r\n"}], "constructors": []}