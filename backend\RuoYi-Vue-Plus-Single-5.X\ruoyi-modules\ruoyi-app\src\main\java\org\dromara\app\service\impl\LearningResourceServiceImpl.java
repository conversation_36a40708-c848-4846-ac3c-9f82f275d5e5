package org.dromara.app.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.LearningResource;
import org.dromara.app.mapper.LearningResourceMapper;
import org.dromara.app.service.ILearningResourceService;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 学习资源服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LearningResourceServiceImpl implements ILearningResourceService {

    private final LearningResourceMapper resourceMapper;

    // 资源类型常量
    private static final List<String> VALID_RESOURCE_TYPES = List.of("video", "article", "course", "practice", "book", "tool");

    // 难度等级常量
    private static final List<String> VALID_DIFFICULTIES = List.of("简单", "中等", "困难");

    // 资源状态常量
    private static final List<String> VALID_STATUSES = List.of("active", "inactive", "deprecated");

    // 字段长度限制常量
    private static final int MAX_TITLE_LENGTH = 200;
    private static final int MAX_TYPE_LENGTH = 50;
    private static final int MAX_URL_LENGTH = 500;
    private static final int MAX_DURATION_LENGTH = 50;
    private static final int MAX_DIFFICULTY_LENGTH = 20;
    private static final int MAX_SKILL_AREA_LENGTH = 100;
    private static final int MAX_CATEGORY_LENGTH = 100;
    private static final int MAX_PROVIDER_LENGTH = 100;
    private static final int MAX_PRICE_LENGTH = 50;
    private static final int MAX_LANGUAGE_LENGTH = 20;
    private static final int MAX_STATUS_LENGTH = 20;
    private static final int MAX_REMARK_LENGTH = 500;


    /**
     * 检查资源数据与数据库模式的兼容性
     * 主要用于防止数据库错误，检查字段长度和约束
     *
     * @param resource 学习资源
     */
    private void checkDatabaseCompatibility(LearningResource resource) {
        List<String> errors = new ArrayList<>();

        // 检查字段长度限制
        checkFieldLength(errors, "标题", resource.getTitle(), MAX_TITLE_LENGTH);
        checkFieldLength(errors, "类型", resource.getType(), MAX_TYPE_LENGTH);
        checkFieldLength(errors, "资源URL", resource.getUrl(), MAX_URL_LENGTH);
        checkFieldLength(errors, "学习时长", resource.getDuration(), MAX_DURATION_LENGTH);
        checkFieldLength(errors, "难度等级", resource.getDifficulty(), MAX_DIFFICULTY_LENGTH);
        checkFieldLength(errors, "技能领域", resource.getSkillArea(), MAX_SKILL_AREA_LENGTH);
        checkFieldLength(errors, "资源分类", resource.getCategory(), MAX_CATEGORY_LENGTH);
        checkFieldLength(errors, "资源提供者", resource.getProvider(), MAX_PROVIDER_LENGTH);
        checkFieldLength(errors, "价格", resource.getPrice(), MAX_PRICE_LENGTH);
        checkFieldLength(errors, "语言", resource.getLanguage(), MAX_LANGUAGE_LENGTH);
        checkFieldLength(errors, "状态", resource.getStatus(), MAX_STATUS_LENGTH);

        // 检查字段值约束
        if (resource.getRating() != null) {
            if (resource.getRating() < 0 || resource.getRating() > 5) {
                errors.add("资源评分必须在0-5之间，当前值: " + resource.getRating());
            }

            // 检查精度，最多保留2位小数
            String ratingStr = String.valueOf(resource.getRating());
            int decimalIndex = ratingStr.indexOf('.');
            if (decimalIndex != -1 && (ratingStr.length() - decimalIndex - 1) > 2) {
                errors.add("资源评分最多保留2位小数，当前值: " + resource.getRating());
            }
        }

        if (resource.getRatingCount() != null && resource.getRatingCount() < 0) {
            errors.add("评分人数不能为负数，当前值: " + resource.getRatingCount());
        }

        if (resource.getIsFree() != null) {
            // 布尔值不需要特别检查，但可以加入业务逻辑检查
            if (!resource.getIsFree() && StringUtils.isEmpty(resource.getPrice())) {
                errors.add("非免费资源必须设置价格");
            }
        }

        // 检查JSON格式字段的一致性
        checkJsonFieldsConsistency(resource, errors);

        // 如果有错误，抛出异常
        if (!errors.isEmpty()) {
            throw new ServiceException("数据库兼容性检查失败: " + String.join("; ", errors));
        }
    }

    /**
     * 检查字段长度
     *
     * @param errors 错误列表
     * @param fieldName 字段名称
     * @param value 字段值
     * @param maxLength 最大长度
     */
    private void checkFieldLength(List<String> errors, String fieldName, String value, int maxLength) {
        if (value != null && value.length() > maxLength) {
            errors.add(fieldName + "长度超过数据库限制(" + maxLength + "个字符), 当前长度: " + value.length());
        }
    }

    /**
     * 检查JSON字段的一致性
     *
     * @param resource 学习资源
     * @param errors 错误列表
     */
    private void checkJsonFieldsConsistency(LearningResource resource, List<String> errors) {
        // 检查标签字段
        if (resource.getTags() != null) {
            for (String tag : resource.getTags()) {
                if (tag == null) {
                    errors.add("标签不能包含空值");
                }
            }
        }

        // 检查前置条件字段
        if (resource.getPrerequisites() != null) {
            for (String prerequisite : resource.getPrerequisites()) {
                if (prerequisite == null) {
                    errors.add("前置条件不能包含空值");
                }
            }
        }

        // 检查学习目标字段
        if (resource.getLearningObjectives() != null) {
            for (String objective : resource.getLearningObjectives()) {
                if (objective == null) {
                    errors.add("学习目标不能包含空值");
                }
            }
        }

        // 检查元数据字段
        if (resource.getMetadata() != null && resource.getMetadata().isEmpty()) {
            errors.add("元数据不能为空JSON对象");
        }

        // 检查质量评估字段
        if (resource.getQualityAssessment() != null) {
            LearningResource.QualityAssessment qa = resource.getQualityAssessment();
            // 检查日期格式
            if (qa.getAssessmentDate() != null) {
                try {
                    DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").parse(qa.getAssessmentDate());
                } catch (Exception e) {
                    errors.add("质量评估日期格式无效，应为: yyyy-MM-dd HH:mm:ss");
                }
            }
        }

        // 检查使用统计字段
        if (resource.getUsageStatistics() != null) {
            LearningResource.UsageStatistics stats = resource.getUsageStatistics();
            // 检查日期格式
            if (stats.getLastUpdated() != null) {
                try {
                    DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").parse(stats.getLastUpdated());
                } catch (Exception e) {
                    errors.add("使用统计最后更新日期格式无效，应为: yyyy-MM-dd HH:mm:ss");
                }
            }

            // 检查完成率一致性
            if (stats.getLearnerCount() != null && stats.getLearnerCount() > 0 &&
                stats.getCompletionCount() != null && stats.getCompletionRate() != null) {
                double expectedRate = (double) stats.getCompletionCount() / stats.getLearnerCount() * 100;
                if (Math.abs(stats.getCompletionRate() - expectedRate) > 0.01) {
                    stats.setCompletionRate(expectedRate); // 自动修正完成率
                }
            }
        }
    }

    @Override
    @Transactional
    public boolean createResource(LearningResource resource) {
        try {
            log.info("创建学习资源: {}", resource.getTitle());

            // 数据校验
            validateResource(resource, true);

            // 数据库兼容性检查
            checkDatabaseCompatibility(resource);

            // 设置默认值
            if (resource.getStatus() == null) {
                resource.setStatus("active");
            }
            if (resource.getRating() == null) {
                resource.setRating(0.0);
            }
            if (resource.getRatingCount() == null) {
                resource.setRatingCount(0);
            }
            if (resource.getIsFree() == null) {
                resource.setIsFree(true);
            }

            // 初始化使用统计
            if (resource.getUsageStatistics() == null) {
                LearningResource.UsageStatistics stats = new LearningResource.UsageStatistics();
                stats.setViewCount(0L);
                stats.setLearnerCount(0L);
                stats.setCompletionCount(0L);
                stats.setRecommendationCount(0L);
                stats.setFavoriteCount(0L);
                stats.setShareCount(0L);
                stats.setCompletionRate(0.0);
                stats.setLastUpdated(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                resource.setUsageStatistics(stats);
            }

            int result = resourceMapper.insert(resource);
            log.info("学习资源创建成功，ID: {}", resource.getId());
            return result > 0;

        } catch (Exception e) {
            log.error("创建学习资源失败", e);
            throw new ServiceException("创建学习资源失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean updateResource(LearningResource resource) {
        try {
            log.info("更新学习资源: {}", resource.getId());

            // 验证资源存在
            if (resource.getId() == null) {
                throw new ServiceException("资源ID不能为空");
            }

            LearningResource existingResource = resourceMapper.selectById(resource.getId());
            if (existingResource == null) {
                throw new ServiceException("资源不存在，ID: " + resource.getId());
            }

            // 数据校验
            validateResource(resource, false);

            // 数据库兼容性检查
            checkDatabaseCompatibility(resource);

            int result = resourceMapper.updateById(resource);
            log.info("学习资源更新成功");
            return result > 0;

        } catch (Exception e) {
            log.error("更新学习资源失败", e);
            throw new ServiceException("更新学习资源失败: " + e.getMessage());
        }
    }

    /**
     * 验证学习资源数据
     *
     * @param resource 学习资源
     * @param isCreate 是否为创建操作
     */
    private void validateResource(LearningResource resource, boolean isCreate) {
        List<String> errors = new ArrayList<>();

        // 必填字段校验
        if (isCreate || resource.getTitle() != null) {
            if (StringUtils.isBlank(resource.getTitle())) {
                errors.add("资源标题不能为空");
            } else if (resource.getTitle().length() > MAX_TITLE_LENGTH) {
                errors.add("资源标题长度不能超过" + MAX_TITLE_LENGTH + "个字符");
            }
        }

        if (isCreate || resource.getType() != null) {
            if (StringUtils.isBlank(resource.getType())) {
                errors.add("资源类型不能为空");
            } else {
                if (resource.getType().length() > MAX_TYPE_LENGTH) {
                    errors.add("资源类型长度不能超过" + MAX_TYPE_LENGTH + "个字符");
                }
                if (!VALID_RESOURCE_TYPES.contains(resource.getType())) {
                    errors.add("无效的资源类型，有效值为: " + String.join(", ", VALID_RESOURCE_TYPES));
                }
            }
        }

        // 非必填字段长度校验
        if (resource.getUrl() != null && resource.getUrl().length() > MAX_URL_LENGTH) {
            errors.add("资源URL长度不能超过" + MAX_URL_LENGTH + "个字符");
        }

        if (resource.getDuration() != null && resource.getDuration().length() > MAX_DURATION_LENGTH) {
            errors.add("学习时长长度不能超过" + MAX_DURATION_LENGTH + "个字符");
        }

        if (resource.getDifficulty() != null) {
            if (resource.getDifficulty().length() > MAX_DIFFICULTY_LENGTH) {
                errors.add("难度等级长度不能超过" + MAX_DIFFICULTY_LENGTH + "个字符");
            }
            if (!VALID_DIFFICULTIES.contains(resource.getDifficulty())) {
                errors.add("无效的难度等级，有效值为: " + String.join(", ", VALID_DIFFICULTIES));
            }
        }

        if (resource.getSkillArea() != null && resource.getSkillArea().length() > MAX_SKILL_AREA_LENGTH) {
            errors.add("技能领域长度不能超过" + MAX_SKILL_AREA_LENGTH + "个字符");
        }

        if (resource.getCategory() != null && resource.getCategory().length() > MAX_CATEGORY_LENGTH) {
            errors.add("资源分类长度不能超过" + MAX_CATEGORY_LENGTH + "个字符");
        }

        if (resource.getRating() != null) {
            if (resource.getRating() < 0 || resource.getRating() > 5) {
                errors.add("资源评分必须在0-5之间");
            }
        }

        if (resource.getProvider() != null && resource.getProvider().length() > MAX_PROVIDER_LENGTH) {
            errors.add("资源提供者长度不能超过" + MAX_PROVIDER_LENGTH + "个字符");
        }

        if (resource.getPrice() != null && resource.getPrice().length() > MAX_PRICE_LENGTH) {
            errors.add("价格长度不能超过" + MAX_PRICE_LENGTH + "个字符");
        }

        if (resource.getLanguage() != null && resource.getLanguage().length() > MAX_LANGUAGE_LENGTH) {
            errors.add("语言长度不能超过" + MAX_LANGUAGE_LENGTH + "个字符");
        }

        if (resource.getStatus() != null) {
            if (resource.getStatus().length() > MAX_STATUS_LENGTH) {
                errors.add("资源状态长度不能超过" + MAX_STATUS_LENGTH + "个字符");
            }
            if (!VALID_STATUSES.contains(resource.getStatus())) {
                errors.add("无效的资源状态，有效值为: " + String.join(", ", VALID_STATUSES));
            }
        }

        // JSON字段校验
        validateJsonFields(resource, errors);

        // 如果有错误，抛出异常
        if (!errors.isEmpty()) {
            throw new ServiceException(String.join("; ", errors));
        }
    }

    /**
     * 验证JSON格式字段
     *
     * @param resource 学习资源
     * @param errors 错误列表
     */
    private void validateJsonFields(LearningResource resource, List<String> errors) {
        // 标签字段验证
        if (resource.getTags() != null) {
            // 验证标签数量不超过限制
            if (resource.getTags().size() > 20) {
                errors.add("标签数量不能超过20个");
            }

            // 验证每个标签的长度
            for (String tag : resource.getTags()) {
                if (tag != null && tag.length() > 50) {
                    errors.add("标签长度不能超过50个字符: " + tag);
                }
            }
        }

        // 前置条件字段验证
        if (resource.getPrerequisites() != null) {
            if (resource.getPrerequisites().size() > 15) {
                errors.add("前置条件数量不能超过15个");
            }

            for (String prerequisite : resource.getPrerequisites()) {
                if (prerequisite != null && prerequisite.length() > 200) {
                    errors.add("前置条件描述长度不能超过200个字符");
                }
            }
        }

        // 学习目标字段验证
        if (resource.getLearningObjectives() != null) {
            if (resource.getLearningObjectives().size() > 15) {
                errors.add("学习目标数量不能超过15个");
            }

            for (String objective : resource.getLearningObjectives()) {
                if (objective != null && objective.length() > 200) {
                    errors.add("学习目标描述长度不能超过200个字符");
                }
            }
        }

        // 元数据字段验证
        if (resource.getMetadata() != null) {
            // 元数据结构验证逻辑（如果有特定结构要求）
            // 这里只是简单示例，实际应根据元数据的具体结构进行验证
            if (resource.getMetadata().size() > 50) {
                errors.add("元数据字段数量不能超过50个");
            }
        }

        // 质量评估字段校验
        if (resource.getQualityAssessment() != null) {
            validateQualityAssessment(resource.getQualityAssessment());
        }

        // 使用统计字段校验
        if (resource.getUsageStatistics() != null) {
            LearningResource.UsageStatistics stats = resource.getUsageStatistics();

            // 验证数值字段非负
            if (stats.getViewCount() != null && stats.getViewCount() < 0) {
                errors.add("浏览次数不能为负数");
            }

            if (stats.getLearnerCount() != null && stats.getLearnerCount() < 0) {
                errors.add("学习人数不能为负数");
            }

            if (stats.getCompletionCount() != null && stats.getCompletionCount() < 0) {
                errors.add("完成人数不能为负数");
            }

            if (stats.getCompletionRate() != null && (stats.getCompletionRate() < 0 || stats.getCompletionRate() > 100)) {
                errors.add("完成率必须在0-100之间");
            }
        }
    }

    @Override
    @Transactional
    public boolean deleteResource(Long resourceId) {
        try {
            log.info("删除学习资源: {}", resourceId);

            // 验证资源存在
            if (resourceId == null) {
                throw new ServiceException("资源ID不能为空");
            }

            LearningResource existingResource = resourceMapper.selectById(resourceId);
            if (existingResource == null) {
                throw new ServiceException("资源不存在，ID: " + resourceId);
            }

            // 软删除，将状态设置为不活跃
            LearningResource resource = new LearningResource();
            resource.setId(resourceId);
            resource.setStatus("inactive");

            int result = resourceMapper.updateById(resource);
            log.info("学习资源删除成功");
            return result > 0;

        } catch (Exception e) {
            log.error("删除学习资源失败", e);
            throw new ServiceException("删除学习资源失败: " + e.getMessage());
        }
    }

    @Override
    public LearningResource getResourceById(Long resourceId) {
        try {
            // 验证参数
            if (resourceId == null) {
                throw new ServiceException("资源ID不能为空");
            }

            LearningResource resource = resourceMapper.selectById(resourceId);
            if (resource != null && "active".equals(resource.getStatus())) {
                // 增加浏览次数
                incrementViewCount(resourceId);
                return resource;
            }
            return null;
        } catch (Exception e) {
            log.error("获取学习资源失败", e);
            throw new ServiceException("获取学习资源失败: " + e.getMessage());
        }
    }

    @Override
    public Page<LearningResource> getResourcesPage(Integer pageNum, Integer pageSize,
                                                  String skillArea, String type, String difficulty) {
        try {
            Page<LearningResource> page = new Page<>(pageNum, pageSize);
            LambdaQueryWrapper<LearningResource> wrapper = new LambdaQueryWrapper<LearningResource>()
                .eq(LearningResource::getStatus, "active")
                .eq(skillArea != null, LearningResource::getSkillArea, skillArea)
                .eq(type != null, LearningResource::getType, type)
                .eq(difficulty != null, LearningResource::getDifficulty, difficulty)
                .orderByDesc(LearningResource::getRating)
                .orderByDesc(LearningResource::getRatingCount);

            return resourceMapper.selectPage(page, wrapper);
        } catch (Exception e) {
            log.error("分页查询学习资源失败", e);
            throw new ServiceException("分页查询学习资源失败: " + e.getMessage());
        }
    }

    @Override
    public List<LearningResource> getRecommendedResources(String skillArea, String difficulty, Integer limit) {
        try {
            if (limit == null || limit <= 0) {
                limit = 10;
            }

            // 如果指定了技能领域和难度，使用精确匹配
            if (skillArea != null && difficulty != null) {
                return resourceMapper.selectBySkillAreaAndDifficulty(skillArea, difficulty, limit);
            }

            // 否则使用智能推荐算法
            return resourceMapper.selectRecommendedResources(null, limit);
        } catch (Exception e) {
            log.error("获取推荐资源失败", e);
            throw new ServiceException("获取推荐资源失败: " + e.getMessage());
        }
    }

    @Override
    public List<LearningResource> getResourcesByType(String type, Integer limit) {
        try {
            if (limit == null || limit <= 0) {
                limit = 10;
            }
            return resourceMapper.selectByType(type, limit);
        } catch (Exception e) {
            log.error("根据类型获取资源失败", e);
            throw new ServiceException("根据类型获取资源失败: " + e.getMessage());
        }
    }

    @Override
    public List<LearningResource> getPopularResources(Integer limit) {
        try {
            if (limit == null || limit <= 0) {
                limit = 10;
            }
            return resourceMapper.selectPopularResources(limit);
        } catch (Exception e) {
            log.error("获取热门资源失败", e);
            throw new ServiceException("获取热门资源失败: " + e.getMessage());
        }
    }

    @Override
    public List<LearningResource> getFreeResources(String skillArea, Integer limit) {
        try {
            if (limit == null || limit <= 0) {
                limit = 10;
            }
            return resourceMapper.selectFreeResources(skillArea, limit);
        } catch (Exception e) {
            log.error("获取免费资源失败", e);
            throw new ServiceException("获取免费资源失败: " + e.getMessage());
        }
    }

    @Override
    public List<LearningResource> getResourcesByTag(String tag, Integer limit) {
        try {
            if (limit == null || limit <= 0) {
                limit = 10;
            }
            return resourceMapper.selectByTag(tag, limit);
        } catch (Exception e) {
            log.error("根据标签获取资源失败", e);
            throw new ServiceException("根据标签获取资源失败: " + e.getMessage());
        }
    }

    @Override
    public List<LearningResource> searchResources(String keyword, Integer limit) {
        try {
            if (limit == null || limit <= 0) {
                limit = 20;
            }
            return resourceMapper.searchResources(keyword, limit);
        } catch (Exception e) {
            log.error("搜索资源失败", e);
            throw new ServiceException("搜索资源失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean assessResourceQuality(Long resourceId, LearningResource.QualityAssessment qualityAssessment) {
        try {
            log.info("评估资源质量: {}", resourceId);

            // 验证参数
            if (resourceId == null) {
                throw new ServiceException("资源ID不能为空");
            }
            if (qualityAssessment == null) {
                throw new ServiceException("质量评估信息不能为空");
            }

            // 验证评估分数范围
            validateQualityAssessment(qualityAssessment);

            LearningResource resource = resourceMapper.selectById(resourceId);
            if (resource == null) {
                throw new ServiceException("资源不存在");
            }

            // 设置评估时间
            qualityAssessment.setAssessmentDate(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

            // 计算综合质量评分
            double overallQuality = calculateOverallQuality(qualityAssessment);
            qualityAssessment.setOverallQuality(overallQuality);

            resource.setQualityAssessment(qualityAssessment);

            int result = resourceMapper.updateById(resource);
            log.info("资源质量评估完成");
            return result > 0;

        } catch (Exception e) {
            log.error("评估资源质量失败", e);
            throw new ServiceException("评估资源质量失败: " + e.getMessage());
        }
    }

    /**
     * 验证质量评估信息
     * @param qualityAssessment 质量评估信息
     */
    private void validateQualityAssessment(LearningResource.QualityAssessment qualityAssessment) {
        List<String> errors = new ArrayList<>();

        // 验证评分范围(0-5分)
        if (qualityAssessment.getContentQuality() != null) {
            if (qualityAssessment.getContentQuality() < 0 || qualityAssessment.getContentQuality() > 5) {
                errors.add("内容质量评分必须在0-5之间");
            }
        }

        if (qualityAssessment.getTeachingEffectiveness() != null) {
            if (qualityAssessment.getTeachingEffectiveness() < 0 || qualityAssessment.getTeachingEffectiveness() > 5) {
                errors.add("教学效果评分必须在0-5之间");
            }
        }

        if (qualityAssessment.getTimeliness() != null) {
            if (qualityAssessment.getTimeliness() < 0 || qualityAssessment.getTimeliness() > 5) {
                errors.add("时效性评分必须在0-5之间");
            }
        }

        if (qualityAssessment.getUserExperience() != null) {
            if (qualityAssessment.getUserExperience() < 0 || qualityAssessment.getUserExperience() > 5) {
                errors.add("用户体验评分必须在0-5之间");
            }
        }

        if (!errors.isEmpty()) {
            throw new ServiceException(String.join("; ", errors));
        }
    }

    @Override
    @Transactional
    public boolean updateResourceRating(Long resourceId, Double rating) {
        try {
            log.info("更新资源评分: {}, 评分: {}", resourceId, rating);

            // 验证资源ID不为空
            if (resourceId == null) {
                throw new ServiceException("资源ID不能为空");
            }

            // 验证评分范围
            if (rating == null) {
                throw new ServiceException("评分不能为空");
            }
            if (rating < 1 || rating > 5) {
                throw new ServiceException("评分必须在1-5之间");
            }

            LearningResource resource = resourceMapper.selectById(resourceId);
            if (resource == null) {
                throw new ServiceException("资源不存在");
            }

            // 计算新的平均评分
            double currentRating = resource.getRating() != null ? resource.getRating() : 0.0;
            int currentCount = resource.getRatingCount() != null ? resource.getRatingCount() : 0;

            double newRating = (currentRating * currentCount + rating) / (currentCount + 1);

            resource.setRating(newRating);
            resource.setRatingCount(currentCount + 1);

            int result = resourceMapper.updateById(resource);
            log.info("资源评分更新成功");
            return result > 0;

        } catch (Exception e) {
            log.error("更新资源评分失败", e);
            throw new ServiceException("更新资源评分失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean incrementViewCount(Long resourceId) {
        try {
            // 验证参数
            if (resourceId == null) {
                throw new ServiceException("资源ID不能为空");
            }

            LearningResource resource = resourceMapper.selectById(resourceId);
            if (resource == null) {
                throw new ServiceException("资源不存在，ID: " + resourceId);
            }

            LearningResource.UsageStatistics stats = resource.getUsageStatistics();
            if (stats == null) {
                stats = new LearningResource.UsageStatistics();
                stats.setViewCount(0L);
            }

            stats.setViewCount(stats.getViewCount() + 1);
            stats.setLastUpdated(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

            resource.setUsageStatistics(stats);

            return resourceMapper.updateById(resource) > 0;

        } catch (ServiceException e) {
            log.error("增加浏览次数失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("增加浏览次数失败", e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean incrementLearnerCount(Long resourceId) {
        try {
            // 验证参数
            if (resourceId == null) {
                throw new ServiceException("资源ID不能为空");
            }

            LearningResource resource = resourceMapper.selectById(resourceId);
            if (resource == null) {
                throw new ServiceException("资源不存在，ID: " + resourceId);
            }

            LearningResource.UsageStatistics stats = resource.getUsageStatistics();
            if (stats == null) {
                stats = new LearningResource.UsageStatistics();
                stats.setLearnerCount(0L);
            }

            stats.setLearnerCount(stats.getLearnerCount() + 1);
            stats.setLastUpdated(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

            resource.setUsageStatistics(stats);

            return resourceMapper.updateById(resource) > 0;

        } catch (ServiceException e) {
            log.error("增加学习人数失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("增加学习人数失败", e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean incrementCompletionCount(Long resourceId) {
        try {
            // 验证参数
            if (resourceId == null) {
                throw new ServiceException("资源ID不能为空");
            }

            LearningResource resource = resourceMapper.selectById(resourceId);
            if (resource == null) {
                throw new ServiceException("资源不存在，ID: " + resourceId);
            }

            LearningResource.UsageStatistics stats = resource.getUsageStatistics();
            if (stats == null) {
                stats = new LearningResource.UsageStatistics();
                stats.setCompletionCount(0L);
                stats.setLearnerCount(0L);
            }

            stats.setCompletionCount(stats.getCompletionCount() + 1);

            // 更新完成率
            if (stats.getLearnerCount() > 0) {
                double completionRate = (double) stats.getCompletionCount() / stats.getLearnerCount() * 100;
                stats.setCompletionRate(completionRate);
            }

            stats.setLastUpdated(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

            resource.setUsageStatistics(stats);

            return resourceMapper.updateById(resource) > 0;

        } catch (ServiceException e) {
            log.error("增加完成人数失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("增加完成人数失败", e);
            return false;
        }
    }

    @Override
    public Map<String, Object> getResourceStatistics() {
        try {
            return resourceMapper.selectResourceStatistics();
        } catch (Exception e) {
            log.error("获取资源统计信息失败", e);
            throw new ServiceException("获取资源统计信息失败: " + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getSkillAreaDistribution() {
        try {
            return resourceMapper.selectSkillAreaDistribution();
        } catch (Exception e) {
            log.error("获取技能领域分布失败", e);
            throw new ServiceException("获取技能领域分布失败: " + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getResourceTypeDistribution() {
        try {
            return resourceMapper.selectResourceTypeDistribution();
        } catch (Exception e) {
            log.error("获取资源类型分布失败", e);
            throw new ServiceException("获取资源类型分布失败: " + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getTopProviders(Integer limit) {
        try {
            if (limit == null || limit <= 0) {
                limit = 10;
            }
            return resourceMapper.selectTopProviders(limit);
        } catch (Exception e) {
            log.error("获取顶级提供者失败", e);
            throw new ServiceException("获取顶级提供者失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public int batchImportResources(List<LearningResource> resources) {
        try {
            log.info("批量导入资源，数量: {}", resources.size());

            // 验证输入列表不为空
            if (resources == null || resources.isEmpty()) {
                throw new ServiceException("导入的资源列表不能为空");
            }

            int successCount = 0;
            List<String> errors = new ArrayList<>();

            for (int i = 0; i < resources.size(); i++) {
                LearningResource resource = resources.get(i);
                try {
                    // 对每个资源进行验证
                    validateResource(resource, true);
                    if (createResource(resource)) {
                        successCount++;
                    }
                } catch (Exception e) {
                    String errorMsg = "第" + (i + 1) + "条资源导入失败: " +
                        (resource.getTitle() != null ? resource.getTitle() : "无标题") +
                        ", 原因: " + e.getMessage();
                    log.warn(errorMsg);
                    errors.add(errorMsg);
                }
            }

            log.info("批量导入完成，成功: {}, 总数: {}", successCount, resources.size());

            // 如果全部失败，抛出异常
            if (successCount == 0 && !resources.isEmpty()) {
                throw new ServiceException("批量导入全部失败: " + String.join("; ", errors));
            }

            return successCount;

        } catch (Exception e) {
            log.error("批量导入资源失败", e);
            throw new ServiceException("批量导入资源失败: " + e.getMessage());
        }
    }

    @Override
    public List<LearningResource> sortResourcesByRecommendation(List<LearningResource> resources,
                                                               Map<String, Object> userPreferences) {
        try {
            if (resources == null || resources.isEmpty()) {
                return resources;
            }

            // 根据用户偏好进行排序
            return resources.stream()
                .sorted((r1, r2) -> {
                    double score1 = calculateRecommendationScore(r1, userPreferences);
                    double score2 = calculateRecommendationScore(r2, userPreferences);
                    return Double.compare(score2, score1); // 降序排列
                })
                .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("资源推荐排序失败", e);
            return resources; // 排序失败时返回原列表
        }
    }

    @Override
    public List<LearningResource> getSimilarResources(Long resourceId, Integer limit) {
        try {
            if (limit == null || limit <= 0) {
                limit = 5;
            }

            LearningResource targetResource = resourceMapper.selectById(resourceId);
            if (targetResource == null) {
                return new ArrayList<>();
            }

            // 根据技能领域和类型查找相似资源
            LambdaQueryWrapper<LearningResource> wrapper = new LambdaQueryWrapper<LearningResource>()
                .eq(LearningResource::getStatus, "active")
                .ne(LearningResource::getId, resourceId)
                .and(w -> w.eq(LearningResource::getSkillArea, targetResource.getSkillArea())
                         .or()
                         .eq(LearningResource::getType, targetResource.getType()))
                .orderByDesc(LearningResource::getRating)
                .last("LIMIT " + limit);

            return resourceMapper.selectList(wrapper);

        } catch (Exception e) {
            log.error("获取相似资源失败", e);
            throw new ServiceException("获取相似资源失败: " + e.getMessage());
        }
    }

    @Override
    public LearningResource.QualityAssessment performQualityAssessment(Long resourceId) {
        try {
            log.info("执行资源质量评估: {}", resourceId);

            LearningResource resource = resourceMapper.selectById(resourceId);
            if (resource == null) {
                throw new ServiceException("资源不存在");
            }

            LearningResource.QualityAssessment assessment = new LearningResource.QualityAssessment();

            // 基于现有数据进行质量评估
            // 内容质量评分（基于评分和评分人数）
            double contentQuality = calculateContentQuality(resource);
            assessment.setContentQuality(contentQuality);

            // 教学效果评分（基于完成率）
            double teachingEffectiveness = calculateTeachingEffectiveness(resource);
            assessment.setTeachingEffectiveness(teachingEffectiveness);

            // 更新及时性评分（基于创建时间和更新时间）
            double timeliness = calculateTimeliness(resource);
            assessment.setTimeliness(timeliness);

            // 用户体验评分（基于浏览量和学习人数比例）
            double userExperience = calculateUserExperience(resource);
            assessment.setUserExperience(userExperience);

            // 综合质量评分
            double overallQuality = calculateOverallQuality(assessment);
            assessment.setOverallQuality(overallQuality);

            assessment.setAssessmentDate(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            assessment.setNotes("系统自动评估");

            // 保存评估结果
            assessResourceQuality(resourceId, assessment);

            log.info("资源质量评估完成，综合评分: {}", overallQuality);
            return assessment;

        } catch (Exception e) {
            log.error("执行资源质量评估失败", e);
            throw new ServiceException("执行资源质量评估失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Integer> getResourceTagCloud(Integer limit) {
        try {
            if (limit == null || limit <= 0) {
                limit = 50;
            }

            // 获取所有活跃资源的标签
            LambdaQueryWrapper<LearningResource> wrapper = new LambdaQueryWrapper<LearningResource>()
                .eq(LearningResource::getStatus, "active")
                .isNotNull(LearningResource::getTags);

            List<LearningResource> resources = resourceMapper.selectList(wrapper);

            // 统计标签频次
            Map<String, Integer> tagCount = new HashMap<>();
            for (LearningResource resource : resources) {
                if (resource.getTags() != null) {
                    for (String tag : resource.getTags()) {
                        tagCount.put(tag, tagCount.getOrDefault(tag, 0) + 1);
                    }
                }
            }

            // 按频次排序并限制数量
            return tagCount.entrySet().stream()
                .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                .limit(limit)
                .collect(Collectors.toMap(
                    Map.Entry::getKey,
                    Map.Entry::getValue,
                    (e1, e2) -> e1,
                    LinkedHashMap::new
                ));

        } catch (Exception e) {
            log.error("获取资源标签云失败", e);
            throw new ServiceException("获取资源标签云失败: " + e.getMessage());
        }
    }

    @Override
    public List<LearningResource> getPersonalizedRecommendations(Long userId, Integer limit) {
        try {
            if (limit == null || limit <= 0) {
                limit = 10;
            }

            log.info("获取用户个性化推荐资源，用户ID: {}, 限制数量: {}", userId, limit);
            return resourceMapper.selectRecommendedResources(userId, limit);

        } catch (Exception e) {
            log.error("获取个性化推荐资源失败", e);
            throw new ServiceException("获取个性化推荐资源失败: " + e.getMessage());
        }
    }

    @Override
    public List<LearningResource> getResourcesBySkillGaps(List<String> skillAreas, String difficulty, Integer limit) {
        try {
            if (limit == null || limit <= 0) {
                limit = 10;
            }

            log.info("根据技能差距推荐资源，技能领域: {}, 难度: {}, 限制数量: {}", skillAreas, difficulty, limit);
            return resourceMapper.selectResourcesBySkillGaps(skillAreas, difficulty, limit);

        } catch (Exception e) {
            log.error("根据技能差距推荐资源失败", e);
            throw new ServiceException("根据技能差距推荐资源失败: " + e.getMessage());
        }
    }

    @Override
    public List<LearningResource> getCollaborativeFilteringRecommendations(Long userId, Integer limit) {
        try {
            if (limit == null || limit <= 0) {
                limit = 10;
            }

            log.info("获取协同过滤推荐资源，用户ID: {}, 限制数量: {}", userId, limit);
            return resourceMapper.selectCollaborativeFilteringResources(userId, limit);

        } catch (Exception e) {
            log.error("获取协同过滤推荐资源失败", e);
            throw new ServiceException("获取协同过滤推荐资源失败: " + e.getMessage());
        }
    }

    @Override
    public List<LearningResource> getTrendingResources(Integer days, Integer limit) {
        try {
            if (days == null || days <= 0) {
                days = 30; // 默认30天
            }
            if (limit == null || limit <= 0) {
                limit = 10;
            }

            log.info("获取趋势资源，天数: {}, 限制数量: {}", days, limit);
            return resourceMapper.selectTrendingResources(days, limit);

        } catch (Exception e) {
            log.error("获取趋势资源失败", e);
            throw new ServiceException("获取趋势资源失败: " + e.getMessage());
        }
    }

    // ========== 私有辅助方法 ==========

    /**
     * 计算综合质量评分
     */
    private double calculateOverallQuality(LearningResource.QualityAssessment assessment) {
        double contentWeight = 0.3;
        double teachingWeight = 0.3;
        double timelinessWeight = 0.2;
        double userExperienceWeight = 0.2;

        double contentQuality = assessment.getContentQuality() != null ? assessment.getContentQuality() : 0.0;
        double teachingEffectiveness = assessment.getTeachingEffectiveness() != null ? assessment.getTeachingEffectiveness() : 0.0;
        double timeliness = assessment.getTimeliness() != null ? assessment.getTimeliness() : 0.0;
        double userExperience = assessment.getUserExperience() != null ? assessment.getUserExperience() : 0.0;

        return contentQuality * contentWeight +
               teachingEffectiveness * teachingWeight +
               timeliness * timelinessWeight +
               userExperience * userExperienceWeight;
    }

    /**
     * 计算内容质量评分
     */
    private double calculateContentQuality(LearningResource resource) {
        double rating = resource.getRating() != null ? resource.getRating() : 0.0;
        int ratingCount = resource.getRatingCount() != null ? resource.getRatingCount() : 0;

        // 基于评分和评分人数计算内容质量
        if (ratingCount == 0) {
            return 3.0; // 默认中等质量
        }

        // 评分权重随评分人数增加而增加，但有上限
        double countWeight = Math.min(1.0, ratingCount / 100.0);
        return rating * countWeight + 3.0 * (1 - countWeight);
    }

    /**
     * 计算教学效果评分
     */
    private double calculateTeachingEffectiveness(LearningResource resource) {
        if (resource.getUsageStatistics() == null) {
            return 3.0; // 默认中等效果
        }

        LearningResource.UsageStatistics stats = resource.getUsageStatistics();
        Double completionRate = stats.getCompletionRate();

        if (completionRate == null || completionRate == 0.0) {
            return 3.0;
        }

        // 完成率转换为5分制评分
        return Math.min(5.0, completionRate / 20.0); // 100%完成率对应5分
    }

    /**
     * 计算更新及时性评分
     */
    private double calculateTimeliness(LearningResource resource) {
        // 简化实现：基于创建时间计算
        // 实际应该考虑内容的更新频率和行业变化速度
        return 4.0; // 默认较好的及时性
    }

    /**
     * 计算用户体验评分
     */
    private double calculateUserExperience(LearningResource resource) {
        if (resource.getUsageStatistics() == null) {
            return 3.0; // 默认中等体验
        }

        LearningResource.UsageStatistics stats = resource.getUsageStatistics();
        Long viewCount = stats.getViewCount() != null ? stats.getViewCount() : 0L;
        Long learnerCount = stats.getLearnerCount() != null ? stats.getLearnerCount() : 0L;

        if (viewCount == 0) {
            return 3.0;
        }

        // 学习转化率：浏览者中有多少人开始学习
        double conversionRate = learnerCount.doubleValue() / viewCount.doubleValue();

        // 转化率转换为5分制评分
        return Math.min(5.0, conversionRate * 20 + 2.0); // 基础分2分，转化率加分
    }

    /**
     * 计算推荐评分
     */
    private double calculateRecommendationScore(LearningResource resource, Map<String, Object> userPreferences) {
        double score = 0.0;

        // 基础评分（资源评分）
        score += (resource.getRating() != null ? resource.getRating() : 0.0) * 20;

        // 用户偏好加分
        if (userPreferences != null) {
            // 免费资源偏好
            Boolean preferFree = (Boolean) userPreferences.get("preferFreeResources");
            if (preferFree != null && preferFree && resource.getIsFree()) {
                score += 10;
            }

            // 语言偏好
            String preferredLanguage = (String) userPreferences.get("preferredLanguage");
            if (preferredLanguage != null && preferredLanguage.equals(resource.getLanguage())) {
                score += 5;
            }

            // 资源类型偏好
            String preferredType = (String) userPreferences.get("preferredResourceType");
            if (preferredType != null && preferredType.equals(resource.getType())) {
                score += 8;
            }
        }

        // 热门程度加分
        if (resource.getUsageStatistics() != null) {
            Long viewCount = resource.getUsageStatistics().getViewCount();
            if (viewCount != null && viewCount > 1000) {
                score += Math.min(15, viewCount / 1000.0); // 浏览量加分，最多15分
            }
        }

        return score;
    }

}
