{"doc": "\n 数据权限助手\r\n\r\n <AUTHOR>\r\n @version 3.5.0\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "getPermission", "paramTypes": [], "doc": "\n 获取当前执行mapper权限注解\r\n\r\n @return 返回当前执行mapper权限注解\r\n"}, {"name": "setPermission", "paramTypes": ["org.dromara.common.mybatis.annotation.DataPermission"], "doc": "\n 设置当前执行mapper权限注解\r\n\r\n @param dataPermission 数据权限注解\r\n"}, {"name": "removePermission", "paramTypes": [], "doc": "\n 删除当前执行mapper权限注解\r\n"}, {"name": "getVariable", "paramTypes": ["java.lang.String"], "doc": "\n 从上下文中获取指定键的变量值，并将其转换为指定的类型\r\n\r\n @param key 变量的键\r\n @param <T> 变量值的类型\r\n @return 指定键的变量值，如果不存在则返回 null\r\n"}, {"name": "setVariable", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": "\n 向上下文中设置指定键的变量值\r\n\r\n @param key   要设置的变量的键\r\n @param value 要设置的变量值\r\n"}, {"name": "getContext", "paramTypes": [], "doc": "\n 获取数据权限上下文\r\n\r\n @return 存储在SaStorage中的Map对象，用于存储数据权限相关的上下文信息\r\n @throws NullPointerException 如果数据权限上下文类型异常，则抛出NullPointerException\r\n"}, {"name": "enableIgnore", "paramTypes": [], "doc": "\n 开启忽略数据权限(开启后需手动调用 {@link #disableIgnore()} 关闭)\r\n"}, {"name": "disableIgnore", "paramTypes": [], "doc": "\n 关闭忽略数据权限\r\n"}, {"name": "ignore", "paramTypes": ["java.lang.Runnable"], "doc": "\n 在忽略数据权限中执行\r\n\r\n @param handle 处理执行方法\r\n"}, {"name": "ignore", "paramTypes": ["java.util.function.Supplier"], "doc": "\n 在忽略数据权限中执行\r\n\r\n @param handle 处理执行方法\r\n"}], "constructors": []}