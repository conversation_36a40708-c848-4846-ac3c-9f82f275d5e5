package org.dromara.app.handler;

import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.exception.InterviewException;
import org.dromara.common.core.domain.R;
import org.springframework.core.annotation.Order;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * 面试模块全局异常处理器
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Slf4j
@Order(1) // 优先级高于全局异常处理器
@RestControllerAdvice(basePackages = "org.dromara.app.controller")
public class InterviewExceptionHandler {

    /**
     * 面试业务异常
     */
    @ExceptionHandler(InterviewException.class)
    public R<Void> handleInterviewException(InterviewException e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',发生面试业务异常: {}", requestURI, e.getMessage());

        Integer code = e.getCode();
        return code != null ? R.fail(code, e.getMessage()) : R.fail(e.getMessage());
    }

    /**
     * 岗位不存在异常
     */
    @ExceptionHandler(InterviewException.JobNotFoundException.class)
    public R<Void> handleJobNotFoundException(InterviewException.JobNotFoundException e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.warn("请求地址'{}',岗位不存在: {}", requestURI, e.getMessage());
        return R.fail(404, e.getMessage());
    }

    /**
     * 面试模式不存在异常
     */
    @ExceptionHandler(InterviewException.InterviewModeNotFoundException.class)
    public R<Void> handleInterviewModeNotFoundException(InterviewException.InterviewModeNotFoundException e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.warn("请求地址'{}',面试模式不存在: {}", requestURI, e.getMessage());
        return R.fail(404, e.getMessage());
    }

    /**
     * 会话已过期异常
     */
    @ExceptionHandler(InterviewException.SessionExpiredException.class)
    public R<Void> handleSessionExpiredException(InterviewException.SessionExpiredException e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.warn("请求地址'{}',会话已过期: {}", requestURI, e.getMessage());
        return R.fail(410, e.getMessage());
    }

    /**
     * 设备检测失败异常
     */
    @ExceptionHandler(InterviewException.DeviceCheckFailedException.class)
    public R<Void> handleDeviceCheckFailedException(InterviewException.DeviceCheckFailedException e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',设备检测失败: {}", requestURI, e.getMessage());
        return R.fail(500, e.getMessage());
    }

    /**
     * 用户未登录异常
     */
    @ExceptionHandler(InterviewException.UserNotLoginException.class)
    public R<Void> handleUserNotLoginException(InterviewException.UserNotLoginException e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.warn("请求地址'{}',用户未登录: {}", requestURI, e.getMessage());
        return R.fail(401, e.getMessage());
    }

}
