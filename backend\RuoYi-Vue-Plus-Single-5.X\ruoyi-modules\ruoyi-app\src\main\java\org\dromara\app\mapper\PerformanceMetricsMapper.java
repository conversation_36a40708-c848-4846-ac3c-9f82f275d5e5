package org.dromara.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.dromara.app.domain.PerformanceMetrics;

import java.util.List;

/**
 * 性能指标Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@Mapper
public interface PerformanceMetricsMapper extends BaseMapper<PerformanceMetrics> {

    /**
     * 根据结果ID查询性能指标
     *
     * @param resultId 结果ID
     * @return 性能指标
     */
    PerformanceMetrics selectByResultId(@Param("resultId") String resultId);

    /**
     * 根据结果ID删除性能指标
     *
     * @param resultId 结果ID
     * @return 删除数量
     */
    int deleteByResultId(@Param("resultId") String resultId);

    /**
     * 查询用户性能指标历史记录
     *
     * @param userId 用户ID
     * @param limit  限制数量
     * @return 性能指标列表
     */
    List<PerformanceMetrics> selectHistoryByUserId(@Param("userId") Long userId, @Param("limit") Integer limit);

    /**
     * 查询行业平均性能指标
     *
     * @param industry 行业
     * @return 性能指标
     */
    PerformanceMetrics selectIndustryAverage(@Param("industry") String industry);

    /**
     * 查询用户性能指标平均值
     *
     * @param userId 用户ID
     * @return 用户性能指标平均值
     */
    PerformanceMetrics selectAverageMetricsByUserId(@Param("userId") Long userId);

    /**
     * 查询性能指标排名
     *
     * @param resultId 结果ID
     * @return 排名信息
     */
    Integer selectRankingByResultId(@Param("resultId") String resultId);

}
