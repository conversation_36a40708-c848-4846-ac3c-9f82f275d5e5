package org.dromara.app.service.avatar;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.ProtocolException;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Consumer;

/**
 * 讯飞数字人WebSocket连接管理服务
 *
 * <AUTHOR>
 * @date 2025-07-20
 */
@Slf4j
@Service
public class XunfeiAvatarWebSocketService {

    private WebSocket webSocket;
    private final AtomicBoolean isConnected = new AtomicBoolean(false);
    private CountDownLatch connectLatch;
    private CountDownLatch responseLatch;
    /**
     * -- SETTER --
     *  设置消息处理器
     *
     * @param messageHandler 消息处理器
     */
    @Setter
    private Consumer<JSONObject> messageHandler;
    /**
     * -- SETTER --
     *  设置错误处理器
     *
     * @param errorHandler 错误处理器
     */
    @Setter
    private Consumer<String> errorHandler;
    @Setter
    @Getter
    private String streamUrl;


    /**
     * 建立WebSocket连接
     *
     * @param requestUrl 请求URL（已包含认证信息）
     * @param timeout    连接超时时间（秒）
     * @return 是否连接成功
     */
    public boolean connect(String requestUrl, long timeout) {
        try {
            Request wsRequest = new Request.Builder().url(requestUrl).build();
            OkHttpClient okHttpClient = new OkHttpClient.Builder()
                    .connectTimeout(timeout, TimeUnit.SECONDS)
                    .readTimeout(timeout, TimeUnit.SECONDS)
                    .writeTimeout(timeout, TimeUnit.SECONDS)
                    .build();

            connectLatch = new CountDownLatch(1);
            this.webSocket = okHttpClient.newWebSocket(wsRequest, buildWebSocketListener());

            // 等待连接建立
            boolean connected = connectLatch.await(timeout, TimeUnit.SECONDS);
            if (!connected) {
                log.error("WebSocket连接超时");
                return false;
            }

            log.info("WebSocket连接成功");
            return isConnected.get();

        } catch (Exception e) {
            log.error("WebSocket连接失败", e);
            return false;
        }
    }

    /**
     * 发送消息
     *
     * @param request 请求消息
     * @return 是否发送成功
     */
    public boolean sendMessage(JSONObject request) {
        if (!isConnected.get()) {
            log.warn("WebSocket未连接，无法发送消息");
            return false;
        }

        try {
            String jsonStr = JSONUtil.toJsonStr(request);
            log.debug("发送消息: {}", jsonStr);
            return webSocket.send(jsonStr);
        } catch (Exception e) {
            log.error("发送消息失败", e);
            return false;
        }
    }

    /**
     * 发送启动请求并等待响应
     *
     * @param request 启动请求
     * @param timeout 超时时间（秒）
     * @return 是否启动成功
     */
    public boolean startAndWait(JSONObject request, long timeout) {
        if (!isConnected.get()) {
            log.warn("WebSocket未连接，无法发送启动请求");
            return false;
        }

        try {
            responseLatch = new CountDownLatch(1);
            boolean sent = sendMessage(request);
            if (!sent) {
                return false;
            }

            // 等待启动响应
            boolean received = responseLatch.await(timeout, TimeUnit.SECONDS);
            if (!received) {
                log.error("等待启动响应超时");
                return false;
            }

            log.info("数字人启动成功");
            return true;

        } catch (Exception e) {
            log.error("启动请求失败", e);
            return false;
        }
    }

    /**
     * 关闭连接
     */
    public void close() {
        if (webSocket != null) {
            isConnected.set(false);
            webSocket.close(1000, "正常关闭");
            log.info("WebSocket连接已关闭");
        }
    }

    /**
     * 检查连接状态
     *
     * @return 是否已连接
     */
    public boolean isConnected() {
        return isConnected.get();
    }

    /**
     * 构建WebSocket监听器
     */
    private WebSocketListener buildWebSocketListener() {
        return new WebSocketListener() {
            @Override
            public void onOpen(@NotNull WebSocket webSocket, @NotNull Response response) {
                log.info("WebSocket连接已打开");
                isConnected.set(true);
                if (connectLatch != null) {
                    connectLatch.countDown();
                }
            }

            @Override
            public void onMessage(@NotNull WebSocket webSocket, @NotNull String text) {
                log.info("收到WebSocket消息: {}", text);
                try {
                    JSONObject jsonObject = JSON.parseObject(text);
                    JSONObject header = jsonObject.getJSONObject("header");

                    if (header == null) {
                        log.warn("消息缺少header字段");
                        return;
                    }

                    int code = header.getIntValue("code");
                    String ctrl = header.getString("ctrl");

                    log.info("消息类型: ctrl={}, code={}", ctrl, code);

                    if (code != 0) {
                        String message = header.getString("message");
                        log.error("服务器返回错误: code={}, message={}", code, message);
                        onEvent(webSocket, 1002, message, "server error");
                        if (errorHandler != null) {
                            errorHandler.accept("服务器错误: " + message);
                        }
                        return;
                    }

                    // 检查推流地址
                    XunfeiAvatarWebSocketService.this.extractStreamUrl(jsonObject);

                    // 调用消息处理器
                    if (messageHandler != null) {
                        messageHandler.accept(jsonObject);
                    }

                } catch (Exception e) {
                    log.error("处理消息失败", e);
                }
            }

            @Override
            public void onClosing(@NotNull WebSocket webSocket, int code, @NotNull String reason) {
                onEvent(webSocket, code, reason, "onClosing");
            }

            @Override
            public void onClosed(@NotNull WebSocket webSocket, int code, @NotNull String reason) {
                onEvent(webSocket, code, reason, "onClosed");
            }

            @Override
            public void onFailure(@NotNull WebSocket webSocket, @NotNull Throwable tx, Response response) {
                Object error;
                try {
                    if (response != null && response.body() != null) {
                        String responseBody = response.body().string();
                        JSONObject body = JSON.parseObject(responseBody);
                        error = new ProtocolException(body.toString());
                    } else {
                        error = tx;
                    }
                } catch (IOException e) {
                    error = e;
                }
                log.error("WebSocket连接失败: {}", error);
                if (errorHandler != null) {
                    errorHandler.accept("连接失败: " + error.toString());
                }
            }

            private void onEvent(@NotNull WebSocket webSocket, int code, String reason, String event) {
                log.info("WebSocket事件 - {}: code={}, reason={}", event, code, reason);
                isConnected.set(false);
                if (responseLatch != null) {
                    responseLatch.countDown();
                }
                try {
                    webSocket.close(code, reason);
                } catch (Exception e) {
                    log.error("关闭WebSocket连接失败: {}", e.getMessage());
                }
            }
        };
    }

    /**
     * 从WebSocket消息中提取推流地址
     */
    private void extractStreamUrl(JSONObject jsonObject) {
        try {
            // 记录完整消息用于调试
            log.info("🔍 完整WebSocket消息: {}", jsonObject.toJSONString());

            // 检查消息头信息
            JSONObject header = jsonObject.getJSONObject("header");
            if (header != null) {
                String ctrl = header.getString("ctrl");
                int code = header.getIntValue("code");
                log.info("📋 消息头信息: ctrl={}, code={}", ctrl, code);
            }

            JSONObject payload = jsonObject.getJSONObject("payload");
            if (payload != null) {
                log.info("📦 payload内容: {}", payload.toJSONString());

                // 方法1: 检查avatar字段
                JSONObject avatar = payload.getJSONObject("avatar");
                if (avatar != null) {
                    log.info("👤 avatar内容: {}", avatar.toJSONString());
                    String foundStreamUrl = extractFromAvatar(avatar);
                    if (foundStreamUrl != null) {
                        this.streamUrl = foundStreamUrl;
                        log.info("✅ 从avatar字段获取到推流地址: {}", foundStreamUrl);
                        notifyStreamUrlFound();
                        return;
                    }
                }

                // 方法2: 检查payload根级别
                String payloadStreamUrl = extractFromPayload(payload);
                if (payloadStreamUrl != null) {
                    this.streamUrl = payloadStreamUrl;
                    log.info("✅ 从payload根级别获取到推流地址: {}", payloadStreamUrl);
                    notifyStreamUrlFound();
                    return;
                }

                // 方法3: 检查其他可能的字段
                String otherStreamUrl = extractFromOtherFields(payload);
                if (otherStreamUrl != null) {
                    this.streamUrl = otherStreamUrl;
                    log.info("✅ 从其他字段获取到推流地址: {}", otherStreamUrl);
                    notifyStreamUrlFound();
                    return;
                }

                log.info("⚠️ 在payload中未找到推流地址");
            } else {
                log.info("❌ 消息中未找到payload字段");
            }
        } catch (Exception e) {
            log.error("❌ 提取推流地址失败", e);
        }
    }

    /**
     * 从avatar字段中提取推流地址
     */
    private String extractFromAvatar(JSONObject avatar) {
        String[] possibleKeys = {"stream_url", "streamUrl", "url", "rtmp_url", "rtmpUrl", "play_url", "playUrl"};

        for (String key : possibleKeys) {
            if (avatar.containsKey(key)) {
                String url = avatar.getString(key);
                if (url != null && !url.isEmpty()) {
                    log.info("🎯 在avatar.{}中找到推流地址: {}", key, url);
                    return url;
                }
            }
        }

        log.info("🔍 avatar字段中未找到推流地址，可用字段: {}", avatar.keySet());
        return null;
    }

    /**
     * 从payload根级别提取推流地址
     */
    private String extractFromPayload(JSONObject payload) {
        String[] possibleKeys = {"stream_url", "streamUrl", "url", "rtmp_url", "rtmpUrl", "play_url", "playUrl"};

        for (String key : possibleKeys) {
            if (payload.containsKey(key)) {
                String url = payload.getString(key);
                if (url != null && !url.isEmpty()) {
                    log.info("🎯 在payload.{}中找到推流地址: {}", key, url);
                    return url;
                }
            }
        }

        return null;
    }

    /**
     * 从其他可能的字段中提取推流地址
     */
    private String extractFromOtherFields(JSONObject payload) {
        // 检查是否有嵌套的对象包含推流地址
        for (String key : payload.keySet()) {
            Object value = payload.get(key);
            if (value instanceof JSONObject) {
                JSONObject nestedObj = (JSONObject) value;
                String url = extractFromAvatar(nestedObj); // 复用avatar的提取逻辑
                if (url != null) {
                    log.info("🎯 在payload.{}中找到推流地址: {}", key, url);
                    return url;
                }
            }
        }

        return null;
    }

    /**
     * 通知推流地址已找到
     */
    private void notifyStreamUrlFound() {
        if (responseLatch != null) {
            responseLatch.countDown();
        }
    }

}
