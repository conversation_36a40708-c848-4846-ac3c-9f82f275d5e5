package org.dromara.app.service.impl;

import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.vo.DashboardSummaryVO;
import org.dromara.app.domain.vo.SmartTaskVO;
import org.dromara.app.domain.vo.StudyStatsVO;
import org.dromara.app.domain.vo.UserAbilitiesVO;
import org.dromara.app.service.IDashboardService;
import org.dromara.common.core.utils.DateUtils;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 首页仪表盘服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class DashboardServiceImpl implements IDashboardService {

    @Override
    public DashboardSummaryVO getDashboardSummary(Long userId) {
        // 实际业务中应从数据库获取数据，此处为模拟数据
        DashboardSummaryVO summary = new DashboardSummaryVO();

        // 设置用户信息
        DashboardSummaryVO.UserInfoVO user = new DashboardSummaryVO.UserInfoVO();
        user.setId(userId.toString());
        user.setName("张三");
        user.setAvatar("https://example.com/avatar.jpg");
        user.setTargetPosition("前端工程师");
        user.setLevel(3);
        summary.setUser(user);

        // 设置欢迎消息和AI激励
        int hour = Calendar.getInstance().get(Calendar.HOUR_OF_DAY);
        String greeting = hour < 12 ? "早上好" : (hour < 18 ? "下午好" : "晚上好");
        summary.setWelcomeMessage(greeting + "，" + user.getName() + "！");
        summary.setAiMotivation("今天也要加油学习哦！");

        // 设置任务和消息数量
        summary.setTodayTasks(3);
        summary.setUnreadMessages(2);

        // 设置下次面试安排
        DashboardSummaryVO.NextInterviewVO nextInterview = new DashboardSummaryVO.NextInterviewVO();
        nextInterview.setTime(DateUtils.getDate() + " 15:00");
        nextInterview.setPosition("前端开发工程师");
        nextInterview.setCompany("科技有限公司");
        summary.setNextInterview(nextInterview);

        return summary;
    }

    @Override
    public UserAbilitiesVO getUserAbilities(Long userId) {
        // 实际业务中应从数据库获取数据，此处为模拟数据
        UserAbilitiesVO abilities = new UserAbilitiesVO();
        abilities.setProfessionalKnowledge(85);
        abilities.setLogicalThinking(75);
        abilities.setLanguageExpression(70);
        abilities.setStressResistance(80);
        abilities.setTeamCollaboration(90);
        abilities.setInnovation(65);
        return abilities;
    }

    @Override
    public StudyStatsVO getStudyStats(Long userId) {
        // 实际业务中应从数据库获取数据，此处为模拟数据
        StudyStatsVO stats = new StudyStatsVO();
        stats.setTotalInterviews(15);
        stats.setAverageScore(78);
        stats.setImprovementRate(23);
        stats.setTargetPosition("前端工程师");
        stats.setRecentInterviews(3);
        stats.setWeeklyProgress(65);
        return stats;
    }

    @Override
    public List<SmartTaskVO> getSmartTasks(Long userId, Integer limit, String type) {
        // 实际业务中应从数据库获取数据，此处为模拟数据
        List<SmartTaskVO> taskList = new ArrayList<>();

        SmartTaskVO task1 = new SmartTaskVO();
        task1.setId(1L);
        task1.setTitle("提升算法思维能力");
        task1.setDescription("基于你最近的面试表现，建议加强算法题的练习");
        task1.setType("skill");
        task1.setPriority("high");
        task1.setLink("/pages/resources/index?tag=algorithm");
        task1.setEstimatedTime(60);

        SmartTaskVO task2 = new SmartTaskVO();
        task2.setId(2L);
        task2.setTitle("完善项目经验表达");
        task2.setDescription("学习STAR法则，让你的项目经验更有说服力");
        task2.setType("expression");
        task2.setPriority("medium");
        task2.setLink("/pages/resources/index?tag=star-method");
        task2.setEstimatedTime(30);

        SmartTaskVO task3 = new SmartTaskVO();
        task3.setId(3L);
        task3.setTitle("学习React Hooks");
        task3.setDescription("根据你的求职意向，建议学习React Hooks相关知识");
        task3.setType("knowledge");
        task3.setPriority("medium");
        task3.setLink("/pages/resources/index?tag=react-hooks");
        task3.setEstimatedTime(120);

        taskList.add(task1);
        taskList.add(task2);
        taskList.add(task3);

        // 根据类型筛选
        if (StrUtil.isNotBlank(type)) {
            taskList = taskList.stream()
                .filter(task -> type.equals(task.getType()))
                .toList();
        }

        // 限制返回数量
        if (limit != null && limit > 0 && taskList.size() > limit) {
            taskList = taskList.subList(0, limit);
        }

        return taskList;
    }

    @Override
    public List<Map<String, Object>> getRecentInterviews(Long userId, Integer limit, Integer page) {
        // 实际业务中应从数据库获取数据，此处为模拟数据
        List<Map<String, Object>> interviews = new ArrayList<>();

        Map<String, Object> interview1 = new HashMap<>();
        interview1.put("id", 1);
        interview1.put("company", "科技有限公司");
        interview1.put("position", "前端开发工程师");
        interview1.put("date", DateUtils.getDate());
        interview1.put("duration", "45分钟");
        interview1.put("score", 85);
        interview1.put("status", "completed");
        interview1.put("feedback", "表现良好，但需要加强算法知识");

        Map<String, Object> interview2 = new HashMap<>();
        interview2.put("id", 2);
        interview2.put("company", "软件科技公司");
        interview2.put("position", "Web前端工程师");
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -3);
        interview2.put("date", DateUtils.parseDateToStr(org.dromara.common.core.enums.FormatsType.YYYY_MM_DD, calendar.getTime()));
        interview2.put("duration", "60分钟");
        interview2.put("score", 78);
        interview2.put("status", "completed");
        interview2.put("feedback", "项目经验描述需要更加清晰");

        interviews.add(interview1);
        interviews.add(interview2);

        // 分页处理
        if (page != null && limit != null) {
            int start = (page - 1) * limit;
            int end = Math.min(start + limit, interviews.size());
            if (start < interviews.size()) {
                interviews = interviews.subList(start, end);
            } else {
                interviews = Collections.emptyList();
            }
        }

        return interviews;
    }

    @Override
    public boolean updateTargetPosition(Long userId, String targetPosition) {
        // 实际业务中应更新数据库，此处只返回成功
        log.info("用户 {} 更新目标岗位为 {}", userId, targetPosition);
        return true;
    }

    @Override
    public boolean completeTask(Long userId, Long taskId) {
        // 实际业务中应更新数据库，此处只返回成功
        log.info("用户 {} 完成任务 {}", userId, taskId);
        return true;
    }

    @Override
    public Map<String, Object> getDashboardData(Long userId) {
        // 聚合所有数据
        Map<String, Object> data = new HashMap<>();
        data.put("summary", getDashboardSummary(userId));
        data.put("abilities", getUserAbilities(userId));
        data.put("stats", getStudyStats(userId));
        data.put("tasks", getSmartTasks(userId, 3, null));
        data.put("interviews", getRecentInterviews(userId, 2, 1));
        return data;
    }
}
