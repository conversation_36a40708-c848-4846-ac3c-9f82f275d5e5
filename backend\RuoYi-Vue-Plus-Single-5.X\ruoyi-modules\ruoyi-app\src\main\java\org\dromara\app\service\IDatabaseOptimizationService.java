package org.dromara.app.service;

import org.dromara.app.domain.vo.DatabasePerformanceVo;
import org.dromara.app.domain.vo.SlowQueryVo;

import java.util.List;
import java.util.Map;

/**
 * 数据库优化服务接口
 * 用于数据库查询优化和性能监控
 *
 * <AUTHOR>
 * @date 2025-01-19
 */
public interface IDatabaseOptimizationService {

    /**
     * 分析慢查询
     *
     * @param limit 限制数量
     * @return 慢查询列表
     */
    List<SlowQueryVo> analyzeSlowQueries(int limit);

    /**
     * 优化数据库索引
     *
     * @return 优化建议
     */
    List<String> optimizeIndexes();

    /**
     * 获取数据库性能指标
     *
     * @return 性能指标
     */
    DatabasePerformanceVo getDatabasePerformance();

    /**
     * 分析表空间使用情况
     *
     * @return 表空间使用情况
     */
    Map<String, Object> analyzeTableSpaceUsage();

    /**
     * 优化查询计划
     *
     * @param sql SQL语句
     * @return 优化建议
     */
    List<String> optimizeQueryPlan(String sql);

    /**
     * 监控数据库连接池
     *
     * @return 连接池状态
     */
    Map<String, Object> monitorConnectionPool();

    /**
     * 清理无用数据
     *
     * @return 清理的记录数
     */
    int cleanupUnusedData();

    /**
     * 分析数据分布
     *
     * @param tableName 表名
     * @return 数据分布分析
     */
    Map<String, Object> analyzeDataDistribution(String tableName);

    /**
     * 建议分区策略
     *
     * @param tableName 表名
     * @return 分区建议
     */
    List<String> suggestPartitionStrategy(String tableName);

    /**
     * 监控锁等待
     *
     * @return 锁等待信息
     */
    List<Map<String, Object>> monitorLockWaits();

    /**
     * 优化批量操作
     *
     * @param batchSize 批次大小
     * @return 优化后的批次大小
     */
    int optimizeBatchSize(int batchSize);

    /**
     * 分析查询频率
     *
     * @return 查询频率统计
     */
    Map<String, Long> analyzeQueryFrequency();
}