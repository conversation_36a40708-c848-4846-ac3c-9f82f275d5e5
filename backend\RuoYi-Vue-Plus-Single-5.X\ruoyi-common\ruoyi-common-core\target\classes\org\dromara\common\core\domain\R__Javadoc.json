{"doc": "\n 响应信息主体\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "SUCCESS", "doc": "\n 成功\r\n"}, {"name": "FAIL", "doc": "\n 失败\r\n"}], "enumConstants": [], "methods": [{"name": "warn", "paramTypes": ["java.lang.String"], "doc": "\n 返回警告消息\r\n\r\n @param msg 返回内容\r\n @return 警告消息\r\n"}, {"name": "warn", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": "\n 返回警告消息\r\n\r\n @param msg  返回内容\r\n @param data 数据对象\r\n @return 警告消息\r\n"}], "constructors": []}