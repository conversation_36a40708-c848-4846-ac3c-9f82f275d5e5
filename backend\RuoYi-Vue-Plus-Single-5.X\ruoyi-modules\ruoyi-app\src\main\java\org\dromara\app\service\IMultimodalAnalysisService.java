package org.dromara.app.service;

import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 多模态分析服务接口
 *
 * <AUTHOR>
 */
public interface IMultimodalAnalysisService {

    /**
     * 综合多模态分析
     *
     * @param sessionId 面试会话ID
     * @param audioFile 音频文件
     * @param videoFile 视频文件
     * @param textContent 文本内容
     * @param jobPosition 岗位信息
     * @return 综合分析结果
     */
    MultimodalAnalysisResult comprehensiveAnalysis(String sessionId, MultipartFile audioFile, 
                                                  MultipartFile videoFile, String textContent, 
                                                  String jobPosition);

    /**
     * 音频分析
     *
     * @param audioFile 音频文件
     * @param sessionId 会话ID
     * @return 音频分析结果
     */
    AudioAnalysisResult analyzeAudio(MultipartFile audioFile, String sessionId);

    /**
     * 视频分析
     *
     * @param videoFile 视频文件
     * @param sessionId 会话ID
     * @return 视频分析结果
     */
    VideoAnalysisResult analyzeVideo(MultipartFile videoFile, String sessionId);

    /**
     * 文本分析
     *
     * @param textContent 文本内容
     * @param jobPosition 岗位信息
     * @param sessionId 会话ID
     * @return 文本分析结果
     */
    TextAnalysisResult analyzeText(String textContent, String jobPosition, String sessionId);

    /**
     * 实时音频分析（流式）
     *
     * @param audioStream 音频流数据
     * @param sessionId 会话ID
     * @param callback 实时回调
     */
    void analyzeAudioStream(byte[] audioStream, String sessionId, AnalysisCallback callback);

    /**
     * 实时视频分析（流式）
     *
     * @param videoFrame 视频帧数据
     * @param sessionId 会话ID
     * @param callback 实时回调
     */
    void analyzeVideoFrame(byte[] videoFrame, String sessionId, AnalysisCallback callback);

    /**
     * 获取分析历史
     *
     * @param sessionId 会话ID
     * @return 分析历史记录
     */
    List<MultimodalAnalysisResult> getAnalysisHistory(String sessionId);

    /**
     * 分析回调接口
     */
    interface AnalysisCallback {
        /**
         * 分析进度更新
         *
         * @param progress 进度百分比
         * @param stage 当前阶段
         */
        void onProgress(int progress, String stage);

        /**
         * 分析完成
         *
         * @param result 分析结果
         */
        void onComplete(Object result);

        /**
         * 分析失败
         *
         * @param error 错误信息
         */
        void onError(Throwable error);
    }

    /**
     * 多模态分析结果
     */
    class MultimodalAnalysisResult {
        private String sessionId;
        private AudioAnalysisResult audioResult;
        private VideoAnalysisResult videoResult;
        private TextAnalysisResult textResult;
        private OverallAssessment overallAssessment;
        private long analysisTime;
        private String status;
        private Map<String, Object> metadata;

        // Getters and Setters
        public String getSessionId() { return sessionId; }
        public void setSessionId(String sessionId) { this.sessionId = sessionId; }
        public AudioAnalysisResult getAudioResult() { return audioResult; }
        public void setAudioResult(AudioAnalysisResult audioResult) { this.audioResult = audioResult; }
        public VideoAnalysisResult getVideoResult() { return videoResult; }
        public void setVideoResult(VideoAnalysisResult videoResult) { this.videoResult = videoResult; }
        public TextAnalysisResult getTextResult() { return textResult; }
        public void setTextResult(TextAnalysisResult textResult) { this.textResult = textResult; }
        public OverallAssessment getOverallAssessment() { return overallAssessment; }
        public void setOverallAssessment(OverallAssessment overallAssessment) { this.overallAssessment = overallAssessment; }
        public long getAnalysisTime() { return analysisTime; }
        public void setAnalysisTime(long analysisTime) { this.analysisTime = analysisTime; }
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        public Map<String, Object> getMetadata() { return metadata; }
        public void setMetadata(Map<String, Object> metadata) { this.metadata = metadata; }
    }

    /**
     * 音频分析结果
     */
    class AudioAnalysisResult {
        private int clarity;           // 清晰度 (0-100)
        private int fluency;           // 流利度 (0-100)
        private int confidence;        // 自信度 (0-100)
        private int pace;              // 语速 (0-100)
        private int overall;           // 总体评分 (0-100)
        private String transcript;     // 语音转文字结果
        private Map<String, Double> emotionScores; // 情感分析结果
        private List<String> keyInsights; // 关键洞察
        private Map<String, Object> technicalMetrics; // 技术指标

        // Getters and Setters
        public int getClarity() { return clarity; }
        public void setClarity(int clarity) { this.clarity = clarity; }
        public int getFluency() { return fluency; }
        public void setFluency(int fluency) { this.fluency = fluency; }
        public int getConfidence() { return confidence; }
        public void setConfidence(int confidence) { this.confidence = confidence; }
        public int getPace() { return pace; }
        public void setPace(int pace) { this.pace = pace; }
        public int getOverall() { return overall; }
        public void setOverall(int overall) { this.overall = overall; }
        public String getTranscript() { return transcript; }
        public void setTranscript(String transcript) { this.transcript = transcript; }
        public Map<String, Double> getEmotionScores() { return emotionScores; }
        public void setEmotionScores(Map<String, Double> emotionScores) { this.emotionScores = emotionScores; }
        public List<String> getKeyInsights() { return keyInsights; }
        public void setKeyInsights(List<String> keyInsights) { this.keyInsights = keyInsights; }
        public Map<String, Object> getTechnicalMetrics() { return technicalMetrics; }
        public void setTechnicalMetrics(Map<String, Object> technicalMetrics) { this.technicalMetrics = technicalMetrics; }
    }

    /**
     * 视频分析结果
     */
    class VideoAnalysisResult {
        private int eyeContact;        // 眼神交流 (0-100)
        private int posture;           // 姿态 (0-100)
        private int expressions;       // 表情 (0-100)
        private int gestures;          // 手势 (0-100)
        private int overall;           // 总体评分 (0-100)
        private List<String> detectedEmotions; // 检测到的情绪
        private Map<String, Integer> gestureCount; // 手势统计
        private List<String> postureIssues; // 姿态问题
        private Map<String, Object> faceMetrics; // 面部指标

        // Getters and Setters
        public int getEyeContact() { return eyeContact; }
        public void setEyeContact(int eyeContact) { this.eyeContact = eyeContact; }
        public int getPosture() { return posture; }
        public void setPosture(int posture) { this.posture = posture; }
        public int getExpressions() { return expressions; }
        public void setExpressions(int expressions) { this.expressions = expressions; }
        public int getGestures() { return gestures; }
        public void setGestures(int gestures) { this.gestures = gestures; }
        public int getOverall() { return overall; }
        public void setOverall(int overall) { this.overall = overall; }
        public List<String> getDetectedEmotions() { return detectedEmotions; }
        public void setDetectedEmotions(List<String> detectedEmotions) { this.detectedEmotions = detectedEmotions; }
        public Map<String, Integer> getGestureCount() { return gestureCount; }
        public void setGestureCount(Map<String, Integer> gestureCount) { this.gestureCount = gestureCount; }
        public List<String> getPostureIssues() { return postureIssues; }
        public void setPostureIssues(List<String> postureIssues) { this.postureIssues = postureIssues; }
        public Map<String, Object> getFaceMetrics() { return faceMetrics; }
        public void setFaceMetrics(Map<String, Object> faceMetrics) { this.faceMetrics = faceMetrics; }
    }

    /**
     * 文本分析结果
     */
    class TextAnalysisResult {
        private int professionalKnowledge; // 专业知识水平 (0-100)
        private int logicalThinking;       // 逻辑思维能力 (0-100)
        private int innovation;            // 创新能力 (0-100)
        private int skillMatching;         // 技能匹配度 (0-100)
        private int starStructure;         // STAR结构完整性 (0-100)
        private List<String> keyWords;     // 关键词
        private List<String> skillsIdentified; // 识别的技能
        private Map<String, Double> topicRelevance; // 话题相关性
        private List<String> improvementSuggestions; // 改进建议

        // Getters and Setters
        public int getProfessionalKnowledge() { return professionalKnowledge; }
        public void setProfessionalKnowledge(int professionalKnowledge) { this.professionalKnowledge = professionalKnowledge; }
        public int getLogicalThinking() { return logicalThinking; }
        public void setLogicalThinking(int logicalThinking) { this.logicalThinking = logicalThinking; }
        public int getInnovation() { return innovation; }
        public void setInnovation(int innovation) { this.innovation = innovation; }
        public int getSkillMatching() { return skillMatching; }
        public void setSkillMatching(int skillMatching) { this.skillMatching = skillMatching; }
        public int getStarStructure() { return starStructure; }
        public void setStarStructure(int starStructure) { this.starStructure = starStructure; }
        public List<String> getKeyWords() { return keyWords; }
        public void setKeyWords(List<String> keyWords) { this.keyWords = keyWords; }
        public List<String> getSkillsIdentified() { return skillsIdentified; }
        public void setSkillsIdentified(List<String> skillsIdentified) { this.skillsIdentified = skillsIdentified; }
        public Map<String, Double> getTopicRelevance() { return topicRelevance; }
        public void setTopicRelevance(Map<String, Double> topicRelevance) { this.topicRelevance = topicRelevance; }
        public List<String> getImprovementSuggestions() { return improvementSuggestions; }
        public void setImprovementSuggestions(List<String> improvementSuggestions) { this.improvementSuggestions = improvementSuggestions; }
    }

    /**
     * 综合评估结果
     */
    class OverallAssessment {
        private int totalScore;            // 总分 (0-100)
        private String level;              // 等级 (excellent/good/average/poor)
        private int percentile;            // 百分位数
        private List<String> topStrengths; // 主要优势
        private List<String> topWeaknesses; // 主要劣势
        private List<DimensionScore> dimensionScores; // 维度评分
        private String overallFeedback;    // 总体反馈
        private Map<String, Object> comparisonData; // 对比数据

        // Getters and Setters
        public int getTotalScore() { return totalScore; }
        public void setTotalScore(int totalScore) { this.totalScore = totalScore; }
        public String getLevel() { return level; }
        public void setLevel(String level) { this.level = level; }
        public int getPercentile() { return percentile; }
        public void setPercentile(int percentile) { this.percentile = percentile; }
        public List<String> getTopStrengths() { return topStrengths; }
        public void setTopStrengths(List<String> topStrengths) { this.topStrengths = topStrengths; }
        public List<String> getTopWeaknesses() { return topWeaknesses; }
        public void setTopWeaknesses(List<String> topWeaknesses) { this.topWeaknesses = topWeaknesses; }
        public List<DimensionScore> getDimensionScores() { return dimensionScores; }
        public void setDimensionScores(List<DimensionScore> dimensionScores) { this.dimensionScores = dimensionScores; }
        public String getOverallFeedback() { return overallFeedback; }
        public void setOverallFeedback(String overallFeedback) { this.overallFeedback = overallFeedback; }
        public Map<String, Object> getComparisonData() { return comparisonData; }
        public void setComparisonData(Map<String, Object> comparisonData) { this.comparisonData = comparisonData; }
    }

    /**
     * 维度评分
     */
    class DimensionScore {
        private String dimension;
        private int score;
        private int maxScore;
        private int percentile;
        private String description;
        private List<String> strengths;
        private List<String> weaknesses;
        private List<String> recommendations;

        // Getters and Setters
        public String getDimension() { return dimension; }
        public void setDimension(String dimension) { this.dimension = dimension; }
        public int getScore() { return score; }
        public void setScore(int score) { this.score = score; }
        public int getMaxScore() { return maxScore; }
        public void setMaxScore(int maxScore) { this.maxScore = maxScore; }
        public int getPercentile() { return percentile; }
        public void setPercentile(int percentile) { this.percentile = percentile; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        public List<String> getStrengths() { return strengths; }
        public void setStrengths(List<String> strengths) { this.strengths = strengths; }
        public List<String> getWeaknesses() { return weaknesses; }
        public void setWeaknesses(List<String> weaknesses) { this.weaknesses = weaknesses; }
        public List<String> getRecommendations() { return recommendations; }
        public void setRecommendations(List<String> recommendations) { this.recommendations = recommendations; }
    }
}