package org.dromara.app.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.math.BigDecimal;
import java.util.List;

/**
 * 面试书籍对象 app_book
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_book")
public class Book extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 书籍ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 书籍标题
     */
    private String title;

    /**
     * 作者
     */
    private String author;

    /**
     * 封面图片URL
     */
    private String cover;

    /**
     * 分类：technical/behavioral/algorithm/resume/salary
     */
    private String category;

    /**
     * 评分
     */
    private BigDecimal rating;

    /**
     * 阅读次数
     */
    private Integer readCount;

    /**
     * 章节数
     */
    private Integer chapters;

    /**
     * 页数
     */
    private Integer pages;

    /**
     * 是否完结：0-连载中，1-已完结
     */
    private Boolean isCompleted;

    /**
     * 标签，逗号分隔
     */
    private String tags;

    /**
     * 书籍描述
     */
    private String description;

    /**
     * 难度：入门/进阶/高级
     */
    private String difficulty;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 是否免费：0-付费，1-免费
     */
    private Boolean isFree;

    /**
     * 状态：0-下架，1-上架
     */
    private Boolean status;

    /**
     * 排序序号
     */
    private Integer sortOrder;

    /**
     * 标签列表（转换后的字段，不存储到数据库）
     */
    @TableField(exist = false)
    private List<String> tagList;

    /**
     * 当前用户阅读进度（不存储到数据库）
     */
    @TableField(exist = false)
    private BigDecimal readingProgress;

    /**
     * 是否已购买（不存储到数据库）
     */
    @TableField(exist = false)
    private Boolean isPurchased;
}
