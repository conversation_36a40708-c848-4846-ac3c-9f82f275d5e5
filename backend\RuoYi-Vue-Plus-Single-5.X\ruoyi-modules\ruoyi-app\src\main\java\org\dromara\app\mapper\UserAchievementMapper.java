package org.dromara.app.mapper;

import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.apache.ibatis.annotations.Param;
import org.dromara.app.domain.UserAchievement;
import org.dromara.app.domain.vo.AchievementStatsVo;
import org.dromara.app.domain.vo.AchievementVo;
import org.dromara.app.domain.vo.UserAchievementVo;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户成就数据层
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
public interface UserAchievementMapper extends BaseMapperPlus<UserAchievement, UserAchievementVo> {

    /**
     * 获取用户已解锁成就数
     *
     * @param userId 用户ID
     * @return 已解锁成就数
     */
    int countUnlockedAchievements(@Param("userId") String userId);

    /**
     * 获取用户某类别已解锁成就数
     *
     * @param userId   用户ID
     * @param category 类别
     * @return 该类别已解锁成就数
     */
    int countUnlockedAchievementsByCategory(@Param("userId") String userId, @Param("category") String category);

    /**
     * 获取用户某稀有度已解锁成就数
     *
     * @param userId 用户ID
     * @param rarity 稀有度
     * @return 该稀有度已解锁成就数
     */
    int countUnlockedAchievementsByRarity(@Param("userId") String userId, @Param("rarity") String rarity);

    /**
     * 获取用户某日期解锁的成就数
     *
     * @param userId 用户ID
     * @param date   日期
     * @return 该日期解锁的成就数
     */
    int countUnlockedAchievementsByDate(@Param("userId") String userId, @Param("date") LocalDateTime date);

    /**
     * 获取用户获得的成就点数总和
     *
     * @param userId 用户ID
     * @return 成就点数总和
     */
    int sumEarnedPoints(@Param("userId") String userId);

    /**
     * 获取用户最近解锁的成就
     *
     * @param userId 用户ID
     * @param limit  数量限制
     * @return 最近解锁的成就
     */
    List<AchievementVo> selectRecentAchievements(@Param("userId") String userId, @Param("limit") int limit);

    /**
     * 获取用户进行中的成就
     *
     * @param userId 用户ID
     * @return 进行中的成就
     */
    List<UserAchievementVo> selectInProgressAchievements(@Param("userId") String userId);

    /**
     * 获取用户成就详情
     *
     * @param userId        用户ID
     * @param achievementId 成就ID
     * @return 用户成就详情
     */
    UserAchievementVo selectUserAchievementDetail(@Param("userId") String userId, @Param("achievementId") String achievementId);

    /**
     * 获取用户推荐的成就
     *
     * @param userId 用户ID
     * @param limit  数量限制
     * @return 推荐的成就
     */
    List<UserAchievementVo> selectRecommendedAchievements(@Param("userId") String userId, @Param("limit") int limit);

    /**
     * 获取成就排行榜
     *
     * @param category 类别（可选）
     * @param limit    数量限制
     * @return 排行榜列表
     */
    List<AchievementStatsVo.LeaderboardEntry> selectLeaderboard(@Param("category") String category, @Param("limit") int limit);

    /**
     * 获取用户排名
     *
     * @param userId   用户ID
     * @param category 类别（可选）
     * @return 用户排名
     */
    Integer getUserRanking(@Param("userId") String userId, @Param("category") String category);

    /**
     * 根据用户ID和成就ID获取用户成就
     *
     * @param userId        用户ID
     * @param achievementId 成就ID
     * @return 用户成就
     */
    UserAchievement selectByUserIdAndAchievementId(@Param("userId") Long userId, @Param("achievementId") Long achievementId);

    /**
     * 获取用户某事件类型的累计事件值
     *
     * @param userId    用户ID
     * @param eventType 事件类型
     * @return 累计事件值
     */
    int sumEventValues(@Param("userId") String userId, @Param("eventType") String eventType);

    /**
     * 更新用户成就进度
     *
     * @param userAchievement 用户成就
     * @return 更新行数
     */
    int updateProgress(UserAchievement userAchievement);

    /**
     * 查询未解锁且符合条件的成就
     *
     * @param userId 用户ID
     * @return 符合条件的成就列表
     */
    List<UserAchievementVo> selectUnlockedCandidates(@Param("userId") String userId);

    /**
     * 更新通知状态
     *
     * @param userId 用户ID
     * @param status 状态（0=未通知，1=已通知）
     * @return 更新行数
     */
    int updateNotificationStatus(@Param("userId") String userId, @Param("status") int status);

    /**
     * 标记成就为已查看
     *
     * @param userId        用户ID
     * @param achievementId 成就ID
     * @return 更新行数
     */
    int markAsViewed(@Param("userId") String userId, @Param("achievementId") String achievementId);

    // ==================== 服务实现中需要的方法 ====================

    /**
     * 根据用户ID查询用户成就列表
     *
     * @param userId 用户ID
     * @return 用户成就列表
     */
    List<UserAchievementVo> selectByUserId(@Param("userId") Long userId);

    /**
     * 查询用户已完成的成就列表
     *
     * @param userId 用户ID
     * @return 已完成的成就列表
     */
    List<UserAchievementVo> selectCompletedByUserId(@Param("userId") Long userId);

    /**
     * 查询用户进行中的成就列表
     *
     * @param userId 用户ID
     * @return 进行中的成就列表
     */
    List<UserAchievementVo> selectInProgressByUserId(@Param("userId") Long userId);

    /**
     * 统计用户已完成的成就数量
     *
     * @param userId 用户ID
     * @return 已完成的成就数量
     */
    Long countCompletedByUserId(@Param("userId") Long userId);

    /**
     * 统计用户获得的总积分
     *
     * @param userId 用户ID
     * @return 总积分
     */
    Long sumRewardPointsByUserId(@Param("userId") Long userId);

    /**
     * 更新用户成就进度
     *
     * @param userId        用户ID
     * @param achievementId 成就ID
     * @param currentValue  当前值
     * @param progress      进度
     * @return 更新行数
     */
    int updateProgress(@Param("userId") Long userId, @Param("achievementId") Long achievementId,
                      @Param("currentValue") Long currentValue, @Param("progress") java.math.BigDecimal progress);

    /**
     * 完成用户成就
     *
     * @param userId        用户ID
     * @param achievementId 成就ID
     * @return 更新行数
     */
    int completeAchievement(@Param("userId") Long userId, @Param("achievementId") Long achievementId);

    /**
     * 根据成就类型查询用户成就统计
     *
     * @param userId          用户ID
     * @param achievementType 成就类型
     * @return 用户成就统计列表
     */
    List<UserAchievementVo> selectStatsByUserIdAndType(@Param("userId") Long userId, @Param("achievementType") String achievementType);

    /**
     * 查询用户最近的成就列表
     *
     * @param aLong 用户ID
     * @param limit  数量限制
     * @return 最近的成就列表
     */
    List<UserAchievementVo> selectRecentByUserId(Long aLong, Integer limit);
}
