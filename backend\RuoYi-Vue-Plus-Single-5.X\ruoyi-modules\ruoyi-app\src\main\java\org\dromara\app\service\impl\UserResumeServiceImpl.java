package org.dromara.app.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hwpf.HWPFDocument;
import org.apache.poi.hwpf.extractor.WordExtractor;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.dromara.app.domain.UserResume;
import org.dromara.app.domain.bo.UserResumeBo;
import org.dromara.app.domain.vo.UserResumeUploadVo;
import org.dromara.app.domain.vo.UserResumeVo;
import org.dromara.app.mapper.UserResumeMapper;
import org.dromara.app.service.IUserResumeService;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.oss.core.OssClient;
import org.dromara.common.oss.factory.OssFactory;
import org.dromara.system.domain.vo.SysOssVo;
import org.dromara.system.service.ISysOssService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 用户简历服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class UserResumeServiceImpl implements IUserResumeService {

    private final UserResumeMapper baseMapper;
    private final ISysOssService ossService;

    /**
     * 查询用户简历分页列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 分页结果
     */
    @Override
    public TableDataInfo<UserResumeVo> queryPageList(UserResumeBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<UserResume> lqw = buildQueryWrapper(bo);
        Page<UserResumeVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);

        // 格式化文件大小
        result.getRecords().forEach(this::formatFileSize);

        return TableDataInfo.build(result);
    }

    /**
     * 查询用户的所有简历列表
     *
     * @param userId 用户ID
     * @return 简历列表
     */
    @Override
    public List<UserResumeVo> selectUserResumeList(Long userId) {
        LambdaQueryWrapper<UserResume> lqw = Wrappers.lambdaQuery();
        lqw.eq(UserResume::getUserId, userId)
            .eq(UserResume::getStatus, "0") // 正常状态
            .orderByDesc(UserResume::getIsDefault)
            .orderByDesc(UserResume::getCreateTime);

        List<UserResumeVo> list = baseMapper.selectVoList(lqw);

        // 格式化文件大小
        list.forEach(this::formatFileSize);

        return list;
    }

    /**
     * 根据简历ID查询简历详情
     *
     * @param resumeId 简历ID
     * @return 简历详情
     */
    @Override
    public UserResumeVo queryById(Long resumeId) {
        UserResumeVo vo = baseMapper.selectVoById(resumeId);
        if (ObjectUtil.isNotNull(vo)) {
            formatFileSize(vo);
        }
        return vo;
    }

    /**
     * 上传用户简历文件
     *
     * @param userId   用户ID
     * @param file     简历文件
     * @param fileName 原始文件名（可选）
     * @param fileType 文件类型（可选）
     * @return 上传结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserResumeUploadVo uploadResume(Long userId, MultipartFile file, String fileName, String fileType) {
        // 使用OSS服务上传文件
        SysOssVo ossVo = ossService.upload(file);

        // 获取原始文件名
        String originalName = file.getOriginalFilename();

        // 如果传递了fileName参数，优先使用
        if (StrUtil.isNotBlank(fileName)) {
            originalName = fileName;
        }

        // 生成简历名称（去掉扩展名）
        String resumeName = originalName;
        if (StrUtil.isNotBlank(originalName) && originalName.contains(".")) {
            resumeName = originalName.substring(0, originalName.lastIndexOf("."));
        }

        // 检查是否是用户的第一份简历
        long count = baseMapper.selectCount(Wrappers.lambdaQuery(UserResume.class)
            .eq(UserResume::getUserId, userId)
            .eq(UserResume::getStatus, "0"));
        boolean isFirst = count == 0;

        // 创建简历记录
        UserResume resume = new UserResume();
        resume.setUserId(userId);
        resume.setResumeName(resumeName);
        resume.setOriginalName(originalName);
        resume.setFilePath(ossVo.getFileName());
        resume.setFileUrl(ossVo.getUrl());
        resume.setFileSize(file.getSize());
        resume.setFileSuffix(ossVo.getFileSuffix());
        resume.setIsDefault(isFirst ? 1 : 0); // 第一份简历自动设为默认
        resume.setStatus("0");
        resume.setOssId(ossVo.getOssId());

        // 如果传递了fileType参数，可以在备注中记录
        String remark = "用户上传";
        if (StrUtil.isNotBlank(fileType)) {
            remark += "，文件类型：" + fileType;
        }
        resume.setRemark(remark);

        baseMapper.insert(resume);

        // 返回上传结果
        UserResumeUploadVo uploadVo = new UserResumeUploadVo();
        uploadVo.setResumeId(resume.getResumeId());
        uploadVo.setResumeName(resumeName);
        uploadVo.setFileUrl(ossVo.getUrl());
        uploadVo.setFileSizeStr(formatFileSize(file.getSize()));
        uploadVo.setIsDefault(resume.getIsDefault());

        return uploadVo;
    }

    /**
     * 新增用户简历
     *
     * @param bo 简历信息
     * @return 新增结果
     */
    @Override
    public Boolean insertByBo(UserResumeBo bo) {
        UserResume add = MapstructUtils.convert(bo, UserResume.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setResumeId(add.getResumeId());
        }
        return flag;
    }

    /**
     * 修改用户简历
     *
     * @param bo 简历信息
     * @return 修改结果
     */
    @Override
    public Boolean updateByBo(UserResumeBo bo) {
        UserResume update = MapstructUtils.convert(bo, UserResume.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 重命名简历
     *
     * @param resumeId   简历ID
     * @param resumeName 新名称
     * @return 重命名结果
     */
    @Override
    public Boolean renameResume(Long resumeId, String resumeName) {
        if (StrUtil.isBlank(resumeName)) {
            throw new ServiceException("简历名称不能为空");
        }

        LambdaUpdateWrapper<UserResume> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(UserResume::getResumeId, resumeId)
            .set(UserResume::getResumeName, resumeName.trim());

        return baseMapper.update(null, updateWrapper) > 0;
    }

    /**
     * 设置默认简历
     *
     * @param userId   用户ID
     * @param resumeId 简历ID
     * @return 设置结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean setDefaultResume(Long userId, Long resumeId) {
        // 先取消当前用户的所有默认简历
        LambdaUpdateWrapper<UserResume> cancelWrapper = Wrappers.lambdaUpdate();
        cancelWrapper.eq(UserResume::getUserId, userId)
            .set(UserResume::getIsDefault, 0);
        baseMapper.update(null, cancelWrapper);

        // 设置指定简历为默认
        LambdaUpdateWrapper<UserResume> setWrapper = Wrappers.lambdaUpdate();
        setWrapper.eq(UserResume::getResumeId, resumeId)
            .eq(UserResume::getUserId, userId)
            .set(UserResume::getIsDefault, 1);

        return baseMapper.update(null, setWrapper) > 0;
    }

    /**
     * 取消默认简历
     *
     * @param userId   用户ID
     * @param resumeId 简历ID
     * @return 取消结果
     */
    @Override
    public Boolean cancelDefaultResume(Long userId, Long resumeId) {
        LambdaUpdateWrapper<UserResume> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(UserResume::getResumeId, resumeId)
            .eq(UserResume::getUserId, userId)
            .set(UserResume::getIsDefault, 0);

        return baseMapper.update(null, updateWrapper) > 0;
    }

    /**
     * 校验并批量删除用户简历信息
     *
     * @param ids     主键集合
     * @param isValid 是否校验,true-删除前校验,false-不校验
     * @return 删除结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        // 查询要删除的简历信息，用于删除OSS文件
        List<UserResumeVo> resumeList = baseMapper.selectVoByIds(ids);

        // 删除数据库记录
        boolean flag = baseMapper.deleteBatchIds(ids) > 0;

        if (flag) {
            // 删除对应的OSS文件
            for (UserResumeVo resume : resumeList) {
                if (ObjectUtil.isNotNull(resume.getOssId())) {
                    try {
                        ossService.deleteWithValidByIds(List.of(resume.getOssId()), false);
                    } catch (Exception e) {
                        // 文件删除失败不影响数据库删除结果
                        System.err.println("删除OSS文件失败: " + e.getMessage());
                    }
                }
            }
        }

        return flag;
    }

    /**
     * 下载用户简历文件
     *
     * @param resumeId 简历ID
     * @param response 响应对象
     * @throws IOException IO异常
     */
    @Override
    public void downloadResume(Long resumeId, HttpServletResponse response) throws IOException {
        UserResumeVo resume = baseMapper.selectVoById(resumeId);
        if (ObjectUtil.isNull(resume)) {
            throw new ServiceException("简历不存在");
        }

        if (ObjectUtil.isNull(resume.getOssId())) {
            throw new ServiceException("简历文件不存在");
        }

        // 使用OSS服务下载文件
        ossService.download(resume.getOssId(), response);
    }

    /**
     * 获取用户的默认简历
     *
     * @param userId 用户ID
     * @return 默认简历
     */
    @Override
    public UserResumeVo getDefaultResume(Long userId) {
        LambdaQueryWrapper<UserResume> lqw = Wrappers.lambdaQuery();
        lqw.eq(UserResume::getUserId, userId)
            .eq(UserResume::getIsDefault, 1)
            .eq(UserResume::getStatus, "0");

        UserResumeVo vo = baseMapper.selectVoOne(lqw);
        if (ObjectUtil.isNotNull(vo)) {
            formatFileSize(vo);
        }
        return vo;
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(UserResume entity) {
        // TODO 做一些数据校验,如唯一约束
    }

    /**
     * 构建查询条件
     *
     * @param bo 业务对象
     * @return 查询条件
     */
    private LambdaQueryWrapper<UserResume> buildQueryWrapper(UserResumeBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<UserResume> lqw = Wrappers.lambdaQuery();
        lqw.eq(ObjectUtil.isNotNull(bo.getUserId()), UserResume::getUserId, bo.getUserId());
        lqw.like(StringUtils.isNotBlank(bo.getResumeName()), UserResume::getResumeName, bo.getResumeName());
        lqw.like(StringUtils.isNotBlank(bo.getOriginalName()), UserResume::getOriginalName, bo.getOriginalName());
        lqw.eq(StringUtils.isNotBlank(bo.getFileSuffix()), UserResume::getFileSuffix, bo.getFileSuffix());
        lqw.eq(ObjectUtil.isNotNull(bo.getIsDefault()), UserResume::getIsDefault, bo.getIsDefault());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), UserResume::getStatus, bo.getStatus());
        lqw.between(params.get("beginCreateTime") != null && params.get("endCreateTime") != null,
            UserResume::getCreateTime, params.get("beginCreateTime"), params.get("endCreateTime"));
        lqw.orderByDesc(UserResume::getIsDefault);
        lqw.orderByDesc(UserResume::getCreateTime);
        return lqw;
    }

    /**
     * 格式化文件大小
     *
     * @param vo 视图对象
     */
    private void formatFileSize(UserResumeVo vo) {
        if (ObjectUtil.isNotNull(vo.getFileSize())) {
            vo.setFileSizeStr(formatFileSize(vo.getFileSize()));
        }
    }

    /**
     * 格式化文件大小
     *
     * @param size 文件大小(字节)
     * @return 格式化后的大小字符串
     */
    private String formatFileSize(Long size) {
        if (size == null || size <= 0) {
            return "0 B";
        }

        double fileSize = size.doubleValue();
        if (fileSize < 1024) {
            return String.format("%.0f B", fileSize);
        } else if (fileSize < 1024 * 1024) {
            return String.format("%.1f KB", fileSize / 1024);
        } else if (fileSize < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", fileSize / (1024 * 1024));
        } else {
            return String.format("%.1f GB", fileSize / (1024 * 1024 * 1024));
        }
    }

    /**
     * 预览简历文件内容
     *
     * @param resumeId 简历ID
     * @return 预览内容数据
     * @throws Exception 预览异常
     */
    @Override
    public Map<String, Object> previewResumeContent(Long resumeId) throws Exception {
        UserResumeVo resume = baseMapper.selectVoById(resumeId);
        if (ObjectUtil.isNull(resume)) {
            throw new ServiceException("简历不存在");
        }

        if (ObjectUtil.isNull(resume.getOssId())) {
            throw new ServiceException("简历文件不存在");
        }

        // 获取OSS文件信息
        SysOssVo ossFile = ossService.getById(resume.getOssId());
        if (ObjectUtil.isNull(ossFile)) {
            throw new ServiceException("文件已删除或不存在");
        }

        Map<String, Object> result = new HashMap<>();
        String fileSuffix = resume.getFileSuffix().toLowerCase();

        try {
            // 根据文件类型进行处理
            switch (fileSuffix) {
                case "pdf":
                    String pdfContent = extractPdfContent(ossFile);
                    result.put("content", pdfContent);
                    result.put("type", "text");
                    break;
                case "doc":
                    String docContent = extractDocContent(ossFile);
                    result.put("content", docContent);
                    result.put("type", "text");
                    break;
                case "docx":
                    String docxContent = extractDocxContent(ossFile);
                    result.put("content", docxContent);
                    result.put("type", "text");
                    break;
                default:
                    throw new ServiceException("不支持预览该文件类型：" + fileSuffix.toUpperCase());
            }
        } catch (Exception e) {
            throw new ServiceException("文件内容解析失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 获取简历结构化预览内容
     *
     * @param resumeId 简历ID
     * @return 结构化简历内容数据
     * @throws Exception 解析异常
     */
    @Override
    public Map<String, Object> getStructuredResumeContent(Long resumeId) throws Exception {
        // 参数验证
        if (ObjectUtil.isNull(resumeId) || resumeId <= 0) {
            throw new ServiceException("简历ID不能为空或无效");
        }

        UserResumeVo resume = baseMapper.selectVoById(resumeId);
        if (ObjectUtil.isNull(resume)) {
            throw new ServiceException("简历不存在");
        }

        // 检查简历状态
        if (!"0".equals(resume.getStatus())) {
            throw new ServiceException("简历已被禁用，无法预览");
        }

        if (ObjectUtil.isNull(resume.getOssId())) {
            // 如果没有文件，返回基本的结构化数据
            return parseResumeContent(null, resume);
        }

        // 获取OSS文件信息
        SysOssVo ossFile = ossService.getById(resume.getOssId());
        if (ObjectUtil.isNull(ossFile)) {
            // 文件不存在时，返回基本信息
            return parseResumeContent(null, resume);
        }

        String fileContent = null;
        try {
            // 首先获取文件的原始内容
            fileContent = extractFileContent(resume.getFileSuffix().toLowerCase(), ossFile);
        } catch (Exception e) {
            log.warn("提取文件内容失败，使用基本信息: resumeId={}, error={}", resumeId, e.getMessage());
            // 文件内容提取失败时，仍然返回基本信息
        }

        // 解析文件内容为结构化数据
        Map<String, Object> structuredData = parseResumeContent(fileContent, resume);

        return structuredData;
    }

    /**
     * 生成简历预览图片
     *
     * @param resumeId 简历ID
     * @return 预览图片数据
     * @throws Exception 生成异常
     */
    @Override
    public Map<String, Object> generateResumePreviewImage(Long resumeId) throws Exception {
        // 参数验证
        if (ObjectUtil.isNull(resumeId) || resumeId <= 0) {
            throw new ServiceException("简历ID不能为空或无效");
        }

        UserResumeVo resume = baseMapper.selectVoById(resumeId);
        if (ObjectUtil.isNull(resume)) {
            throw new ServiceException("简历不存在");
        }

        // 检查简历状态
        if (!"0".equals(resume.getStatus())) {
            throw new ServiceException("简历已被禁用，无法生成预览图");
        }

        Map<String, Object> result = new HashMap<>();

        try {
            // 目前返回占位图片，后续可以实现真实的图片生成功能
            // 可以使用HTML转图片的方式生成简历预览图
            String baseUrl = "https://via.placeholder.com";
            String resumeNameEncoded = java.net.URLEncoder.encode(
                StringUtils.isNotBlank(resume.getResumeName()) ? resume.getResumeName() : "简历预览图",
                "UTF-8"
            );

            String imageUrl = baseUrl + "/800x1200/f0f0f0/666666?text=" + resumeNameEncoded;
            String thumbnailUrl = baseUrl + "/400x600/f0f0f0/666666?text=" + resumeNameEncoded;

            result.put("imageUrl", imageUrl);
            result.put("thumbnailUrl", thumbnailUrl);
            result.put("resumeId", resumeId);
            result.put("resumeName", resume.getResumeName());

            log.info("生成简历预览图成功: resumeId={}, resumeName={}", resumeId, resume.getResumeName());

        } catch (Exception e) {
            log.error("生成简历预览图失败: resumeId={}, error={}", resumeId, e.getMessage(), e);
            throw new ServiceException("生成预览图失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 提取PDF文件内容
     */
    private String extractPdfContent(SysOssVo ossFile) throws Exception {
//        try (InputStream inputStream = getFileInputStream(ossFile)) {
//            // 将输入流转换为字节数组
//            byte[] pdfBytes = inputStream.readAllBytes();
//            // 尝试使用PDFBox 3.x兼容的方式
//            try (PDDocument document = PDDocument.load(pdfBytes)) {
//                PDFTextStripper stripper = new PDFTextStripper();
//                return stripper.getText(document);
//            }
//        }
        return null;
    }

    /**
     * 提取DOC文件内容
     */
    private String extractDocContent(SysOssVo ossFile) throws Exception {
        try (InputStream inputStream = getFileInputStream(ossFile);
             HWPFDocument document = new HWPFDocument(inputStream);
             WordExtractor extractor = new WordExtractor(document)) {
            return extractor.getText();
        }
    }

    /**
     * 提取DOCX文件内容
     */
    private String extractDocxContent(SysOssVo ossFile) throws Exception {
        try (InputStream inputStream = getFileInputStream(ossFile);
             XWPFDocument document = new XWPFDocument(inputStream)) {
            StringBuilder content = new StringBuilder();
            for (XWPFParagraph paragraph : document.getParagraphs()) {
                content.append(paragraph.getText()).append("\n");
            }
            return content.toString();
        }
    }

    /**
     * 根据文件类型提取文件内容
     */
    private String extractFileContent(String fileSuffix, SysOssVo ossFile) throws Exception {
        switch (fileSuffix) {
            case "pdf":
                return extractPdfContent(ossFile);
            case "doc":
                return extractDocContent(ossFile);
            case "docx":
                return extractDocxContent(ossFile);
            default:
                throw new ServiceException("不支持的文件类型：" + fileSuffix.toUpperCase());
        }
    }

    /**
     * 解析简历内容为结构化数据
     */
    private Map<String, Object> parseResumeContent(String fileContent, UserResumeVo resume) {
        Map<String, Object> result = new HashMap<>();

        // 基本信息
        Map<String, Object> basicInfo = new HashMap<>();
        basicInfo.put("resumeId", resume.getResumeId());
        basicInfo.put("resumeName", resume.getResumeName());
        basicInfo.put("createTime", resume.getCreateTime());
        basicInfo.put("updateTime", resume.getUpdateTime());
        basicInfo.put("isDefault", resume.getIsDefault() == 1);
        result.put("basicInfo", basicInfo);

        // 如果文件内容为空，返回基本的结构化数据
        if (ObjectUtil.isEmpty(fileContent)) {
            result.put("personalInfo", createDefaultPersonalInfo());
            result.put("educationList", new ArrayList<>());
            result.put("workExperienceList", new ArrayList<>());
            result.put("skillList", new ArrayList<>());
            result.put("projectList", new ArrayList<>());
            result.put("awardList", new ArrayList<>());
            result.put("certificateList", new ArrayList<>());
            result.put("languageList", new ArrayList<>());
            result.put("hobbies", new ArrayList<>());
            result.put("selfEvaluation", "");
            return result;
        }

        // 解析文件内容（这里是简化版本，实际项目中可能需要更复杂的NLP解析）
        result.put("personalInfo", parsePersonalInfo(fileContent));
        result.put("educationList", parseEducationList(fileContent));
        result.put("workExperienceList", parseWorkExperienceList(fileContent));
        result.put("skillList", parseSkillList(fileContent));
        result.put("projectList", parseProjectList(fileContent));
        result.put("awardList", parseAwardList(fileContent));
        result.put("certificateList", parseCertificateList(fileContent));
        result.put("languageList", parseLanguageList(fileContent));
        result.put("hobbies", parseHobbies(fileContent));
        result.put("selfEvaluation", parseSelfEvaluation(fileContent));

        return result;
    }

    /**
     * 创建默认个人信息
     */
    private Map<String, Object> createDefaultPersonalInfo() {
        Map<String, Object> personalInfo = new HashMap<>();
        personalInfo.put("name", "");
        personalInfo.put("gender", "");
        personalInfo.put("age", null);
        personalInfo.put("phone", "");
        personalInfo.put("email", "");
        personalInfo.put("address", "");
        personalInfo.put("summary", "");

        Map<String, Object> jobIntention = new HashMap<>();
        jobIntention.put("position", "");
        jobIntention.put("industry", "");
        jobIntention.put("city", "");
        jobIntention.put("salary", "");
        jobIntention.put("jobType", "");
        personalInfo.put("jobIntention", jobIntention);

        return personalInfo;
    }

    /**
     * 解析个人信息
     */
    private Map<String, Object> parsePersonalInfo(String content) {
        Map<String, Object> personalInfo = createDefaultPersonalInfo();

        // 简单的正则表达式解析（实际项目中可能需要更复杂的NLP处理）
        extractField(content, "姓名[：:](.*?)\\n", personalInfo, "name");
        extractField(content, "性别[：:](.*?)\\n", personalInfo, "gender");
        extractField(content, "手机[：:](.*?)\\n", personalInfo, "phone");
        extractField(content, "邮箱[：:](.*?)\\n", personalInfo, "email");
        extractField(content, "地址[：:](.*?)\\n", personalInfo, "address");

        return personalInfo;
    }

    /**
     * 解析教育经历
     */
    private List<Map<String, Object>> parseEducationList(String content) {
        List<Map<String, Object>> educationList = new ArrayList<>();

        // 简化版本：返回空列表，实际项目中需要复杂的文本解析
        // 可以使用正则表达式或NLP技术来提取教育信息

        return educationList;
    }

    /**
     * 解析工作经验
     */
    private List<Map<String, Object>> parseWorkExperienceList(String content) {
        List<Map<String, Object>> workList = new ArrayList<>();

        // 简化版本：返回空列表，实际项目中需要复杂的文本解析

        return workList;
    }

    /**
     * 解析技能列表
     */
    private List<Map<String, Object>> parseSkillList(String content) {
        List<Map<String, Object>> skillList = new ArrayList<>();

        // 简化版本：返回空列表，实际项目中需要复杂的文本解析

        return skillList;
    }

    /**
     * 解析项目经历
     */
    private List<Map<String, Object>> parseProjectList(String content) {
        return new ArrayList<>();
    }

    /**
     * 解析获奖情况
     */
    private List<Map<String, Object>> parseAwardList(String content) {
        return new ArrayList<>();
    }

    /**
     * 解析证书资质
     */
    private List<Map<String, Object>> parseCertificateList(String content) {
        return new ArrayList<>();
    }

    /**
     * 解析语言能力
     */
    private List<Map<String, Object>> parseLanguageList(String content) {
        return new ArrayList<>();
    }

    /**
     * 解析兴趣爱好
     */
    private List<String> parseHobbies(String content) {
        return new ArrayList<>();
    }

    /**
     * 解析自我评价
     */
    private String parseSelfEvaluation(String content) {
        return "";
    }

    /**
     * 提取字段值的辅助方法
     */
    private void extractField(String content, String regex, Map<String, Object> target, String key) {
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(content);
        if (matcher.find()) {
            String value = matcher.group(1).trim();
            if (StrUtil.isNotBlank(value)) {
                target.put(key, value);
            }
        }
    }

    /**
     * 获取OSS文件的输入流
     */
    private InputStream getFileInputStream(SysOssVo ossFile) throws Exception {
        try {
            // 获取OSS客户端
            OssClient ossClient = OssFactory.instance(ossFile.getService());
            // 获取文件的相对路径（去除域名部分）
            String filePath = ossClient.removeBaseUrl(ossFile.getUrl());
            // 直接获取文件内容的输入流
            return ossClient.getObjectContent(filePath);
        } catch (Exception e) {
            throw new ServiceException("获取文件流失败：" + e.getMessage());
        }
    }
}
