package org.dromara.app.service;

import org.dromara.app.domain.bo.CommentQueryBo;
import org.dromara.app.domain.bo.VideoCommentBo;
import org.dromara.app.domain.bo.VideoQueryBo;
import org.dromara.app.domain.vo.*;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.List;

/**
 * 视频课程Service接口
 *
 * <AUTHOR>
 */
public interface IVideoService {

    /**
     * 查询视频课程列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页查询条件
     * @return 视频列表
     */
    VideoListResultVo queryVideoList(VideoQueryBo bo);

    /**
     * 获取视频详情
     *
     * @param videoId 视频ID
     * @param userId  用户ID
     * @return 视频详情
     */
    VideoDetailVo getVideoDetail(Long videoId, Long userId);

    /**
     * 获取学习统计数据
     *
     * @param userId 用户ID
     * @return 学习统计
     */
    VideoLearningStatsVo getLearningStats(Long userId);

    /**
     * 获取收藏的视频
     *
     * @param userId    用户ID
     * @param bo        查询条件
     * @param pageQuery 分页查询条件
     * @return 收藏视频列表
     */
    VideoListResultVo getBookmarkedVideos(Long userId, VideoQueryBo bo, PageQuery pageQuery);

    /**
     * 获取已购买的视频
     *
     * @param userId    用户ID
     * @param bo        查询条件
     * @param pageQuery 分页查询条件
     * @return 已购买视频列表
     */
    VideoListResultVo getPurchasedVideos(Long userId, VideoQueryBo bo, PageQuery pageQuery);

    /**
     * 获取学习历史
     *
     * @param userId    用户ID
     * @param bo        查询条件
     * @param pageQuery 分页查询条件
     * @return 学习历史列表
     */
    VideoListResultVo getLearningHistory(Long userId, VideoQueryBo bo, PageQuery pageQuery);

    /**
     * 获取相关推荐视频
     *
     * @param videoId 视频ID
     * @param limit   限制数量
     * @return 相关推荐视频列表
     */
    List<RelatedVideoVo> getRelatedVideos(Long videoId, Integer limit);

    /**
     * 切换视频点赞状态
     *
     * @param videoId 视频ID
     * @param userId  用户ID
     * @param isLike  是否点赞
     */
    void toggleVideoLike(Long videoId, Long userId, Boolean isLike);

    /**
     * 切换视频收藏状态
     *
     * @param videoId   视频ID
     * @param userId    用户ID
     * @param isCollect 是否收藏
     */
    void toggleVideoCollect(Long videoId, Long userId, Boolean isCollect);

    /**
     * 分享视频
     *
     * @param videoId  视频ID
     * @param userId   用户ID
     * @param platform 分享平台
     */
    void shareVideo(Long videoId, Long userId, String platform);

    /**
     * 增加视频播放次数
     *
     * @param videoId 视频ID
     */
    void incrementVideoView(Long videoId);

    /**
     * 更新视频播放进度
     *
     * @param videoId     视频ID
     * @param userId      用户ID
     * @param currentTime 当前播放时间
     * @param duration    视频总时长
     */
    void updateVideoProgress(Long videoId, Long userId, Integer currentTime, Integer duration);

    /**
     * 获取视频播放记录
     *
     * @param videoId 视频ID
     * @param userId  用户ID
     * @return 播放记录
     */
    VideoPlayRecordVo getVideoPlayRecord(Long videoId, Long userId);

    /**
     * 检查视频购买状态
     *
     * @param videoId 视频ID
     * @param userId  用户ID
     * @return 购买状态
     */
    VideoPurchaseStatusVo checkVideoPurchaseStatus(Long videoId, Long userId);

    /**
     * 关注/取关讲师
     *
     * @param instructorId 讲师ID
     * @param userId       用户ID
     * @param isFollow     是否关注
     */
    void toggleInstructorFollow(Long instructorId, Long userId, Boolean isFollow);

    /**
     * 获取视频评论列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页查询条件
     * @return 评论列表
     */
    VideoCommentListVo getVideoComments(CommentQueryBo bo, PageQuery pageQuery);

    /**
     * 发布视频评论
     *
     * @param bo 评论信息
     * @return 评论详情
     */
    VideoCommentVo publishVideoComment(VideoCommentBo bo);

    /**
     * 切换评论点赞状态
     *
     * @param commentId 评论ID
     * @param userId    用户ID
     * @param isLike    是否点赞
     */
    void toggleCommentLike(Long commentId, Long userId, Boolean isLike);

    /**
     * 保存视频播放记录
     *
     * @param videoId 视频ID
     * @param userId  用户ID
     */
    void saveVideoPlayRecord(Long videoId, Long userId);

    /**
     * 获取热门视频列表
     *
     * @param limit 限制数量
     * @return 热门视频列表
     */
    List<VideoDetailVo> getHotVideos(Integer limit);
}
