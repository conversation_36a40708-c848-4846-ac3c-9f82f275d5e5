package org.dromara.app.service;

import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * 支付SSE服务接口
 * 用于管理SSE连接和推送支付状态消息
 *
 * <AUTHOR>
 */
public interface IPaymentSseService {

    /**
     * 验证支付token
     *
     * @param orderNo  订单号
     * @param payToken 支付token
     * @return 验证结果
     */
    boolean validatePaymentToken(String orderNo, String payToken);

    /**
     * 创建SSE连接
     *
     * @param orderNo  订单号
     * @param payToken 支付token
     * @return SSE发射器
     */
    SseEmitter createConnection(String orderNo, String payToken);

    /**
     * 推送支付成功消息
     *
     * @param orderNo 订单号
     */
    void pushPaymentSuccess(String orderNo);

    /**
     * 推送支付失败消息
     *
     * @param orderNo 订单号
     * @param reason  失败原因
     */
    void pushPaymentFailed(String orderNo, String reason);

    /**
     * 推送支付取消消息
     *
     * @param orderNo 订单号
     */
    void pushPaymentCancelled(String orderNo);

    /**
     * 手动查询订单状态
     *
     * @param orderNo  订单号
     * @param payToken 支付token
     */
    void manualQueryOrderStatus(String orderNo, String payToken);

    /**
     * 关闭SSE连接
     *
     * @param orderNo 订单号
     */
    void closeConnection(String orderNo);

    /**
     * 清理过期连接
     */
    void cleanupExpiredConnections();
}
