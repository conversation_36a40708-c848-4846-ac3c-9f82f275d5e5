{"doc": "\n 支付宝配置属性类\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "enabled", "doc": "\n 支付宝功能开关\r\n"}, {"name": "environment", "doc": "\n 环境类型 - sandbox(沙箱) 或 production(生产)\r\n"}, {"name": "appId", "doc": "\n 应用ID - 支付宝应用ID\r\n"}, {"name": "privateKey", "doc": "\n 商户私钥 - 应用私钥\r\n"}, {"name": "alipayPublicKey", "doc": "\n 支付宝公钥 - 支付宝公钥\r\n"}, {"name": "notifyUrl", "doc": "\n 服务器异步通知页面路径 - 支付成功后支付宝异步通知的地址\r\n"}, {"name": "returnUrl", "doc": "\n 页面跳转同步通知页面路径 - 支付成功后页面跳转的地址\r\n"}, {"name": "serverUrl", "doc": "\n 应用服务器地址 - 用于构建回调URL的基础地址\r\n"}, {"name": "signType", "doc": "\n 签名方式 - 默认RSA2\r\n"}, {"name": "charset", "doc": "\n 字符编码格式 - 默认UTF-8\r\n"}, {"name": "format", "doc": "\n 返回格式 - 默认JSON\r\n"}], "enumConstants": [], "methods": [{"name": "getGatewayUrl", "paramTypes": [], "doc": "\n 获取支付宝网关地址\r\n 根据环境类型自动选择对应的网关地址\r\n\r\n @return 网关地址\r\n"}, {"name": "isSandbox", "paramTypes": [], "doc": "\n 是否为沙箱环境\r\n\r\n @return true-沙箱环境 false-生产环境\r\n"}, {"name": "getNotifyUrl", "paramTypes": [], "doc": "\n 获取异步通知URL，如果未配置则使用默认值\r\n\r\n @return 异步通知URL\r\n"}, {"name": "setNotifyUrl", "paramTypes": ["java.lang.String"], "doc": "\n 设置异步通知URL\r\n\r\n @param notifyUrl 异步通知URL\r\n"}, {"name": "getReturnUrl", "paramTypes": [], "doc": "\n 获取同步回调URL，如果未配置则使用默认值\r\n\r\n @return 同步回调URL\r\n"}, {"name": "setReturnUrl", "paramTypes": ["java.lang.String"], "doc": "\n 设置同步回调URL\r\n\r\n @param returnUrl 同步回调URL\r\n"}, {"name": "buildDefaultNotifyUrl", "paramTypes": [], "doc": "\n 构建默认的异步通知URL\r\n\r\n @return 默认异步通知URL\r\n"}, {"name": "buildDefaultReturnUrl", "paramTypes": [], "doc": "\n 构建默认的同步回调URL\r\n\r\n @return 默认同步回调URL\r\n"}, {"name": "normalizeServerUrl", "paramTypes": [], "doc": "\n 规范化服务器URL，确保不以斜杠结尾\r\n\r\n @return 规范化后的服务器URL\r\n"}], "constructors": []}