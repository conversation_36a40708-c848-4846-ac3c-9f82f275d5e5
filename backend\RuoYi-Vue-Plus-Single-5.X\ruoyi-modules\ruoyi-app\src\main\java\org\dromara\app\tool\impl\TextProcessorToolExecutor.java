package org.dromara.app.tool.impl;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.service.IToolService;
import org.dromara.app.tool.ToolExecutor;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 文本处理工具执行器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class TextProcessorToolExecutor implements ToolExecutor {

    @Override
    public IToolService.ToolCallResult execute(Map<String, Object> parameters, IToolService.ToolCallContext context) {
        try {
            String text = (String) parameters.get("text");
            String operation = (String) parameters.get("operation");

            if (StrUtil.isBlank(text)) {
                return new IToolService.ToolCallResult(false, null, "文本内容不能为空");
            }

            if (StrUtil.isBlank(operation)) {
                return new IToolService.ToolCallResult(false, null, "操作类型不能为空");
            }

            Object result = processText(text, operation, parameters);

            return new IToolService.ToolCallResult(true, result, "文本处理成功");

        } catch (Exception e) {
            log.error("文本处理失败", e);
            return new IToolService.ToolCallResult(false, null, "文本处理失败: " + e.getMessage());
        }
    }

    @Override
    public String getToolId() {
        return "text_processor";
    }

    @Override
    public String getToolName() {
        return "文本处理器";
    }

    @Override
    public String getDescription() {
        return "处理和分析文本内容，支持统计、格式化、提取等操作";
    }

    /**
     * 处理文本
     */
    private Object processText(String text, String operation, Map<String, Object> parameters) {
        switch (operation.toLowerCase()) {
            case "count":
                return countText(text);
            case "format":
                return formatText(text, parameters);
            case "extract":
                return extractText(text, parameters);
            case "replace":
                return replaceText(text, parameters);
            case "analyze":
                return analyzeText(text);
            default:
                throw new IllegalArgumentException("不支持的操作类型: " + operation);
        }
    }

    /**
     * 统计文本
     */
    private Map<String, Object> countText(String text) {
        Map<String, Object> result = new HashMap<>();

        result.put("characters", text.length());
        result.put("charactersNoSpaces", text.replaceAll("\\s", "").length());
        result.put("words", text.split("\\s+").length);
        result.put("lines", text.split("\n").length);
        result.put("paragraphs", text.split("\n\\s*\n").length);

        return result;
    }

    /**
     * 格式化文本
     */
    private String formatText(String text, Map<String, Object> parameters) {
        String formatType = (String) parameters.get("formatType");

        if (formatType == null) {
            formatType = "trim";
        }

        switch (formatType.toLowerCase()) {
            case "uppercase":
                return text.toUpperCase();
            case "lowercase":
                return text.toLowerCase();
            case "capitalize":
                return capitalizeWords(text);
            case "trim":
                return text.trim();
            case "removeSpaces":
                return text.replaceAll("\\s+", " ");
            case "removeEmptyLines":
                return text.replaceAll("(?m)^\\s*$[\n\r]{1,}", "");
            default:
                return text;
        }
    }

    /**
     * 提取文本
     */
    private Object extractText(String text, Map<String, Object> parameters) {
        String extractType = (String) parameters.get("extractType");

        if (extractType == null) {
            extractType = "emails";
        }

        switch (extractType.toLowerCase()) {
            case "emails":
                return extractEmails(text);
            case "urls":
                return extractUrls(text);
            case "phones":
                return extractPhones(text);
            case "numbers":
                return extractNumbers(text);
            case "keywords":
                return extractKeywords(text);
            default:
                return Collections.emptyList();
        }
    }

    /**
     * 替换文本
     */
    private String replaceText(String text, Map<String, Object> parameters) {
        String searchText = (String) parameters.get("search");
        String replaceText = (String) parameters.get("replace");
        Boolean useRegex = (Boolean) parameters.get("useRegex");

        if (searchText == null) {
            return text;
        }

        if (replaceText == null) {
            replaceText = "";
        }

        if (Boolean.TRUE.equals(useRegex)) {
            return text.replaceAll(searchText, replaceText);
        } else {
            return text.replace(searchText, replaceText);
        }
    }

    /**
     * 分析文本
     */
    private Map<String, Object> analyzeText(String text) {
        Map<String, Object> result = new HashMap<>();

        // 基础统计
        result.putAll(countText(text));

        // 语言检测（简单实现）
        result.put("language", detectLanguage(text));

        // 关键词提取
        result.put("keywords", extractKeywords(text));

        // 情感分析（简单实现）
        result.put("sentiment", analyzeSentiment(text));

        return result;
    }

    /**
     * 首字母大写
     */
    private String capitalizeWords(String text) {
        StringBuilder result = new StringBuilder();
        boolean capitalizeNext = true;

        for (char c : text.toCharArray()) {
            if (Character.isWhitespace(c)) {
                capitalizeNext = true;
                result.append(c);
            } else if (capitalizeNext) {
                result.append(Character.toUpperCase(c));
                capitalizeNext = false;
            } else {
                result.append(Character.toLowerCase(c));
            }
        }

        return result.toString();
    }

    /**
     * 提取邮箱
     */
    private List<String> extractEmails(String text) {
        List<String> emails = new ArrayList<>();
        Pattern pattern = Pattern.compile("\\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Z|a-z]{2,}\\b");
        Matcher matcher = pattern.matcher(text);

        while (matcher.find()) {
            emails.add(matcher.group());
        }

        return emails;
    }

    /**
     * 提取URL
     */
    private List<String> extractUrls(String text) {
        List<String> urls = new ArrayList<>();
        Pattern pattern = Pattern.compile("https?://[\\w\\-_]+(\\.[\\w\\-_]+)+([\\w\\-\\.,@?^=%&:/~\\+#]*[\\w\\-\\@?^=%&/~\\+#])?");
        Matcher matcher = pattern.matcher(text);

        while (matcher.find()) {
            urls.add(matcher.group());
        }

        return urls;
    }

    /**
     * 提取电话号码
     */
    private List<String> extractPhones(String text) {
        List<String> phones = new ArrayList<>();
        Pattern pattern = Pattern.compile("\\b\\d{3}[-.]?\\d{3}[-.]?\\d{4}\\b|\\b\\d{11}\\b");
        Matcher matcher = pattern.matcher(text);

        while (matcher.find()) {
            phones.add(matcher.group());
        }

        return phones;
    }

    /**
     * 提取数字
     */
    private List<String> extractNumbers(String text) {
        List<String> numbers = new ArrayList<>();
        Pattern pattern = Pattern.compile("\\b\\d+(\\.\\d+)?\\b");
        Matcher matcher = pattern.matcher(text);

        while (matcher.find()) {
            numbers.add(matcher.group());
        }

        return numbers;
    }

    /**
     * 提取关键词（简单实现）
     */
    private List<String> extractKeywords(String text) {
        // 简单的关键词提取：去除常见停用词，统计词频
        Set<String> stopWords = Set.of("的", "是", "在", "了", "和", "有", "为", "不", "这", "那", "一", "个", "上", "下", "来", "去", "会", "能", "要", "也", "就", "都", "可以", "但是", "如果", "因为", "所以", "虽然", "然后", "the", "a", "an", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by", "is", "are", "was", "were", "be", "been", "have", "has", "had", "do", "does", "did", "will", "would", "could", "should", "may", "might", "can", "must");

        String[] words = text.toLowerCase().replaceAll("[^\\w\\s]", "").split("\\s+");
        Map<String, Integer> wordCount = new HashMap<>();

        for (String word : words) {
            if (word.length() > 2 && !stopWords.contains(word)) {
                wordCount.put(word, wordCount.getOrDefault(word, 0) + 1);
            }
        }

        return wordCount.entrySet().stream()
            .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
            .limit(10)
            .map(Map.Entry::getKey)
            .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
    }

    /**
     * 检测语言（简单实现）
     */
    private String detectLanguage(String text) {
        // 简单的语言检测
        if (text.matches(".*[\\u4e00-\\u9fa5].*")) {
            return "zh";
        } else if (text.matches(".*[a-zA-Z].*")) {
            return "en";
        } else {
            return "unknown";
        }
    }

    /**
     * 情感分析（简单实现）
     */
    private String analyzeSentiment(String text) {
        // 简单的情感分析
        Set<String> positiveWords = Set.of("好", "棒", "优秀", "成功", "喜欢", "满意", "高兴", "快乐", "great", "good", "excellent", "amazing", "wonderful", "fantastic", "awesome", "love", "like", "happy", "joy");
        Set<String> negativeWords = Set.of("坏", "差", "失败", "讨厌", "不满", "难过", "生气", "糟糕", "bad", "terrible", "awful", "hate", "dislike", "sad", "angry", "disappointed", "frustrated");

        String[] words = text.toLowerCase().replaceAll("[^\\w\\s]", "").split("\\s+");
        int positiveCount = 0;
        int negativeCount = 0;

        for (String word : words) {
            if (positiveWords.contains(word)) {
                positiveCount++;
            } else if (negativeWords.contains(word)) {
                negativeCount++;
            }
        }

        if (positiveCount > negativeCount) {
            return "positive";
        } else if (negativeCount > positiveCount) {
            return "negative";
        } else {
            return "neutral";
        }
    }
}
