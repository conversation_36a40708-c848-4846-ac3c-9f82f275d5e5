package org.dromara.app.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.VectorEmbedding;
import org.dromara.app.service.PromptEngineeringService;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Prompt工程服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class PromptEngineeringServiceImpl implements PromptEngineeringService {

    /**
     * 构建RAG增强提示词
     *
     * @param userQuery        用户查询
     * @param retrievalResults 检索结果
     * @param agentType        Agent类型
     * @param contextWindow    上下文窗口大小
     * @return 增强后的提示词
     */
    @Override
    public String buildRagEnhancedPrompt(String userQuery, List<VectorEmbedding> retrievalResults,
                                         String agentType, int contextWindow) {
        StringBuilder promptBuilder = new StringBuilder();

        // 添加系统指令
        promptBuilder.append("你是一个专业的").append(agentType).append("助手。请基于以下检索到的相关信息回答用户的问题。\n\n");

        // 添加检索内容
        promptBuilder.append("相关信息：\n");

        // 限制检索结果数量以符合上下文窗口大小
        int totalTokenCount = 0;
        List<VectorEmbedding> filteredResults = new ArrayList<>();

        for (VectorEmbedding embedding : retrievalResults) {
            int estimatedTokens = estimateTokenCount(embedding.getContent());
            if (totalTokenCount + estimatedTokens <= contextWindow) {
                filteredResults.add(embedding);
                totalTokenCount += estimatedTokens;
            } else {
                break;
            }
        }

        // 按相关性排序
        filteredResults.sort(Comparator.comparing(VectorEmbedding::getSimilarity).reversed());

        // 添加检索内容到提示词
        for (int i = 0; i < filteredResults.size(); i++) {
            VectorEmbedding embedding = filteredResults.get(i);
            promptBuilder.append("[").append(i + 1).append("] ");
            promptBuilder.append(embedding.getContent());
            promptBuilder.append("\n\n");
        }

        // 添加用户查询
        promptBuilder.append("用户问题：").append(userQuery).append("\n\n");

        // 添加响应指引
        promptBuilder.append("请根据上述信息提供详细、准确的回答。如果相关信息中不包含回答问题所需的内容，请明确指出并基于你的知识谨慎回答。");

        return promptBuilder.toString();
    }

    /**
     * 构建工具调用提示词
     *
     * @param userQuery           用户查询
     * @param availableTools      可用工具列表
     * @param conversationHistory 对话历史
     * @return 工具调用提示词
     */
    @Override
    public String buildToolCallPrompt(String userQuery, List<Map<String, Object>> availableTools,
                                      List<String> conversationHistory) {
        StringBuilder promptBuilder = new StringBuilder();

        // 添加系统指令
        promptBuilder.append("你是一个能够使用工具的rjb-sias。根据用户的需求，你可以决定使用以下工具：\n\n");

        // 添加可用工具描述
        for (int i = 0; i < availableTools.size(); i++) {
            Map<String, Object> tool = availableTools.get(i);
            promptBuilder.append("工具").append(i + 1).append("：").append(tool.get("name")).append("\n");
            promptBuilder.append("描述：").append(tool.get("description")).append("\n");
            promptBuilder.append("参数：").append(tool.get("parameters")).append("\n\n");
        }

        // 添加对话历史（如果有）
        if (conversationHistory != null && !conversationHistory.isEmpty()) {
            promptBuilder.append("对话历史：\n");
            for (String turn : conversationHistory) {
                promptBuilder.append(turn).append("\n");
            }
            promptBuilder.append("\n");
        }

        // 添加用户查询
        promptBuilder.append("用户问题：").append(userQuery).append("\n\n");

        // 添加响应指引
        promptBuilder.append("如果用户的请求需要使用工具，请按以下格式调用工具：\n");
        promptBuilder.append("```tool-call\n");
        promptBuilder.append("{\n");
        promptBuilder.append("  \"tool\": \"工具名称\",\n");
        promptBuilder.append("  \"parameters\": {\n");
        promptBuilder.append("    \"参数1\": \"值1\",\n");
        promptBuilder.append("    \"参数2\": \"值2\"\n");
        promptBuilder.append("  }\n");
        promptBuilder.append("}\n");
        promptBuilder.append("```\n\n");

        promptBuilder.append("如果不需要使用工具，请直接回答用户的问题。");

        return promptBuilder.toString();
    }

    /**
     * 优化系统提示词
     *
     * @param originalPrompt 原始提示词
     * @param agentType      Agent类型
     * @param userContext    用户上下文
     * @return 优化后的提示词
     */
    @Override
    public String optimizeSystemPrompt(String originalPrompt, String agentType, Map<String, Object> userContext) {
        StringBuilder promptBuilder = new StringBuilder(originalPrompt);

        // 根据Agent类型添加特定指导
        if ("客服".equals(agentType)) {
            promptBuilder.append("\n\n你是一个专业的客服代表，应保持礼貌、耐心，并提供准确的信息。");
        } else if ("技术支持".equals(agentType)) {
            promptBuilder.append("\n\n你是一个技术支持专家，应提供详细的技术解决方案和步骤指导。");
        } else if ("销售".equals(agentType)) {
            promptBuilder.append("\n\n你是一个销售顾问，应了解产品优势并提供有说服力的信息。");
        }

        // 根据用户上下文添加个性化指导
        if (userContext != null) {
            promptBuilder.append("\n\n用户背景信息：\n");

            if (userContext.containsKey("preferredLanguage")) {
                promptBuilder.append("- 优先使用").append(userContext.get("preferredLanguage")).append("语言回复\n");
            }

            if (userContext.containsKey("technicalLevel")) {
                promptBuilder.append("- 技术水平：").append(userContext.get("technicalLevel")).append("\n");
            }

            if (userContext.containsKey("previousInteractions")) {
                promptBuilder.append("- 参考之前的交互历史进行回答\n");
            }
        }

        // 添加通用优化指导
        promptBuilder.append("\n\n请注意：\n");
        promptBuilder.append("1. 回答应清晰、简洁、准确\n");
        promptBuilder.append("2. 如果不确定，请坦诚表明\n");
        promptBuilder.append("3. 避免提供误导性或过时的信息\n");
        promptBuilder.append("4. 回应应当与用户查询保持相关性");

        return promptBuilder.toString();
    }

    /**
     * 构建多轮对话提示词
     *
     * @param currentQuery        当前查询
     * @param conversationHistory 对话历史
     * @param maxHistoryLength    最大历史长度
     * @return 多轮对话提示词
     */
    @Override
    public String buildConversationalPrompt(String currentQuery, List<ConversationTurn> conversationHistory,
                                            int maxHistoryLength) {
        StringBuilder promptBuilder = new StringBuilder();

        // 添加系统指导
        promptBuilder.append("以下是与rjb-sias的对话历史，请基于历史上下文回答当前问题。\n\n");

        // 裁剪历史对话，保留最近的对话
        List<ConversationTurn> recentHistory = trimConversationHistory(conversationHistory, maxHistoryLength);

        // 添加对话历史
        for (ConversationTurn turn : recentHistory) {
            String roleDisplay = "用户";
            if ("assistant".equals(turn.getRole())) {
                roleDisplay = "AI";
            } else if ("system".equals(turn.getRole())) {
                roleDisplay = "系统";
            }

            promptBuilder.append(roleDisplay).append("：").append(turn.getContent()).append("\n\n");
        }

        // 添加当前查询
        promptBuilder.append("用户：").append(currentQuery).append("\n\n");
        promptBuilder.append("AI：");

        return promptBuilder.toString();
    }

    /**
     * 构建思维链提示词
     *
     * @param userQuery 用户查询
     * @param taskType  任务类型
     * @return 思维链提示词
     */
    @Override
    public String buildChainOfThoughtPrompt(String userQuery, String taskType) {
        StringBuilder promptBuilder = new StringBuilder();

        // 基本指导
        promptBuilder.append("请一步一步地思考以下问题：\n\n");
        promptBuilder.append("问题：").append(userQuery).append("\n\n");

        // 根据任务类型添加特定的思维指导
        if ("数学".equals(taskType)) {
            promptBuilder.append("让我们一步步地解决这个数学问题：\n");
            promptBuilder.append("1. 首先，确定已知条件和需要求解的目标\n");
            promptBuilder.append("2. 识别可以应用的数学概念或公式\n");
            promptBuilder.append("3. 列出解题步骤\n");
            promptBuilder.append("4. 计算结果并验证\n");
            promptBuilder.append("5. 给出最终答案\n\n");
        } else if ("逻辑推理".equals(taskType)) {
            promptBuilder.append("让我们通过逻辑推理解决这个问题：\n");
            promptBuilder.append("1. 分析已知前提条件\n");
            promptBuilder.append("2. 确定相关的逻辑规则\n");
            promptBuilder.append("3. 通过推理得出中间结论\n");
            promptBuilder.append("4. 综合推理得出最终结论\n");
            promptBuilder.append("5. 验证结论的合理性\n\n");
        } else if ("决策分析".equals(taskType)) {
            promptBuilder.append("让我们分析这个决策问题：\n");
            promptBuilder.append("1. 明确决策目标和约束条件\n");
            promptBuilder.append("2. 列出可行的选项\n");
            promptBuilder.append("3. 评估每个选项的优缺点\n");
            promptBuilder.append("4. 比较不同选项\n");
            promptBuilder.append("5. 给出最优决策建议\n\n");
        } else {
            // 通用思维链
            promptBuilder.append("让我们分步思考：\n");
            promptBuilder.append("1. 理解问题的核心要点\n");
            promptBuilder.append("2. 分析相关因素\n");
            promptBuilder.append("3. 考虑可能的解决方案\n");
            promptBuilder.append("4. 评估每种方案\n");
            promptBuilder.append("5. 得出结论\n\n");
        }

        promptBuilder.append("思考过程：\n");

        return promptBuilder.toString();
    }

    /**
     * 构建少样本学习提示词
     *
     * @param userQuery       用户查询
     * @param examples        示例列表
     * @param taskDescription 任务描述
     * @return 少样本学习提示词
     */
    @Override
    public String buildFewShotPrompt(String userQuery, List<PromptExample> examples, String taskDescription) {
        StringBuilder promptBuilder = new StringBuilder();

        // 添加任务描述
        promptBuilder.append(taskDescription).append("\n\n");
        promptBuilder.append("以下是一些示例：\n\n");

        // 添加示例
        for (int i = 0; i < examples.size(); i++) {
            PromptExample example = examples.get(i);
            promptBuilder.append("示例 ").append(i + 1).append("：\n");
            promptBuilder.append("输入：").append(example.getInput()).append("\n");
            promptBuilder.append("输出：").append(example.getOutput()).append("\n");

            if (example.getExplanation() != null && !example.getExplanation().isEmpty()) {
                promptBuilder.append("解释：").append(example.getExplanation()).append("\n");
            }

            promptBuilder.append("\n");
        }

        // 添加用户查询
        promptBuilder.append("现在，请按照上面的示例格式回答以下问题：\n\n");
        promptBuilder.append("输入：").append(userQuery).append("\n");
        promptBuilder.append("输出：");

        return promptBuilder.toString();
    }

    /**
     * 动态调整提示词长度
     *
     * @param prompt    原始提示词
     * @param maxTokens 最大token数
     * @param priority  优先级策略
     * @return 调整后的提示词
     */
    @Override
    public String adjustPromptLength(String prompt, int maxTokens, LengthAdjustmentStrategy priority) {
        // 估计提示词的token数量
        int estimatedTokens = estimateTokenCount(prompt);

        // 如果提示词已经在限制范围内，直接返回
        if (estimatedTokens <= maxTokens) {
            return prompt;
        }

        // 根据不同的优先级策略调整提示词长度
        return switch (priority) {
            case TRUNCATE_MIDDLE -> truncateMiddle(prompt, maxTokens);
            case COMPRESS_CONTENT -> compressContent(prompt, maxTokens);
            case PRIORITIZE_RECENT -> prioritizeRecent(prompt, maxTokens);
            case PRIORITIZE_RELEVANT -> prioritizeRelevant(prompt, maxTokens);
            default -> truncateEnd(prompt, maxTokens);
        };
    }

    /**
     * 提取关键信息
     *
     * @param content   内容
     * @param maxLength 最大长度
     * @return 关键信息
     */
    @Override
    public String extractKeyInformation(String content, int maxLength) {
        // 如果内容已经在限制长度内，直接返回
        if (content.length() <= maxLength) {
            return content;
        }

        // 简单的关键信息提取实现
        // 1. 按句子分割内容
        String[] sentences = content.split("[.!?。！？]");

        // 2. 计算每个句子的重要性分数（这里使用简单的启发式方法）
        Map<String, Double> sentenceScores = new HashMap<>();
        for (String sentence : sentences) {
            String trimmed = sentence.trim();
            if (trimmed.isEmpty()) {
                continue;
            }

            // 计算重要性分数（这里使用句子长度和关键词频率的简单组合）
            double score = calculateSentenceImportance(trimmed);
            sentenceScores.put(trimmed, score);
        }

        // 3. 按重要性排序句子
        List<String> sortedSentences = sentenceScores.entrySet().stream()
            .sorted(Map.Entry.<String, Double>comparingByValue().reversed())
            .map(Map.Entry::getKey)
            .collect(Collectors.toList());

        // 4. 从最重要的句子开始，构建摘要，直到达到最大长度
        StringBuilder summary = new StringBuilder();
        for (String sentence : sortedSentences) {
            if (summary.length() + sentence.length() + 2 <= maxLength) {
                summary.append(sentence).append(". ");
            } else {
                break;
            }
        }

        return summary.toString().trim();
    }

    // 辅助方法

    /**
     * 估计文本的token数量（简化版本）
     * 注意：这是一个简化的估算方法，实际token数会因模型和分词器而异
     *
     * @param text 文本
     * @return 估计的token数量
     */
    private int estimateTokenCount(String text) {
        if (text == null || text.isEmpty()) {
            return 0;
        }

        // 粗略估计：英文平均每4个字符约为1个token，中文每个字符约为1个token
        int englishChars = 0;
        int chineseChars = 0;

        for (char c : text.toCharArray()) {
            if (Character.UnicodeBlock.of(c) == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS) {
                chineseChars++;
            } else {
                englishChars++;
            }
        }

        return chineseChars + (englishChars / 4);
    }

    /**
     * 截断提示词末尾
     *
     * @param prompt    提示词
     * @param maxTokens 最大token数
     * @return 调整后的提示词
     */
    private String truncateEnd(String prompt, int maxTokens) {
        // 估算每个字符的平均token数
        double avgTokensPerChar = 0.25; // 假设平均4个字符为1个token

        // 计算需要保留的字符数
        int charsToKeep = (int) (maxTokens / avgTokensPerChar);

        // 确保不超出原始字符串长度
        charsToKeep = Math.min(charsToKeep, prompt.length());

        // 截断并添加指示符
        return prompt.substring(0, charsToKeep) + "...（内容因长度限制被截断）";
    }

    /**
     * 截断提示词中间部分
     *
     * @param prompt    提示词
     * @param maxTokens 最大token数
     * @return 调整后的提示词
     */
    private String truncateMiddle(String prompt, int maxTokens) {
        // 估算每个字符的平均token数
        double avgTokensPerChar = 0.25;

        // 计算需要保留的字符数
        int totalCharsToKeep = (int) (maxTokens / avgTokensPerChar);

        // 确保不超出原始字符串长度
        totalCharsToKeep = Math.min(totalCharsToKeep, prompt.length());

        // 计算开头和结尾各保留多少字符
        int charsForEachEnd = totalCharsToKeep / 2;

        // 截断中间部分
        String start = prompt.substring(0, charsForEachEnd);
        String end = prompt.substring(prompt.length() - charsForEachEnd);

        return start + "...（中间内容因长度限制被省略）..." + end;
    }

    /**
     * 压缩内容（通过删除冗余信息）
     *
     * @param prompt    提示词
     * @param maxTokens 最大token数
     * @return 调整后的提示词
     */
    private String compressContent(String prompt, int maxTokens) {
        // 简单的压缩策略：
        // 1. 删除多余空格
        String compressed = prompt.replaceAll("\\s+", " ");

        // 2. 删除重复段落（简化处理）
        // 这里只是一个简单示例，实际应用中可能需要更复杂的重复检测
        String[] paragraphs = compressed.split("\n\n");
        List<String> uniqueParagraphs = new ArrayList<>();

        for (String paragraph : paragraphs) {
            if (!uniqueParagraphs.contains(paragraph)) {
                uniqueParagraphs.add(paragraph);
            }
        }

        compressed = String.join("\n\n", uniqueParagraphs);

        // 如果仍然超出限制，使用截断末尾方法
        if (estimateTokenCount(compressed) > maxTokens) {
            return truncateEnd(compressed, maxTokens);
        }

        return compressed;
    }

    /**
     * 优先保留最近的内容
     *
     * @param prompt    提示词
     * @param maxTokens 最大token数
     * @return 调整后的提示词
     */
    private String prioritizeRecent(String prompt, int maxTokens) {
        // 假设提示词以段落组织，最近的内容在后面
        String[] paragraphs = prompt.split("\n\n");
        StringBuilder result = new StringBuilder();

        // 从后向前添加段落，直到达到token限制
        for (int i = paragraphs.length - 1; i >= 0; i--) {
            String currentResult = paragraphs[i] + "\n\n" + result.toString();
            if (estimateTokenCount(currentResult) <= maxTokens) {
                result.insert(0, paragraphs[i] + "\n\n");
            } else {
                break;
            }
        }

        // 如果没有添加任何内容（第一个段落就超出限制），则使用截断末尾方法
        if (result.length() == 0) {
            return truncateEnd(paragraphs[paragraphs.length - 1], maxTokens);
        }

        return result.toString().trim();
    }

    /**
     * 优先保留相关的内容
     *
     * @param prompt    提示词
     * @param maxTokens 最大token数
     * @return 调整后的提示词
     */
    private String prioritizeRelevant(String prompt, int maxTokens) {
        // 按段落分割提示词
        String[] paragraphs = prompt.split("\n\n");

        // 计算每个段落的重要性分数
        Map<String, Double> paragraphScores = new HashMap<>();
        for (String paragraph : paragraphs) {
            double score = calculateParagraphRelevance(paragraph);
            paragraphScores.put(paragraph, score);
        }

        // 按重要性排序段落
        List<String> sortedParagraphs = paragraphScores.entrySet().stream()
            .sorted(Map.Entry.<String, Double>comparingByValue().reversed())
            .map(Map.Entry::getKey)
            .collect(Collectors.toList());

        // 从最重要的段落开始，构建结果，直到达到token限制
        StringBuilder result = new StringBuilder();
        for (String paragraph : sortedParagraphs) {
            String currentResult = result.toString() + paragraph + "\n\n";
            if (estimateTokenCount(currentResult) <= maxTokens) {
                result.append(paragraph).append("\n\n");
            } else {
                break;
            }
        }

        return result.toString().trim();
    }

    /**
     * 计算段落相关性（简化版本）
     *
     * @param paragraph 段落
     * @return 相关性分数
     */
    private double calculateParagraphRelevance(String paragraph) {
        // 这里使用一些启发式规则来计算段落重要性
        // 1. 包含关键词的段落更重要
        List<String> keywords = Arrays.asList("重要", "关键", "必须", "要点", "总结", "结论", "核心", "essential", "important", "key", "critical", "summary", "conclusion");

        double score = 1.0;

        // 包含关键词加分
        for (String keyword : keywords) {
            if (paragraph.contains(keyword)) {
                score += 2.0;
            }
        }

        // 段落长度适中加分（太长或太短的段落可能不是核心内容）
        int length = paragraph.length();
        if (length > 50 && length < 500) {
            score += 1.0;
        }

        return score;
    }

    /**
     * 计算句子重要性（简化版本）
     *
     * @param sentence 句子
     * @return 重要性分数
     */
    private double calculateSentenceImportance(String sentence) {
        // 关键词列表
        List<String> keywords = Arrays.asList("重要", "关键", "必须", "要点", "总结", "结论", "核心", "essential", "important", "key", "critical", "summary", "conclusion");

        double score = 1.0;

        // 包含关键词加分
        for (String keyword : keywords) {
            if (sentence.toLowerCase().contains(keyword.toLowerCase())) {
                score += 1.5;
            }
        }

        // 句子长度适中加分
        int length = sentence.length();
        if (length > 10 && length < 100) {
            score += 1.0;
        }

        // 包含数字加分（可能表示重要数据）
        if (sentence.matches(".*\\d+.*")) {
            score += 0.5;
        }

        return score;
    }

    /**
     * 裁剪对话历史，保留最近和最相关的对话
     *
     * @param history   完整对话历史
     * @param maxLength 最大保留长度
     * @return 裁剪后的对话历史
     */
    private List<ConversationTurn> trimConversationHistory(List<ConversationTurn> history, int maxLength) {
        if (history.size() <= maxLength) {
            return new ArrayList<>(history);
        }

        // 默认保留最近的对话
        return history.subList(history.size() - maxLength, history.size());
    }
}
