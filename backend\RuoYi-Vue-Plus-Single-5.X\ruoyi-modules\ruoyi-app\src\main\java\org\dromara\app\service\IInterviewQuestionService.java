package org.dromara.app.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.dromara.app.domain.InterviewQuestion;

import java.util.List;

/**
 * 面试问题Service接口
 *
 * <AUTHOR> Assistant
 * @date 2025-07-19
 */
public interface IInterviewQuestionService {

    /**
     * 根据岗位ID查询问题列表
     *
     * @param jobId      岗位ID
     * @param difficulty 难度等级
     * @param limit      限制数量
     * @return 问题列表
     */
    List<InterviewQuestion> selectByJobId(Long jobId, Integer difficulty, Integer limit);

    /**
     * 根据技术领域查询问题
     *
     * @param technicalDomain 技术领域
     * @param questionType    问题类型
     * @param limit          限制数量
     * @return 问题列表
     */
    List<InterviewQuestion> selectByTechnicalDomain(String technicalDomain, String questionType, Integer limit);

    /**
     * 查询多模态问题
     *
     * @param jobId 岗位ID
     * @param limit 限制数量
     * @return 多模态问题列表
     */
    List<InterviewQuestion> selectMultimodalQuestions(Long jobId, Integer limit);

    /**
     * 根据标签查询问题
     *
     * @param tags  标签列表
     * @param limit 限制数量
     * @return 问题列表
     */
    List<InterviewQuestion> selectByTags(List<String> tags, Integer limit);

    /**
     * 分页查询问题列表
     *
     * @param page           分页参数
     * @param jobId          岗位ID
     * @param questionType   问题类型
     * @param difficulty     难度等级
     * @param category       问题分类
     * @return 分页结果
     */
    IPage<InterviewQuestion> selectQuestionPage(Page<InterviewQuestion> page,
                                              Long jobId,
                                              String questionType,
                                              Integer difficulty,
                                              String category);

    /**
     * 根据难度分级获取问题
     *
     * @param jobId      岗位ID
     * @param easyCount  简单题数量
     * @param mediumCount 中等题数量
     * @param hardCount  困难题数量
     * @return 分级问题列表
     */
    QuestionsByDifficulty getQuestionsByDifficulty(Long jobId, Integer easyCount, Integer mediumCount, Integer hardCount);

    /**
     * 按难度分级的问题结果
     */
    class QuestionsByDifficulty {
        private List<InterviewQuestion> easyQuestions;
        private List<InterviewQuestion> mediumQuestions;
        private List<InterviewQuestion> hardQuestions;

        // getters and setters
        public List<InterviewQuestion> getEasyQuestions() {
            return easyQuestions;
        }

        public void setEasyQuestions(List<InterviewQuestion> easyQuestions) {
            this.easyQuestions = easyQuestions;
        }

        public List<InterviewQuestion> getMediumQuestions() {
            return mediumQuestions;
        }

        public void setMediumQuestions(List<InterviewQuestion> mediumQuestions) {
            this.mediumQuestions = mediumQuestions;
        }

        public List<InterviewQuestion> getHardQuestions() {
            return hardQuestions;
        }

        public void setHardQuestions(List<InterviewQuestion> hardQuestions) {
            this.hardQuestions = hardQuestions;
        }
    }

}