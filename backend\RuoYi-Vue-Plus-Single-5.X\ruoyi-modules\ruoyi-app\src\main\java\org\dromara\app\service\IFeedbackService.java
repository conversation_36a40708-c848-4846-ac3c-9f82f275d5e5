package org.dromara.app.service;

import org.dromara.app.domain.bo.FeedbackBo;
import org.dromara.app.domain.vo.FeedbackVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 意见反馈Service接口
 *
 * <AUTHOR>
 */
public interface IFeedbackService {

    /**
     * 查询意见反馈
     *
     * @param id 意见反馈主键
     * @return 意见反馈
     */
    FeedbackVo queryById(Long id);

    /**
     * 查询意见反馈列表
     *
     * @param bo        意见反馈
     * @param pageQuery 分页查询条件
     * @return 意见反馈分页列表
     */
    TableDataInfo<FeedbackVo> queryPageList(FeedbackBo bo, PageQuery pageQuery);

    /**
     * 查询用户反馈列表
     *
     * @param userId 用户ID
     * @return 反馈列表
     */
    List<FeedbackVo> selectUserFeedbackList(Long userId);

    /**
     * 修改意见反馈
     *
     * @param bo 意见反馈
     * @return 结果
     */
    Boolean updateByBo(FeedbackBo bo);

    /**
     * 校验并批量删除意见反馈信息
     *
     * @param ids     需要删除的意见反馈主键集合
     * @param isValid 是否校验,true-删除前校验,false-不校验
     * @return 结果
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 新增意见反馈
     *
     * @param bo 意见反馈
     * @return 是否新增成功
     */
    Boolean insertByBo(FeedbackBo bo);

    /**
     * 获取用户反馈统计信息
     *
     * @param userId 用户ID
     * @return 统计信息
     */
    Map<String, Object> getUserFeedbackStats(Long userId);
}
