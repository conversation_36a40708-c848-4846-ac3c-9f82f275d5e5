{"doc": "\n 分布式队列工具\r\n 轻量级队列 重量级数据量 请使用 MQ\r\n 要求 redis 5.X 以上\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "getClient", "paramTypes": [], "doc": "\n 获取客户端实例\r\n"}, {"name": "addQueueObject", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": "\n 添加普通队列数据\r\n\r\n @param queueName 队列名\r\n @param data      数据\r\n"}, {"name": "getQueueObject", "paramTypes": ["java.lang.String"], "doc": "\n 通用获取一个队列数据 没有数据返回 null(不支持延迟队列)\r\n\r\n @param queueName 队列名\r\n"}, {"name": "removeQueueObject", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": "\n 通用删除队列数据(不支持延迟队列)\r\n"}, {"name": "destroyQueue", "paramTypes": ["java.lang.String"], "doc": "\n 通用销毁队列 所有阻塞监听 报错(不支持延迟队列)\r\n"}, {"name": "addDelayedQueueObject", "paramTypes": ["java.lang.String", "java.lang.Object", "long"], "doc": "\n 添加延迟队列数据 默认毫秒\r\n\r\n @param queueName 队列名\r\n @param data      数据\r\n @param time      延迟时间\r\n"}, {"name": "addDelayedQueueObject", "paramTypes": ["java.lang.String", "java.lang.Object", "long", "java.util.concurrent.TimeUnit"], "doc": "\n 添加延迟队列数据\r\n\r\n @param queueName 队列名\r\n @param data      数据\r\n @param time      延迟时间\r\n @param timeUnit  单位\r\n"}, {"name": "getDelayedQueueObject", "paramTypes": ["java.lang.String"], "doc": "\n 获取一个延迟队列数据 没有数据返回 null\r\n\r\n @param queueName 队列名\r\n"}, {"name": "removeDelayedQueueObject", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": "\n 删除延迟队列数据\r\n"}, {"name": "destroy<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.String"], "doc": "\n 销毁延迟队列 所有阻塞监听 报错\r\n"}, {"name": "addPriorityQueueObject", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": "\n 添加优先队列数据\r\n\r\n @param queueName 队列名\r\n @param data      数据\r\n"}, {"name": "getPriorityQueueObject", "paramTypes": ["java.lang.String"], "doc": "\n 优先队列获取一个队列数据 没有数据返回 null(不支持延迟队列)\r\n\r\n @param queueName 队列名\r\n"}, {"name": "removePriorityQueueObject", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": "\n 优先队列删除队列数据(不支持延迟队列)\r\n"}, {"name": "destroyPriorityQueue", "paramTypes": ["java.lang.String"], "doc": "\n 优先队列销毁队列 所有阻塞监听 报错(不支持延迟队列)\r\n"}, {"name": "trySetBoundedQueueCapacity", "paramTypes": ["java.lang.String", "int"], "doc": "\n 尝试设置 有界队列 容量 用于限制数量\r\n\r\n @param queueName 队列名\r\n @param capacity  容量\r\n"}, {"name": "trySetBoundedQueueCapacity", "paramTypes": ["java.lang.String", "int", "boolean"], "doc": "\n 尝试设置 有界队列 容量 用于限制数量\r\n\r\n @param queueName 队列名\r\n @param capacity  容量\r\n @param destroy   是否销毁\r\n"}, {"name": "addBoundedQueueObject", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": "\n 添加有界队列数据\r\n\r\n @param queueName 队列名\r\n @param data      数据\r\n @return 添加成功 true 已达到界限 false\r\n"}, {"name": "getBoundedQueueObject", "paramTypes": ["java.lang.String"], "doc": "\n 有界队列获取一个队列数据 没有数据返回 null(不支持延迟队列)\r\n\r\n @param queueName 队列名\r\n"}, {"name": "removeBoundedQueueObject", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": "\n 有界队列删除队列数据(不支持延迟队列)\r\n"}, {"name": "destroyBoundedQueue", "paramTypes": ["java.lang.String"], "doc": "\n 有界队列销毁队列 所有阻塞监听 报错(不支持延迟队列)\r\n"}, {"name": "subscribeBlockingQueue", "paramTypes": ["java.lang.String", "java.util.function.Function", "boolean"], "doc": "\n 订阅阻塞队列(可订阅所有实现类 例如: 延迟 优先 有界 等)\r\n"}], "constructors": []}