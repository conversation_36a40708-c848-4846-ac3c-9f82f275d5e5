<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.app.mapper.AgentMapper">

    <resultMap type="org.dromara.app.domain.Agent" id="AgentResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="description" column="description"/>
        <result property="icon" column="icon"/>
        <result property="color" column="color"/>
        <result property="agentType" column="agent_type"/>
        <result property="systemPrompt" column="system_prompt"/>
        <result property="modelConfig" column="model_config"/>
        <result property="capabilities" column="capabilities"/>
        <result property="quickActions" column="quick_actions"/>
        <result property="enabled" column="enabled"/>
        <result property="sortOrder" column="sort_order"/>
        <result property="usageCount" column="usage_count"/>
        <result property="averageRating" column="average_rating"/>
        <result property="extendConfig" column="extend_config"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <sql id="selectAgentVo">
        select id,
               name,
               description,
               icon,
               color,
               agent_type,
               system_prompt,
               model_config,
               capabilities,
               quick_actions,
               enabled,
               sort_order,
               usage_count,
               average_rating,
               extend_config,
               create_by,
               create_time,
               update_by,
               update_time,
               del_flag
        from app_agent
    </sql>

    <select id="selectEnabledAgentsOrdered" parameterType="String" resultMap="AgentResult">
        <include refid="selectAgentVo"/>
        where enabled = 1 and del_flag = '0'
        order by sort_order asc
    </select>

    <select id="selectEnabledByType" parameterType="String" resultMap="AgentResult">
        <include refid="selectAgentVo"/>
        where agent_type = #{agentType} and enabled = 1 and del_flag = '0'
        limit 1
    </select>

    <insert id="batchInsertDefaultAgents">
        insert into app_agent
        (id, name, description, icon, color, agent_type, system_prompt, model_config, capabilities, quick_actions,
        enabled, sort_order, usage_count, average_rating, extend_config, create_by, create_time, update_by, update_time,
        del_flag)
        values
        <foreach item="agent" collection="agents" separator=",">
            (#{agent.id}, #{agent.name}, #{agent.description}, #{agent.icon}, #{agent.color}, #{agent.agentType},
            #{agent.systemPrompt}, #{agent.modelConfig}, #{agent.capabilities}, #{agent.quickActions}, #{agent.enabled},
            #{agent.sortOrder}, #{agent.usageCount}, #{agent.averageRating}, #{agent.extendConfig}, #{agent.createBy},
            #{agent.createTime}, #{agent.updateBy}, #{agent.updateTime}, #{agent.delFlag})
        </foreach>
    </insert>

</mapper>
