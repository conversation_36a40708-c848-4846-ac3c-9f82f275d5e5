package org.dromara.app.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 面试会话对象 app_interview_session
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_interview_session")
public class InterviewSession extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 会话ID
     */
    @TableId(value = "id")
    private String id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 岗位ID
     */
    private Long jobId;

    /**
     * 面试模式ID
     */
    private String modeId;

    /**
     * 会话令牌
     */
    private String sessionToken;

    /**
     * 预计时长（分钟）
     */
    private Integer estimatedDuration;

    /**
     * 题目数量
     */
    private Integer questionCount;

    /**
     * 简历URL
     */
    private String resumeUrl;

    /**
     * 自定义问题（JSON数组）
     */
    private String customizedQuestions;

    /**
     * 状态（created,started,paused,completed,expired）
     */
    private String status;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 过期时间
     */
    private LocalDateTime expiresAt;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    private String delFlag;

}
