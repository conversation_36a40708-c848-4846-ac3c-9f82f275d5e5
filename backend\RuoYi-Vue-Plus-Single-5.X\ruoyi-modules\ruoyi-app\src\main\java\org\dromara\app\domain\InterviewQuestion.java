package org.dromara.app.domain;

import com.baomidou.mybatisplus.annotation.OrderBy;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;
import java.util.List;
import java.util.Map;

/**
 * 面试问题对象 app_interview_question
 *
 * <AUTHOR> Assistant
 * @date 2025-07-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_interview_question")
public class InterviewQuestion extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 问题ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 关联岗位ID（NULL表示通用问题）
     */
    private Long jobId;

    /**
     * 问题分类ID
     */
    private Long categoryId;

    /**
     * 问题内容
     */
    private String question;

    /**
     * 问题类型：choice-选择题，coding-编程题，open-开放题，multimodal-多模态
     */
    private String questionType;

    /**
     * 难度等级（1-5）
     */
    private Integer difficulty;

    /**
     * 问题分类
     */
    private String category;

    /**
     * 问题标签
     */
    private List<String> tags;

    /**
     * 提示信息
     */
    private String hint;

    /**
     * 参考答案
     */
    private String answer;

    /**
     * 答案解释
     */
    private String explanation;

    /**
     * 答题时间限制（秒）
     */
    private Integer timeLimit;

    /**
     * 排序号
     */
    @OrderBy(asc = true, sort = 1)
    private Integer sortOrder;

    /**
     * 多模态评估要求（音频/视频/文本）
     */
    private Map<String, Object> multimodalRequirements;

    /**
     * 评估标准
     */
    private Map<String, Object> evaluationCriteria;

    /**
     * 状态（0正常 1停用）
     */
    private String status;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    private String delFlag;

}