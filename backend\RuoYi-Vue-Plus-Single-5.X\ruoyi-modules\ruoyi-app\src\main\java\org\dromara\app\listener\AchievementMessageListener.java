package org.dromara.app.listener;

import cn.hutool.json.JSONUtil;
import com.rabbitmq.client.Channel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.config.AchievementRabbitMqConfig;
import org.dromara.app.domain.dto.TrackEventDto;
import org.dromara.app.service.IAchievementService;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * 成就系统消息监听器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "spring.rabbitmq", name = "enabled", havingValue = "true")
public class AchievementMessageListener {

    private final IAchievementService achievementService;

    /**
     * 处理成就检查消息
     */
    @RabbitListener(queues = AchievementRabbitMqConfig.ACHIEVEMENT_CHECK_QUEUE)
    public void handleAchievementCheck(TrackEventDto trackEventDto, Message message, Channel channel) {
        try {
            log.debug("收到成就检查消息: userId={}, eventType={}", 
                trackEventDto.getUserId(), trackEventDto.getEventType());

            // 检查用户成就
            achievementService.checkAchievements(trackEventDto.getUserId(), trackEventDto);

            // 手动确认消息
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            
            log.debug("成就检查完成: userId={}, eventType={}", 
                trackEventDto.getUserId(), trackEventDto.getEventType());

        } catch (Exception e) {
            log.error("处理成就检查消息失败: {}", trackEventDto, e);
            
            try {
                // 获取重试次数
                Integer retryCount = (Integer) message.getMessageProperties().getHeaders().get("x-retry-count");
                if (retryCount == null) {
                    retryCount = 0;
                }

                // 最大重试3次
                if (retryCount < 3) {
                    // 增加重试次数并重新发送到队列
                    message.getMessageProperties().getHeaders().put("x-retry-count", retryCount + 1);
                    
                    // 拒绝消息并重新入队
                    channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, true);
                    
                    log.warn("成就检查消息重试: userId={}, eventType={}, retryCount={}", 
                        trackEventDto.getUserId(), trackEventDto.getEventType(), retryCount + 1);
                } else {
                    // 超过最大重试次数，发送到死信队列
                    channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
                    
                    log.error("成就检查消息超过最大重试次数，发送到死信队列: userId={}, eventType={}", 
                        trackEventDto.getUserId(), trackEventDto.getEventType());
                }
            } catch (IOException ioException) {
                log.error("处理消息确认失败", ioException);
            }
        }
    }

    /**
     * 处理成就通知消息
     */
    @RabbitListener(queues = AchievementRabbitMqConfig.ACHIEVEMENT_NOTIFICATION_QUEUE)
    public void handleAchievementNotification(String notificationData, Message message, Channel channel) {
        try {
            log.debug("收到成就通知消息: {}", notificationData);

            // 解析通知数据
            // 这里可以实现推送通知、邮件通知、短信通知等逻辑
            // 例如：推送到前端、发送邮件、记录通知日志等
            
            // TODO: 实现具体的通知逻辑
            processAchievementNotification(notificationData);

            // 手动确认消息
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            
            log.debug("成就通知处理完成: {}", notificationData);

        } catch (Exception e) {
            log.error("处理成就通知消息失败: {}", notificationData, e);
            
            try {
                // 获取重试次数
                Integer retryCount = (Integer) message.getMessageProperties().getHeaders().get("x-retry-count");
                if (retryCount == null) {
                    retryCount = 0;
                }

                // 最大重试3次
                if (retryCount < 3) {
                    // 增加重试次数并重新发送到队列
                    message.getMessageProperties().getHeaders().put("x-retry-count", retryCount + 1);
                    
                    // 拒绝消息并重新入队
                    channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, true);
                    
                    log.warn("成就通知消息重试: {}, retryCount={}", notificationData, retryCount + 1);
                } else {
                    // 超过最大重试次数，发送到死信队列
                    channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
                    
                    log.error("成就通知消息超过最大重试次数，发送到死信队列: {}", notificationData);
                }
            } catch (IOException ioException) {
                log.error("处理消息确认失败", ioException);
            }
        }
    }

    /**
     * 处理死信队列消息
     */
    @RabbitListener(queues = AchievementRabbitMqConfig.ACHIEVEMENT_DLX_QUEUE)
    public void handleDeadLetterMessage(String messageData, Message message, Channel channel) {
        try {
            log.error("收到死信队列消息，需要人工处理: {}", messageData);
            
            // 记录死信消息到数据库或日志文件，供后续人工处理
            // TODO: 实现死信消息的持久化存储
            
            // 确认消息
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            
        } catch (Exception e) {
            log.error("处理死信队列消息失败: {}", messageData, e);
            try {
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
            } catch (IOException ioException) {
                log.error("死信消息确认失败", ioException);
            }
        }
    }

    /**
     * 处理成就通知的具体逻辑
     */
    private void processAchievementNotification(String notificationData) {
        // 这里可以实现各种通知方式：
        // 1. WebSocket推送到前端
        // 2. 发送邮件通知
        // 3. 发送短信通知
        // 4. 推送到移动端
        // 5. 记录通知日志
        
        log.info("处理成就通知: {}", notificationData);
        
        // 示例：解析通知数据并处理
        try {
            // 假设通知数据是JSON格式
            // {"userId": 123, "achievementId": 456, "achievementName": "初来乍到", "message": "恭喜您获得成就：初来乍到"}
            
            // 这里可以调用其他服务进行通知
            // 例如：websocketService.sendToUser(userId, notificationMessage);
            // 例如：emailService.sendAchievementNotification(userId, achievementInfo);
            
        } catch (Exception e) {
            log.error("解析成就通知数据失败: {}", notificationData, e);
            throw e;
        }
    }

}
