{"doc": "\n RabbitMQ 自动配置类\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "rabbitConnectionFactory", "paramTypes": ["org.dromara.common.rabbitmq.properties.RabbitMqProperties"], "doc": "\n 连接工厂\r\n"}, {"name": "rabbitAdmin", "paramTypes": ["org.springframework.amqp.rabbit.connection.ConnectionFactory"], "doc": "\n RabbitAdmin\r\n"}, {"name": "messageConverter", "paramTypes": ["com.fasterxml.jackson.databind.ObjectMapper"], "doc": "\n 消息转换器\r\n"}, {"name": "rabbitTemplate", "paramTypes": ["org.springframework.amqp.rabbit.connection.ConnectionFactory", "org.springframework.amqp.support.converter.MessageConverter", "org.dromara.common.rabbitmq.properties.RabbitMqProperties"], "doc": "\n RabbitTemplate\r\n"}, {"name": "retryTemplate", "paramTypes": ["org.dromara.common.rabbitmq.properties.RabbitMqProperties"], "doc": "\n 重试模板\r\n"}, {"name": "rabbitListenerContainerFactory", "paramTypes": ["org.springframework.amqp.rabbit.connection.ConnectionFactory", "org.springframework.amqp.support.converter.MessageConverter", "org.dromara.common.rabbitmq.properties.RabbitMqProperties"], "doc": "\n 监听器容器工厂\r\n"}, {"name": "validateRabbitMqConfig", "paramTypes": ["org.dromara.common.rabbitmq.properties.RabbitMqProperties"], "doc": "\n 验证 RabbitMQ 配置\r\n"}], "constructors": []}