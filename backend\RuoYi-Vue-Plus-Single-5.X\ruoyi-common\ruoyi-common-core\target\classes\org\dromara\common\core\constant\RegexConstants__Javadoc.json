{"doc": "\n 常用正则表达式字符串\r\n <p>\r\n 常用正则表达式集合，更多正则见: https://any86.github.io/any-rule/\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "DICTIONARY_TYPE", "doc": "\n 字典类型必须以字母开头，且只能为（小写字母，数字，下滑线）\r\n"}, {"name": "PERMISSION_STRING", "doc": "\n 权限标识必须符合以下格式：\r\n 1. 标准格式：xxx:yyy:zzz\r\n - 第一部分（xxx）：只能包含字母、数字和下划线（_），不能使用 `*`\r\n - 第二部分（yyy）：可以包含字母、数字、下划线（_）和 `*`\r\n - 第三部分（zzz）：可以包含字母、数字、下划线（_）和 `*`\r\n 2. 允许空字符串（\"\"），表示没有权限标识\r\n"}, {"name": "ID_CARD_LAST_6", "doc": "\n 身份证号码（后6位）\r\n"}, {"name": "QQ_NUMBER", "doc": "\n QQ号码\r\n"}, {"name": "POSTAL_CODE", "doc": "\n 邮政编码\r\n"}, {"name": "ACCOUNT", "doc": "\n 注册账号\r\n"}, {"name": "PASSWORD", "doc": "\n 密码：包含至少8个字符，包括大写字母、小写字母、数字和特殊字符\r\n"}, {"name": "STATUS", "doc": "\n 通用状态（0表示正常，1表示停用）\r\n"}], "enumConstants": [], "methods": [], "constructors": []}