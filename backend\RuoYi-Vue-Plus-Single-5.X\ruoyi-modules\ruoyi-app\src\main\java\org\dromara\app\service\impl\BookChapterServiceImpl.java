package org.dromara.app.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.Book;
import org.dromara.app.domain.BookChapter;
import org.dromara.app.domain.BookReadingRecord;
import org.dromara.app.mapper.BookMapper;
import org.dromara.app.mapper.BookReadingRecordMapper;
import org.dromara.app.repository.BookChapterRepository;
import org.dromara.app.service.IBookChapterService;
import org.dromara.common.core.utils.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * 书籍章节Service业务层处理
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BookChapterServiceImpl implements IBookChapterService {

    private final BookChapterRepository bookChapterRepository;
    private final BookMapper bookMapper;
    private final BookReadingRecordMapper bookReadingRecordMapper;

    /**
     * 根据书籍ID查询章节列表
     */
    @Override
    public List<BookChapter> queryChaptersByBookId(Long bookId, Long userId) {
        if (bookId == null) {
            return List.of();
        }

        // 查询章节列表
        List<BookChapter> chapters = bookChapterRepository.findByBookIdAndStatusTrueOrderByChapterOrderAsc(bookId);

        // 如果有用户ID，设置用户相关的状态信息
        if (userId != null && !chapters.isEmpty()) {
            setUserChapterStatus(chapters, userId, bookId);
        }

        return chapters;
    }

    /**
     * 根据章节ID查询章节内容
     */
    @Override
    public BookChapter queryChapterContent(String chapterId, Long userId) {
        if (StringUtils.isBlank(chapterId)) {
            return null;
        }

        Optional<BookChapter> optional = bookChapterRepository.findById(chapterId);
        if (optional.isEmpty()) {
            return null;
        }

        BookChapter chapter = optional.get();

        // 设置用户相关状态
        if (userId != null) {
            setUserChapterStatus(List.of(chapter), userId, chapter.getBookId());
        }

        return chapter;
    }

    /**
     * 根据书籍ID和章节序号查询章节
     */
    @Override
    public BookChapter queryChapterByOrder(Long bookId, Integer chapterOrder, Long userId) {
        if (bookId == null || chapterOrder == null) {
            return null;
        }

        BookChapter chapter = bookChapterRepository.findByBookIdAndChapterOrder(bookId, chapterOrder);
        if (chapter == null) {
            return null;
        }

        // 设置用户相关状态
        if (userId != null) {
            setUserChapterStatus(List.of(chapter), userId, bookId);
        }

        return chapter;
    }

    /**
     * 查询试读章节列表
     */
    @Override
    public List<BookChapter> queryPreviewChapters(Long bookId) {
        if (bookId == null) {
            return List.of();
        }

        return bookChapterRepository.findPreviewChaptersByBookId(bookId);
    }

    /**
     * 新增章节
     */
    @Override
    public boolean insertChapter(BookChapter chapter) {
        try {
            // 设置创建时间
            chapter.setCreateTime(new Date());
            chapter.setUpdateTime(new Date());

            // 设置默认值
            if (chapter.getStatus() == null) {
                chapter.setStatus(true);
            }
            if (chapter.getIsUnlocked() == null) {
                chapter.setIsUnlocked(false);
            }
            if (chapter.getIsPreview() == null) {
                chapter.setIsPreview(false);
            }

            // 如果没有设置排序序号，自动设置为最大值+1
            if (chapter.getChapterOrder() == null) {
                BookChapter maxChapter = bookChapterRepository.findMaxChapterOrderByBookId(chapter.getBookId());
                int maxOrder = maxChapter != null && maxChapter.getChapterOrder() != null ?
                    maxChapter.getChapterOrder() : 0;
                chapter.setChapterOrder(maxOrder + 1);
            }

            // 计算字数和阅读时间
            calculateChapterStats(chapter);

            // 保存章节
            BookChapter savedChapter = bookChapterRepository.save(chapter);

            // 更新书籍的章节数
            updateBookChapterCount(chapter.getBookId());

            log.info("新增章节成功: {} - {}", chapter.getBookId(), chapter.getTitle());
            return savedChapter.getId() != null;

        } catch (Exception e) {
            log.error("新增章节失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 修改章节
     */
    @Override
    public boolean updateChapter(BookChapter chapter) {
        try {
            if (StringUtils.isBlank(chapter.getId())) {
                return false;
            }

            // 设置更新时间
            chapter.setUpdateTime(new Date());

            // 重新计算字数和阅读时间
            calculateChapterStats(chapter);

            // 保存更新
            BookChapter savedChapter = bookChapterRepository.save(chapter);

            log.info("修改章节成功: {}", chapter.getTitle());
            return savedChapter.getId() != null;

        } catch (Exception e) {
            log.error("修改章节失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 删除章节
     */
    @Override
    public boolean deleteChapter(String chapterId) {
        try {
            if (StringUtils.isBlank(chapterId)) {
                return false;
            }

            // 获取章节信息用于更新书籍章节数
            Optional<BookChapter> optional = bookChapterRepository.findById(chapterId);
            if (optional.isEmpty()) {
                return false;
            }

            BookChapter chapter = optional.get();
            Long bookId = chapter.getBookId();

            // 删除章节
            bookChapterRepository.deleteById(chapterId);

            // 更新书籍的章节数
            updateBookChapterCount(bookId);

            log.info("删除章节成功: {}", chapter.getTitle());
            return true;

        } catch (Exception e) {
            log.error("删除章节失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 根据书籍ID删除所有章节
     */
    @Override
    public boolean deleteChaptersByBookId(Long bookId) {
        try {
            if (bookId == null) {
                return false;
            }

            bookChapterRepository.deleteByBookId(bookId);
            log.info("删除书籍{}的所有章节成功", bookId);
            return true;

        } catch (Exception e) {
            log.error("删除书籍章节失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 检查用户是否有权限访问章节
     */
    @Override
    public boolean checkChapterAccess(String chapterId, Long userId) {
        if (StringUtils.isBlank(chapterId) || userId == null) {
            return false;
        }

        Optional<BookChapter> optional = bookChapterRepository.findById(chapterId);
        if (optional.isEmpty()) {
            return false;
        }

        BookChapter chapter = optional.get();

        // 试读章节或已解锁章节可以访问
        if (Boolean.TRUE.equals(chapter.getIsPreview()) || Boolean.TRUE.equals(chapter.getIsUnlocked())) {
            return true;
        }

        // 检查书籍是否免费
        Book book = bookMapper.selectById(chapter.getBookId());
        if (book != null && Boolean.TRUE.equals(book.getIsFree())) {
            return true;
        }

        // 检查用户是否已购买
        // TODO: 这里需要根据实际的购买逻辑来判断
        // 目前暂时返回true，后续可以添加购买状态检查

        return true;
    }

    /**
     * 更新章节解锁状态
     */
    @Override
    public boolean updateChapterUnlockStatus(String chapterId, Boolean isUnlocked) {
        try {
            if (StringUtils.isBlank(chapterId) || isUnlocked == null) {
                return false;
            }

            Optional<BookChapter> optional = bookChapterRepository.findById(chapterId);
            if (optional.isEmpty()) {
                return false;
            }

            BookChapter chapter = optional.get();
            chapter.setIsUnlocked(isUnlocked);
            chapter.setUpdateTime(new Date());

            bookChapterRepository.save(chapter);

            log.info("更新章节{}解锁状态为: {}", chapterId, isUnlocked);
            return true;

        } catch (Exception e) {
            log.error("更新章节解锁状态失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 设置用户章节状态（是否已完成等）
     */
    private void setUserChapterStatus(List<BookChapter> chapters, Long userId, Long bookId) {
        if (chapters.isEmpty() || userId == null || bookId == null) {
            return;
        }

        try {
            // 查询用户阅读记录
            BookReadingRecord record = bookReadingRecordMapper.selectByUserIdAndBookId(userId, bookId);

            if (record != null && StringUtils.isNotBlank(record.getCompletedChapters())) {
                // 解析已完成章节列表
                List<String> completedChapterIds = Arrays.asList(record.getCompletedChapters().split(","));

                // 设置章节完成状态
                for (BookChapter chapter : chapters) {
                    chapter.setIsCompleted(completedChapterIds.contains(chapter.getId()));
                }
            } else {
                // 如果没有阅读记录，所有章节都标记为未完成
                for (BookChapter chapter : chapters) {
                    chapter.setIsCompleted(false);
                }
            }
        } catch (Exception e) {
            log.warn("设置用户章节状态失败: {}", e.getMessage());
            // 出错时设置默认状态
            for (BookChapter chapter : chapters) {
                chapter.setIsCompleted(false);
            }
        }
    }

    /**
     * 计算章节统计信息（字数、阅读时间）
     */
    private void calculateChapterStats(BookChapter chapter) {
        if (chapter == null || StringUtils.isBlank(chapter.getContent())) {
            return;
        }

        // 计算字数（去除Markdown标记）
        String content = chapter.getContent()
            .replaceAll("#+\\s*", "")  // 去除标题标记
            .replaceAll("\\*\\*([^*]+)\\*\\*", "$1")  // 去除粗体标记
            .replaceAll("\\*([^*]+)\\*", "$1")  // 去除斜体标记
            .replaceAll("`([^`]+)`", "$1")  // 去除代码标记
            .replaceAll("\\[([^\\]]+)\\]\\([^)]+\\)", "$1")  // 去除链接标记
            .replaceAll("\\n+", " ")  // 换行替换为空格
            .trim();

        int wordCount = content.length();
        chapter.setWordCount(wordCount);

        // 估算阅读时间（按平均500字/分钟计算）
        int readTime = Math.max(1, wordCount / 500);
        chapter.setReadTime(readTime);
    }

    /**
     * 更新书籍的章节数
     */
    private void updateBookChapterCount(Long bookId) {
        if (bookId == null) {
            return;
        }

        try {
            long chapterCount = bookChapterRepository.countByBookIdAndStatusTrue(bookId);

            Book book = new Book();
            book.setId(bookId);
            book.setChapters((int) chapterCount);

            bookMapper.updateById(book);

        } catch (Exception e) {
            log.warn("更新书籍章节数失败: {}", e.getMessage());
        }
    }
}
