{"doc": "\n MongoDB配置属性\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "uri", "doc": "\n MongoDB连接字符串\r\n"}, {"name": "database", "doc": "\n 数据库名称\r\n"}, {"name": "username", "doc": "\n 用户名\r\n"}, {"name": "password", "doc": "\n 密码\r\n"}, {"name": "host", "doc": "\n 主机地址\r\n"}, {"name": "port", "doc": "\n 端口号\r\n"}, {"name": "authenticationDatabase", "doc": "\n 认证数据库\r\n"}, {"name": "pool", "doc": "\n 连接池配置\r\n"}], "enumConstants": [], "methods": [], "constructors": []}