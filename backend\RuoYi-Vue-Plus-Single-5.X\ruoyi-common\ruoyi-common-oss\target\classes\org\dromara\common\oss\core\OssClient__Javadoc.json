{"doc": "\n S3 存储协议 所有兼容S3协议的云厂商均支持\r\n 阿里云 腾讯云 七牛云 minio\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "config<PERSON><PERSON>", "doc": "\n 服务商\r\n"}, {"name": "properties", "doc": "\n 配置属性\r\n"}, {"name": "client", "doc": "\n Amazon S3 异步客户端\r\n"}, {"name": "transferManager", "doc": "\n 用于管理 S3 数据传输的高级工具\r\n"}, {"name": "presigner", "doc": "\n AWS S3 预签名 URL 的生成器\r\n"}], "enumConstants": [], "methods": [{"name": "upload", "paramTypes": ["java.nio.file.Path", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 上传文件到 Amazon S3，并返回上传结果\r\n\r\n @param filePath    本地文件路径\r\n @param key         在 Amazon S3 中的对象键\r\n @param md5Digest   本地文件的 MD5 哈希值（可选）\r\n @param contentType 文件内容类型\r\n @return UploadResult 包含上传后的文件信息\r\n @throws OssException 如果上传失败，抛出自定义异常\r\n"}, {"name": "upload", "paramTypes": ["java.io.InputStream", "java.lang.String", "java.lang.Long", "java.lang.String"], "doc": "\n 上传 InputStream 到 Amazon S3\r\n\r\n @param inputStream 要上传的输入流\r\n @param key         在 Amazon S3 中的对象键\r\n @param length      输入流的长度\r\n @param contentType 文件内容类型\r\n @return UploadResult 包含上传后的文件信息\r\n @throws OssException 如果上传失败，抛出自定义异常\r\n"}, {"name": "fileDownload", "paramTypes": ["java.lang.String"], "doc": "\n 下载文件从 Amazon S3 到临时目录\r\n\r\n @param path 文件在 Amazon S3 中的对象键\r\n @return 下载后的文件在本地的临时路径\r\n @throws OssException 如果下载失败，抛出自定义异常\r\n"}, {"name": "download", "paramTypes": ["java.lang.String", "java.io.OutputStream", "java.util.function.Consumer"], "doc": "\n 下载文件从 Amazon S3 到 输出流\r\n\r\n @param key      文件在 Amazon S3 中的对象键\r\n @param out      输出流\r\n @param consumer 自定义处理逻辑\r\n @return 输出流中写入的字节数（长度）\r\n @throws OssException 如果下载失败，抛出自定义异常\r\n"}, {"name": "delete", "paramTypes": ["java.lang.String"], "doc": "\n 删除云存储服务中指定路径下文件\r\n\r\n @param path 指定路径\r\n"}, {"name": "getPrivateUrl", "paramTypes": ["java.lang.String", "java.time.Duration"], "doc": "\n 获取私有URL链接\r\n\r\n @param objectKey   对象KEY\r\n @param expiredTime 链接授权到期时间\r\n"}, {"name": "uploadSuffix", "paramTypes": ["byte[]", "java.lang.String", "java.lang.String"], "doc": "\n 上传 byte[] 数据到 Amazon S3，使用指定的后缀构造对象键。\r\n\r\n @param data   要上传的 byte[] 数据\r\n @param suffix 对象键的后缀\r\n @return UploadResult 包含上传后的文件信息\r\n @throws OssException 如果上传失败，抛出自定义异常\r\n"}, {"name": "uploadSuffix", "paramTypes": ["java.io.InputStream", "java.lang.String", "java.lang.Long", "java.lang.String"], "doc": "\n 上传 InputStream 到 Amazon S3，使用指定的后缀构造对象键。\r\n\r\n @param inputStream 要上传的输入流\r\n @param suffix      对象键的后缀\r\n @param length      输入流的长度\r\n @return UploadResult 包含上传后的文件信息\r\n @throws OssException 如果上传失败，抛出自定义异常\r\n"}, {"name": "uploadSuffix", "paramTypes": ["java.io.File", "java.lang.String"], "doc": "\n 上传文件到 Amazon S3，使用指定的后缀构造对象键\r\n\r\n @param file   要上传的文件\r\n @param suffix 对象键的后缀\r\n @return UploadResult 包含上传后的文件信息\r\n @throws OssException 如果上传失败，抛出自定义异常\r\n"}, {"name": "getObjectContent", "paramTypes": ["java.lang.String"], "doc": "\n 获取文件输入流\r\n\r\n @param path 完整文件路径\r\n @return 输入流\r\n"}, {"name": "getEndpoint", "paramTypes": [], "doc": "\n 获取 S3 客户端的终端点 URL\r\n\r\n @return 终端点 URL\r\n"}, {"name": "getDomain", "paramTypes": [], "doc": "\n 获取 S3 客户端的终端点 URL（自定义域名）\r\n\r\n @return 终端点 URL\r\n"}, {"name": "of", "paramTypes": [], "doc": "\n 根据传入的 region 参数返回相应的 AWS 区域\r\n 如果 region 参数非空，使用 Region.of 方法创建并返回对应的 AWS 区域对象\r\n 如果 region 参数为空，返回一个默认的 AWS 区域（例如，us-east-1），作为广泛支持的区域\r\n\r\n @return 对应的 AWS 区域对象，或者默认的广泛支持的区域（us-east-1）\r\n"}, {"name": "getUrl", "paramTypes": [], "doc": "\n 获取云存储服务的URL\r\n\r\n @return 文件路径\r\n"}, {"name": "<PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 生成一个符合特定规则的、唯一的文件路径。通过使用日期、UUID、前缀和后缀等元素的组合，确保了文件路径的独一无二性\r\n\r\n @param prefix 前缀\r\n @param suffix 后缀\r\n @return 文件路径\r\n"}, {"name": "removeBaseUrl", "paramTypes": ["java.lang.String"], "doc": "\n 移除路径中的基础URL部分，得到相对路径\r\n\r\n @param path 完整的路径，包括基础URL和相对路径\r\n @return 去除基础URL后的相对路径\r\n"}, {"name": "getConfigKey", "paramTypes": [], "doc": "\n 服务商\r\n"}, {"name": "getIsHttps", "paramTypes": [], "doc": "\n 获取是否使用 HTTPS 的配置，并返回相应的协议头部。\r\n\r\n @return 协议头部，根据是否使用 HTTPS 返回 \"https://\" 或 \"http://\"\r\n"}, {"name": "checkPropertiesSame", "paramTypes": ["org.dromara.common.oss.properties.OssProperties"], "doc": "\n 检查配置是否相同\r\n"}, {"name": "getAccessPolicy", "paramTypes": [], "doc": "\n 获取当前桶权限类型\r\n\r\n @return 当前桶权限类型code\r\n"}], "constructors": [{"name": "<init>", "paramTypes": ["java.lang.String", "org.dromara.common.oss.properties.OssProperties"], "doc": "\n 构造方法\r\n\r\n @param configKey     配置键\r\n @param ossProperties Oss配置属性\r\n"}]}