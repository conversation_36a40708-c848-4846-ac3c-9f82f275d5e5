{"groups": [{"name": "pay.alipay", "type": "org.dromara.common.pay.properties.AlipayProperties", "sourceType": "org.dromara.common.pay.properties.AlipayProperties"}], "properties": [{"name": "pay.alipay.alipay-public-key", "type": "java.lang.String", "description": "支付宝公钥 - 支付宝公钥", "sourceType": "org.dromara.common.pay.properties.AlipayProperties"}, {"name": "pay.alipay.app-id", "type": "java.lang.String", "description": "应用ID - 支付宝应用ID", "sourceType": "org.dromara.common.pay.properties.AlipayProperties"}, {"name": "pay.alipay.charset", "type": "java.lang.String", "description": "字符编码格式 - 默认UTF-8", "sourceType": "org.dromara.common.pay.properties.AlipayProperties"}, {"name": "pay.alipay.enabled", "type": "java.lang.Bo<PERSON>an", "description": "支付宝功能开关", "sourceType": "org.dromara.common.pay.properties.AlipayProperties"}, {"name": "pay.alipay.environment", "type": "java.lang.String", "description": "环境类型 - sandbox(沙箱) 或 production(生产)", "sourceType": "org.dromara.common.pay.properties.AlipayProperties"}, {"name": "pay.alipay.format", "type": "java.lang.String", "description": "返回格式 - 默认JSON", "sourceType": "org.dromara.common.pay.properties.AlipayProperties"}, {"name": "pay.alipay.notify-url", "type": "java.lang.String", "description": "服务器异步通知页面路径 - 支付成功后支付宝异步通知的地址", "sourceType": "org.dromara.common.pay.properties.AlipayProperties"}, {"name": "pay.alipay.private-key", "type": "java.lang.String", "description": "商户私钥 - 应用私钥", "sourceType": "org.dromara.common.pay.properties.AlipayProperties"}, {"name": "pay.alipay.return-url", "type": "java.lang.String", "description": "页面跳转同步通知页面路径 - 支付成功后页面跳转的地址", "sourceType": "org.dromara.common.pay.properties.AlipayProperties"}, {"name": "pay.alipay.server-url", "type": "java.lang.String", "description": "应用服务器地址 - 用于构建回调URL的基础地址", "sourceType": "org.dromara.common.pay.properties.AlipayProperties"}, {"name": "pay.alipay.sign-type", "type": "java.lang.String", "description": "签名方式 - 默认RSA2", "sourceType": "org.dromara.common.pay.properties.AlipayProperties"}], "hints": []}