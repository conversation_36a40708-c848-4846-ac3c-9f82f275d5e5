package org.dromara.app.service;

import org.dromara.app.domain.dto.*;
import org.dromara.app.domain.enums.ActivityType;
import org.dromara.app.domain.vo.ActivityHistoryVO;
import org.dromara.app.domain.vo.ActivityStatisticsVO;

/**
 * 活动时长记录服务接口
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
public interface IActivityTimerService {

    /**
     * 开始活动记录
     *
     * @param request 开始活动请求
     * @return 是否成功
     */
    boolean startActivity(ActivityStartRequest request);

    /**
     * 暂停活动记录
     *
     * @param request 暂停活动请求
     * @return 是否成功
     */
    boolean pauseActivity(ActivityPauseRequest request);

    /**
     * 结束活动记录
     *
     * @param request 结束活动请求
     * @return 是否成功
     */
    boolean endActivity(ActivityEndRequest request);

    /**
     * 同步活动会话
     *
     * @param request 同步请求
     * @return 是否成功
     */
    boolean syncSession(ActivitySyncRequest request);

    /**
     * 获取活动统计数据
     *
     * @param request 统计请求
     * @return 统计数据
     */
    ActivityStatisticsVO getStatistics(ActivityStatisticsRequest request);

    /**
     * 获取活动历史记录
     *
     * @param request 历史记录请求
     * @return 历史记录
     */
    ActivityHistoryVO getHistory(ActivityHistoryRequest request);

    /**
     * 清除活动记录
     *
     * @param userId       用户ID
     * @param activityType 活动类型(可选)
     * @return 是否成功
     */
    boolean clearRecords(Long userId, ActivityType activityType);

    /**
     * 获取用户指定类型的活动统计
     *
     * @param userId       用户ID
     * @param activityType 活动类型
     * @return 统计数据
     */
    ActivityStatisticsVO getStatsByType(Long userId, ActivityType activityType);

    /**
     * 初始化用户活动总览
     *
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean initializeUserSummary(Long userId);
}
