{"doc": "\n Redisson 配置属性\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "keyPrefix", "doc": "\n redis缓存key前缀\r\n"}, {"name": "threads", "doc": "\n 线程池数量,默认值 = 当前处理核数量 * 2\r\n"}, {"name": "nettyThreads", "doc": "\n Netty线程池数量,默认值 = 当前处理核数量 * 2\r\n"}, {"name": "singleServerConfig", "doc": "\n 单机服务配置\r\n"}, {"name": "clusterServersConfig", "doc": "\n 集群服务配置\r\n"}], "enumConstants": [], "methods": [], "constructors": []}