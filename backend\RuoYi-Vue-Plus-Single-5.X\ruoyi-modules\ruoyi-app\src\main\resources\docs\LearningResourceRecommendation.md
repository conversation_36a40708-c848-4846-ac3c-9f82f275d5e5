# 学习资源智能推荐系统

## 概述

本文档描述了学习资源智能推荐系统的实现，包括多种推荐算法和相关API接口。

## 核心推荐方法

### 1. selectRecommendedResources - 智能推荐资源

**方法签名：**
```java
List<LearningResource> selectRecommendedResources(@Param("userId") Long userId, @Param("limit") Integer limit)
```

**功能描述：**
- 当 `userId` 为 null 时，返回通用推荐资源
- 当 `userId` 不为 null 时，基于用户学习历史进行个性化推荐

**推荐算法：**
1. **基于用户历史学习记录推荐相关资源**
   - 查找用户已学习的技能领域
   - 推荐相同技能领域的其他资源

2. **推荐用户未学习但相关的技能领域**
   - 基于技能关联关系推荐进阶内容
   - 例如：Java → Spring → Spring Boot

3. **综合推荐分数计算**
   - 资源评分权重：30%
   - 浏览量权重：20%
   - 完成率权重：20%
   - 质量评估权重：20%
   - 免费资源加分：10%

### 2. selectResourcesBySkillGaps - 技能差距推荐

**方法签名：**
```java
List<LearningResource> selectResourcesBySkillGaps(@Param("skillAreas") List<String> skillAreas, 
                                                 @Param("difficulty") String difficulty, 
                                                 @Param("limit") Integer limit)
```

**功能描述：**
根据指定的技能领域和难度等级推荐学习资源，用于填补技能差距。

### 3. selectCollaborativeFilteringResources - 协同过滤推荐

**方法签名：**
```java
List<LearningResource> selectCollaborativeFilteringResources(@Param("userId") Long userId, @Param("limit") Integer limit)
```

**功能描述：**
基于用户相似度进行协同过滤推荐：
- 找到与目标用户有相似学习经历的其他用户
- 推荐这些相似用户学习过但目标用户未学习的资源

### 4. selectTrendingResources - 趋势资源推荐

**方法签名：**
```java
List<LearningResource> selectTrendingResources(@Param("days") Integer days, @Param("limit") Integer limit)
```

**功能描述：**
推荐最近热门的学习资源，基于：
- 浏览量增长趋势
- 学习人数增长趋势
- 资源评分和评分人数

## API 接口

### 个性化推荐
```http
GET /app/learning/resource/personalized/{userId}?limit=10
```

### 技能差距推荐
```http
GET /app/learning/resource/skill-gaps?skillAreas=Java,Spring&difficulty=中等&limit=10
```

### 协同过滤推荐
```http
GET /app/learning/resource/collaborative/{userId}?limit=10
```

### 趋势资源
```http
GET /app/learning/resource/trending?days=30&limit=10
```

### 智能排序
```http
POST /app/learning/resource/sort-by-recommendation
Content-Type: application/json

{
  "resources": [...],
  "userPreferences": {
    "preferFreeResources": true,
    "preferredLanguage": "中文",
    "preferredResourceType": "video"
  }
}
```

## 技能关联关系

系统内置了技能进阶路径：
- Java → Spring → Spring Boot
- Python → 机器学习 → 深度学习
- JavaScript → React → Node.js
- SQL → 数据分析 → 大数据

## 推荐评分算法

### 综合推荐分数计算公式：
```
推荐分数 = 资源评分 × 0.3 + 
          浏览量/1000 × 0.2 + 
          完成率 × 0.2 + 
          质量评估 × 0.2 + 
          免费资源加分
```

### 用户偏好加分：
- 免费资源偏好：+10分
- 语言偏好匹配：+5分
- 资源类型偏好匹配：+8分
- 热门程度加分：最多+15分

## 使用示例

### Java 代码示例：
```java
// 获取个性化推荐
List<LearningResource> personalizedResources = 
    learningResourceService.getPersonalizedRecommendations(userId, 10);

// 根据技能差距推荐
List<String> skillGaps = Arrays.asList("Java", "Spring Boot");
List<LearningResource> skillResources = 
    learningResourceService.getResourcesBySkillGaps(skillGaps, "中等", 8);

// 获取趋势资源
List<LearningResource> trendingResources = 
    learningResourceService.getTrendingResources(30, 10);
```

## 性能优化

1. **数据库索引优化**
   - 技术领域和级别组合索引
   - 用户ID和技能领域索引
   - 问题类型索引

2. **查询优化**
   - 使用 LIMIT 限制结果数量
   - 合理使用 LEFT JOIN 避免笛卡尔积
   - 缓存热门推荐结果

3. **推荐算法优化**
   - 预计算用户相似度矩阵
   - 缓存技能关联关系
   - 异步更新推荐结果

## 扩展性

系统设计支持以下扩展：
1. 添加新的推荐算法
2. 自定义技能关联关系
3. 动态调整推荐权重
4. 集成外部推荐引擎
5. A/B 测试不同推荐策略

## 监控和分析

建议监控以下指标：
- 推荐点击率
- 推荐转化率
- 用户满意度
- 推荐多样性
- 系统响应时间

## 总结

学习资源智能推荐系统通过多种推荐算法的组合，为用户提供个性化、精准的学习资源推荐。系统具有良好的扩展性和性能，能够满足不同场景下的推荐需求。