package org.dromara.app.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.InterviewReport;
import org.dromara.app.service.IChartGenerationService;
import org.dromara.app.service.IMultimodalAnalysisService.DimensionScore;
import org.dromara.common.core.exception.ServiceException;
import org.jfree.chart.ChartFactory;
import org.jfree.chart.ChartUtils;
import org.jfree.chart.JFreeChart;
import org.jfree.chart.axis.CategoryAxis;
import org.jfree.chart.axis.NumberAxis;
import org.jfree.chart.plot.CategoryPlot;
import org.jfree.chart.plot.PiePlot;
import org.jfree.chart.plot.SpiderWebPlot;
import org.jfree.chart.plot.XYPlot;
import org.jfree.chart.renderer.category.BarRenderer;
import org.jfree.chart.renderer.category.LineAndShapeRenderer;
import org.jfree.chart.title.TextTitle;
import org.jfree.data.category.DefaultCategoryDataset;
import org.jfree.data.general.DefaultPieDataset;
import org.jfree.data.xy.XYSeries;
import org.jfree.data.xy.XYSeriesCollection;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 图表生成服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ChartGenerationServiceImpl implements IChartGenerationService {

    // 颜色配置
    private static final Color PRIMARY_COLOR = new Color(0, 102, 204);
    private static final Color SECONDARY_COLOR = new Color(102, 102, 102);
    private static final Color SUCCESS_COLOR = new Color(0, 153, 0);
    private static final Color WARNING_COLOR = new Color(255, 153, 0);
    private static final Color DANGER_COLOR = new Color(220, 53, 69);
    private static final Color INFO_COLOR = new Color(23, 162, 184);

    // 图表尺寸配置
    private static final int DEFAULT_WIDTH = 800;
    private static final int DEFAULT_HEIGHT = 600;
    private static final int RADAR_SIZE = 500;

    @Override
    public BufferedImage generateRadarChart(List<DimensionScore> dimensionScores, String title) {
        try {
            log.info("开始生成雷达图，标题: {}", title);
            
            // 创建数据集
            DefaultCategoryDataset dataset = new DefaultCategoryDataset();
            
            for (DimensionScore score : dimensionScores) {
                dataset.addValue(score.getScore(), "当前得分", score.getDimension());
            }
            
            // 创建雷达图
            SpiderWebPlot plot = new SpiderWebPlot(dataset);
            plot.setWebFilled(true);
            plot.setAxisLinePaint(SECONDARY_COLOR);
            plot.setSeriesPaint(0, PRIMARY_COLOR);
            plot.setSeriesOutlinePaint(0, PRIMARY_COLOR);
            plot.setBackgroundPaint(Color.WHITE);
            
            // 设置最大值
            plot.setMaxValue(100);
            
            JFreeChart chart = new JFreeChart(title, JFreeChart.DEFAULT_TITLE_FONT, plot, true);
            chart.setBackgroundPaint(Color.WHITE);
            
            // 设置标题样式
            TextTitle chartTitle = chart.getTitle();
            chartTitle.setFont(new Font("微软雅黑", Font.BOLD, 18));
            chartTitle.setPaint(PRIMARY_COLOR);
            
            BufferedImage image = chart.createBufferedImage(RADAR_SIZE, RADAR_SIZE);
            log.info("雷达图生成成功");
            return image;
            
        } catch (Exception e) {
            log.error("生成雷达图失败", e);
            throw new ServiceException("生成雷达图失败: " + e.getMessage());
        }
    }

    @Override
    public BufferedImage generateRadarChartWithComparison(List<DimensionScore> dimensionScores, 
                                                         List<Integer> industryAverages, String title) {
        try {
            log.info("开始生成对比雷达图，标题: {}", title);
            
            // 创建数据集
            DefaultCategoryDataset dataset = new DefaultCategoryDataset();
            
            for (int i = 0; i < dimensionScores.size(); i++) {
                DimensionScore score = dimensionScores.get(i);
                dataset.addValue(score.getScore(), "个人得分", score.getDimension());
                
                if (i < industryAverages.size()) {
                    dataset.addValue(industryAverages.get(i), "行业平均", score.getDimension());
                }
            }
            
            // 创建雷达图
            SpiderWebPlot plot = new SpiderWebPlot(dataset);
            plot.setWebFilled(true);
            plot.setAxisLinePaint(SECONDARY_COLOR);
            plot.setSeriesPaint(0, PRIMARY_COLOR);
            plot.setSeriesPaint(1, WARNING_COLOR);
            plot.setSeriesOutlinePaint(0, PRIMARY_COLOR);
            plot.setSeriesOutlinePaint(1, WARNING_COLOR);
            plot.setBackgroundPaint(Color.WHITE);
            
            // 设置最大值
            plot.setMaxValue(100);
            
            JFreeChart chart = new JFreeChart(title, JFreeChart.DEFAULT_TITLE_FONT, plot, true);
            chart.setBackgroundPaint(Color.WHITE);
            
            // 设置标题样式
            TextTitle chartTitle = chart.getTitle();
            chartTitle.setFont(new Font("微软雅黑", Font.BOLD, 18));
            chartTitle.setPaint(PRIMARY_COLOR);
            
            BufferedImage image = chart.createBufferedImage(RADAR_SIZE, RADAR_SIZE);
            log.info("对比雷达图生成成功");
            return image;
            
        } catch (Exception e) {
            log.error("生成对比雷达图失败", e);
            throw new ServiceException("生成对比雷达图失败: " + e.getMessage());
        }
    }

    @Override
    public BufferedImage generateBarChart(Map<String, Integer> data, String title, 
                                        String xAxisLabel, String yAxisLabel) {
        try {
            log.info("开始生成柱状图，标题: {}", title);
            
            // 创建数据集
            DefaultCategoryDataset dataset = new DefaultCategoryDataset();
            for (Map.Entry<String, Integer> entry : data.entrySet()) {
                dataset.addValue(entry.getValue(), "数据", entry.getKey());
            }
            
            // 创建柱状图
            JFreeChart chart = ChartFactory.createBarChart(
                title, xAxisLabel, yAxisLabel, dataset);
            
            // 设置样式
            chart.setBackgroundPaint(Color.WHITE);
            
            CategoryPlot plot = chart.getCategoryPlot();
            plot.setBackgroundPaint(Color.WHITE);
            plot.setRangeGridlinePaint(SECONDARY_COLOR);
            
            BarRenderer renderer = (BarRenderer) plot.getRenderer();
            renderer.setSeriesPaint(0, PRIMARY_COLOR);
            renderer.setSeriesOutlinePaint(0, PRIMARY_COLOR);
            
            // 设置字体
            CategoryAxis domainAxis = plot.getDomainAxis();
            domainAxis.setTickLabelFont(new Font("微软雅黑", Font.PLAIN, 12));
            domainAxis.setLabelFont(new Font("微软雅黑", Font.BOLD, 14));
            
            NumberAxis rangeAxis = (NumberAxis) plot.getRangeAxis();
            rangeAxis.setTickLabelFont(new Font("微软雅黑", Font.PLAIN, 12));
            rangeAxis.setLabelFont(new Font("微软雅黑", Font.BOLD, 14));
            
            TextTitle chartTitle = chart.getTitle();
            chartTitle.setFont(new Font("微软雅黑", Font.BOLD, 18));
            chartTitle.setPaint(PRIMARY_COLOR);
            
            BufferedImage image = chart.createBufferedImage(DEFAULT_WIDTH, DEFAULT_HEIGHT);
            log.info("柱状图生成成功");
            return image;
            
        } catch (Exception e) {
            log.error("生成柱状图失败", e);
            throw new ServiceException("生成柱状图失败: " + e.getMessage());
        }
    }

    @Override
    public BufferedImage generateLineChart(Map<String, Integer> data, String title, 
                                         String xAxisLabel, String yAxisLabel) {
        try {
            log.info("开始生成折线图，标题: {}", title);
            
            // 创建数据集
            DefaultCategoryDataset dataset = new DefaultCategoryDataset();
            for (Map.Entry<String, Integer> entry : data.entrySet()) {
                dataset.addValue(entry.getValue(), "数据", entry.getKey());
            }
            
            // 创建折线图
            JFreeChart chart = ChartFactory.createLineChart(
                title, xAxisLabel, yAxisLabel, dataset);
            
            // 设置样式
            chart.setBackgroundPaint(Color.WHITE);
            
            CategoryPlot plot = chart.getCategoryPlot();
            plot.setBackgroundPaint(Color.WHITE);
            plot.setRangeGridlinePaint(SECONDARY_COLOR);
            
            LineAndShapeRenderer renderer = (LineAndShapeRenderer) plot.getRenderer();
            renderer.setSeriesPaint(0, PRIMARY_COLOR);
            renderer.setSeriesStroke(0, new BasicStroke(3.0f));
            renderer.setSeriesShapesVisible(0, true);
            
            // 设置字体
            CategoryAxis domainAxis = plot.getDomainAxis();
            domainAxis.setTickLabelFont(new Font("微软雅黑", Font.PLAIN, 12));
            domainAxis.setLabelFont(new Font("微软雅黑", Font.BOLD, 14));
            
            NumberAxis rangeAxis = (NumberAxis) plot.getRangeAxis();
            rangeAxis.setTickLabelFont(new Font("微软雅黑", Font.PLAIN, 12));
            rangeAxis.setLabelFont(new Font("微软雅黑", Font.BOLD, 14));
            
            TextTitle chartTitle = chart.getTitle();
            chartTitle.setFont(new Font("微软雅黑", Font.BOLD, 18));
            chartTitle.setPaint(PRIMARY_COLOR);
            
            BufferedImage image = chart.createBufferedImage(DEFAULT_WIDTH, DEFAULT_HEIGHT);
            log.info("折线图生成成功");
            return image;
            
        } catch (Exception e) {
            log.error("生成折线图失败", e);
            throw new ServiceException("生成折线图失败: " + e.getMessage());
        }
    }

    @Override
    public BufferedImage generatePieChart(Map<String, Integer> data, String title) {
        try {
            log.info("开始生成饼图，标题: {}", title);
            
            // 创建数据集
            DefaultPieDataset dataset = new DefaultPieDataset();
            for (Map.Entry<String, Integer> entry : data.entrySet()) {
                dataset.setValue(entry.getKey(), entry.getValue());
            }
            
            // 创建饼图
            JFreeChart chart = ChartFactory.createPieChart(title, dataset, true, true, false);
            
            // 设置样式
            chart.setBackgroundPaint(Color.WHITE);
            
            PiePlot plot = (PiePlot) chart.getPlot();
            plot.setBackgroundPaint(Color.WHITE);
            plot.setOutlineStroke(new BasicStroke(2.0f));
            plot.setOutlinePaint(Color.WHITE);
            
            // 设置颜色
            Color[] colors = {PRIMARY_COLOR, SUCCESS_COLOR, WARNING_COLOR, DANGER_COLOR, INFO_COLOR};
            int colorIndex = 0;
            for (Object key : dataset.getKeys()) {
                plot.setSectionPaint((Comparable) key, colors[colorIndex % colors.length]);
                colorIndex++;
            }
            
            // 设置字体
            plot.setLabelFont(new Font("微软雅黑", Font.PLAIN, 12));
            
            TextTitle chartTitle = chart.getTitle();
            chartTitle.setFont(new Font("微软雅黑", Font.BOLD, 18));
            chartTitle.setPaint(PRIMARY_COLOR);
            
            BufferedImage image = chart.createBufferedImage(DEFAULT_WIDTH, DEFAULT_HEIGHT);
            log.info("饼图生成成功");
            return image;
            
        } catch (Exception e) {
            log.error("生成饼图失败", e);
            throw new ServiceException("生成饼图失败: " + e.getMessage());
        }
    }

    @Override
    public BufferedImage generateCapabilityDistributionChart(List<DimensionScore> dimensionScores) {
        try {
            log.info("开始生成能力分布图");
            
            // 创建数据集
            DefaultCategoryDataset dataset = new DefaultCategoryDataset();
            
            for (DimensionScore score : dimensionScores) {
                dataset.addValue(score.getScore(), "得分", score.getDimension());
                dataset.addValue(score.getPercentile(), "百分位", score.getDimension());
            }
            
            // 创建柱状图
            JFreeChart chart = ChartFactory.createBarChart(
                "能力分布分析", "能力维度", "分数", dataset);
            
            // 设置样式
            chart.setBackgroundPaint(Color.WHITE);
            
            CategoryPlot plot = chart.getCategoryPlot();
            plot.setBackgroundPaint(Color.WHITE);
            plot.setRangeGridlinePaint(SECONDARY_COLOR);
            
            BarRenderer renderer = (BarRenderer) plot.getRenderer();
            renderer.setSeriesPaint(0, PRIMARY_COLOR);
            renderer.setSeriesPaint(1, WARNING_COLOR);
            
            // 设置字体
            CategoryAxis domainAxis = plot.getDomainAxis();
            domainAxis.setTickLabelFont(new Font("微软雅黑", Font.PLAIN, 10));
            domainAxis.setLabelFont(new Font("微软雅黑", Font.BOLD, 14));
            domainAxis.setCategoryLabelPositions(
                org.jfree.chart.axis.CategoryLabelPositions.UP_45);
            
            NumberAxis rangeAxis = (NumberAxis) plot.getRangeAxis();
            rangeAxis.setTickLabelFont(new Font("微软雅黑", Font.PLAIN, 12));
            rangeAxis.setLabelFont(new Font("微软雅黑", Font.BOLD, 14));
            
            TextTitle chartTitle = chart.getTitle();
            chartTitle.setFont(new Font("微软雅黑", Font.BOLD, 18));
            chartTitle.setPaint(PRIMARY_COLOR);
            
            BufferedImage image = chart.createBufferedImage(DEFAULT_WIDTH, DEFAULT_HEIGHT);
            log.info("能力分布图生成成功");
            return image;
            
        } catch (Exception e) {
            log.error("生成能力分布图失败", e);
            throw new ServiceException("生成能力分布图失败: " + e.getMessage());
        }
    }

    @Override
    public BufferedImage generateTrendChart(Map<String, List<Integer>> historicalData, String title) {
        try {
            log.info("开始生成趋势分析图，标题: {}", title);
            
            // 创建数据集
            XYSeriesCollection dataset = new XYSeriesCollection();
            
            for (Map.Entry<String, List<Integer>> entry : historicalData.entrySet()) {
                XYSeries series = new XYSeries(entry.getKey());
                List<Integer> values = entry.getValue();
                for (int i = 0; i < values.size(); i++) {
                    series.add(i + 1, values.get(i));
                }
                dataset.addSeries(series);
            }
            
            // 创建折线图
            JFreeChart chart = ChartFactory.createXYLineChart(
                title, "时间", "分数", dataset);
            
            // 设置样式
            chart.setBackgroundPaint(Color.WHITE);
            
            XYPlot plot = chart.getXYPlot();
            plot.setBackgroundPaint(Color.WHITE);
            plot.setRangeGridlinePaint(SECONDARY_COLOR);
            plot.setDomainGridlinePaint(SECONDARY_COLOR);
            
            // 设置颜色
            Color[] colors = {PRIMARY_COLOR, SUCCESS_COLOR, WARNING_COLOR, DANGER_COLOR};
            for (int i = 0; i < dataset.getSeriesCount(); i++) {
                plot.getRenderer().setSeriesPaint(i, colors[i % colors.length]);
                plot.getRenderer().setSeriesStroke(i, new BasicStroke(2.0f));
            }
            
            // 设置字体
            TextTitle chartTitle = chart.getTitle();
            chartTitle.setFont(new Font("微软雅黑", Font.BOLD, 18));
            chartTitle.setPaint(PRIMARY_COLOR);
            
            BufferedImage image = chart.createBufferedImage(DEFAULT_WIDTH, DEFAULT_HEIGHT);
            log.info("趋势分析图生成成功");
            return image;
            
        } catch (Exception e) {
            log.error("生成趋势分析图失败", e);
            throw new ServiceException("生成趋势分析图失败: " + e.getMessage());
        }
    }

    @Override
    public BufferedImage generateDashboard(InterviewReport report) {
        try {
            log.info("开始生成综合评估仪表盘，报告ID: {}", report.getId());
            
            // 创建画布
            BufferedImage dashboard = new BufferedImage(1200, 800, BufferedImage.TYPE_INT_RGB);
            Graphics2D g2d = dashboard.createGraphics();
            
            // 设置抗锯齿
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
            
            // 设置背景
            g2d.setColor(Color.WHITE);
            g2d.fillRect(0, 0, 1200, 800);
            
            // 绘制标题
            g2d.setColor(PRIMARY_COLOR);
            g2d.setFont(new Font("微软雅黑", Font.BOLD, 24));
            g2d.drawString("面试评估仪表盘", 50, 50);
            
            // 绘制基本信息
            g2d.setColor(SECONDARY_COLOR);
            g2d.setFont(new Font("微软雅黑", Font.PLAIN, 16));
            g2d.drawString("应聘岗位: " + report.getJobPosition(), 50, 80);
            g2d.drawString("总分: " + report.getTotalScore(), 50, 105);
            g2d.drawString("等级: " + getLevelText(report.getLevel()), 50, 130);
            g2d.drawString("百分位: " + report.getPercentile() + "%", 50, 155);
            
            // 生成并嵌入雷达图
            if (report.getDimensionScores() != null && !report.getDimensionScores().isEmpty()) {
                List<DimensionScore> dimensionScores = convertToDimensionScores(report.getDimensionScores());
                BufferedImage radarChart = generateRadarChart(dimensionScores, "能力雷达图");
                g2d.drawImage(radarChart, 50, 180, 400, 400, null);
            }
            
            // 生成并嵌入能力分布图
            if (report.getDimensionScores() != null && !report.getDimensionScores().isEmpty()) {
                List<DimensionScore> dimensionScores = convertToDimensionScores(report.getDimensionScores());
                BufferedImage distributionChart = generateCapabilityDistributionChart(dimensionScores);
                g2d.drawImage(distributionChart, 500, 180, 650, 400, null);
            }
            
            // 绘制优势和劣势
            g2d.setColor(SUCCESS_COLOR);
            g2d.setFont(new Font("微软雅黑", Font.BOLD, 18));
            g2d.drawString("主要优势", 50, 620);
            
            g2d.setColor(Color.BLACK);
            g2d.setFont(new Font("微软雅黑", Font.PLAIN, 14));
            if (report.getStrengths() != null) {
                int y = 645;
                for (String strength : report.getStrengths()) {
                    g2d.drawString("• " + strength, 50, y);
                    y += 20;
                }
            }
            
            g2d.setColor(WARNING_COLOR);
            g2d.setFont(new Font("微软雅黑", Font.BOLD, 18));
            g2d.drawString("需要改进", 400, 620);
            
            g2d.setColor(Color.BLACK);
            g2d.setFont(new Font("微软雅黑", Font.PLAIN, 14));
            if (report.getWeaknesses() != null) {
                int y = 645;
                for (String weakness : report.getWeaknesses()) {
                    g2d.drawString("• " + weakness, 400, y);
                    y += 20;
                }
            }
            
            g2d.dispose();
            
            log.info("综合评估仪表盘生成成功");
            return dashboard;
            
        } catch (Exception e) {
            log.error("生成综合评估仪表盘失败", e);
            throw new ServiceException("生成综合评估仪表盘失败: " + e.getMessage());
        }
    }

    @Override
    public byte[] imageToByteArray(BufferedImage image, String format) {
        try {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(image, format, baos);
            return baos.toByteArray();
        } catch (IOException e) {
            log.error("图像转换为字节数组失败", e);
            throw new ServiceException("图像转换失败: " + e.getMessage());
        }
    }

    @Override
    public boolean saveImageToFile(BufferedImage image, String filePath, String format) {
        try {
            File file = new File(filePath);
            // 确保目录存在
            file.getParentFile().mkdirs();
            
            ImageIO.write(image, format, file);
            log.info("图像保存成功: {}", filePath);
            return true;
        } catch (IOException e) {
            log.error("保存图像到文件失败: {}", filePath, e);
            return false;
        }
    }

    // ========== 辅助方法 ==========

    /**
     * 获取等级文本
     */
    private String getLevelText(String level) {
        if (level == null) return "未评级";
        switch (level.toLowerCase()) {
            case "excellent": return "优秀";
            case "good": return "良好";
            case "average": return "中等";
            case "poor": return "较差";
            default: return "未知";
        }
    }

    /**
     * 转换维度评分格式
     */
    private List<DimensionScore> convertToDimensionScores(List<org.dromara.app.domain.DimensionScore> domainScores) {
        return domainScores.stream().map(score -> {
            DimensionScore dimensionScore = new DimensionScore();
            dimensionScore.setDimension(score.getDimension());
            dimensionScore.setScore(score.getScore());
            dimensionScore.setMaxScore(score.getMaxScore());
            dimensionScore.setPercentile(score.getPercentile());
            dimensionScore.setDescription(score.getDescription());
            return dimensionScore;
        }).collect(java.util.stream.Collectors.toList());
    }
}