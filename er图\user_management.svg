<?xml version="1.0" encoding="UTF-8"?>
<svg width="900" height="700" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .entity { fill: white; stroke: black; stroke-width: 2; }
      .attribute { fill: white; stroke: black; stroke-width: 1; }
      .relationship { fill: white; stroke: black; stroke-width: 2; }
      .text { font-family: Arial, sans-serif; font-size: 12px; text-anchor: middle; dominant-baseline: middle; }
      .line { stroke: black; stroke-width: 1; fill: none; }
      .cardinality { font-family: Arial, sans-serif; font-size: 10px; text-anchor: middle; }
    </style>
  </defs>
  
  <!-- 系统用户实体 -->
  <rect x="100" y="200" width="120" height="60" class="entity"/>
  <text x="160" y="230" class="text">系统用户</text>
  
  <!-- 系统用户属性 -->
  <ellipse cx="50" cy="120" rx="35" ry="20" class="attribute"/>
  <text x="50" y="120" class="text">用户ID</text>
  <line x1="70" y1="135" x2="130" y2="200" class="line"/>
  
  <ellipse cx="120" cy="100" rx="30" ry="20" class="attribute"/>
  <text x="120" y="100" class="text">用户名</text>
  <line x1="135" y1="115" x2="150" y2="200" class="line"/>
  
  <ellipse cx="200" cy="100" rx="25" ry="20" class="attribute"/>
  <text x="200" y="100" class="text">昵称</text>
  <line x1="185" y1="115" x2="170" y2="200" class="line"/>
  
  <ellipse cx="270" cy="120" rx="25" ry="20" class="attribute"/>
  <text x="270" y="120" class="text">邮箱</text>
  <line x1="250" y1="135" x2="190" y2="200" class="line"/>
  
  <ellipse cx="50" cy="350" rx="30" ry="20" class="attribute"/>
  <text x="50" y="350" class="text">手机号</text>
  <line x1="70" y1="335" x2="130" y2="260" class="line"/>

  <ellipse cx="120" cy="380" rx="25" ry="20" class="attribute"/>
  <text x="120" y="380" class="text">性别</text>
  <line x1="135" y1="365" x2="150" y2="260" class="line"/>

  <ellipse cx="200" cy="380" rx="25" ry="20" class="attribute"/>
  <text x="200" y="380" class="text">头像</text>
  <line x1="185" y1="365" x2="170" y2="260" class="line"/>

  <ellipse cx="270" cy="350" rx="25" ry="20" class="attribute"/>
  <text x="270" y="350" class="text">状态</text>
  <line x1="250" y1="335" x2="190" y2="260" class="line"/>
  
  <!-- 部门实体 -->
  <rect x="400" y="80" width="100" height="60" class="entity"/>
  <text x="450" y="110" class="text">部门</text>
  
  <!-- 部门属性 -->
  <ellipse cx="350" cy="50" rx="30" ry="20" class="attribute"/>
  <text x="350" y="50" class="text">部门ID</text>
  <line x1="370" y1="65" x2="420" y2="80" class="line"/>
  
  <ellipse cx="450" cy="30" rx="30" ry="20" class="attribute"/>
  <text x="450" y="30" class="text">部门名称</text>
  <line x1="450" y1="50" x2="450" y2="80" class="line"/>
  
  <ellipse cx="550" cy="50" rx="30" ry="20" class="attribute"/>
  <text x="550" y="50" class="text">负责人</text>
  <line x1="530" y1="65" x2="480" y2="80" class="line"/>
  
  <!-- 角色实体 -->
  <rect x="400" y="400" width="100" height="60" class="entity"/>
  <text x="450" y="430" class="text">角色</text>

  <!-- 角色属性 -->
  <ellipse cx="350" cy="480" rx="30" ry="20" class="attribute"/>
  <text x="350" y="480" class="text">角色ID</text>
  <line x1="370" y1="465" x2="420" y2="460" class="line"/>

  <ellipse cx="450" cy="500" rx="30" ry="20" class="attribute"/>
  <text x="450" y="500" class="text">角色名称</text>
  <line x1="450" y1="480" x2="450" y2="460" class="line"/>

  <ellipse cx="550" cy="480" rx="30" ry="20" class="attribute"/>
  <text x="550" y="480" class="text">权限范围</text>
  <line x1="530" y1="465" x2="480" y2="460" class="line"/>
  
  <!-- 应用用户实体 -->
  <rect x="600" y="200" width="120" height="60" class="entity"/>
  <text x="660" y="230" class="text">应用用户</text>
  
  <!-- 应用用户属性 -->
  <ellipse cx="750" cy="120" rx="30" ry="20" class="attribute"/>
  <text x="750" y="120" class="text">学号</text>
  <line x1="730" y1="135" x2="690" y2="200" class="line"/>
  
  <ellipse cx="750" cy="160" rx="25" ry="20" class="attribute"/>
  <text x="750" y="160" class="text">专业</text>
  <line x1="730" y1="175" x2="710" y2="200" class="line"/>
  
  <ellipse cx="750" cy="200" rx="25" ry="20" class="attribute"/>
  <text x="750" y="200" class="text">年级</text>
  <line x1="725" y1="200" x2="720" y2="230" class="line"/>
  
  <ellipse cx="750" cy="240" rx="25" ry="20" class="attribute"/>
  <text x="750" y="240" class="text">学校</text>
  <line x1="730" y1="255" x2="710" y2="260" class="line"/>
  
  <ellipse cx="750" cy="280" rx="30" ry="20" class="attribute"/>
  <text x="750" y="280" class="text">个人简介</text>
  <line x1="730" y1="295" x2="690" y2="260" class="line"/>
  
  <!-- 关系：属于部门 -->
  <polygon points="300,180 330,200 300,220 270,200" class="relationship"/>
  <text x="300" y="200" class="text">属于</text>
  <line x1="220" y1="220" x2="270" y2="200" class="line"/>
  <line x1="330" y1="200" x2="400" y2="120" class="line"/>
  <text x="240" y="210" class="cardinality">n</text>
  <text x="380" y="140" class="cardinality">1</text>
  
  <!-- 关系：拥有角色 -->
  <polygon points="300,420 330,440 300,460 270,440" class="relationship"/>
  <text x="300" y="440" class="text">拥有</text>
  <line x1="220" y1="250" x2="270" y2="440" class="line"/>
  <line x1="330" y1="440" x2="400" y2="430" class="line"/>
  <text x="240" y="340" class="cardinality">n</text>
  <text x="370" y="430" class="cardinality">n</text>
  
  <!-- 关系：继承关系 -->
  <polygon points="520,230 550,250 520,270 490,250" class="relationship"/>
  <text x="520" y="250" class="text">继承</text>
  <line x1="220" y1="230" x2="490" y2="250" class="line"/>
  <line x1="550" y1="250" x2="600" y2="230" class="line"/>
  <text x="350" y="240" class="cardinality">1</text>
  <text x="580" y="240" class="cardinality">1</text>
</svg>
