package org.dromara.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.dromara.app.domain.ChatSession;

import java.util.List;
import java.util.Map;

/**
 * 聊天会话Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface ChatSessionMapper extends BaseMapper<ChatSession> {

    /**
     * 分页查询用户会话列表
     *
     * @param page   分页对象
     * @param userId 用户ID
     * @param status 会话状态（可选）
     * @return 会话分页结果
     */
    Page<ChatSession> selectUserSessions(Page<ChatSession> page, @Param("userId") Long userId, @Param("status") Integer status);

    /**
     * 根据会话ID和用户ID查询会话详情
     *
     * @param sessionId 会话ID
     * @param userId    用户ID
     * @return 会话详情
     */
    ChatSession selectByIdAndUserId(@Param("sessionId") String sessionId, @Param("userId") Long userId);

    /**
     * 更新会话消息统计
     *
     * @param sessionId      会话ID
     * @param messageCount   消息数量
     * @param lastMessage    最后消息内容
     * @param lastActiveTime 最后活跃时间
     * @return 影响行数
     */
    @Update("UPDATE app_chat_session SET message_count = #{messageCount}, last_message = #{lastMessage}, " +
        "last_active_time = #{lastActiveTime}, update_time = NOW() WHERE id = #{sessionId}")
    int updateSessionStats(@Param("sessionId") String sessionId,
                           @Param("messageCount") Integer messageCount,
                           @Param("lastMessage") String lastMessage,
                           @Param("lastActiveTime") Long lastActiveTime);

    /**
     * 更新会话标题
     *
     * @param sessionId 会话ID
     * @param userId    用户ID
     * @param title     新标题
     * @return 影响行数
     */
    @Update("UPDATE app_chat_session SET title = #{title}, update_time = NOW() " +
        "WHERE id = #{sessionId} AND user_id = #{userId}")
    int updateSessionTitle(@Param("sessionId") String sessionId,
                           @Param("userId") Long userId,
                           @Param("title") String title);

    /**
     * 归档/取消归档会话
     *
     * @param sessionId 会话ID
     * @param userId    用户ID
     * @param status    状态（0-归档，1-活跃）
     * @return 影响行数
     */
    @Update("UPDATE app_chat_session SET status = #{status}, update_time = NOW() " +
        "WHERE id = #{sessionId} AND user_id = #{userId}")
    int updateSessionStatus(@Param("sessionId") String sessionId,
                            @Param("userId") Long userId,
                            @Param("status") Integer status);

    /**
     * 获取用户聊天统计信息
     *
     * @param userId 用户ID
     * @return 统计信息
     */
    @Select("SELECT " +
        "COUNT(*) as totalSessions, " +
        "SUM(message_count) as totalMessages, " +
        "COUNT(CASE WHEN status = 1 THEN 1 END) as activeSessions " +
        "FROM app_chat_session WHERE user_id = #{userId}")
    Map<String, Object> getUserChatStats(@Param("userId") Long userId);

    /**
     * 删除用户会话（软删除）
     *
     * @param sessionId 会话ID
     * @param userId    用户ID
     * @return 影响行数
     */
    @Update("UPDATE app_chat_session SET del_flag = '1', update_time = NOW() " +
        "WHERE id = #{sessionId} AND user_id = #{userId}")
    int deleteByIdAndUserId(@Param("sessionId") String sessionId, @Param("userId") Long userId);

    /**
     * 获取热门Agent类型统计
     *
     * @param limit 返回数量限制
     * @return Agent使用统计
     */
    @Select("SELECT agent_type, COUNT(*) as count FROM app_chat_session " +
        "WHERE del_flag = '0' GROUP BY agent_type ORDER BY count DESC LIMIT #{limit}")
    List<Map<String, Object>> getPopularAgentTypes(@Param("limit") Integer limit);

    /**
     * 查询用户活跃会话列表
     *
     * @param userId 用户ID
     * @param limit  数量限制
     * @return 活跃会话列表
     */
    @Select("SELECT * FROM app_chat_session " +
        "WHERE user_id = #{userId} AND status = 1 AND del_flag = '0' " +
        "ORDER BY last_active_time DESC LIMIT #{limit}")
    List<ChatSession> selectActiveSessions(@Param("userId") Long userId, @Param("limit") Integer limit);

    /**
     * 根据Agent类型查询用户会话
     *
     * @param userId    用户ID
     * @param agentType Agent类型
     * @param limit     数量限制
     * @return 会话列表
     */
    @Select("SELECT * FROM app_chat_session " +
        "WHERE user_id = #{userId} AND agent_type = #{agentType} AND del_flag = '0' " +
        "ORDER BY last_active_time DESC LIMIT #{limit}")
    List<ChatSession> selectUserSessionsByAgent(@Param("userId") Long userId,
                                                @Param("agentType") String agentType,
                                                @Param("limit") Integer limit);

    /**
     * 统计用户会话数量
     *
     * @param userId 用户ID
     * @param status 会话状态（可选）
     * @return 会话数量
     */
    Integer countUserSessions(@Param("userId") Long userId, @Param("status") Integer status);

    /**
     * 查询最近活跃的会话
     *
     * @param userId    用户ID
     * @param timestamp 时间戳阈值
     * @return 最近活跃会话列表
     */
    @Select("SELECT * FROM app_chat_session " +
        "WHERE user_id = #{userId} AND last_active_time > #{timestamp} AND del_flag = '0' " +
        "ORDER BY last_active_time DESC")
    List<ChatSession> selectRecentSessions(@Param("userId") Long userId, @Param("timestamp") Long timestamp);

    /**
     * 更新会话最后活跃时间
     *
     * @param sessionId      会话ID
     * @param lastActiveTime 最后活跃时间
     * @return 影响行数
     */
    @Update("UPDATE app_chat_session SET last_active_time = #{lastActiveTime}, update_time = NOW() " +
        "WHERE id = #{sessionId}")
    int updateLastActiveTime(@Param("sessionId") String sessionId, @Param("lastActiveTime") Long lastActiveTime);

    /**
     * 批量更新会话状态
     *
     * @param sessionIds 会话ID列表
     * @param userId     用户ID
     * @param status     新状态
     * @return 影响行数
     */
    @Update("<script>" +
        "UPDATE app_chat_session SET status = #{status}, update_time = NOW() " +
        "WHERE user_id = #{userId} AND id IN " +
        "<foreach collection='sessionIds' item='id' open='(' close=')' separator=','>" +
        "#{id}" +
        "</foreach>" +
        "</script>")
    int batchUpdateSessionStatus(@Param("sessionIds") List<String> sessionIds,
                                 @Param("userId") Long userId,
                                 @Param("status") Integer status);

    /**
     * 获取用户各Agent类型的会话统计
     *
     * @param userId 用户ID
     * @return 统计信息
     */
    @Select("SELECT " +
        "agent_type as agentType, " +
        "COUNT(*) as sessionCount, " +
        "SUM(message_count) as totalMessages, " +
        "MAX(last_active_time) as lastActiveTime " +
        "FROM app_chat_session " +
        "WHERE user_id = #{userId} AND del_flag = '0' " +
        "GROUP BY agent_type " +
        "ORDER BY sessionCount DESC")
    List<Map<String, Object>> selectSessionStats(@Param("userId") Long userId);

    /**
     * 清理长时间未活跃的会话（自动归档）
     *
     * @param threshold 时间阈值
     * @return 影响行数
     */
    @Update("UPDATE app_chat_session SET status = 0, update_time = NOW() " +
        "WHERE status = 1 AND last_active_time < #{threshold}")
    int archiveInactiveSessions(@Param("threshold") Long threshold);

    /**
     * 重置会话统计信息
     *
     * @param sessionId 会话ID
     * @return 影响行数
     */
    @Update("UPDATE app_chat_session SET message_count = 0, last_message = '', update_time = NOW() " +
        "WHERE id = #{sessionId}")
    int resetSessionStats(@Param("sessionId") String sessionId);

    /**
     * 更新会话标题
     *
     * @param sessionId 会话ID
     * @param userId    用户ID
     * @param title     新标题
     * @return 影响行数
     */
    @Update("UPDATE app_chat_session SET title = #{title}, update_time = NOW() " +
        "WHERE id = #{sessionId} AND user_id = #{userId}")
    int updateTitle(@Param("sessionId") String sessionId,
                    @Param("userId") Long userId,
                    @Param("title") String title);

    /**
     * 更新归档状态
     *
     * @param sessionId 会话ID
     * @param userId    用户ID
     * @param archived  归档状态
     * @return 影响行数
     */
    @Update("UPDATE app_chat_session SET status = #{archived} ? 0 : 1, update_time = NOW() " +
        "WHERE id = #{sessionId} AND user_id = #{userId}")
    int updateArchiveStatus(@Param("sessionId") String sessionId,
                            @Param("userId") Long userId,
                            @Param("archived") Boolean archived);

    /**
     * 统计用户会话总数
     *
     * @param userId 用户ID
     * @return 会话总数
     */
    @Select("SELECT COUNT(*) FROM app_chat_session WHERE user_id = #{userId} AND del_flag = '0'")
    Integer countUserSessions(@Param("userId") Long userId);

    /**
     * 统计活跃会话数
     *
     * @param userId 用户ID
     * @return 活跃会话数
     */
    @Select("SELECT COUNT(*) FROM app_chat_session WHERE user_id = #{userId} AND status = 1 AND del_flag = '0'")
    Integer countActiveSessions(@Param("userId") Long userId);

    /**
     * 按Agent类型统计会话
     *
     * @param userId 用户ID
     * @return 统计结果
     */
    @Select("SELECT agent_type as agentType, COUNT(*) as count " +
        "FROM app_chat_session " +
        "WHERE user_id = #{userId} AND del_flag = '0' " +
        "GROUP BY agent_type " +
        "ORDER BY count DESC")
    List<Map<String, Object>> countSessionsByAgent(@Param("userId") Long userId);
}
