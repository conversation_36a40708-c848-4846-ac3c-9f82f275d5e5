package org.dromara.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.dromara.app.domain.InterviewReport;

import java.util.List;
import java.util.Map;

/**
 * 面试报告Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface InterviewReportMapper extends BaseMapper<InterviewReport> {

    /**
     * 根据会话ID和用户ID查询报告
     *
     * @param sessionId 会话ID
     * @param userId 用户ID
     * @return 面试报告
     */
    @Select("SELECT * FROM app_interview_report WHERE session_id = #{sessionId} AND user_id = #{userId} LIMIT 1")
    InterviewReport selectBySessionIdAndUserId(@Param("sessionId") String sessionId, @Param("userId") Long userId);

    /**
     * 查询用户的报告统计
     *
     * @param userId 用户ID
     * @return 统计数据
     */
    @Select("SELECT " +
            "COUNT(*) as total_reports, " +
            "AVG(total_score) as avg_score, " +
            "MAX(total_score) as max_score, " +
            "MIN(total_score) as min_score " +
            "FROM app_interview_report WHERE user_id = #{userId}")
    Map<String, Object> selectUserReportStats(@Param("userId") Long userId);

    /**
     * 查询用户最近的报告
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 报告列表
     */
    @Select("SELECT * FROM app_interview_report WHERE user_id = #{userId} " +
            "ORDER BY create_time DESC LIMIT #{limit}")
    List<InterviewReport> selectRecentReports(@Param("userId") Long userId, @Param("limit") Integer limit);

    /**
     * 查询岗位相关的报告统计
     *
     * @param jobPosition 岗位
     * @return 统计数据
     */
    @Select("SELECT " +
            "job_position, " +
            "COUNT(*) as report_count, " +
            "AVG(total_score) as avg_score " +
            "FROM app_interview_report " +
            "WHERE job_position = #{jobPosition} " +
            "GROUP BY job_position")
    Map<String, Object> selectJobPositionStats(@Param("jobPosition") String jobPosition);

    /**
     * 查询分数分布统计
     *
     * @return 分数分布
     */
    @Select("SELECT " +
            "CASE " +
            "WHEN total_score >= 90 THEN '优秀' " +
            "WHEN total_score >= 80 THEN '良好' " +
            "WHEN total_score >= 70 THEN '中等' " +
            "WHEN total_score >= 60 THEN '及格' " +
            "ELSE '不及格' " +
            "END as score_level, " +
            "COUNT(*) as count " +
            "FROM app_interview_report " +
            "GROUP BY score_level")
    List<Map<String, Object>> selectScoreDistribution();
}