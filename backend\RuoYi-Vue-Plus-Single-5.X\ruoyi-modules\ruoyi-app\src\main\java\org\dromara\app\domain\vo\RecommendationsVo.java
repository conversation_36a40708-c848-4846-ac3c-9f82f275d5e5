package org.dromara.app.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 推荐内容视图对象
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Schema(description = "推荐内容视图对象")
public class RecommendationsVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 推荐资源列表
     */
    @Schema(description = "推荐资源列表")
    private List<ResourceVo> resources;

    /**
     * 推荐练习列表
     */
    @Schema(description = "推荐练习列表")
    private List<PracticeVo> practices;
}
