{"doc": "\n 任务受让人\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "total", "doc": "\n 总大小\r\n"}], "enumConstants": [], "methods": [{"name": "convertToHandlerList", "paramTypes": ["java.util.List", "java.util.function.Function", "java.util.function.Function", "java.util.function.Function", "java.util.function.Function", "java.util.function.Function"], "doc": "\n 将源列表转换为 TaskHandler 列表\r\n\r\n @param <T>              通用类型\r\n @param sourceList       待转换的源列表\r\n @param storageId        提取 storageId 的函数\r\n @param handlerCode      提取 handlerCode 的函数\r\n @param handlerName      提取 handlerName 的函数\r\n @param groupName        提取 groupName 的函数\r\n @param createTimeMapper 提取 createTime 的函数\r\n @return 转换后的 TaskHandler 列表\r\n"}], "constructors": []}