{"groups": [{"name": "langchain4j", "type": "org.dromara.common.chat.config.properties.LangChain4jProperties", "sourceType": "org.dromara.common.chat.config.properties.LangChain4jProperties"}, {"name": "langchain4j.agent", "type": "org.dromara.common.chat.config.properties.LangChain4jProperties$AgentConfig", "sourceType": "org.dromara.common.chat.config.properties.LangChain4jProperties", "sourceMethod": "public org.dromara.common.chat.config.properties.LangChain4jProperties.AgentConfig getAgent() "}, {"name": "langchain4j.dashscope", "type": "org.dromara.common.chat.config.properties.LangChain4jProperties$DashscopeConfig", "sourceType": "org.dromara.common.chat.config.properties.LangChain4jProperties", "sourceMethod": "public org.dromara.common.chat.config.properties.LangChain4jProperties.DashscopeConfig getDashscope() "}, {"name": "langchain4j.ollama", "type": "org.dromara.common.chat.config.properties.LangChain4jProperties$OllamaConfig", "sourceType": "org.dromara.common.chat.config.properties.LangChain4jProperties", "sourceMethod": "public org.dromara.common.chat.config.properties.LangChain4jProperties.OllamaConfig getOllama() "}, {"name": "langchain4j.openai", "type": "org.dromara.common.chat.config.properties.LangChain4jProperties$OpenAiConfig", "sourceType": "org.dromara.common.chat.config.properties.LangChain4jProperties", "sourceMethod": "public org.dromara.common.chat.config.properties.LangChain4jProperties.OpenAiConfig getOpenai() "}], "properties": [{"name": "langchain4j.agent.default-provider", "type": "java.lang.String", "sourceType": "org.dromara.common.chat.config.properties.LangChain4jProperties$AgentConfig"}, {"name": "langchain4j.agent.max-memory-size", "type": "java.lang.Integer", "sourceType": "org.dromara.common.chat.config.properties.LangChain4jProperties$AgentConfig"}, {"name": "langchain4j.agent.session-timeout", "type": "java.lang.Integer", "sourceType": "org.dromara.common.chat.config.properties.LangChain4jProperties$AgentConfig"}, {"name": "langchain4j.dashscope.api-key", "type": "java.lang.String", "sourceType": "org.dromara.common.chat.config.properties.LangChain4jProperties$DashscopeConfig"}, {"name": "langchain4j.dashscope.max-tokens", "type": "java.lang.Integer", "sourceType": "org.dromara.common.chat.config.properties.LangChain4jProperties$DashscopeConfig"}, {"name": "langchain4j.dashscope.model", "type": "java.lang.String", "sourceType": "org.dromara.common.chat.config.properties.LangChain4jProperties$DashscopeConfig"}, {"name": "langchain4j.dashscope.temperature", "type": "java.lang.Double", "sourceType": "org.dromara.common.chat.config.properties.LangChain4jProperties$DashscopeConfig"}, {"name": "langchain4j.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用LangChain4j", "sourceType": "org.dromara.common.chat.config.properties.LangChain4jProperties"}, {"name": "langchain4j.ollama.base-url", "type": "java.lang.String", "sourceType": "org.dromara.common.chat.config.properties.LangChain4jProperties$OllamaConfig"}, {"name": "langchain4j.ollama.model", "type": "java.lang.String", "sourceType": "org.dromara.common.chat.config.properties.LangChain4jProperties$OllamaConfig"}, {"name": "langchain4j.ollama.temperature", "type": "java.lang.Double", "sourceType": "org.dromara.common.chat.config.properties.LangChain4jProperties$OllamaConfig"}, {"name": "langchain4j.ollama.timeout", "type": "java.lang.Integer", "sourceType": "org.dromara.common.chat.config.properties.LangChain4jProperties$OllamaConfig"}, {"name": "langchain4j.openai.api-key", "type": "java.lang.String", "sourceType": "org.dromara.common.chat.config.properties.LangChain4jProperties$OpenAiConfig"}, {"name": "langchain4j.openai.base-url", "type": "java.lang.String", "sourceType": "org.dromara.common.chat.config.properties.LangChain4jProperties$OpenAiConfig"}, {"name": "langchain4j.openai.max-tokens", "type": "java.lang.Integer", "sourceType": "org.dromara.common.chat.config.properties.LangChain4jProperties$OpenAiConfig"}, {"name": "langchain4j.openai.model", "type": "java.lang.String", "sourceType": "org.dromara.common.chat.config.properties.LangChain4jProperties$OpenAiConfig"}, {"name": "langchain4j.openai.temperature", "type": "java.lang.Double", "sourceType": "org.dromara.common.chat.config.properties.LangChain4jProperties$OpenAiConfig"}, {"name": "langchain4j.openai.timeout", "type": "java.lang.Integer", "sourceType": "org.dromara.common.chat.config.properties.LangChain4jProperties$OpenAiConfig"}], "hints": []}