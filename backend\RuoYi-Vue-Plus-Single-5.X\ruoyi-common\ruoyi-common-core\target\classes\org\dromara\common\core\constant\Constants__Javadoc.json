{"doc": "\n 通用常量信息\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "UTF8", "doc": "\n UTF-8 字符集\r\n"}, {"name": "GBK", "doc": "\n GBK 字符集\r\n"}, {"name": "WWW", "doc": "\n www主域\r\n"}, {"name": "HTTP", "doc": "\n http请求\r\n"}, {"name": "HTTPS", "doc": "\n https请求\r\n"}, {"name": "SUCCESS", "doc": "\n 通用成功标识\r\n"}, {"name": "FAIL", "doc": "\n 通用失败标识\r\n"}, {"name": "LOGIN_SUCCESS", "doc": "\n 登录成功\r\n"}, {"name": "LOGOUT", "doc": "\n 注销\r\n"}, {"name": "REGISTER", "doc": "\n 注册\r\n"}, {"name": "LOGIN_FAIL", "doc": "\n 登录失败\r\n"}, {"name": "CAPTCHA_EXPIRATION", "doc": "\n 验证码有效期（分钟）\r\n"}, {"name": "TOP_PARENT_ID", "doc": "\n 顶级父级id\r\n"}], "enumConstants": [], "methods": [], "constructors": []}