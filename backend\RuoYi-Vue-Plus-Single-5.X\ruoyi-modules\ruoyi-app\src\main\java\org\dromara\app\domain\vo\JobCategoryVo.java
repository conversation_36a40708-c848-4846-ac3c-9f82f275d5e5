package org.dromara.app.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.app.domain.JobCategory;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 岗位分类视图对象 app_job_category
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Data
@AutoMapper(target = JobCategory.class)
public class JobCategoryVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 分类ID
     */
    private Long id;

    /**
     * 分类名称
     */
    private String name;

    /**
     * 分类图标
     */
    private String icon;

    /**
     * 分类颜色
     */
    private String color;

    /**
     * 分类描述
     */
    private String description;

    /**
     * 岗位数量
     */
    private Integer jobCount;

    /**
     * 排序号
     */
    private Integer sortOrder;

    /**
     * 状态（0正常 1停用）
     */
    private String status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

}
