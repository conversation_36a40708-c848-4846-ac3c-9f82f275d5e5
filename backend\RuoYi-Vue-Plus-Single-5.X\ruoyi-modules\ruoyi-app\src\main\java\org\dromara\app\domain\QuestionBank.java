package org.dromara.app.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;
import java.io.Serializable;

/**
 * 题库实体
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_question_bank")
public class QuestionBank extends BaseEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 题库ID
     */
    @TableId(value = "bank_id")
    private Long bankId;

    /**
     * 题库编码
     */
    @TableField("bank_code")
    private String bankCode;

    /**
     * 题库标题
     */
    @TableField("title")
    private String title;

    /**
     * 题库描述
     */
    @TableField("description")
    private String description;

    /**
     * 专业ID
     */
    @TableField("major_id")
    private Long majorId;

    /**
     * 题库图标
     */
    @TableField("icon")
    private String icon;

    /**
     * 题库颜色
     */
    @TableField("color")
    private String color;

    /**
     * 难度（1-简单 2-中等 3-困难）
     */
    @TableField("difficulty")
    private Integer difficulty;

    /**
     * 题目总数
     */
    @TableField("total_questions")
    private Integer totalQuestions;

    /**
     * 练习次数
     */
    @TableField("practice_count")
    private Integer practiceCount;

    /**
     * 分类标签（JSON格式）
     */
    @TableField("categories")
    private String categories;

    /**
     * 排序
     */
    @TableField("sort")
    private Integer sort;

    /**
     * 状态（0正常 1停用）
     */
    @TableField("status")
    private String status;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 是否收藏（非数据库字段）
     */
    @TableField(exist = false)
    private Boolean isBookmarked;

    /**
     * 学习进度（非数据库字段）
     */
    @TableField(exist = false)
    private Integer progress;
}
