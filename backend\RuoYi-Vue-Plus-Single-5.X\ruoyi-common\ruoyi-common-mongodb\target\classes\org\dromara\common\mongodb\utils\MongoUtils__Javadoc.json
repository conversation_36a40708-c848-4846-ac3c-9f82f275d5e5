{"doc": "\n MongoDB工具类\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "save", "paramTypes": ["java.lang.Object"], "doc": "\n 保存文档\r\n"}, {"name": "saveAll", "paramTypes": ["java.util.Collection"], "doc": "\n 批量保存文档\r\n"}, {"name": "findById", "paramTypes": ["java.lang.Object", "java.lang.Class"], "doc": "\n 根据ID查询文档\r\n"}, {"name": "findAll", "paramTypes": ["java.lang.Class"], "doc": "\n 查询所有文档\r\n"}, {"name": "find", "paramTypes": ["org.springframework.data.mongodb.core.query.Query", "java.lang.Class"], "doc": "\n 根据条件查询文档\r\n"}, {"name": "findOne", "paramTypes": ["org.springframework.data.mongodb.core.query.Query", "java.lang.Class"], "doc": "\n 根据条件查询单个文档\r\n"}, {"name": "findByField", "paramTypes": ["java.lang.String", "java.lang.Object", "java.lang.Class"], "doc": "\n 根据字段查询文档\r\n"}, {"name": "findOneByField", "paramTypes": ["java.lang.String", "java.lang.Object", "java.lang.Class"], "doc": "\n 根据字段查询单个文档\r\n"}, {"name": "findByPage", "paramTypes": ["org.springframework.data.mongodb.core.query.Query", "int", "int", "java.lang.Class"], "doc": "\n 分页查询\r\n"}, {"name": "findByPage", "paramTypes": ["org.springframework.data.mongodb.core.query.Query", "int", "int", "org.springframework.data.domain.Sort", "java.lang.Class"], "doc": "\n 分页查询带排序\r\n"}, {"name": "count", "paramTypes": ["org.springframework.data.mongodb.core.query.Query", "java.lang.Class"], "doc": "\n 计数\r\n"}, {"name": "countAll", "paramTypes": ["java.lang.Class"], "doc": "\n 计数所有\r\n"}, {"name": "update", "paramTypes": ["org.springframework.data.mongodb.core.query.Query", "org.springframework.data.mongodb.core.query.Update", "java.lang.Class"], "doc": "\n 更新文档\r\n"}, {"name": "updateMulti", "paramTypes": ["org.springframework.data.mongodb.core.query.Query", "org.springframework.data.mongodb.core.query.Update", "java.lang.Class"], "doc": "\n 批量更新文档\r\n"}, {"name": "updateById", "paramTypes": ["java.lang.Object", "org.springframework.data.mongodb.core.query.Update", "java.lang.Class"], "doc": "\n 根据ID更新文档\r\n"}, {"name": "remove", "paramTypes": ["org.springframework.data.mongodb.core.query.Query", "java.lang.Class"], "doc": "\n 删除文档\r\n"}, {"name": "removeById", "paramTypes": ["java.lang.Object", "java.lang.Class"], "doc": "\n 根据ID删除文档\r\n"}, {"name": "removeAll", "paramTypes": ["java.lang.Class"], "doc": "\n 删除所有文档\r\n"}, {"name": "exists", "paramTypes": ["org.springframework.data.mongodb.core.query.Query", "java.lang.Class"], "doc": "\n 检查文档是否存在\r\n"}, {"name": "existsById", "paramTypes": ["java.lang.Object", "java.lang.Class"], "doc": "\n 根据ID检查文档是否存在\r\n"}, {"name": "createQuery", "paramTypes": [], "doc": "\n 创建查询条件\r\n"}, {"name": "createUpdate", "paramTypes": [], "doc": "\n 创建更新条件\r\n"}, {"name": "createSort", "paramTypes": ["org.springframework.data.domain.Sort.Direction", "java.lang.String[]"], "doc": "\n 创建排序条件\r\n"}, {"name": "createSortAsc", "paramTypes": ["java.lang.String[]"], "doc": "\n 创建升序排序\r\n"}, {"name": "createSortDesc", "paramTypes": ["java.lang.String[]"], "doc": "\n 创建降序排序\r\n"}, {"name": "buildLikeCriteria", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 构建模糊查询条件\r\n"}, {"name": "buildRangeCriteria", "paramTypes": ["java.lang.String", "java.lang.Object", "java.lang.Object"], "doc": "\n 构建范围查询条件\r\n"}, {"name": "buildInCriteria", "paramTypes": ["java.lang.String", "java.util.Collection"], "doc": "\n 构建IN查询条件\r\n"}], "constructors": []}