package org.dromara.app.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.vo.InterviewResponseVo;
import org.dromara.app.service.IInterviewStatsService;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 面试统计服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class InterviewStatsServiceImpl implements IInterviewStatsService {

    @Override
    public InterviewResponseVo.UserStats getUserStats(Long userId) {
        log.info("获取用户面试统计，userId: {}", userId);

        try {
            // 模拟用户统计数据
            InterviewResponseVo.UserStats stats = new InterviewResponseVo.UserStats();
            stats.setUserId(userId);
            stats.setTotalInterviews(25);
            stats.setCompletedInterviews(20);
            stats.setAverageScore(78.5);
            stats.setTotalTime(1800); // 30小时
            stats.setRank("B+");
            stats.setWeeklyProgress(15);
            stats.setMonthlyProgress(65);
            stats.setCurrentStreak(7);
            stats.setLongestStreak(15);
            stats.setLastInterviewTime(LocalDateTime.now().minusDays(1));

            // 技能分布
            Map<String, Double> skillScores = new HashMap<>();
            skillScores.put("Java", 85.0);
            skillScores.put("Spring", 78.0);
            skillScores.put("MySQL", 72.0);
            skillScores.put("Redis", 68.0);
            stats.setSkillScores(skillScores);

            // 分类表现
            Map<String, Integer> categoryPerformance = new HashMap<>();
            categoryPerformance.put("技术开发", 15);
            categoryPerformance.put("算法题", 8);
            categoryPerformance.put("系统设计", 5);
            stats.setCategoryPerformance(categoryPerformance);

            return stats;
        } catch (Exception e) {
            log.error("获取用户统计失败", e);
            throw new RuntimeException("获取用户统计失败");
        }
    }

    @Override
    public InterviewResponseVo.JobStats getJobStats(Long jobId) {
        log.info("获取岗位面试统计，jobId: {}", jobId);

        try {
            // 模拟岗位统计数据
            InterviewResponseVo.JobStats stats = new InterviewResponseVo.JobStats();
            stats.setJobId(jobId);
            stats.setTotalInterviews(1250);
            stats.setAverageScore(75.5);
            stats.setCompletionRate(0.85);
            stats.setAverageDuration(45); // 45分钟
            stats.setPopularityRank(3);

            // 难度分布
            Map<String, Integer> difficultyDistribution = new HashMap<>();
            difficultyDistribution.put("简单", 30);
            difficultyDistribution.put("中等", 50);
            difficultyDistribution.put("困难", 20);
            stats.setDifficultyDistribution(difficultyDistribution);

            // 分数分布
            Map<String, Integer> scoreDistribution = new HashMap<>();
            scoreDistribution.put("90-100", 15);
            scoreDistribution.put("80-89", 25);
            scoreDistribution.put("70-79", 35);
            scoreDistribution.put("60-69", 20);
            scoreDistribution.put("0-59", 5);
            stats.setScoreDistribution(scoreDistribution);

            return stats;
        } catch (Exception e) {
            log.error("获取岗位统计失败", e);
            throw new RuntimeException("获取岗位统计失败");
        }
    }

    @Override
    public InterviewResponseVo.SystemStats getSystemStats() {
        log.info("获取系统整体统计");

        try {
            // 模拟系统统计数据
            InterviewResponseVo.SystemStats stats = new InterviewResponseVo.SystemStats();
            stats.setTotalUsers(5000);
            stats.setActiveUsers(1200);
            stats.setTotalJobs(290);
            stats.setTotalQuestions(1500);
            stats.setTotalInterviews(8000);
            stats.setTodayInterviews(150);
            stats.setAverageScore(76.8);
            stats.setCompletionRate(0.82);

            // 用户增长趋势
            List<Map<String, Object>> userGrowth = new ArrayList<>();
            for (int i = 6; i >= 0; i--) {
                Map<String, Object> point = new HashMap<>();
                point.put("date", LocalDateTime.now().minusDays(i).toLocalDate().toString());
                point.put("count", 50 + (int) (Math.random() * 100));
                userGrowth.add(point);
            }
            stats.setUserGrowthTrend(userGrowth);

            return stats;
        } catch (Exception e) {
            log.error("获取系统统计失败", e);
            throw new RuntimeException("获取系统统计失败");
        }
    }

    @Override
    public List<InterviewResponseVo.TrendData> getInterviewTrends(LocalDateTime startTime, LocalDateTime endTime, String granularity) {
        log.info("获取面试趋势数据，startTime: {}, endTime: {}, granularity: {}", startTime, endTime, granularity);

        try {
            List<InterviewResponseVo.TrendData> trends = new ArrayList<>();

            // 根据粒度生成模拟数据
            int days = granularity.equals("day") ? 7 : granularity.equals("week") ? 4 : 12;
            for (int i = days - 1; i >= 0; i--) {
                InterviewResponseVo.TrendData trend = new InterviewResponseVo.TrendData();
                trend.setDate(LocalDateTime.now().minusDays(i).toLocalDate().toString());
                trend.setInterviewCount(50 + (int) (Math.random() * 100));
                trend.setAverageScore(70.0 + Math.random() * 20);
                trend.setCompletionRate(0.7 + Math.random() * 0.2);
                trends.add(trend);
            }

            return trends;
        } catch (Exception e) {
            log.error("获取面试趋势失败", e);
            throw new RuntimeException("获取面试趋势失败");
        }
    }

    @Override
    public List<InterviewResponseVo.PopularQuestion> getPopularQuestions(Integer limit) {
        log.info("获取热门问题统计，limit: {}", limit);

        try {
            int questionLimit = limit != null ? limit : 10;
            List<InterviewResponseVo.PopularQuestion> questions = new ArrayList<>();

            for (int i = 1; i <= questionLimit; i++) {
                InterviewResponseVo.PopularQuestion question = new InterviewResponseVo.PopularQuestion();
                question.setQuestionId("q_" + i);
                question.setContent("这是第" + i + "个热门问题");
                question.setCategory("技术问题");
                question.setDifficulty("中等");
                question.setAskedCount(1000 - i * 50);
                question.setAverageScore(75.0 + Math.random() * 15);
                question.setCorrectRate(0.6 + Math.random() * 0.3);
                question.setTags(Arrays.asList("Java", "Spring", "面试"));
                questions.add(question);
            }

            return questions;
        } catch (Exception e) {
            log.error("获取热门问题失败", e);
            throw new RuntimeException("获取热门问题失败");
        }
    }

    @Override
    public List<InterviewResponseVo.UserRanking> getUserRankings(Integer limit) {
        log.info("获取用户表现排行，limit: {}", limit);

        try {
            int rankLimit = limit != null ? limit : 10;
            List<InterviewResponseVo.UserRanking> rankings = new ArrayList<>();

            for (int i = 1; i <= rankLimit; i++) {
                InterviewResponseVo.UserRanking ranking = new InterviewResponseVo.UserRanking();
                ranking.setUserId((long) i);
                ranking.setUsername("用户" + i);
                ranking.setAvatar("/images/avatar" + i + ".png");
                ranking.setRank(i);
                ranking.setScore(95.0 - i * 2);
                ranking.setTotalInterviews(50 - i * 2);
                ranking.setLevel("A" + (i <= 3 ? "+" : ""));
                rankings.add(ranking);
            }

            return rankings;
        } catch (Exception e) {
            log.error("获取用户排行失败", e);
            throw new RuntimeException("获取用户排行失败");
        }
    }

    @Override
    public Map<String, Double> getCompletionRates(Long jobId) {
        log.info("获取面试完成率统计，jobId: {}", jobId);

        try {
            Map<String, Double> rates = new HashMap<>();
            rates.put("overall", 0.82);
            rates.put("today", 0.85);
            rates.put("week", 0.80);
            rates.put("month", 0.78);

            if (jobId != null) {
                rates.put("job_specific", 0.75 + Math.random() * 0.2);
            }

            return rates;
        } catch (Exception e) {
            log.error("获取完成率统计失败", e);
            throw new RuntimeException("获取完成率统计失败");
        }
    }

    @Override
    public Map<String, Double> getAverageScores(Long jobId) {
        log.info("获取平均分数统计，jobId: {}", jobId);

        try {
            Map<String, Double> scores = new HashMap<>();
            scores.put("overall", 76.8);
            scores.put("today", 78.2);
            scores.put("week", 77.5);
            scores.put("month", 76.0);

            if (jobId != null) {
                scores.put("job_specific", 70.0 + Math.random() * 20);
            }

            return scores;
        } catch (Exception e) {
            log.error("获取平均分数统计失败", e);
            throw new RuntimeException("获取平均分数统计失败");
        }
    }

    @Override
    public void recordInterviewEvent(String sessionId, String eventType, Map<String, Object> eventData) {
        log.info("记录面试事件，sessionId: {}, eventType: {}", sessionId, eventType);

        try {
            // 这里应该将事件数据保存到数据库或消息队列
            // 暂时只记录日志
            log.info("面试事件记录：sessionId={}, eventType={}, data={}", sessionId, eventType, eventData);
        } catch (Exception e) {
            log.error("记录面试事件失败", e);
            // 不抛出异常，避免影响主业务流程
        }
    }

    @Override
    public Integer cleanExpiredStats(LocalDateTime beforeTime) {
        log.info("清理过期面试统计数据，beforeTime: {}", beforeTime);
        return 0;
    }
}
