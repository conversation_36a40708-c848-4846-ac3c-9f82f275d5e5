package org.dromara.app.event;

import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;

import java.time.LocalDateTime;

/**
 * 支付事件基类
 * 用于支付状态变化时的事件通知
 *
 * <AUTHOR>
 */
@Getter
@Setter
public abstract class PaymentEvent extends ApplicationEvent {

    /**
     * 订单号
     */
    private final String orderNo;

    /**
     * 事件时间
     */
    private final LocalDateTime eventTime;

    /**
     * 事件描述
     */
    private final String description;

    public PaymentEvent(Object source, String orderNo, String description) {
        super(source);
        this.orderNo = orderNo;
        this.description = description;
        this.eventTime = LocalDateTime.now();
    }

    /**
     * 支付成功事件
     */
    public static class PaymentSuccessEvent extends PaymentEvent {
        public PaymentSuccessEvent(Object source, String orderNo) {
            super(source, orderNo, "支付成功");
        }
    }

    /**
     * 支付失败事件
     */
    public static class PaymentFailedEvent extends PaymentEvent {
        private final String reason;

        public PaymentFailedEvent(Object source, String orderNo, String reason) {
            super(source, orderNo, "支付失败");
            this.reason = reason;
        }

        public String getReason() {
            return reason;
        }
    }

    /**
     * 支付取消事件
     */
    public static class PaymentCancelledEvent extends PaymentEvent {
        public PaymentCancelledEvent(Object source, String orderNo) {
            super(source, orderNo, "支付取消");
        }
    }

    /**
     * 支付超时事件
     */
    public static class PaymentTimeoutEvent extends PaymentEvent {
        public PaymentTimeoutEvent(Object source, String orderNo) {
            super(source, orderNo, "支付超时");
        }
    }
}
