package org.dromara.app.service;

import org.dromara.app.domain.UserBehavior;
import org.dromara.app.domain.bo.UserBehaviorBo;
import org.dromara.app.domain.dto.TrackEventDto;

import java.util.List;

/**
 * 用户行为记录Service接口
 *
 * <AUTHOR>
 */
public interface IUserBehaviorService {

    /**
     * 处理用户行为数据
     *
     * @param trackEventDto 埋点事件数据
     */
    void processUserBehavior(TrackEventDto trackEventDto);

    /**
     * 批量处理用户行为数据
     *
     * @param trackEventDtos 埋点事件数据列表
     */
    void batchProcessUserBehavior(List<TrackEventDto> trackEventDtos);

    /**
     * 保存用户行为记录
     *
     * @param userBehaviorBo 用户行为业务对象
     * @return 保存结果
     */
    Boolean insertUserBehavior(UserBehaviorBo userBehaviorBo);

    /**
     * 批量保存用户行为记录
     *
     * @param userBehaviorBos 用户行为业务对象列表
     * @return 保存结果
     */
    Boolean batchInsertUserBehavior(List<UserBehaviorBo> userBehaviorBos);

    /**
     * 根据用户ID和行为类型查询行为记录
     *
     * @param userId       用户ID
     * @param behaviorType 行为类型
     * @return 行为记录列表
     */
    List<UserBehavior> queryByUserIdAndBehaviorType(Long userId, String behaviorType);

    /**
     * 统计用户某种行为的次数
     *
     * @param userId       用户ID
     * @param behaviorType 行为类型
     * @return 行为次数
     */
    Long countByUserIdAndBehaviorType(Long userId, String behaviorType);

    /**
     * 查询用户连续登录天数
     *
     * @param userId 用户ID
     * @return 连续登录天数
     */
    Integer getConsecutiveLoginDays(Long userId);

    /**
     * 查询用户累计学习时长（分钟）
     *
     * @param userId 用户ID
     * @return 累计学习时长
     */
    Long getTotalStudyMinutes(Long userId);

}
