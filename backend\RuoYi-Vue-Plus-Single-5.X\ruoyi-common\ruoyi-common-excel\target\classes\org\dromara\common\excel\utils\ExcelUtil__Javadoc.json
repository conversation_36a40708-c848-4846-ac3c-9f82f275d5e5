{"doc": "\n Excel相关处理\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "importExcel", "paramTypes": ["java.io.InputStream", "java.lang.Class"], "doc": "\n 同步导入(适用于小数据量)\r\n\r\n @param is 输入流\r\n @return 转换后集合\r\n"}, {"name": "importExcel", "paramTypes": ["java.io.InputStream", "java.lang.Class", "boolean"], "doc": "\n 使用校验监听器 异步导入 同步返回\r\n\r\n @param is         输入流\r\n @param clazz      对象类型\r\n @param isValidate 是否 Validator 检验 默认为是\r\n @return 转换后集合\r\n"}, {"name": "importExcel", "paramTypes": ["java.io.InputStream", "java.lang.Class", "org.dromara.common.excel.core.ExcelListener"], "doc": "\n 使用自定义监听器 异步导入 自定义返回\r\n\r\n @param is       输入流\r\n @param clazz    对象类型\r\n @param listener 自定义监听器\r\n @return 转换后集合\r\n"}, {"name": "exportExcel", "paramTypes": ["java.util.List", "java.lang.String", "java.lang.Class", "jakarta.servlet.http.HttpServletResponse"], "doc": "\n 导出excel\r\n\r\n @param list      导出数据集合\r\n @param sheetName 工作表的名称\r\n @param clazz     实体类\r\n @param response  响应体\r\n"}, {"name": "exportExcel", "paramTypes": ["java.util.List", "java.lang.String", "java.lang.Class", "jakarta.servlet.http.HttpServletResponse", "java.util.List"], "doc": "\n 导出excel\r\n\r\n @param list      导出数据集合\r\n @param sheetName 工作表的名称\r\n @param clazz     实体类\r\n @param response  响应体\r\n @param options   级联下拉选\r\n"}, {"name": "exportExcel", "paramTypes": ["java.util.List", "java.lang.String", "java.lang.Class", "boolean", "jakarta.servlet.http.HttpServletResponse"], "doc": "\n 导出excel\r\n\r\n @param list      导出数据集合\r\n @param sheetName 工作表的名称\r\n @param clazz     实体类\r\n @param merge     是否合并单元格\r\n @param response  响应体\r\n"}, {"name": "exportExcel", "paramTypes": ["java.util.List", "java.lang.String", "java.lang.Class", "boolean", "jakarta.servlet.http.HttpServletResponse", "java.util.List"], "doc": "\n 导出excel\r\n\r\n @param list      导出数据集合\r\n @param sheetName 工作表的名称\r\n @param clazz     实体类\r\n @param merge     是否合并单元格\r\n @param response  响应体\r\n @param options   级联下拉选\r\n"}, {"name": "exportExcel", "paramTypes": ["java.util.List", "java.lang.String", "java.lang.Class", "java.io.OutputStream"], "doc": "\n 导出excel\r\n\r\n @param list      导出数据集合\r\n @param sheetName 工作表的名称\r\n @param clazz     实体类\r\n @param os        输出流\r\n"}, {"name": "exportExcel", "paramTypes": ["java.util.List", "java.lang.String", "java.lang.Class", "java.io.OutputStream", "java.util.List"], "doc": "\n 导出excel\r\n\r\n @param list      导出数据集合\r\n @param sheetName 工作表的名称\r\n @param clazz     实体类\r\n @param os        输出流\r\n @param options   级联下拉选内容\r\n"}, {"name": "exportExcel", "paramTypes": ["java.util.List", "java.lang.String", "java.lang.Class", "boolean", "java.io.OutputStream", "java.util.List"], "doc": "\n 导出excel\r\n\r\n @param list      导出数据集合\r\n @param sheetName 工作表的名称\r\n @param clazz     实体类\r\n @param merge     是否合并单元格\r\n @param os        输出流\r\n"}, {"name": "exportTemplate", "paramTypes": ["java.util.List", "java.lang.String", "java.lang.String", "jakarta.servlet.http.HttpServletResponse"], "doc": "\n 单表多数据模板导出 模板格式为 {.属性}\r\n\r\n @param filename     文件名\r\n @param templatePath 模板路径 resource 目录下的路径包括模板文件名\r\n                     例如: excel/temp.xlsx\r\n                     重点: 模板文件必须放置到启动类对应的 resource 目录下\r\n @param data         模板需要的数据\r\n @param response     响应体\r\n"}, {"name": "exportTemplate", "paramTypes": ["java.util.List", "java.lang.String", "java.io.OutputStream"], "doc": "\n 单表多数据模板导出 模板格式为 {.属性}\r\n\r\n @param templatePath 模板路径 resource 目录下的路径包括模板文件名\r\n                     例如: excel/temp.xlsx\r\n                     重点: 模板文件必须放置到启动类对应的 resource 目录下\r\n @param data         模板需要的数据\r\n @param os           输出流\r\n"}, {"name": "exportTemplateMultiList", "paramTypes": ["java.util.Map", "java.lang.String", "java.lang.String", "jakarta.servlet.http.HttpServletResponse"], "doc": "\n 多表多数据模板导出 模板格式为 {key.属性}\r\n\r\n @param filename     文件名\r\n @param templatePath 模板路径 resource 目录下的路径包括模板文件名\r\n                     例如: excel/temp.xlsx\r\n                     重点: 模板文件必须放置到启动类对应的 resource 目录下\r\n @param data         模板需要的数据\r\n @param response     响应体\r\n"}, {"name": "exportTemplateMultiSheet", "paramTypes": ["java.util.List", "java.lang.String", "java.lang.String", "jakarta.servlet.http.HttpServletResponse"], "doc": "\n 多sheet模板导出 模板格式为 {key.属性}\r\n\r\n @param filename     文件名\r\n @param templatePath 模板路径 resource 目录下的路径包括模板文件名\r\n                     例如: excel/temp.xlsx\r\n                     重点: 模板文件必须放置到启动类对应的 resource 目录下\r\n @param data         模板需要的数据\r\n @param response     响应体\r\n"}, {"name": "exportTemplateMultiList", "paramTypes": ["java.util.Map", "java.lang.String", "java.io.OutputStream"], "doc": "\n 多表多数据模板导出 模板格式为 {key.属性}\r\n\r\n @param templatePath 模板路径 resource 目录下的路径包括模板文件名\r\n                     例如: excel/temp.xlsx\r\n                     重点: 模板文件必须放置到启动类对应的 resource 目录下\r\n @param data         模板需要的数据\r\n @param os           输出流\r\n"}, {"name": "exportTemplateMultiSheet", "paramTypes": ["java.util.List", "java.lang.String", "java.io.OutputStream"], "doc": "\n 多sheet模板导出 模板格式为 {key.属性}\r\n\r\n @param templatePath 模板路径 resource 目录下的路径包括模板文件名\r\n                     例如: excel/temp.xlsx\r\n                     重点: 模板文件必须放置到启动类对应的 resource 目录下\r\n @param data         模板需要的数据\r\n @param os           输出流\r\n"}, {"name": "resetResponse", "paramTypes": ["java.lang.String", "jakarta.servlet.http.HttpServletResponse"], "doc": "\n 重置响应体\r\n"}, {"name": "convertByExp", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 解析导出值 0=男,1=女,2=未知\r\n\r\n @param propertyValue 参数值\r\n @param converterExp  翻译注解\r\n @param separator     分隔符\r\n @return 解析后值\r\n"}, {"name": "reverseByExp", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 反向解析值 男=0,女=1,未知=2\r\n\r\n @param propertyValue 参数值\r\n @param converterExp  翻译注解\r\n @param separator     分隔符\r\n @return 解析后值\r\n"}, {"name": "encodingFilename", "paramTypes": ["java.lang.String"], "doc": "\n 编码文件名\r\n"}], "constructors": []}