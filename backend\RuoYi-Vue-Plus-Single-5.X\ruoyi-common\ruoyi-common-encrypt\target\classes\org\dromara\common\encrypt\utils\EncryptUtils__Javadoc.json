{"doc": "\n 安全相关工具类\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "PUBLIC_KEY", "doc": "\n 公钥\r\n"}, {"name": "PRIVATE_KEY", "doc": "\n 私钥\r\n"}], "enumConstants": [], "methods": [{"name": "encryptByBase64", "paramTypes": ["java.lang.String"], "doc": "\n Base64加密\r\n\r\n @param data 待加密数据\r\n @return 加密后字符串\r\n"}, {"name": "decryptByBase64", "paramTypes": ["java.lang.String"], "doc": "\n Base64解密\r\n\r\n @param data 待解密数据\r\n @return 解密后字符串\r\n"}, {"name": "encryptByAes", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n AES加密\r\n\r\n @param data     待加密数据\r\n @param password 秘钥字符串\r\n @return 加密后字符串, 采用Base64编码\r\n"}, {"name": "encryptByAesHex", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n AES加密\r\n\r\n @param data     待加密数据\r\n @param password 秘钥字符串\r\n @return 加密后字符串, 采用Hex编码\r\n"}, {"name": "decryptByAes", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n AES解密\r\n\r\n @param data     待解密数据\r\n @param password 秘钥字符串\r\n @return 解密后字符串\r\n"}, {"name": "encryptBySm4", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n SM4加密（Base64编码）\r\n\r\n @param data     待加密数据\r\n @param password 秘钥字符串\r\n @return 加密后字符串, 采用Base64编码\r\n"}, {"name": "encryptBySm4Hex", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n SM4加密（Hex编码）\r\n\r\n @param data     待加密数据\r\n @param password 秘钥字符串\r\n @return 加密后字符串, 采用Hex编码\r\n"}, {"name": "decryptBySm4", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n sm4解密\r\n\r\n @param data     待解密数据（可以是Base64或Hex编码）\r\n @param password 秘钥字符串\r\n @return 解密后字符串\r\n"}, {"name": "generateSm2Key", "paramTypes": [], "doc": "\n 产生sm2加解密需要的公钥和私钥\r\n\r\n @return 公私钥Map\r\n"}, {"name": "encryptBySm2", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n sm2公钥加密\r\n\r\n @param data      待加密数据\r\n @param publicKey 公钥\r\n @return 加密后字符串, 采用Base64编码\r\n"}, {"name": "encryptBySm2Hex", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n sm2公钥加密\r\n\r\n @param data      待加密数据\r\n @param publicKey 公钥\r\n @return 加密后字符串, 采用Hex编码\r\n"}, {"name": "decryptBySm2", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n sm2私钥解密\r\n\r\n @param data       待解密数据\r\n @param privateKey 私钥\r\n @return 解密后字符串\r\n"}, {"name": "generateRsaKey", "paramTypes": [], "doc": "\n 产生RSA加解密需要的公钥和私钥\r\n\r\n @return 公私钥Map\r\n"}, {"name": "encryptByRsa", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n rsa公钥加密\r\n\r\n @param data      待加密数据\r\n @param publicKey 公钥\r\n @return 加密后字符串, 采用Base64编码\r\n"}, {"name": "encryptByRsaHex", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n rsa公钥加密\r\n\r\n @param data      待加密数据\r\n @param publicKey 公钥\r\n @return 加密后字符串, 采用Hex编码\r\n"}, {"name": "decryptByRsa", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n rsa私钥解密\r\n\r\n @param data       待解密数据\r\n @param privateKey 私钥\r\n @return 解密后字符串\r\n"}, {"name": "encryptByMd5", "paramTypes": ["java.lang.String"], "doc": "\n md5加密\r\n\r\n @param data 待加密数据\r\n @return 加密后字符串, 采用Hex编码\r\n"}, {"name": "encryptBySha256", "paramTypes": ["java.lang.String"], "doc": "\n sha256加密\r\n\r\n @param data 待加密数据\r\n @return 加密后字符串, 采用Hex编码\r\n"}, {"name": "encryptBySm3", "paramTypes": ["java.lang.String"], "doc": "\n sm3加密\r\n\r\n @param data 待加密数据\r\n @return 加密后字符串, 采用Hex编码\r\n"}], "constructors": []}