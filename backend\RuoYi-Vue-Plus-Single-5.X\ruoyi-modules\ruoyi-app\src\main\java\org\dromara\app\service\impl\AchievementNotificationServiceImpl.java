package org.dromara.app.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.config.AchievementRabbitMqConfig;
import org.dromara.app.domain.vo.AchievementVo;
import org.dromara.app.domain.vo.UserAchievementVo;
import org.dromara.app.service.IAchievementNotificationService;
import org.dromara.common.rabbitmq.core.RabbitMqTemplate;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 成就通知服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AchievementNotificationServiceImpl implements IAchievementNotificationService {

    private final Optional<RabbitMqTemplate> rabbitMqTemplate;

    @Override
    public void sendAchievementUnlockNotification(Long userId, AchievementVo achievement) {
        try {
            // 构建通知数据
            Map<String, Object> notificationData = new HashMap<>();
            notificationData.put("id", IdUtil.fastSimpleUUID());
            notificationData.put("type", "ACHIEVEMENT_UNLOCK");
            notificationData.put("userId", userId);
            notificationData.put("achievementId", achievement.getId());
            notificationData.put("achievementCode", achievement.getAchievementCode());
            notificationData.put("achievementName", achievement.getAchievementName());
            notificationData.put("achievementDesc", achievement.getAchievementDesc());
            notificationData.put("achievementIcon", achievement.getAchievementIcon());
            notificationData.put("rewardPoints", achievement.getRewardPoints());
            notificationData.put("title", "🎉 恭喜解锁新成就！");
            notificationData.put("message", String.format("恭喜您解锁了成就「%s」，获得 %d 积分奖励！", 
                achievement.getAchievementName(), achievement.getRewardPoints()));
            notificationData.put("createTime", new Date());

            // 发送到通知队列
            sendNotificationToQueue(notificationData);

            log.info("成就解锁通知已发送: userId={}, achievementCode={}", userId, achievement.getAchievementCode());

        } catch (Exception e) {
            log.error("发送成就解锁通知失败: userId={}, achievementId={}", userId, achievement.getId(), e);
        }
    }

    @Override
    public void batchSendAchievementUnlockNotification(Long userId, List<AchievementVo> achievements) {
        for (AchievementVo achievement : achievements) {
            sendAchievementUnlockNotification(userId, achievement);
        }
    }

    @Override
    public void sendAchievementProgressNotification(Long userId, UserAchievementVo userAchievement) {
        try {
            // 只有在特定进度节点才发送通知（如25%, 50%, 75%）
            double progress = userAchievement.getProgress().doubleValue();
            if (!shouldSendProgressNotification(progress)) {
                return;
            }

            // 构建通知数据
            Map<String, Object> notificationData = new HashMap<>();
            notificationData.put("id", IdUtil.fastSimpleUUID());
            notificationData.put("type", "ACHIEVEMENT_PROGRESS");
            notificationData.put("userId", userId);
            notificationData.put("achievementId", userAchievement.getAchievementId());
            notificationData.put("achievementName", userAchievement.getAchievementName());
            notificationData.put("progress", progress);
            notificationData.put("currentValue", userAchievement.getCurrentValue());
            notificationData.put("targetValue", userAchievement.getTargetValue());
            notificationData.put("title", "📈 成就进度更新");
            notificationData.put("message", String.format("成就「%s」进度已达到 %.0f%%，继续加油！", 
                userAchievement.getAchievementName(), progress));
            notificationData.put("createTime", new Date());

            // 发送到通知队列
            sendNotificationToQueue(notificationData);

            log.debug("成就进度通知已发送: userId={}, achievementId={}, progress={}%", 
                userId, userAchievement.getAchievementId(), progress);

        } catch (Exception e) {
            log.error("发送成就进度通知失败: userId={}, achievementId={}", 
                userId, userAchievement.getAchievementId(), e);
        }
    }

    @Override
    public void sendAchievementReminderNotification(Long userId, AchievementVo achievement, String message) {
        try {
            // 构建通知数据
            Map<String, Object> notificationData = new HashMap<>();
            notificationData.put("id", IdUtil.fastSimpleUUID());
            notificationData.put("type", "ACHIEVEMENT_REMINDER");
            notificationData.put("userId", userId);
            notificationData.put("achievementId", achievement.getId());
            notificationData.put("achievementName", achievement.getAchievementName());
            notificationData.put("title", "💡 成就提醒");
            notificationData.put("message", message);
            notificationData.put("createTime", new Date());

            // 发送到通知队列
            sendNotificationToQueue(notificationData);

            log.debug("成就提醒通知已发送: userId={}, achievementId={}", userId, achievement.getId());

        } catch (Exception e) {
            log.error("发送成就提醒通知失败: userId={}, achievementId={}", userId, achievement.getId(), e);
        }
    }

    @Override
    public void sendLeaderboardNotification(Long userId, Integer ranking, String category) {
        try {
            // 构建通知数据
            Map<String, Object> notificationData = new HashMap<>();
            notificationData.put("id", IdUtil.fastSimpleUUID());
            notificationData.put("type", "LEADERBOARD_UPDATE");
            notificationData.put("userId", userId);
            notificationData.put("ranking", ranking);
            notificationData.put("category", category);
            notificationData.put("title", "🏆 排行榜更新");
            notificationData.put("message", String.format("您在%s排行榜中的排名已更新至第 %d 名！", 
                getCategoryDisplayName(category), ranking));
            notificationData.put("createTime", new Date());

            // 发送到通知队列
            sendNotificationToQueue(notificationData);

            log.debug("排行榜通知已发送: userId={}, ranking={}, category={}", userId, ranking, category);

        } catch (Exception e) {
            log.error("发送排行榜通知失败: userId={}, ranking={}, category={}", userId, ranking, category, e);
        }
    }

    @Override
    public Boolean markNotificationAsRead(Long userId, String notificationId) {
        // TODO: 实现标记通知为已读的逻辑
        // 这里需要与通知存储系统集成（如数据库、Redis等）
        log.debug("标记通知为已读: userId={}, notificationId={}", userId, notificationId);
        return true;
    }

    @Override
    public Long getUnreadNotificationCount(Long userId) {
        // TODO: 实现获取未读通知数量的逻辑
        // 这里需要与通知存储系统集成
        log.debug("获取未读通知数量: userId={}", userId);
        return 0L;
    }

    @Override
    public List<NotificationVo> getUserNotifications(Long userId, Integer limit) {
        // TODO: 实现获取用户通知列表的逻辑
        // 这里需要与通知存储系统集成
        log.debug("获取用户通知列表: userId={}, limit={}", userId, limit);
        return List.of();
    }

    @Override
    public Integer cleanExpiredNotifications(Integer days) {
        // TODO: 实现清理过期通知的逻辑
        log.debug("清理过期通知: days={}", days);
        return 0;
    }

    /**
     * 发送通知到消息队列
     */
    private void sendNotificationToQueue(Map<String, Object> notificationData) {
        if (rabbitMqTemplate.isPresent()) {
            try {
                rabbitMqTemplate.get().send(
                    AchievementRabbitMqConfig.ACHIEVEMENT_EXCHANGE,
                    AchievementRabbitMqConfig.ACHIEVEMENT_NOTIFICATION_ROUTING_KEY,
                    JSONUtil.toJsonStr(notificationData)
                );
            } catch (Exception e) {
                log.error("发送通知到消息队列失败: {}", notificationData, e);
                // 如果MQ发送失败，可以考虑直接处理通知
                processNotificationDirectly(notificationData);
            }
        } else {
            // 如果没有配置MQ，直接处理通知
            processNotificationDirectly(notificationData);
        }
    }

    /**
     * 直接处理通知（不通过MQ）
     */
    private void processNotificationDirectly(Map<String, Object> notificationData) {
        // 这里可以实现直接的通知处理逻辑
        // 例如：WebSocket推送、邮件发送、短信发送等
        log.info("直接处理通知: {}", notificationData);
    }

    /**
     * 判断是否应该发送进度通知
     */
    private boolean shouldSendProgressNotification(double progress) {
        // 在特定进度节点发送通知
        return progress == 25.0 || progress == 50.0 || progress == 75.0;
    }

    /**
     * 获取类别显示名称
     */
    private String getCategoryDisplayName(String category) {
        switch (category) {
            case "LOGIN": return "登录";
            case "LEARNING": return "学习";
            case "SOCIAL": return "社交";
            case "TIME": return "时长";
            default: return "综合";
        }
    }

}
