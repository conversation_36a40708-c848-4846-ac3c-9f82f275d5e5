{"doc": "\n 支付状态枚举\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "code", "doc": "\n 状态码\r\n"}, {"name": "description", "doc": "\n 状态描述\r\n"}], "enumConstants": [{"name": "UNPAID", "doc": "\n 未支付\r\n"}, {"name": "PENDING", "doc": "\n 待支付\r\n"}, {"name": "PAYING", "doc": "\n 支付中\r\n"}, {"name": "PAID", "doc": "\n 支付成功\r\n"}, {"name": "FAILED", "doc": "\n 支付失败\r\n"}, {"name": "CANCELLED", "doc": "\n 已取消\r\n"}, {"name": "EXPIRED", "doc": "\n 已过期\r\n"}, {"name": "REFUNDED", "doc": "\n 已退款\r\n"}, {"name": "PARTIAL_REFUNDED", "doc": "\n 部分退款\r\n"}], "methods": [{"name": "fromCode", "paramTypes": ["java.lang.String"], "doc": "\n 根据状态码获取枚举\r\n\r\n @param code 状态码\r\n @return PaymentStatus\r\n"}, {"name": "isFinalStatus", "paramTypes": [], "doc": "\n 判断是否为终态\r\n\r\n @return true-终态 false-非终态\r\n"}, {"name": "isSuccess", "paramTypes": [], "doc": "\n 判断是否为成功状态\r\n\r\n @return true-成功 false-非成功\r\n"}], "constructors": []}