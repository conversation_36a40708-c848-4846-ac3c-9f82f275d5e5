package org.dromara.app.event;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * 成就事件发布器
 * 提供便捷的方法来发布各种成就相关事件
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AchievementEventPublisher {

    private final ApplicationEventPublisher eventPublisher;

    /**
     * 发布用户注册事件
     *
     * @param userId 用户ID
     * @param source 注册来源
     */
    public void publishUserRegistrationEvent(String userId, String source) {
        log.info("发布用户注册事件: userId={}, source={}", userId, source);
        AchievementEventListener.UserRegistrationEvent event =
            new AchievementEventListener.UserRegistrationEvent(userId, source);
        eventPublisher.publishEvent(event);
    }

    /**
     * 发布学习完成事件
     *
     * @param userId       用户ID
     * @param resourceId   资源ID
     * @param resourceType 资源类型
     * @param duration     学习时长（分钟）
     * @param score        得分
     */
    public void publishLearningCompletedEvent(String userId, String resourceId, String resourceType,
                                            int duration, int score) {
        log.info("发布学习完成事件: userId={}, resourceId={}, type={}, score={}",
                userId, resourceId, resourceType, score);
        AchievementEventListener.LearningCompletedEvent event =
            new AchievementEventListener.LearningCompletedEvent(userId, resourceId, resourceType, duration, score);
        eventPublisher.publishEvent(event);
    }

    /**
     * 发布面试完成事件
     *
     * @param userId      用户ID
     * @param interviewId 面试ID
     * @param jobId       职位ID
     * @param score       面试得分
     * @param duration    面试时长（分钟）
     * @param mode        面试模式
     */
    public void publishInterviewCompletedEvent(String userId, Long interviewId, Long jobId,
                                             int score, int duration, String mode) {
        log.info("发布面试完成事件: userId={}, interviewId={}, score={}", userId, interviewId, score);
        AchievementEventListener.InterviewCompletedEvent event =
            new AchievementEventListener.InterviewCompletedEvent(userId, interviewId, jobId, score, duration, mode);
        eventPublisher.publishEvent(event);
    }

    /**
     * 发布能力提升事件
     *
     * @param userId      用户ID
     * @param abilityType 能力类型
     * @param oldScore    原分数
     * @param newScore    新分数
     */
    public void publishAbilityImproveEvent(String userId, String abilityType, int oldScore, int newScore) {
        log.info("发布能力提升事件: userId={}, abilityType={}, improvement={}",
                userId, abilityType, newScore - oldScore);
        AchievementEventListener.AbilityImproveEvent event =
            new AchievementEventListener.AbilityImproveEvent(userId, abilityType, oldScore, newScore);
        eventPublisher.publishEvent(event);
    }

    /**
     * 发布视频观看完成事件
     *
     * @param userId   用户ID
     * @param videoId  视频ID
     * @param duration 观看时长（秒）
     * @param progress 观看进度（百分比）
     */
    public void publishVideoWatchedEvent(String userId, String videoId, int duration, int progress) {
        log.info("发布视频观看事件: userId={}, videoId={}, progress={}%", userId, videoId, progress);
        // 可以创建新的事件类型或复用学习完成事件
        publishLearningCompletedEvent(userId, videoId, "video", duration / 60, progress);
    }

    /**
     * 发布文章阅读完成事件
     *
     * @param userId    用户ID
     * @param articleId 文章ID
     * @param readTime  阅读时长（分钟）
     */
    public void publishArticleReadEvent(String userId, String articleId, int readTime) {
        log.info("发布文章阅读事件: userId={}, articleId={}, readTime={}min", userId, articleId, readTime);
        publishLearningCompletedEvent(userId, articleId, "article", readTime, 100);
    }

    /**
     * 发布题库练习完成事件
     *
     * @param userId       用户ID
     * @param questionBankId 题库ID
     * @param correctCount 正确题数
     * @param totalCount   总题数
     * @param duration     练习时长（分钟）
     */
    public void publishQuestionBankPracticeEvent(String userId, String questionBankId,
                                                int correctCount, int totalCount, int duration) {
        int score = totalCount > 0 ? (correctCount * 100 / totalCount) : 0;
        log.info("发布题库练习事件: userId={}, questionBankId={}, score={}%", userId, questionBankId, score);
        publishLearningCompletedEvent(userId, questionBankId, "question_bank", duration, score);
    }

    /**
     * 发布简历完善事件
     *
     * @param userId       用户ID
     * @param resumeId     简历ID
     * @param completeness 完整度（百分比）
     */
    public void publishResumeImproveEvent(String userId, String resumeId, int completeness) {
        log.info("发布简历完善事件: userId={}, resumeId={}, completeness={}%", userId, resumeId, completeness);
        // 可以作为特殊的学习事件处理
        publishLearningCompletedEvent(userId, resumeId, "resume", 0, completeness);
    }

    /**
     * 发布连续登录事件
     *
     * @param userId      用户ID
     * @param consecutiveDays 连续登录天数
     */
    public void publishConsecutiveLoginEvent(String userId, int consecutiveDays) {
        log.info("发布连续登录事件: userId={}, consecutiveDays={}", userId, consecutiveDays);
        // 可以作为特殊的学习事件处理，或创建新的事件类型
        publishLearningCompletedEvent(userId, "login", "daily_login", 0, consecutiveDays);
    }

    /**
     * 发布分享事件
     *
     * @param userId   用户ID
     * @param shareType 分享类型
     * @param targetId  分享目标ID
     * @param platform  分享平台
     */
    public void publishShareEvent(String userId, String shareType, String targetId, String platform) {
        log.info("发布分享事件: userId={}, shareType={}, platform={}", userId, shareType, platform);
        publishLearningCompletedEvent(userId, targetId, "share_" + shareType, 0, 1);
    }

    /**
     * 发布评论事件
     *
     * @param userId    用户ID
     * @param targetType 评论目标类型
     * @param targetId   评论目标ID
     * @param commentLength 评论长度
     */
    public void publishCommentEvent(String userId, String targetType, String targetId, int commentLength) {
        log.info("发布评论事件: userId={}, targetType={}, targetId={}", userId, targetType, targetId);
        publishLearningCompletedEvent(userId, targetId, "comment_" + targetType, 0, Math.min(commentLength / 10, 100));
    }

    /**
     * 发布收藏事件
     *
     * @param userId     用户ID
     * @param targetType 收藏目标类型
     * @param targetId   收藏目标ID
     */
    public void publishFavoriteEvent(String userId, String targetType, String targetId) {
        log.info("发布收藏事件: userId={}, targetType={}, targetId={}", userId, targetType, targetId);
        publishLearningCompletedEvent(userId, targetId, "favorite_" + targetType, 0, 1);
    }

    /**
     * 发布点赞事件
     *
     * @param userId     用户ID
     * @param targetType 点赞目标类型
     * @param targetId   点赞目标ID
     */
    public void publishLikeEvent(String userId, String targetType, String targetId) {
        log.info("发布点赞事件: userId={}, targetType={}, targetId={}", userId, targetType, targetId);
        publishLearningCompletedEvent(userId, targetId, "like_" + targetType, 0, 1);
    }

    /**
     * 发布关注事件
     *
     * @param userId     用户ID
     * @param targetType 关注目标类型
     * @param targetId   关注目标ID
     */
    public void publishFollowEvent(String userId, String targetType, String targetId) {
        log.info("发布关注事件: userId={}, targetType={}, targetId={}", userId, targetType, targetId);
        publishLearningCompletedEvent(userId, targetId, "follow_" + targetType, 0, 1);
    }

    /**
     * 发布支付完成事件
     *
     * @param userId  用户ID
     * @param orderId 订单ID
     * @param amount  支付金额（分）
     * @param productType 产品类型
     */
    public void publishPaymentCompletedEvent(String userId, String orderId, Long amount, String productType) {
        log.info("发布支付完成事件: userId={}, orderId={}, amount={}", userId, orderId, amount);
        publishLearningCompletedEvent(userId, orderId, "payment_" + productType, 0, (int)(amount / 100));
    }

    /**
     * 发布邀请好友事件
     *
     * @param userId     邀请人用户ID
     * @param inviteeId  被邀请人用户ID
     * @param inviteType 邀请类型
     */
    public void publishInviteFriendEvent(String userId, String inviteeId, String inviteType) {
        log.info("发布邀请好友事件: userId={}, inviteeId={}, inviteType={}", userId, inviteeId, inviteType);
        publishLearningCompletedEvent(userId, inviteeId, "invite_" + inviteType, 0, 1);
    }

    /**
     * 发布完善个人信息事件
     *
     * @param userId       用户ID
     * @param profileType  信息类型
     * @param completeness 完整度（百分比）
     */
    public void publishProfileUpdateEvent(String userId, String profileType, int completeness) {
        log.info("发布完善个人信息事件: userId={}, profileType={}, completeness={}%", userId, profileType, completeness);
        publishLearningCompletedEvent(userId, profileType, "profile_update", 0, completeness);
    }

    // ==================== 简化版事件发布方法 ====================

    /**
     * 发布简单的用户登录事件
     *
     * @param userId 用户ID
     * @param source 登录来源
     */
    public void publishSimpleUserLoginEvent(String userId, String source) {
        try {
            SimpleAchievementEventListener.UserLoginEvent event =
                new SimpleAchievementEventListener.UserLoginEvent(userId, source);
            eventPublisher.publishEvent(event);
            log.debug("发布简单用户登录事件: userId={}, source={}", userId, source);
        } catch (Exception e) {
            log.error("发布简单用户登录事件失败: userId={}, source={}", userId, source, e);
        }
    }

    /**
     * 发布简单的视频观看事件
     *
     * @param userId        用户ID
     * @param videoId       视频ID
     * @param videoTitle    视频标题
     * @param watchDuration 观看时长（秒）
     */
    public void publishSimpleVideoWatchEvent(String userId, String videoId, String videoTitle, Integer watchDuration) {
        try {
            SimpleAchievementEventListener.VideoWatchEvent event =
                new SimpleAchievementEventListener.VideoWatchEvent(userId, videoId, videoTitle, watchDuration);
            eventPublisher.publishEvent(event);
            log.debug("发布简单视频观看事件: userId={}, videoId={}, duration={}", userId, videoId, watchDuration);
        } catch (Exception e) {
            log.error("发布简单视频观看事件失败: userId={}, videoId={}", userId, videoId, e);
        }
    }

    /**
     * 发布简单的评论事件
     *
     * @param userId         用户ID
     * @param targetType     目标类型（video, article等）
     * @param targetId       目标ID
     * @param commentContent 评论内容
     */
    public void publishSimpleCommentEvent(String userId, String targetType, String targetId, String commentContent) {
        try {
            SimpleAchievementEventListener.CommentEvent event =
                new SimpleAchievementEventListener.CommentEvent(userId, targetType, targetId, commentContent);
            eventPublisher.publishEvent(event);
            log.debug("发布简单评论事件: userId={}, targetType={}, targetId={}", userId, targetType, targetId);
        } catch (Exception e) {
            log.error("发布简单评论事件失败: userId={}, targetType={}, targetId={}", userId, targetType, targetId, e);
        }
    }

    /**
     * 发布简单的点赞事件
     *
     * @param userId     用户ID
     * @param targetType 目标类型（video, article, comment等）
     * @param targetId   目标ID
     */
    public void publishSimpleLikeEvent(String userId, String targetType, String targetId) {
        try {
            SimpleAchievementEventListener.LikeEvent event =
                new SimpleAchievementEventListener.LikeEvent(userId, targetType, targetId);
            eventPublisher.publishEvent(event);
            log.debug("发布简单点赞事件: userId={}, targetType={}, targetId={}", userId, targetType, targetId);
        } catch (Exception e) {
            log.error("发布简单点赞事件失败: userId={}, targetType={}, targetId={}", userId, targetType, targetId, e);
        }
    }

    /**
     * 发布简单的学习时长事件
     *
     * @param userId       用户ID
     * @param studyMinutes 学习时长（分钟）
     */
    public void publishSimpleStudyTimeEvent(String userId, Integer studyMinutes) {
        try {
            SimpleAchievementEventListener.StudyTimeEvent event =
                new SimpleAchievementEventListener.StudyTimeEvent(userId, studyMinutes);
            eventPublisher.publishEvent(event);
            log.debug("发布简单学习时长事件: userId={}, studyMinutes={}", userId, studyMinutes);
        } catch (Exception e) {
            log.error("发布简单学习时长事件失败: userId={}, studyMinutes={}", userId, studyMinutes, e);
        }
    }

}
