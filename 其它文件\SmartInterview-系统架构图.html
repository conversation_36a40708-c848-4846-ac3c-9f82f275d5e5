<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能面试系统 - 系统架构图</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #f0f9f5 0%, #e8f5e8 100%);
            padding: 20px;
            min-height: 100vh;
        }

        .architecture-container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 200, 150, 0.1);
            padding: 30px;
        }

        .title {
            text-align: center;
            font-size: 28px;
            font-weight: bold;
            color: #00C896;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0, 200, 150, 0.1);
        }

        /* 顶部角色区域 */
        .roles-section {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 15px;
            margin-bottom: 30px;
        }

        .role-card {
            background: linear-gradient(135deg, #00C896 0%, #20B2AA 100%);
            color: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            font-weight: bold;
            box-shadow: 0 4px 15px rgba(0, 200, 150, 0.2);
            transition: transform 0.3s ease;
        }

        .role-card:hover {
            transform: translateY(-3px);
        }

        .role-icon {
            font-size: 24px;
            margin-bottom: 8px;
            display: block;
        }

        /* 核心服务区域 */
        .core-services {
            display: grid;
            grid-template-columns: 1fr 2fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        /* AI智能体区域 - 突出显示 */
        .ai-agents-section {
            background: linear-gradient(135deg, #00C896 0%, #3CB371 50%, #20B2AA 100%);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0, 200, 150, 0.3);
            border: 3px solid #00FF7F;
        }

        .ai-agents-title {
            color: white;
            font-size: 18px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
        }

        .ai-agents-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
        }

        .ai-agent-card {
            background: rgba(255, 255, 255, 0.95);
            color: #00C896;
            padding: 12px;
            border-radius: 8px;
            text-align: center;
            font-weight: bold;
            font-size: 12px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .ai-agent-card:hover {
            background: white;
            transform: scale(1.05);
        }

        .ai-agent-icon {
            font-size: 16px;
            margin-bottom: 5px;
            display: block;
        }

        /* 业务系统区域 */
        .business-systems {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }

        .system-group {
            background: #f8fffe;
            border: 2px solid #00C896;
            border-radius: 10px;
            padding: 15px;
        }

        .system-title {
            color: #00C896;
            font-weight: bold;
            text-align: center;
            margin-bottom: 10px;
            font-size: 14px;
        }

        .system-items {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 8px;
        }

        .system-item {
            background: #e8f5e8;
            color: #2d5a3d;
            padding: 8px;
            border-radius: 6px;
            text-align: center;
            font-size: 11px;
            font-weight: bold;
            border: 1px solid #b8e6b8;
        }

        /* API服务区域 */
        .api-services {
            background: #f0f9f5;
            border: 2px solid #20B2AA;
            border-radius: 10px;
            padding: 15px;
        }

        .api-title {
            color: #20B2AA;
            font-weight: bold;
            text-align: center;
            margin-bottom: 10px;
        }

        .api-items {
            display: grid;
            gap: 8px;
        }

        .api-item {
            background: #20B2AA;
            color: white;
            padding: 8px;
            border-radius: 6px;
            text-align: center;
            font-size: 11px;
            font-weight: bold;
        }

        /* 基础设施区域 */
        .infrastructure {
            background: #f5f5f5;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .infra-title {
            color: #666;
            font-weight: bold;
            text-align: center;
            margin-bottom: 15px;
        }

        .infra-grid {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 15px;
        }

        .infra-item {
            background: white;
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .infra-item:hover {
            border-color: #00C896;
            transform: translateY(-2px);
        }

        .infra-icon {
            font-size: 20px;
            margin-bottom: 8px;
            display: block;
        }

        .infra-name {
            font-weight: bold;
            font-size: 12px;
            color: #333;
        }

        /* 右侧服务区域 */
        .side-services {
            background: #f8fffe;
            border: 2px solid #3CB371;
            border-radius: 10px;
            padding: 15px;
        }

        .side-title {
            color: #3CB371;
            font-weight: bold;
            text-align: center;
            margin-bottom: 15px;
        }

        .side-items {
            display: grid;
            gap: 10px;
        }

        .side-item {
            background: linear-gradient(135deg, #3CB371 0%, #20B2AA 100%);
            color: white;
            padding: 12px;
            border-radius: 8px;
            text-align: center;
            font-weight: bold;
            font-size: 12px;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .roles-section {
                grid-template-columns: repeat(3, 1fr);
            }
            
            .core-services {
                grid-template-columns: 1fr;
            }
            
            .infra-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        @media (max-width: 768px) {
            .roles-section {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .business-systems {
                grid-template-columns: 1fr;
            }
            
            .infra-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="architecture-container">
        <h1 class="title">智能面试系统 - 系统架构图</h1>
        
        <!-- 顶部角色区域 -->
        <div class="roles-section">
            <div class="role-card">
                <span class="role-icon">👨‍💼</span>
                求职者
            </div>
            <div class="role-card">
                <span class="role-icon">🏢</span>
                企业HR
            </div>
            <div class="role-card">
                <span class="role-icon">👨‍🏫</span>
                面试官
            </div>
            <div class="role-card">
                <span class="role-icon">📚</span>
                学习者
            </div>
            <div class="role-card">
                <span class="role-icon">⚙️</span>
                系统管理员
            </div>
            <div class="role-card">
                <span class="role-icon">🔧</span>
                运维人员
            </div>
        </div>

        <!-- 核心服务区域 -->
        <div class="core-services">
            <!-- API服务 -->
            <div class="api-services">
                <div class="api-title">🔌 API服务</div>
                <div class="api-items">
                    <div class="api-item">认证API</div>
                    <div class="api-item">面试API</div>
                    <div class="api-item">学习API</div>
                    <div class="api-item">评估API</div>
                    <div class="api-item">用户API</div>
                    <div class="api-item">数据API</div>
                </div>
            </div>

            <!-- AI智能体区域 - 核心亮点 -->
            <div class="ai-agents-section">
                <div class="ai-agents-title">🧠 7大AI智能体 - 核心引擎</div>
                <div class="ai-agents-grid">
                    <div class="ai-agent-card">
                        <span class="ai-agent-icon">🎯</span>
                        视觉Agent
                    </div>
                    <div class="ai-agent-card">
                        <span class="ai-agent-icon">📄</span>
                       语音Agent 
                    </div>
                    <div class="ai-agent-card">
                        <span class="ai-agent-icon">🔍</span>
                     文本Agent
                    </div>
                    <div class="ai-agent-card">
                        <span class="ai-agent-icon">💼</span>
                       决策Agent 
                    </div>
                    <div class="ai-agent-card">
                        <span class="ai-agent-icon">🎭</span>
                       公平性Agent
                    </div>
                    <div class="ai-agent-card">
                        <span class="ai-agent-icon">📊</span>
                        报告Agent 
                    </div>
                    <div class="ai-agent-card">
                        <span class="ai-agent-icon">🎓</span>
                      调度Agent
                    </div>
                </div>
            </div>

            <!-- 第三方服务 -->
            <div class="side-services">
                <div class="side-title">🌐 第三方服务</div>
                <div class="side-items">
                    <div class="side-item">科大讯飞AI</div>
                    <div class="side-item">语音识别</div>
                    <div class="side-item">文字转语音</div>
                    <div class="side-item">监控服务</div>
                    <div class="side-item">日志分析</div>
                    <div class="side-item">性能监控</div>
                </div>
            </div>
        </div>

        <!-- 业务系统区域 -->
        <div class="business-systems">
            <div class="system-group">
                <div class="system-title">💬 面试系统</div>
                <div class="system-items">
                    <div class="system-item">面试管理</div>
                    <div class="system-item">题库管理</div>
                    <div class="system-item">评分系统</div>
                    <div class="system-item">结果分析</div>
                </div>
            </div>
            
            <div class="system-group">
                <div class="system-title">📖 学习系统</div>
                <div class="system-items">
                    <div class="system-item">课程管理</div>
                    <div class="system-item">练习系统</div>
                    <div class="system-item">进度跟踪</div>
                    <div class="system-item">资源库</div>
                </div>
            </div>
            
            <div class="system-group">
                <div class="system-title">👤 用户系统</div>
                <div class="system-items">
                    <div class="system-item">用户管理</div>
                    <div class="system-item">权限控制</div>
                    <div class="system-item">个人中心</div>
                    <div class="system-item">简历管理</div>
                </div>
            </div>
            
            <div class="system-group">
                <div class="system-title">📈 评估系统</div>
                <div class="system-items">
                    <div class="system-item">能力测试</div>
                    <div class="system-item">技能评估</div>
                    <div class="system-item">报告生成</div>
                    <div class="system-item">数据分析</div>
                </div>
            </div>
        </div>

        <!-- 基础设施区域 -->
        <div class="infrastructure">
            <div class="infra-title">🏗️ 基础设施</div>
            <div class="infra-grid">
                <div class="infra-item">
                    <span class="infra-icon">🌐</span>
                    <div class="infra-name">Nginx</div>
                </div>
                <div class="infra-item">
                    <span class="infra-icon">🍃</span>
                    <div class="infra-name">Spring Boot</div>
                </div>
                <div class="infra-item">
                    <span class="infra-icon">🗄️</span>
                    <div class="infra-name">MySQL</div>
                </div>
                <div class="infra-item">
                    <span class="infra-icon">⚡</span>
                    <div class="infra-name">Redis</div>
                </div>
                <div class="infra-item">
                    <span class="infra-icon">📱</span>
                    <div class="infra-name">UniApp</div>
                </div>
                <div class="infra-item">
                    <span class="infra-icon">🔄</span>
                    <div class="infra-name">消息队列</div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
