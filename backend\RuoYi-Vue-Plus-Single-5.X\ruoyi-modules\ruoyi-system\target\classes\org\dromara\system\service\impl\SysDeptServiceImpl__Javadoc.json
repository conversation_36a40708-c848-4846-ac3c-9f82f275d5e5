{"doc": "\n 部门管理 服务实现\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectPageDeptList", "paramTypes": ["org.dromara.system.domain.bo.SysDeptBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 分页查询部门管理数据\r\n\r\n @param dept      部门信息\r\n @param pageQuery 分页对象\r\n @return 部门信息集合\r\n"}, {"name": "selectDeptList", "paramTypes": ["org.dromara.system.domain.bo.SysDeptBo"], "doc": "\n 查询部门管理数据\r\n\r\n @param dept 部门信息\r\n @return 部门信息集合\r\n"}, {"name": "selectDeptTreeList", "paramTypes": ["org.dromara.system.domain.bo.SysDeptBo"], "doc": "\n 查询部门树结构信息\r\n\r\n @param bo 部门信息\r\n @return 部门树信息集合\r\n"}, {"name": "buildDeptTreeSelect", "paramTypes": ["java.util.List"], "doc": "\n 构建前端所需要下拉树结构\r\n\r\n @param depts 部门列表\r\n @return 下拉树结构列表\r\n"}, {"name": "selectDeptListByRoleId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据角色ID查询部门树信息\r\n\r\n @param roleId 角色ID\r\n @return 选中部门列表\r\n"}, {"name": "selectDeptById", "paramTypes": ["java.lang.Long"], "doc": "\n 根据部门ID查询信息\r\n\r\n @param deptId 部门ID\r\n @return 部门信息\r\n"}, {"name": "selectDeptNameByIds", "paramTypes": ["java.lang.String"], "doc": "\n 通过部门ID查询部门名称\r\n\r\n @param deptIds 部门ID串逗号分隔\r\n @return 部门名称串逗号分隔\r\n"}, {"name": "selectDeptLeaderById", "paramTypes": ["java.lang.Long"], "doc": "\n 根据部门ID查询部门负责人\r\n\r\n @param deptId 部门ID，用于指定需要查询的部门\r\n @return 返回该部门的负责人ID\r\n"}, {"name": "selectDeptsByList", "paramTypes": [], "doc": "\n 查询部门\r\n\r\n @return 部门列表\r\n"}, {"name": "selectNormalChildrenDeptById", "paramTypes": ["java.lang.Long"], "doc": "\n 根据ID查询所有子部门数（正常状态）\r\n\r\n @param deptId 部门ID\r\n @return 子部门数\r\n"}, {"name": "hasChildByDeptId", "paramTypes": ["java.lang.Long"], "doc": "\n 是否存在子节点\r\n\r\n @param deptId 部门ID\r\n @return 结果\r\n"}, {"name": "checkDeptExistUser", "paramTypes": ["java.lang.Long"], "doc": "\n 查询部门是否存在用户\r\n\r\n @param deptId 部门ID\r\n @return 结果 true 存在 false 不存在\r\n"}, {"name": "checkDeptNameUnique", "paramTypes": ["org.dromara.system.domain.bo.SysDeptBo"], "doc": "\n 校验部门名称是否唯一\r\n\r\n @param dept 部门信息\r\n @return 结果\r\n"}, {"name": "checkDeptDataScope", "paramTypes": ["java.lang.Long"], "doc": "\n 校验部门是否有数据权限\r\n\r\n @param deptId 部门id\r\n"}, {"name": "insertDept", "paramTypes": ["org.dromara.system.domain.bo.SysDeptBo"], "doc": "\n 新增保存部门信息\r\n\r\n @param bo 部门信息\r\n @return 结果\r\n"}, {"name": "updateDept", "paramTypes": ["org.dromara.system.domain.bo.SysDeptBo"], "doc": "\n 修改保存部门信息\r\n\r\n @param bo 部门信息\r\n @return 结果\r\n"}, {"name": "updateParentDeptStatusNormal", "paramTypes": ["org.dromara.system.domain.SysDept"], "doc": "\n 修改该部门的父级部门状态\r\n\r\n @param dept 当前部门\r\n"}, {"name": "updateDept<PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.Long", "java.lang.String", "java.lang.String"], "doc": "\n 修改子元素关系\r\n\r\n @param deptId       被修改的部门ID\r\n @param newAncestors 新的父ID集合\r\n @param oldAncestors 旧的父ID集合\r\n"}, {"name": "deleteDeptById", "paramTypes": ["java.lang.Long"], "doc": "\n 删除部门管理信息\r\n\r\n @param deptId 部门ID\r\n @return 结果\r\n"}], "constructors": []}