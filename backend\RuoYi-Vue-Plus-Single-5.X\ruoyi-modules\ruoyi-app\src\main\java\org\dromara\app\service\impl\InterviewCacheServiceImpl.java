package org.dromara.app.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.InterviewSession;
import org.dromara.app.domain.SessionQuestion;
import org.dromara.app.domain.vo.InterviewResponseVo;
import org.dromara.app.service.IInterviewCacheService;
import org.dromara.common.redis.utils.RedisUtils;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 面试缓存服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class InterviewCacheServiceImpl implements IInterviewCacheService {

    private static final String CACHE_PREFIX = "interview:";
    private static final String SESSION_PREFIX = CACHE_PREFIX + "session:";
    private static final String QUESTIONS_PREFIX = CACHE_PREFIX + "questions:";
    private static final String USER_ACTIVE_PREFIX = CACHE_PREFIX + "user_active:";
    private static final String RESULT_PREFIX = CACHE_PREFIX + "result:";
    private static final String AI_EVAL_PREFIX = CACHE_PREFIX + "ai_eval:";
    private static final String POPULAR_QUESTIONS_PREFIX = CACHE_PREFIX + "popular_questions:";
    private static final String USER_STATS_PREFIX = CACHE_PREFIX + "user_stats:";

    // 缓存过期时间
    private static final Duration SESSION_EXPIRE = Duration.ofHours(2);
    private static final Duration QUESTIONS_EXPIRE = Duration.ofHours(1);
    private static final Duration USER_ACTIVE_EXPIRE = Duration.ofHours(4);
    private static final Duration RESULT_EXPIRE = Duration.ofDays(7);
    private static final Duration AI_EVAL_EXPIRE = Duration.ofHours(24);
    private static final Duration POPULAR_QUESTIONS_EXPIRE = Duration.ofHours(6);
    private static final Duration USER_STATS_EXPIRE = Duration.ofMinutes(30);

    // 内存缓存作为备用（当Redis不可用时）
    private final Map<String, Object> memoryCache = new ConcurrentHashMap<>();

    @Override
    public void cacheSession(InterviewSession session) {
        if (session == null || session.getId() == null) {
            return;
        }
        
        try {
            String key = SESSION_PREFIX + session.getId();
            RedisUtils.setCacheObject(key, session, SESSION_EXPIRE);
            log.debug("缓存会话信息：{}", session.getId());
        } catch (Exception e) {
            log.warn("缓存会话信息失败，使用内存缓存：{}", e.getMessage());
            memoryCache.put(SESSION_PREFIX + session.getId(), session);
        }
    }

    @Override
    public Optional<InterviewSession> getCachedSession(String sessionId) {
        if (sessionId == null) {
            return Optional.empty();
        }
        
        try {
            String key = SESSION_PREFIX + sessionId;
            InterviewSession session = RedisUtils.getCacheObject(key);
            if (session != null) {
                log.debug("从缓存获取会话信息：{}", sessionId);
                return Optional.of(session);
            }
        } catch (Exception e) {
            log.warn("从Redis获取会话信息失败，尝试内存缓存：{}", e.getMessage());
            Object cached = memoryCache.get(SESSION_PREFIX + sessionId);
            if (cached instanceof InterviewSession) {
                return Optional.of((InterviewSession) cached);
            }
        }
        
        return Optional.empty();
    }

    @Override
    public void evictSession(String sessionId) {
        if (sessionId == null) {
            return;
        }
        
        try {
            String key = SESSION_PREFIX + sessionId;
            RedisUtils.deleteObject(key);
            memoryCache.remove(key);
            log.debug("删除会话缓存：{}", sessionId);
        } catch (Exception e) {
            log.warn("删除会话缓存失败：{}", e.getMessage());
        }
    }

    @Override
    public void cacheSessionQuestions(String sessionId, List<SessionQuestion> questions) {
        if (sessionId == null || questions == null) {
            return;
        }
        
        try {
            String key = QUESTIONS_PREFIX + sessionId;
            RedisUtils.setCacheObject(key, questions, QUESTIONS_EXPIRE);
            log.debug("缓存会话问题列表：{}, 问题数量：{}", sessionId, questions.size());
        } catch (Exception e) {
            log.warn("缓存会话问题失败，使用内存缓存：{}", e.getMessage());
            memoryCache.put(QUESTIONS_PREFIX + sessionId, questions);
        }
    }

    @Override
    @SuppressWarnings("unchecked")
    public Optional<List<SessionQuestion>> getCachedSessionQuestions(String sessionId) {
        if (sessionId == null) {
            return Optional.empty();
        }
        
        try {
            String key = QUESTIONS_PREFIX + sessionId;
            List<SessionQuestion> questions = RedisUtils.getCacheObject(key);
            if (questions != null) {
                log.debug("从缓存获取会话问题列表：{}, 问题数量：{}", sessionId, questions.size());
                return Optional.of(questions);
            }
        } catch (Exception e) {
            log.warn("从Redis获取会话问题失败，尝试内存缓存：{}", e.getMessage());
            Object cached = memoryCache.get(QUESTIONS_PREFIX + sessionId);
            if (cached instanceof List) {
                return Optional.of((List<SessionQuestion>) cached);
            }
        }
        
        return Optional.empty();
    }

    @Override
    public void evictSessionQuestions(String sessionId) {
        if (sessionId == null) {
            return;
        }
        
        try {
            String key = QUESTIONS_PREFIX + sessionId;
            RedisUtils.deleteObject(key);
            memoryCache.remove(key);
            log.debug("删除会话问题缓存：{}", sessionId);
        } catch (Exception e) {
            log.warn("删除会话问题缓存失败：{}", e.getMessage());
        }
    }

    @Override
    public void cacheUserActiveSession(Long userId, String sessionId) {
        if (userId == null || sessionId == null) {
            return;
        }
        
        try {
            String key = USER_ACTIVE_PREFIX + userId;
            RedisUtils.setCacheObject(key, sessionId, USER_ACTIVE_EXPIRE);
            log.debug("缓存用户活跃会话：userId={}, sessionId={}", userId, sessionId);
        } catch (Exception e) {
            log.warn("缓存用户活跃会话失败，使用内存缓存：{}", e.getMessage());
            memoryCache.put(USER_ACTIVE_PREFIX + userId, sessionId);
        }
    }

    @Override
    public Optional<String> getUserActiveSession(Long userId) {
        if (userId == null) {
            return Optional.empty();
        }
        
        try {
            String key = USER_ACTIVE_PREFIX + userId;
            String sessionId = RedisUtils.getCacheObject(key);
            if (sessionId != null) {
                log.debug("从缓存获取用户活跃会话：userId={}, sessionId={}", userId, sessionId);
                return Optional.of(sessionId);
            }
        } catch (Exception e) {
            log.warn("从Redis获取用户活跃会话失败，尝试内存缓存：{}", e.getMessage());
            Object cached = memoryCache.get(USER_ACTIVE_PREFIX + userId);
            if (cached instanceof String) {
                return Optional.of((String) cached);
            }
        }
        
        return Optional.empty();
    }

    @Override
    public void evictUserActiveSession(Long userId) {
        if (userId == null) {
            return;
        }
        
        try {
            String key = USER_ACTIVE_PREFIX + userId;
            RedisUtils.deleteObject(key);
            memoryCache.remove(key);
            log.debug("删除用户活跃会话缓存：{}", userId);
        } catch (Exception e) {
            log.warn("删除用户活跃会话缓存失败：{}", e.getMessage());
        }
    }

    @Override
    public void cacheInterviewResult(String sessionId, InterviewResponseVo.InterviewResult result) {
        if (sessionId == null || result == null) {
            return;
        }
        
        try {
            String key = RESULT_PREFIX + sessionId;
            RedisUtils.setCacheObject(key, result, RESULT_EXPIRE);
            log.debug("缓存面试结果：{}", sessionId);
        } catch (Exception e) {
            log.warn("缓存面试结果失败，使用内存缓存：{}", e.getMessage());
            memoryCache.put(RESULT_PREFIX + sessionId, result);
        }
    }

    @Override
    public Optional<InterviewResponseVo.InterviewResult> getCachedInterviewResult(String sessionId) {
        if (sessionId == null) {
            return Optional.empty();
        }
        
        try {
            String key = RESULT_PREFIX + sessionId;
            InterviewResponseVo.InterviewResult result = RedisUtils.getCacheObject(key);
            if (result != null) {
                log.debug("从缓存获取面试结果：{}", sessionId);
                return Optional.of(result);
            }
        } catch (Exception e) {
            log.warn("从Redis获取面试结果失败，尝试内存缓存：{}", e.getMessage());
            Object cached = memoryCache.get(RESULT_PREFIX + sessionId);
            if (cached instanceof InterviewResponseVo.InterviewResult) {
                return Optional.of((InterviewResponseVo.InterviewResult) cached);
            }
        }
        
        return Optional.empty();
    }

    @Override
    public void evictInterviewResult(String sessionId) {
        if (sessionId == null) {
            return;
        }
        
        try {
            String key = RESULT_PREFIX + sessionId;
            RedisUtils.deleteObject(key);
            memoryCache.remove(key);
            log.debug("删除面试结果缓存：{}", sessionId);
        } catch (Exception e) {
            log.warn("删除面试结果缓存失败：{}", e.getMessage());
        }
    }

    @Override
    public void cacheAiEvaluation(String questionId, String answer, InterviewResponseVo.FeedbackInfo feedback) {
        if (questionId == null || answer == null || feedback == null) {
            return;
        }
        
        try {
            String key = AI_EVAL_PREFIX + questionId + ":" + answer.hashCode();
            RedisUtils.setCacheObject(key, feedback, AI_EVAL_EXPIRE);
            log.debug("缓存AI评估结果：questionId={}", questionId);
        } catch (Exception e) {
            log.warn("缓存AI评估结果失败，使用内存缓存：{}", e.getMessage());
            memoryCache.put(AI_EVAL_PREFIX + questionId + ":" + answer.hashCode(), feedback);
        }
    }

    @Override
    public Optional<InterviewResponseVo.FeedbackInfo> getCachedAiEvaluation(String questionId, String answer) {
        if (questionId == null || answer == null) {
            return Optional.empty();
        }
        
        try {
            String key = AI_EVAL_PREFIX + questionId + ":" + answer.hashCode();
            InterviewResponseVo.FeedbackInfo feedback = RedisUtils.getCacheObject(key);
            if (feedback != null) {
                log.debug("从缓存获取AI评估结果：questionId={}", questionId);
                return Optional.of(feedback);
            }
        } catch (Exception e) {
            log.warn("从Redis获取AI评估结果失败，尝试内存缓存：{}", e.getMessage());
            Object cached = memoryCache.get(AI_EVAL_PREFIX + questionId + ":" + answer.hashCode());
            if (cached instanceof InterviewResponseVo.FeedbackInfo) {
                return Optional.of((InterviewResponseVo.FeedbackInfo) cached);
            }
        }
        
        return Optional.empty();
    }

    @Override
    public void cachePopularQuestions(Long jobId, List<InterviewResponseVo.QuestionInfo> questions) {
        if (jobId == null || questions == null) {
            return;
        }
        
        try {
            String key = POPULAR_QUESTIONS_PREFIX + jobId;
            RedisUtils.setCacheObject(key, questions, POPULAR_QUESTIONS_EXPIRE);
            log.debug("缓存热门问题：jobId={}, 问题数量：{}", jobId, questions.size());
        } catch (Exception e) {
            log.warn("缓存热门问题失败，使用内存缓存：{}", e.getMessage());
            memoryCache.put(POPULAR_QUESTIONS_PREFIX + jobId, questions);
        }
    }

    @Override
    @SuppressWarnings("unchecked")
    public Optional<List<InterviewResponseVo.QuestionInfo>> getCachedPopularQuestions(Long jobId) {
        if (jobId == null) {
            return Optional.empty();
        }
        
        try {
            String key = POPULAR_QUESTIONS_PREFIX + jobId;
            List<InterviewResponseVo.QuestionInfo> questions = RedisUtils.getCacheObject(key);
            if (questions != null) {
                log.debug("从缓存获取热门问题：jobId={}, 问题数量：{}", jobId, questions.size());
                return Optional.of(questions);
            }
        } catch (Exception e) {
            log.warn("从Redis获取热门问题失败，尝试内存缓存：{}", e.getMessage());
            Object cached = memoryCache.get(POPULAR_QUESTIONS_PREFIX + jobId);
            if (cached instanceof List) {
                return Optional.of((List<InterviewResponseVo.QuestionInfo>) cached);
            }
        }
        
        return Optional.empty();
    }

    @Override
    public void cacheUserStats(Long userId, InterviewResponseVo.UserStats stats) {
        if (userId == null || stats == null) {
            return;
        }
        
        try {
            String key = USER_STATS_PREFIX + userId;
            RedisUtils.setCacheObject(key, stats, USER_STATS_EXPIRE);
            log.debug("缓存用户统计信息：{}", userId);
        } catch (Exception e) {
            log.warn("缓存用户统计信息失败，使用内存缓存：{}", e.getMessage());
            memoryCache.put(USER_STATS_PREFIX + userId, stats);
        }
    }

    @Override
    public Optional<InterviewResponseVo.UserStats> getCachedUserStats(Long userId) {
        if (userId == null) {
            return Optional.empty();
        }
        
        try {
            String key = USER_STATS_PREFIX + userId;
            InterviewResponseVo.UserStats stats = RedisUtils.getCacheObject(key);
            if (stats != null) {
                log.debug("从缓存获取用户统计信息：{}", userId);
                return Optional.of(stats);
            }
        } catch (Exception e) {
            log.warn("从Redis获取用户统计信息失败，尝试内存缓存：{}", e.getMessage());
            Object cached = memoryCache.get(USER_STATS_PREFIX + userId);
            if (cached instanceof InterviewResponseVo.UserStats) {
                return Optional.of((InterviewResponseVo.UserStats) cached);
            }
        }
        
        return Optional.empty();
    }

    @Override
    public void warmupCache(String sessionId) {
        log.info("预热缓存：sessionId={}", sessionId);
        // 这里可以预加载相关数据到缓存
        // 暂时只记录日志
    }

    @Override
    public void cleanExpiredCache() {
        log.info("清理过期缓存");
        try {
            // 清理内存缓存中的过期数据
            // 这里可以实现更复杂的过期策略
            if (memoryCache.size() > 1000) {
                memoryCache.clear();
                log.info("清理内存缓存完成");
            }
        } catch (Exception e) {
            log.error("清理过期缓存失败", e);
        }
    }

    @Override
    public void clearAllCache() {
        log.info("清理所有缓存");
        try {
            // 清理Redis缓存
            Collection<String> keys = RedisUtils.keys(CACHE_PREFIX + "*");
            if (!keys.isEmpty()) {
                RedisUtils.deleteObject(keys);
            }
            
            // 清理内存缓存
            memoryCache.clear();
            
            log.info("清理所有缓存完成");
        } catch (Exception e) {
            log.error("清理所有缓存失败", e);
        }
    }

    @Override
    public Map<String, Object> getCacheStats() {
        Map<String, Object> stats = new HashMap<>();
        
        try {
            // Redis缓存统计
            Collection<String> redisKeys = RedisUtils.keys(CACHE_PREFIX + "*");
            stats.put("redis_keys_count", redisKeys.size());
            
            // 内存缓存统计
            stats.put("memory_cache_size", memoryCache.size());
            
            // 各类型缓存统计
            stats.put("session_cache_count", RedisUtils.keys(SESSION_PREFIX + "*").size());
            stats.put("questions_cache_count", RedisUtils.keys(QUESTIONS_PREFIX + "*").size());
            stats.put("result_cache_count", RedisUtils.keys(RESULT_PREFIX + "*").size());
            
            log.debug("缓存统计信息：{}", stats);
        } catch (Exception e) {
            log.error("获取缓存统计失败", e);
            stats.put("error", e.getMessage());
        }
        
        return stats;
    }
}
