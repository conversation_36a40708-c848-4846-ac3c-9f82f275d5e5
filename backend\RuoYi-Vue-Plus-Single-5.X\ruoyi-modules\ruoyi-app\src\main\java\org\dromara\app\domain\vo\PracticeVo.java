package org.dromara.app.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 推荐练习视图对象
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "推荐练习视图对象")
public class PracticeVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 练习标题
     */
    @Schema(description = "练习标题")
    private String title;

    /**
     * 练习描述
     */
    @Schema(description = "练习描述")
    private String description;

    /**
     * 难度级别
     */
    @Schema(description = "难度级别")
    private String difficulty;
}
