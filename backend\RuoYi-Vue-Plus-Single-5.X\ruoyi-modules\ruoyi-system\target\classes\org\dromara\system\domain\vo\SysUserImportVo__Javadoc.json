{"doc": "\n 用户对象导入VO\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "userId", "doc": "\n 用户ID\r\n"}, {"name": "deptId", "doc": "\n 部门ID\r\n"}, {"name": "userName", "doc": "\n 用户账号\r\n"}, {"name": "nick<PERSON><PERSON>", "doc": "\n 用户昵称\r\n"}, {"name": "email", "doc": "\n 用户邮箱\r\n"}, {"name": "phonenumber", "doc": "\n 手机号码\r\n"}, {"name": "sex", "doc": "\n 用户性别\r\n"}, {"name": "status", "doc": "\n 帐号状态（0正常 1停用）\r\n"}], "enumConstants": [], "methods": [], "constructors": []}