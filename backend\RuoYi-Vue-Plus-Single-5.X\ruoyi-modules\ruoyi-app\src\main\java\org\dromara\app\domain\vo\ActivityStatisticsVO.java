package org.dromara.app.domain.vo;

import lombok.Data;
import org.dromara.app.domain.enums.ActivityType;

import java.util.Map;

/**
 * 活动统计响应VO
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Data
public class ActivityStatisticsVO {

    /**
     * 今日活动时长(毫秒)
     */
    private Long today;

    /**
     * 本周活动时长(毫秒)
     */
    private Long week;

    /**
     * 本月活动时长(毫秒)
     */
    private Long month;

    /**
     * 总活动时长(毫秒)
     */
    private Long total;

    /**
     * 今日活动时长(格式化)
     */
    private String todayFormatted;

    /**
     * 本周活动时长(格式化)
     */
    private String weekFormatted;

    /**
     * 本月活动时长(格式化)
     */
    private String monthFormatted;

    /**
     * 总活动时长(格式化)
     */
    private String totalFormatted;

    /**
     * 按类型统计的活动时长
     */
    private Map<ActivityType, ActivityTypeStatistics> byType;

    /**
     * 格式化时长为可读字符串
     *
     * @param duration 时长(毫秒)
     * @return 格式化后的字符串
     */
    public static String formatDuration(Long duration) {
        if (duration == null || duration <= 0) {
            return "0分钟";
        }

        long seconds = duration / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;

        if (hours > 0) {
            return String.format("%d小时%d分钟", hours, minutes % 60);
        } else if (minutes > 0) {
            return String.format("%d分钟", minutes);
        } else {
            return String.format("%d秒", seconds);
        }
    }

    /**
     * 设置格式化的时长字符串
     */
    public void setFormattedDurations() {
        this.todayFormatted = formatDuration(this.today);
        this.weekFormatted = formatDuration(this.week);
        this.monthFormatted = formatDuration(this.month);
        this.totalFormatted = formatDuration(this.total);

        if (this.byType != null) {
            this.byType.forEach((type, stats) -> {
                if (stats != null) {
                    stats.setTotalFormatted(formatDuration(stats.getTotal()));
                }
            });
        }
    }

    /**
     * 活动类型统计内部类
     */
    @Data
    public static class ActivityTypeStatistics {
        /**
         * 总时长(毫秒)
         */
        private Long total;

        /**
         * 总时长(格式化)
         */
        private String totalFormatted;
    }
}
