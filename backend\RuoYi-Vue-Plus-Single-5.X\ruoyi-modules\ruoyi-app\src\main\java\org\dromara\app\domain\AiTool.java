package org.dromara.app.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.util.List;
import java.util.Map;

/**
 * AI工具对象 app_ai_tool
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_ai_tool")
public class AiTool extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 工具ID
     */
    @TableId(value = "id")
    private String id;

    /**
     * 工具名称
     */
    private String name;

    /**
     * 工具显示名称
     */
    private String displayName;

    /**
     * 工具描述
     */
    private String description;

    /**
     * 工具分类：system/web/file/calculation/database/api
     */
    private String category;

    /**
     * 工具图标
     */
    private String icon;

    /**
     * 工具颜色
     */
    private String color;

    /**
     * 函数定义（JSON格式）
     */
    private String functionDefinition;

    /**
     * 参数schema（JSON格式）
     */
    private String parameterSchema;

    /**
     * 实现类名
     */
    private String implementationClass;

    /**
     * 工具配置（JSON格式）
     */
    private String toolConfig;

    /**
     * 是否启用：0-禁用，1-启用
     */
    private Boolean enabled;

    /**
     * 是否系统工具：0-自定义，1-系统内置
     */
    private Boolean isSystem;

    /**
     * 权限级别：0-公开，1-登录用户，2-特定权限
     */
    private Integer permissionLevel;

    /**
     * 所需权限
     */
    private String requiredPermissions;

    /**
     * 使用次数
     */
    private Long usageCount;

    /**
     * 平均执行时间（毫秒）
     */
    private Long avgExecutionTime;

    /**
     * 成功率
     */
    private Double successRate;

    /**
     * 最后使用时间
     */
    private Long lastUsed;

    /**
     * 排序序号
     */
    private Integer sortOrder;

    /**
     * 标签，逗号分隔
     */
    private String tags;

    /**
     * 工具版本
     */
    private String version;

    /**
     * 作者
     */
    private String author;

    /**
     * 删除标志
     */
    private String delFlag;

    /**
     * 函数定义对象（不存储到数据库）
     */
    @TableField(exist = false)
    private FunctionDefinition functionDefinitionObject;

    /**
     * 参数Schema对象（不存储到数据库）
     */
    @TableField(exist = false)
    private ParameterSchema parameterSchemaObject;

    /**
     * 工具配置对象（不存储到数据库）
     */
    @TableField(exist = false)
    private ToolConfig toolConfigObject;

    /**
     * 标签列表（不存储到数据库）
     */
    @TableField(exist = false)
    private List<String> tagList;

    /**
     * 函数定义内部类
     */
    @Data
    public static class FunctionDefinition {
        private String name;
        private String description;
        private Map<String, Object> parameters;
        private List<String> required;
        private Map<String, String> examples;
    }

    /**
     * 参数Schema内部类
     */
    @Data
    public static class ParameterSchema {
        private String type;
        private Map<String, ParameterProperty> properties;
        private List<String> required;

        @Data
        public static class ParameterProperty {
            private String type;
            private String description;
            private Object defaultValue;
            private List<Object> enumValues;
            private String format;
            private Number minimum;
            private Number maximum;
            private Integer minLength;
            private Integer maxLength;
            private String pattern;
        }
    }

    /**
     * 工具配置内部类
     */
    @Data
    public static class ToolConfig {
        private Integer timeout; // 超时时间（秒）
        private Integer maxRetries; // 最大重试次数
        private Boolean enableCache; // 是否启用缓存
        private Integer cacheTimeout; // 缓存超时时间
        private Boolean enableLogging; // 是否启用日志
        private String logLevel; // 日志级别
        private Map<String, Object> customConfig; // 自定义配置
    }
}
