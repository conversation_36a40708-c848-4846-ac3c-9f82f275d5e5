package org.dromara.app.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.app.domain.Agent;
import org.dromara.app.domain.VectorEmbedding;

import java.util.List;

/**
 * Agent服务接口
 */
public interface IAgentService extends IService<Agent> {

    /**
     * 获取所有启用的代理列表
     *
     * @return 代理列表
     */
    List<Agent> getEnabledAgents();

    /**
     * 根据Agent类型获取Agent配置
     *
     * @param agentType Agent类型
     * @return Agent配置
     */
    Agent getAgentByType(String agentType);

    /**
     * 增加Agent使用次数
     *
     * @param agentType Agent类型
     * @return 是否成功
     */
    boolean incrementUsageCount(String agentType);

    /**
     * 更新代理评分
     *
     * @param agentType 代理类型
     * @param rating    评分
     * @return 是否成功
     */
    boolean updateRating(String agentType, Double rating);

    /**
     * 获取代理的快速操作列表
     *
     * @param agentType 代理类型
     * @return 快速操作列表
     */
    List<Agent.QuickAction> getQuickActions(String agentType);

    /**
     * 初始化默认代理数据
     */
    void initDefaultAgents();

    /**
     * 基于RAG增强用户查询
     *
     * @param agentType Agent类型
     * @param message   用户消息
     * @return 增强后的查询
     */
    String enhanceQueryWithRAG(String agentType, String message);

    /**
     * 检索与查询相关的文档
     *
     * @param agentType Agent类型
     * @param query     查询
     * @return 检索结果列表
     */
    List<VectorEmbedding> retrieveRelevantDocuments(String agentType, String query);

    /**
     * 获取代理相关的知识库ID列表
     *
     * @param agentType 代理类型
     * @return 知识库ID列表
     */
    List<Long> getAgentKnowledgeBaseIds(String agentType);
}
