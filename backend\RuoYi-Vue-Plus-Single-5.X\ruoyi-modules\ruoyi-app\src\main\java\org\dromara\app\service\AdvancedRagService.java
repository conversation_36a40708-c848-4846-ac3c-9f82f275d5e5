package org.dromara.app.service;

import lombok.Getter;
import lombok.Setter;
import org.dromara.app.domain.VectorEmbedding;

import java.util.List;
import java.util.Map;

/**
 * 高级RAG服务接口
 * 提供更智能的检索和增强功能
 *
 * <AUTHOR>
 */
public interface AdvancedRagService {

    /**
     * 智能检索
     * 结合多种检索策略，提供最相关的结果
     *
     * @param query            查询文本
     * @param knowledgeBaseIds 知识库ID列表
     * @param options          检索选项
     * @return 检索结果
     */
    SmartRetrievalResult smartRetrieval(String query, List<Long> knowledgeBaseIds, RetrievalOptions options);

    /**
     * 混合检索
     * 结合向量检索和关键词检索
     *
     * @param query            查询文本
     * @param knowledgeBaseIds 知识库ID列表
     * @param vectorWeight     向量检索权重
     * @param keywordWeight    关键词检索权重
     * @param topK             返回数量
     * @return 检索结果
     */
    List<VectorEmbedding> hybridRetrieval(String query, List<Long> knowledgeBaseIds,
                                          double vectorWeight, double keywordWeight, int topK);

    /**
     * 重排序检索结果
     * 使用重排序模型对检索结果进行重新排序
     *
     * @param query      查询文本
     * @param candidates 候选结果
     * @param topK       返回数量
     * @return 重排序后的结果
     */
    List<VectorEmbedding> rerank(String query, List<VectorEmbedding> candidates, int topK);

    /**
     * 查询扩展
     * 扩展用户查询以提高检索效果
     *
     * @param query         原始查询
     * @param expansionType 扩展类型
     * @return 扩展后的查询
     */
    QueryExpansionResult expandQuery(String query, QueryExpansionType expansionType);

    /**
     * 上下文压缩
     * 压缩检索到的上下文，保留最重要的信息
     *
     * @param query     查询文本
     * @param contexts  上下文列表
     * @param maxLength 最大长度
     * @return 压缩后的上下文
     */
    String compressContext(String query, List<String> contexts, int maxLength);

    /**
     * 生成增强提示词
     * 基于检索结果生成优化的提示词
     *
     * @param query            用户查询
     * @param retrievalResults 检索结果
     * @param promptTemplate   提示词模板
     * @return 增强后的提示词
     */
    String generateEnhancedPrompt(String query, List<VectorEmbedding> retrievalResults, String promptTemplate);

    /**
     * 查询扩展类型
     */
    enum QueryExpansionType {
        SYNONYM,        // 同义词扩展
        RELATED_TERMS,  // 相关词扩展
        CONTEXT_AWARE,  // 上下文感知扩展
        SEMANTIC        // 语义扩展
    }

    /**
     * 检索选项
     */
    @Setter
    @Getter
    class RetrievalOptions {
        // Getters and Setters
        private int topK = 5;
        private double similarityThreshold = 0.7;
        private boolean enableRerank = true;
        private boolean enableQueryExpansion = false;
        private QueryExpansionType expansionType = QueryExpansionType.SYNONYM;
        private double vectorWeight = 0.7;
        private double keywordWeight = 0.3;
        private int maxContextLength = 4000;

    }

    /**
     * 智能检索结果
     */
    @Getter
    class SmartRetrievalResult {
        // Getters
        private List<VectorEmbedding> results;
        private String expandedQuery;
        private Map<String, Object> metadata;
        private double avgSimilarity;
        private int totalCandidates;

        public SmartRetrievalResult(List<VectorEmbedding> results, String expandedQuery,
                                    Map<String, Object> metadata, double avgSimilarity, int totalCandidates) {
            this.results = results;
            this.expandedQuery = expandedQuery;
            this.metadata = metadata;
            this.avgSimilarity = avgSimilarity;
            this.totalCandidates = totalCandidates;
        }

    }

    /**
     * 查询扩展结果
     */
    @Getter
    class QueryExpansionResult {
        // Getters
        private String originalQuery;
        private String expandedQuery;
        private List<String> expandedTerms;
        private QueryExpansionType expansionType;

        public QueryExpansionResult(String originalQuery, String expandedQuery,
                                    List<String> expandedTerms, QueryExpansionType expansionType) {
            this.originalQuery = originalQuery;
            this.expandedQuery = expandedQuery;
            this.expandedTerms = expandedTerms;
            this.expansionType = expansionType;
        }

    }
}
