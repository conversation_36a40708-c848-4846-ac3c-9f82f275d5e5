package org.dromara.app.event;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.Achievement;
import org.dromara.app.domain.AchievementEvent;
import org.dromara.app.domain.UserAchievement;
import org.dromara.app.mapper.AchievementEventMapper;
import org.dromara.app.mapper.AchievementMapper;
import org.dromara.app.mapper.UserAchievementMapper;
import org.dromara.app.mapper.UserBehaviorMapper;
import org.dromara.app.service.IAchievementService;
import org.dromara.common.core.utils.DateUtils;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 成就事件监听器
 * 监听各种业务事件，自动触发成就检查和更新
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AchievementEventListener {

    private final IAchievementService achievementService;
    private final AchievementMapper achievementMapper;
    private final UserAchievementMapper userAchievementMapper;
    private final AchievementEventMapper achievementEventMapper;
    private final UserBehaviorMapper userBehaviorMapper;

    /**
     * 监听用户注册事件
     */
    @Async
    @EventListener
    @Transactional(rollbackFor = Exception.class)
    public void handleUserRegistrationEvent(UserRegistrationEvent event) {
        log.info("处理用户注册事件: userId={}", event.getUserId());

        // 初始化用户成就
        achievementService.initializeUserAchievements(event.getUserId());

        // 记录注册事件
        Map<String, Object> eventData = new HashMap<>();
        eventData.put("registrationTime", event.getRegistrationTime());
        eventData.put("registrationSource", event.getSource());

        achievementService.recordEvent(event.getUserId(), "USER_REGISTRATION", eventData, 1, null, null);
    }

    /**
     * 监听学习完成事件
     */
    @Async
    @EventListener
    @Transactional(rollbackFor = Exception.class)
    public void handleLearningCompletedEvent(LearningCompletedEvent event) {
        log.info("处理学习完成事件: userId={}, resourceId={}, type={}",
                event.getUserId(), event.getResourceId(), event.getResourceType());

        Map<String, Object> eventData = new HashMap<>();
        eventData.put("resourceId", event.getResourceId());
        eventData.put("resourceType", event.getResourceType());
        eventData.put("duration", event.getDuration());
        eventData.put("score", event.getScore());

        // 记录学习事件
        achievementService.recordEvent(event.getUserId(), "LEARNING_COMPLETED", eventData, 1,
                event.getResourceId(), event.getResourceType());

        // 检查学习相关成就
        checkLearningAchievements(event.getUserId(), event);
    }

    /**
     * 监听面试完成事件
     */
    @Async
    @EventListener
    @Transactional(rollbackFor = Exception.class)
    public void handleInterviewCompletedEvent(InterviewCompletedEvent event) {
        log.info("处理面试完成事件: userId={}, score={}", event.getUserId(), event.getScore());

        Map<String, Object> eventData = new HashMap<>();
        eventData.put("interviewId", event.getInterviewId());
        eventData.put("jobId", event.getJobId());
        eventData.put("score", event.getScore());
        eventData.put("duration", event.getDuration());
        eventData.put("mode", event.getMode());

        // 记录面试事件
        achievementService.recordEvent(event.getUserId(), "INTERVIEW_COMPLETED", eventData, 1,
                event.getInterviewId().toString(), "interview");

        // 检查面试相关成就
        checkInterviewAchievements(event.getUserId(), event);
    }

    /**
     * 监听能力提升事件
     */
    @Async
    @EventListener
    @Transactional(rollbackFor = Exception.class)
    public void handleAbilityImproveEvent(AbilityImproveEvent event) {
        log.info("处理能力提升事件: userId={}, abilityType={}, improvement={}",
                event.getUserId(), event.getAbilityType(), event.getImprovement());

        Map<String, Object> eventData = new HashMap<>();
        eventData.put("abilityType", event.getAbilityType());
        eventData.put("oldScore", event.getOldScore());
        eventData.put("newScore", event.getNewScore());
        eventData.put("improvement", event.getImprovement());

        // 记录能力提升事件
        achievementService.recordEvent(event.getUserId(), "ABILITY_IMPROVED", eventData,
                event.getImprovement(), null, "ability");

        // 检查能力提升相关成就
        checkAbilityAchievements(event.getUserId(), event);
    }

    /**
     * 检查学习相关成就
     */
    private void checkLearningAchievements(String userId, LearningCompletedEvent event) {
        try {
            // 查找学习相关的成就 - 使用现有的方法
            List<Achievement> learningAchievements = achievementMapper.selectList(null);

            for (Achievement achievement : learningAchievements) {
                // 只处理学习类型的成就
                if (!"LEARNING".equals(achievement.getAchievementType())) {
                    continue;
                }

                // 获取用户成就记录
                UserAchievement userAchievement = userAchievementMapper.selectByUserIdAndAchievementId(
                    Long.valueOf(userId), achievement.getId());

                if (userAchievement == null || "1".equals(userAchievement.getIsCompleted())) {
                    continue;
                }

                // 根据成就条件检查是否满足
                if (checkLearningCondition(userId, achievement, event)) {
                    // 更新进度或解锁成就
                    int newProgress = calculateLearningProgress(userId, achievement);
                    achievementService.updateAchievementProgress(userId, achievement.getId().toString(), newProgress, null);
                }
            }
        } catch (Exception e) {
            log.error("检查学习相关成就失败: userId={}", userId, e);
        }
    }

    /**
     * 检查面试相关成就
     */
    private void checkInterviewAchievements(String userId, InterviewCompletedEvent event) {
        try {
            // 查找面试相关的成就
            List<Achievement> interviewAchievements = achievementMapper.selectList(null);

            for (Achievement achievement : interviewAchievements) {
                // 只处理自定义类型的成就（面试成就通常是自定义类型）
                if (!"CUSTOM".equals(achievement.getAchievementType())) {
                    continue;
                }

                UserAchievement userAchievement = userAchievementMapper.selectByUserIdAndAchievementId(
                    Long.valueOf(userId), achievement.getId());

                if (userAchievement == null || "1".equals(userAchievement.getIsCompleted())) {
                    continue;
                }

                if (checkInterviewCondition(userId, achievement, event)) {
                    int newProgress = calculateInterviewProgress(userId, achievement);
                    achievementService.updateAchievementProgress(userId, achievement.getId().toString(), newProgress, null);
                }
            }
        } catch (Exception e) {
            log.error("检查面试相关成就失败: userId={}", userId, e);
        }
    }

    /**
     * 检查能力提升相关成就
     */
    private void checkAbilityAchievements(String userId, AbilityImproveEvent event) {
        try {
            // 查找能力提升相关的成就
            List<Achievement> abilityAchievements = achievementMapper.selectList(null);

            for (Achievement achievement : abilityAchievements) {
                // 只处理自定义类型的成就（能力提升成就通常是自定义类型）
                if (!"CUSTOM".equals(achievement.getAchievementType())) {
                    continue;
                }

                UserAchievement userAchievement = userAchievementMapper.selectByUserIdAndAchievementId(
                    Long.valueOf(userId), achievement.getId());

                if (userAchievement == null || "1".equals(userAchievement.getIsCompleted())) {
                    continue;
                }

                if (checkAbilityCondition(userId, achievement, event)) {
                    int newProgress = calculateAbilityProgress(userId, achievement);
                    achievementService.updateAchievementProgress(userId, achievement.getId().toString(), newProgress, null);
                }
            }
        } catch (Exception e) {
            log.error("检查能力提升相关成就失败: userId={}", userId, e);
        }
    }

    /**
     * 检查学习条件
     */
    private boolean checkLearningCondition(String userId, Achievement achievement, LearningCompletedEvent event) {
        // 使用triggerCondition字段替代不存在的eventParams字段
        if (StrUtil.isBlank(achievement.getTriggerCondition())) {
            return true;
        }

        try {
            Map<String, Object> params = JSONUtil.toBean(achievement.getTriggerCondition(), Map.class);

            // 检查行为类型是否匹配
            String behaviorType = (String) params.get("behaviorType");
            if (!"LEARNING_COMPLETED".equals(behaviorType)) {
                return false;
            }

            // 检查资源类型
            if (params.containsKey("resourceType")) {
                String requiredType = (String) params.get("resourceType");
                if (!requiredType.equals(event.getResourceType())) {
                    return false;
                }
            }

            // 检查最低分数
            if (params.containsKey("minScore")) {
                Integer minScore = (Integer) params.get("minScore");
                if (event.getScore() < minScore) {
                    return false;
                }
            }

            return true;
        } catch (Exception e) {
            log.error("解析成就触发条件失败: achievementId={}", achievement.getId(), e);
            return false;
        }
    }

    /**
     * 检查面试条件
     */
    private boolean checkInterviewCondition(String userId, Achievement achievement, InterviewCompletedEvent event) {
        // 使用triggerCondition字段
        if (StrUtil.isBlank(achievement.getTriggerCondition())) {
            return true;
        }

        try {
            Map<String, Object> params = JSONUtil.toBean(achievement.getTriggerCondition(), Map.class);

            // 检查行为类型是否匹配
            String behaviorType = (String) params.get("behaviorType");
            if (!"INTERVIEW_COMPLETED".equals(behaviorType)) {
                return false;
            }

            // 检查最低分数
            if (params.containsKey("minScore")) {
                Integer minScore = (Integer) params.get("minScore");
                if (event.getScore() < minScore) {
                    return false;
                }
            }

            // 检查面试模式
            if (params.containsKey("mode")) {
                String requiredMode = (String) params.get("mode");
                if (!requiredMode.equals(event.getMode())) {
                    return false;
                }
            }

            return true;
        } catch (Exception e) {
            log.error("解析成就触发条件失败: achievementId={}", achievement.getId(), e);
            return false;
        }
    }

    /**
     * 检查能力提升条件
     */
    private boolean checkAbilityCondition(String userId, Achievement achievement, AbilityImproveEvent event) {
        // 使用triggerCondition字段
        if (StrUtil.isBlank(achievement.getTriggerCondition())) {
            return true;
        }

        try {
            Map<String, Object> params = JSONUtil.toBean(achievement.getTriggerCondition(), Map.class);

            // 检查行为类型是否匹配
            String behaviorType = (String) params.get("behaviorType");
            if (!"ABILITY_IMPROVED".equals(behaviorType)) {
                return false;
            }

            // 检查能力类型
            if (params.containsKey("abilityType")) {
                String requiredType = (String) params.get("abilityType");
                if (!requiredType.equals(event.getAbilityType())) {
                    return false;
                }
            }

            // 检查最小提升幅度
            if (params.containsKey("minImprovement")) {
                Integer minImprovement = (Integer) params.get("minImprovement");
                if (event.getImprovement() < minImprovement) {
                    return false;
                }
            }

            return true;
        } catch (Exception e) {
            log.error("解析成就触发条件失败: achievementId={}", achievement.getId(), e);
            return false;
        }
    }

    /**
     * 计算学习进度
     */
    private int calculateLearningProgress(String userId, Achievement achievement) {
        try {
            // 使用UserBehaviorMapper来统计学习完成次数
            Long count = userBehaviorMapper.countByUserIdAndBehaviorType(Long.valueOf(userId), "LEARNING_COMPLETED");
            return count != null ? count.intValue() : 0;
        } catch (Exception e) {
            log.error("计算学习进度失败: userId={}, achievementId={}", userId, achievement.getId(), e);
            return 0;
        }
    }

    /**
     * 计算面试进度
     */
    private int calculateInterviewProgress(String userId, Achievement achievement) {
        try {
            // 使用UserBehaviorMapper来统计面试完成次数
            Long count = userBehaviorMapper.countByUserIdAndBehaviorType(Long.valueOf(userId), "INTERVIEW_COMPLETED");
            return count != null ? count.intValue() : 0;
        } catch (Exception e) {
            log.error("计算面试进度失败: userId={}, achievementId={}", userId, achievement.getId(), e);
            return 0;
        }
    }

    /**
     * 计算能力提升进度
     */
    private int calculateAbilityProgress(String userId, Achievement achievement) {
        try {
            // 使用UserBehaviorMapper来统计能力提升次数
            Long count = userBehaviorMapper.countByUserIdAndBehaviorType(Long.valueOf(userId), "ABILITY_IMPROVED");
            return count != null ? count.intValue() : 0;
        } catch (Exception e) {
            log.error("计算能力提升进度失败: userId={}, achievementId={}", userId, achievement.getId(), e);
            return 0;
        }
    }

    /**
     * 用户注册事件
     */
    public static class UserRegistrationEvent {
        private final String userId;
        private final LocalDateTime registrationTime;
        private final String source;

        public UserRegistrationEvent(String userId, String source) {
            this.userId = userId;
            this.registrationTime = LocalDateTime.now();
            this.source = source;
        }

        public String getUserId() {
            return userId;
        }

        public LocalDateTime getRegistrationTime() {
            return registrationTime;
        }

        public String getSource() {
            return source;
        }
    }

    /**
     * 学习完成事件
     */
    public static class LearningCompletedEvent {
        private final String userId;
        private final String resourceId;
        private final String resourceType;
        private final int duration;
        private final int score;
        private final LocalDateTime completedTime;

        public LearningCompletedEvent(String userId, String resourceId, String resourceType, int duration, int score) {
            this.userId = userId;
            this.resourceId = resourceId;
            this.resourceType = resourceType;
            this.duration = duration;
            this.score = score;
            this.completedTime = LocalDateTime.now();
        }

        public String getUserId() {
            return userId;
        }

        public String getResourceId() {
            return resourceId;
        }

        public String getResourceType() {
            return resourceType;
        }

        public int getDuration() {
            return duration;
        }

        public int getScore() {
            return score;
        }

        public LocalDateTime getCompletedTime() {
            return completedTime;
        }
    }

    /**
     * 面试完成事件
     */
    public static class InterviewCompletedEvent {
        private final String userId;
        private final Long interviewId;
        private final Long jobId;
        private final int score;
        private final int duration;
        private final String mode;
        private final LocalDateTime completedTime;

        public InterviewCompletedEvent(String userId, Long interviewId, Long jobId, int score, int duration, String mode) {
            this.userId = userId;
            this.interviewId = interviewId;
            this.jobId = jobId;
            this.score = score;
            this.duration = duration;
            this.mode = mode;
            this.completedTime = LocalDateTime.now();
        }

        public String getUserId() {
            return userId;
        }

        public Long getInterviewId() {
            return interviewId;
        }

        public Long getJobId() {
            return jobId;
        }

        public int getScore() {
            return score;
        }

        public int getDuration() {
            return duration;
        }

        public String getMode() {
            return mode;
        }

        public LocalDateTime getCompletedTime() {
            return completedTime;
        }
    }

    /**
     * 能力提升事件
     */
    public static class AbilityImproveEvent {
        private final String userId;
        private final String abilityType;
        private final int oldScore;
        private final int newScore;
        private final int improvement;
        private final LocalDateTime improveTime;

        public AbilityImproveEvent(String userId, String abilityType, int oldScore, int newScore) {
            this.userId = userId;
            this.abilityType = abilityType;
            this.oldScore = oldScore;
            this.newScore = newScore;
            this.improvement = newScore - oldScore;
            this.improveTime = LocalDateTime.now();
        }

        public String getUserId() {
            return userId;
        }

        public String getAbilityType() {
            return abilityType;
        }

        public int getOldScore() {
            return oldScore;
        }

        public int getNewScore() {
            return newScore;
        }

        public int getImprovement() {
            return improvement;
        }

        public LocalDateTime getImproveTime() {
            return improveTime;
        }
    }
}
