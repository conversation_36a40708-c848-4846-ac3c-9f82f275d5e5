package org.dromara.app.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.service.IFileService;
import org.dromara.common.oss.core.OssClient;
import org.dromara.common.oss.entity.UploadResult;
import org.dromara.common.oss.factory.OssFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 文件服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class FileServiceImpl implements IFileService {

    // 临时存储文件信息，实际应该存储到数据库
    private final Map<String, FileInfo> fileInfoStorage = new ConcurrentHashMap<>();
    @Value("${app.file.upload.path:/data/uploads}")
    private String uploadPath;
    @Value("${app.file.upload.max-size:10485760}") // 10MB
    private Long maxFileSize;
    @Value("${app.file.allowed-types:jpg,jpeg,png,gif,pdf,doc,docx,txt,mp3,wav,ogg}")
    private String allowedTypes;
    @Value("${app.file.base-url:http://localhost:8080}")
    private String baseUrl;

    @Override
    public FileUploadResult uploadChatFile(MultipartFile file, String type, Long userId) {
        try {
            // 验证文件
            String validationError = validateFile(file, type);
            if (StrUtil.isNotBlank(validationError)) {
                return FileUploadResult.error(validationError);
            }

            // 生成文件信息
            String originalFilename = file.getOriginalFilename();
            String extension = FileUtil.extName(originalFilename);

            // 使用OSS服务上传文件
            OssClient ossClient = OssFactory.instance();

            // 构建OSS存储的文件路径
            String relativePath = generateRelativePath(type);
            String ossFilePath = "chat/" + userId + "/" + relativePath;

            // 上传文件到OSS并获取结果
            UploadResult uploadResult;
            try {
                uploadResult = ossClient.uploadSuffix(file.getBytes(), "." + extension, file.getContentType());
            } catch (IOException e) {
                log.error("上传文件到OSS失败", e);
                return FileUploadResult.error("上传文件失败：" + e.getMessage());
            }

            // 生成文件信息
            FileInfo fileInfo = new FileInfo();
            fileInfo.setFileName(originalFilename);
            fileInfo.setFileSize(file.getSize());
            fileInfo.setMimeType(file.getContentType());
            fileInfo.setFileUrl(uploadResult.getUrl());
            fileInfo.setFilePath(uploadResult.getFilename());
            fileInfo.setUploadTime(System.currentTimeMillis());
            fileInfo.setUserId(userId);
            fileInfo.setFileType(type);

            // 存储文件信息
            String fileId = UUID.randomUUID().toString();
            fileInfo.setFileId(fileId);
            fileInfoStorage.put(fileId, fileInfo);

            // 构造并返回结果
            return FileUploadResult.success(
                fileInfo.getFileUrl(),
                fileInfo.getFileName(),
                fileInfo.getFileSize(),
                fileInfo.getMimeType(),
                Map.of(
                    "fileId", fileId,
                    "uploadTime", fileInfo.getUploadTime(),
                    "type", type
                )
            );
        } catch (Exception e) {
            log.error("文件上传失败", e);
            return FileUploadResult.error("上传文件失败：" + e.getMessage());
        }
    }

    @Override
    public SpeechToTextResult speechToText(MultipartFile audioFile, Long userId) {
        try {
            // 验证文件
            if (audioFile == null || audioFile.isEmpty()) {
                return SpeechToTextResult.error("音频文件为空");
            }

            String originalFilename = audioFile.getOriginalFilename();
            String extension = FileUtil.extName(originalFilename);

            // 检查文件类型
            List<String> allowedAudioTypes = Arrays.asList("mp3", "wav", "ogg", "m4a", "amr");
            if (!allowedAudioTypes.contains(extension.toLowerCase())) {
                return SpeechToTextResult.error("不支持的音频格式，仅支持：" + String.join(", ", allowedAudioTypes));
            }

            // 检查文件大小
            if (audioFile.getSize() > maxFileSize) {
                return SpeechToTextResult.error("音频文件过大，最大支持：" + (maxFileSize / 1024 / 1024) + "MB");
            }

            // 使用OSS服务上传文件
            OssClient ossClient = OssFactory.instance();

            // 构建OSS存储的文件路径
            String relativePath = generateRelativePath("voice");
            String ossFilePath = "chat/" + userId + "/" + relativePath;

            // 上传文件到OSS并获取结果
            UploadResult uploadResult;
            try {
                uploadResult = ossClient.uploadSuffix(audioFile.getBytes(), "." + extension, audioFile.getContentType());
            } catch (IOException e) {
                log.error("上传音频文件到OSS失败", e);
                return SpeechToTextResult.error("上传音频文件失败：" + e.getMessage());
            }

            // 保存文件信息
            FileInfo fileInfo = new FileInfo();
            fileInfo.setFileName(originalFilename);
            fileInfo.setFileSize(audioFile.getSize());
            fileInfo.setMimeType(audioFile.getContentType());
            fileInfo.setFileUrl(uploadResult.getUrl());
            fileInfo.setFilePath(uploadResult.getFilename());
            fileInfo.setUploadTime(System.currentTimeMillis());
            fileInfo.setUserId(userId);
            fileInfo.setFileType("voice");

            // 存储文件信息
            String fileId = UUID.randomUUID().toString();
            fileInfo.setFileId(fileId);
            fileInfoStorage.put(fileId, fileInfo);

            // TODO: 调用实际的语音识别服务
            // 这里应该调用语音识别服务来处理音频文件
            // 暂时返回模拟结果
            String recognizedText = "这是一个语音转文字的模拟结果。";

            return SpeechToTextResult.success(
                recognizedText,
                "zh-CN",
                0.95,
                5000L
            );

        } catch (Exception e) {
            log.error("语音转文字失败", e);
            return SpeechToTextResult.error("语音转文字失败：" + e.getMessage());
        }
    }

    @Override
    public boolean deleteFile(String fileUrl, Long userId) {
        try {
            // 查找文件信息
            FileInfo fileInfo = null;
            for (FileInfo info : fileInfoStorage.values()) {
                if (fileUrl.equals(info.getFileUrl()) && userId.equals(info.getUserId())) {
                    fileInfo = info;
                    break;
                }
            }

            if (fileInfo == null) {
                log.warn("文件不存在或无权限删除: {}", fileUrl);
                return false;
            }

            // 使用OSS客户端删除文件
            OssClient ossClient = OssFactory.instance();
            try {
                // 从URL中提取对象键
                String objectKey = fileInfo.getFilePath();
                if (objectKey == null) {
                    // 尝试从URL中提取文件路径
                    String url = fileInfo.getFileUrl();
                    int lastSlashIndex = url.lastIndexOf('/');
                    if (lastSlashIndex >= 0) {
                        objectKey = url.substring(lastSlashIndex + 1);
                    } else {
                        objectKey = url;
                    }
                }

                ossClient.delete(objectKey);

                // 从存储中移除文件信息
                fileInfoStorage.remove(fileInfo.getFileId());

                log.info("文件删除成功: {}", fileUrl);
                return true;
            } catch (Exception e) {
                log.error("删除OSS文件失败: {}", fileUrl, e);
                return false;
            }
        } catch (Exception e) {
            log.error("删除文件异常", e);
            return false;
        }
    }

    @Override
    public FileInfo getFileInfo(String fileUrl, Long userId) {
        // 查找文件信息
        for (FileInfo info : fileInfoStorage.values()) {
            if (fileUrl.equals(info.getFileUrl()) && userId.equals(info.getUserId())) {
                return info;
            }
        }
        return null;
    }

    @Override
    public Map<String, Boolean> batchDeleteFiles(List<String> fileUrls, Long userId) {
        Map<String, Boolean> results = new HashMap<>();
        for (String fileUrl : fileUrls) {
            results.put(fileUrl, deleteFile(fileUrl, userId));
        }
        return results;
    }

    @Override
    public Map<String, Object> getFileUsageStats(Long userId) {
        Map<String, Object> stats = new HashMap<>();

        // 获取用户上传的所有文件
        List<FileInfo> userFiles = fileInfoStorage.values().stream()
            .filter(info -> userId.equals(info.getUserId()))
            .toList();

        // 计算总文件数
        stats.put("totalFiles", userFiles.size());

        // 计算总存储量
        long totalSize = userFiles.stream().mapToLong(FileInfo::getFileSize).sum();
        stats.put("totalSize", totalSize);

        // 按文件类型分类统计
        Map<String, Long> typeCount = new HashMap<>();
        Map<String, Long> typeSize = new HashMap<>();

        for (FileInfo file : userFiles) {
            String fileType = file.getFileType();
            if (fileType == null) {
                fileType = "unknown";
            }

            typeCount.put(fileType, typeCount.getOrDefault(fileType, 0L) + 1);
            typeSize.put(fileType, typeSize.getOrDefault(fileType, 0L) + file.getFileSize());
        }

        stats.put("typeCount", typeCount);
        stats.put("typeSize", typeSize);

        return stats;
    }

    // =================  私有方法  =================

    /**
     * 验证文件
     */
    private String validateFile(MultipartFile file, String type) {
        if (file == null || file.isEmpty()) {
            return "文件不能为空";
        }

        // 验证文件大小
        if (file.getSize() > maxFileSize) {
            return String.format("文件大小超过限制，最大允许 %s", formatFileSize(maxFileSize));
        }

        // 验证文件类型
        String originalFilename = file.getOriginalFilename();
        if (StrUtil.isBlank(originalFilename)) {
            return "文件名不能为空";
        }

        String extension = FileUtil.extName(originalFilename).toLowerCase();
        if (StrUtil.isBlank(extension)) {
            return "文件扩展名不能为空";
        }

        List<String> allowedTypeList = Arrays.asList(allowedTypes.toLowerCase().split(","));
        if (!allowedTypeList.contains(extension)) {
            return String.format("不支持的文件类型，支持的类型: %s", allowedTypes);
        }

        // 验证MIME类型
        String mimeType = file.getContentType();
        if (StrUtil.isBlank(mimeType)) {
            return "无法识别文件类型";
        }

        // 根据type进行特定验证
        switch (type) {
            case "image":
                if (!mimeType.startsWith("image/")) {
                    return "图片文件的MIME类型不正确";
                }
                break;
            case "voice":
                if (!mimeType.startsWith("audio/")) {
                    return "音频文件的MIME类型不正确";
                }
                break;
            case "file":
                // 文档类型验证
                break;
            default:
                break;
        }

        return null; // 验证通过
    }

    /**
     * 生成文件名
     */
    private String generateFileName(String extension) {
        return IdUtil.fastSimpleUUID() + "." + extension;
    }

    /**
     * 生成相对路径
     */
    private String generateRelativePath(String type) {
        String date = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
        String fileName = generateFileName("tmp");
        return type + File.separator + date + File.separator + fileName;
    }

    /**
     * 格式化文件大小
     */
    private String formatFileSize(long size) {
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.1f KB", size / 1024.0);
        } else if (size < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", size / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", size / (1024.0 * 1024.0 * 1024.0));
        }
    }

    /**
     * 文件信息
     */
    public class FileInfo {
        private String fileId;
        private String fileName;
        private Long fileSize;
        private String mimeType;
        private String fileUrl;
        private String filePath;
        private Long uploadTime;
        private String uploader;
        private Long userId;
        private String fileType;
        private Map<String, Object> metadata;

        public FileInfo() {
        }

        // getters and setters
        public String getFileId() {
            return fileId;
        }

        public void setFileId(String fileId) {
            this.fileId = fileId;
        }

        public String getFileName() {
            return fileName;
        }

        public void setFileName(String fileName) {
            this.fileName = fileName;
        }

        public Long getFileSize() {
            return fileSize;
        }

        public void setFileSize(Long fileSize) {
            this.fileSize = fileSize;
        }

        public String getMimeType() {
            return mimeType;
        }

        public void setMimeType(String mimeType) {
            this.mimeType = mimeType;
        }

        public String getFileUrl() {
            return fileUrl;
        }

        public void setFileUrl(String fileUrl) {
            this.fileUrl = fileUrl;
        }

        public String getFilePath() {
            return filePath;
        }

        public void setFilePath(String filePath) {
            this.filePath = filePath;
        }

        public Long getUploadTime() {
            return uploadTime;
        }

        public void setUploadTime(Long uploadTime) {
            this.uploadTime = uploadTime;
        }

        public String getUploader() {
            return uploader;
        }

        public void setUploader(String uploader) {
            this.uploader = uploader;
        }

        public Long getUserId() {
            return userId;
        }

        public void setUserId(Long userId) {
            this.userId = userId;
        }

        public String getFileType() {
            return fileType;
        }

        public void setFileType(String fileType) {
            this.fileType = fileType;
        }

        public Map<String, Object> getMetadata() {
            return metadata;
        }

        public void setMetadata(Map<String, Object> metadata) {
            this.metadata = metadata;
        }
    }
}
