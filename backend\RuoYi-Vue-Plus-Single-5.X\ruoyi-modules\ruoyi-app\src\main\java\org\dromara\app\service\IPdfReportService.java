package org.dromara.app.service;

import org.dromara.app.domain.InterviewReport;

import java.io.InputStream;

/**
 * PDF报告生成服务接口
 *
 * <AUTHOR>
 */
public interface IPdfReportService {

    /**
     * 生成PDF报告
     *
     * @param report 面试报告
     * @return PDF文件路径
     */
    String generatePdfReport(InterviewReport report);

    /**
     * 生成PDF报告流
     *
     * @param report 面试报告
     * @return PDF文件流
     */
    InputStream generatePdfReportStream(InterviewReport report);

    /**
     * 生成PDF报告字节数组
     *
     * @param report 面试报告
     * @return PDF字节数组
     */
    byte[] generatePdfReportBytes(InterviewReport report);

    /**
     * 验证PDF文件是否存在
     *
     * @param filePath 文件路径
     * @return 是否存在
     */
    boolean isPdfFileExists(String filePath);

    /**
     * 删除PDF文件
     *
     * @param filePath 文件路径
     * @return 是否删除成功
     */
    boolean deletePdfFile(String filePath);

    /**
     * 获取PDF文件大小
     *
     * @param filePath 文件路径
     * @return 文件大小（字节）
     */
    long getPdfFileSize(String filePath);
}