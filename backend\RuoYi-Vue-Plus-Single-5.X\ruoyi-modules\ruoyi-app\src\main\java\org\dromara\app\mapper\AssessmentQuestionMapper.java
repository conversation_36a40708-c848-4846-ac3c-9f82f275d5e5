package org.dromara.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.dromara.app.domain.AssessmentQuestion;

import java.util.List;

/**
 * 评估问题Mapper接口
 *
 * <AUTHOR>
 */
public interface AssessmentQuestionMapper extends BaseMapper<AssessmentQuestion> {

    /**
     * 查询评估问题列表（包含选项）
     *
     * @return 评估问题列表
     */
    List<AssessmentQuestion> selectQuestionsWithOptions();

    /**
     * 根据状态查询评估问题列表（包含选项）
     *
     * @param status 状态
     * @return 评估问题列表
     */
    List<AssessmentQuestion> selectQuestionsWithOptionsByStatus(@Param("status") String status);

    /**
     * 根据问题ID查询评估问题（包含选项）
     *
     * @param questionId 问题ID
     * @return 评估问题
     */
    AssessmentQuestion selectQuestionWithOptionsById(@Param("questionId") Long questionId);

    /**
     * 根据问题编码查询评估问题（包含选项）
     *
     * @param questionCode 问题编码
     * @return 评估问题
     */
    AssessmentQuestion selectQuestionWithOptionsByCode(@Param("questionCode") String questionCode);
}
