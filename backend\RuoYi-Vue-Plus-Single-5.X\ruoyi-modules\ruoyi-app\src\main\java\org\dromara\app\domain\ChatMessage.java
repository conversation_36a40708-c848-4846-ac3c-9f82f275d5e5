package org.dromara.app.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.util.List;

/**
 * 聊天消息对象 app_chat_message
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_chat_message")
public class ChatMessage extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 消息ID
     */
    @TableId(value = "id")
    private String id;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 消息角色：user/assistant/system
     */
    private String role;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 消息类型：text/image/file/voice
     */
    private String messageType;

    /**
     * 附件信息（JSON格式）
     */
    private String attachments;

    /**
     * 消息状态：0-发送中，1-发送成功，2-发送失败
     */
    private Integer status;

    /**
     * 错误信息（如果发送失败）
     */
    private String errorMessage;

    /**
     * 消息元数据（JSON格式，存储额外信息如模型名称、token消耗等）
     */
    private String metadata;

    /**
     * 父消息ID（用于消息回复链）
     */
    private String parentMessageId;

    /**
     * 消息序号（在会话中的顺序）
     */
    private Integer messageIndex;

    /**
     * 是否已读
     */
    private Boolean isRead;

    /**
     * 附件列表（不存储到数据库，用于返回给前端）
     */
    @TableField(exist = false)
    private List<MessageAttachment> attachmentList;

    /**
     * 消息元数据对象（不存储到数据库）
     */
    @TableField(exist = false)
    private MessageMetadata metadataObject;

    /**
     * 删除标志（0-正常，1-删除）
     */
    private String delFlag;

    /**
     * 消息附件内部类
     */
    @Data
    public static class MessageAttachment {
        private String type; // image/file/voice
        private String url;
        private String name;
        private Long size;
        private String mimeType;
        private String thumbnail; // 缩略图URL（仅图片）
        private Integer duration; // 时长（仅音频/视频）
    }

    /**
     * 消息元数据内部类
     */
    @Data
    public static class MessageMetadata {
        private String modelName;
        private Integer inputTokens;
        private Integer outputTokens;
        private Long responseTime;
        private String provider;
        private Double temperature;
        private String finishReason;
    }
}
