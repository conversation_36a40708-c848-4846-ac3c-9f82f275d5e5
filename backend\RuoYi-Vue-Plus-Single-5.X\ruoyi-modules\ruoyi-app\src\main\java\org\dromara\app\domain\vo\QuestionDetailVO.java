package org.dromara.app.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 题目详情VO
 *
 * <AUTHOR>
 */
@Data
public class QuestionDetailVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 题目ID
     */
    private String id;

    /**
     * 题目标题
     */
    private String title;

    /**
     * 题目描述
     */
    private String description;

    /**
     * 题目内容（Markdown格式）
     */
    private String content;

    /**
     * 参考答案（Markdown格式）
     */
    private String answer;

    /**
     * 答案解析（Markdown格式）
     */
    private String analysis;

    /**
     * 难度等级（简单/中等/困难）
     */
    private String difficulty;

    /**
     * 标签列表
     */
    private List<String> tags;

    /**
     * 通过率（百分比）
     */
    private Double acceptanceRate;

    /**
     * 是否已完成
     */
    private Boolean isCompleted;

    /**
     * 练习次数
     */
    private Integer practiceCount;

    /**
     * 正确率（百分比）
     */
    private Integer correctRate;

    /**
     * 评论数
     */
    private Integer commentCount;

    /**
     * 是否收藏
     */
    private Boolean isBookmarked;

    /**
     * 题目类型（single-单选，multiple-多选，judge-判断，essay-简答，code-编程）
     */
    private String questionType;

    /**
     * 题目选项（JSON格式，用于选择题）
     */
    private String options;

    /**
     * 正确答案
     */
    private String correctAnswer;

    /**
     * 分类
     */
    private String category;

    /**
     * 题库ID
     */
    private String bankId;

    /**
     * 题库标题
     */
    private String bankTitle;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;
}
