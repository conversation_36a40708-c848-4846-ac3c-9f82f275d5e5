package org.dromara.app.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.Book;
import org.dromara.app.mapper.BookMapper;
import org.dromara.app.repository.BookChapterRepository;
import org.dromara.app.service.IBookService;
import org.dromara.common.caffeine.annotation.CaffeineCache;
import org.dromara.common.caffeine.annotation.TimeUnit;
import org.dromara.common.core.utils.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 面试书籍Service业务层处理
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BookServiceImpl extends ServiceImpl<BookMapper, Book> implements IBookService {

    private final BookMapper bookMapper;
    private final BookChapterRepository bookChapterRepository;

    /**
     * 分页查询书籍列表
     */
    @Override
    @CaffeineCache(
        key = "#pageNum + '-' + #pageSize + '-' + #category + '-' + #searchQuery + '-' + #userId",
        condition = "#userId != null",
        expire = "3",
        timeUnit = TimeUnit.HOURS
    )
    public Page<Book> queryBookPage(Integer pageNum, Integer pageSize, String category, String searchQuery, Long userId) {
        Page<Book> page = new Page<>(pageNum, pageSize);

        // 使用自定义查询方法，支持用户信息关联
        Page<Book> result = bookMapper.selectBookPageWithUserInfo(page, category, searchQuery, userId);

        // 处理标签字段转换
        result.getRecords().forEach(this::processBookData);

        return result;
    }

    /**
     * 根据ID查询书籍详情
     */
    @Override
    @CaffeineCache(
        key = "#id + '-' + #userId",
        condition = "#userId != null",
        expire = "3",
        timeUnit = TimeUnit.HOURS
    )
    public Book queryBookDetail(Long id, Long userId) {
        if (id == null) {
            return null;
        }

        // 使用自定义查询方法，包含用户信息
        Book book = bookMapper.selectBookByIdWithUserInfo(id, userId);
        if (book != null) {
            processBookData(book);
        }

        return book;
    }

    /**
     * 查询热门书籍列表
     */
    @Override
    @CaffeineCache(
        key = "#limit",
        expire = "1",
        timeUnit = TimeUnit.HOURS
    )
    public List<Book> queryHotBooks(Integer limit) {
        if (limit == null || limit <= 0) {
            limit = 10;
        }

        List<Book> books = bookMapper.selectHotBooks(limit);
        books.forEach(this::processBookData);

        return books;
    }

    /**
     * 查询推荐书籍列表
     */
    @Override
    @CaffeineCache(
        key = "#userId + '-' + #limit",
        condition = "#userId != null",
        expire = "1",
        timeUnit = TimeUnit.HOURS
    )
    public List<Book> queryRecommendedBooks(Long userId, Integer limit) {
        if (limit == null || limit <= 0) {
            limit = 10;
        }

        List<Book> books;
        if (userId != null) {
            // 基于用户阅读历史推荐
            books = bookMapper.selectRecommendedBooks(userId, limit);
        } else {
            // 未登录用户返回热门书籍
            books = queryHotBooks(limit);
        }

        books.forEach(this::processBookData);
        return books;
    }

    /**
     * 查询分类统计信息
     */
    @Override
    @CaffeineCache(
        key = "'category-stats'",
        expire = "1",
        timeUnit = TimeUnit.HOURS
    )
    public Map<String, Object> queryCategoryStats() {
        List<Book> categoryStats = bookMapper.selectCategoryStats();

        Map<String, Object> result = new HashMap<>();
        Map<String, Long> categoryCount = new HashMap<>();

        for (Book book : categoryStats) {
            categoryCount.put(book.getCategory(), book.getReadCount().longValue());
        }

        result.put("categoryCount", categoryCount);
        result.put("totalBooks", categoryStats.size());

        return result;
    }

    /**
     * 增加书籍阅读次数
     */
    @Override
    public boolean incrementReadCount(Long bookId) {
        if (bookId == null) {
            return false;
        }

        try {
            int rows = bookMapper.incrementReadCount(bookId);
            return rows > 0;
        } catch (Exception e) {
            log.error("增加书籍阅读次数失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 新增书籍
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insertBook(Book book) {
        try {
            // 设置默认值
            if (book.getStatus() == null) {
                book.setStatus(true);
            }
            if (book.getIsFree() == null) {
                book.setIsFree(true);
            }
            if (book.getSortOrder() == null) {
                book.setSortOrder(0);
            }

            // 保存到数据库
            boolean success = save(book);

            if (success) {
                log.info("新增书籍成功: {}", book.getTitle());
            }

            return success;
        } catch (Exception e) {
            log.error("新增书籍失败: {}", e.getMessage());
            throw new RuntimeException("新增书籍失败", e);
        }
    }

    /**
     * 修改书籍
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateBook(Book book) {
        try {
            boolean success = updateById(book);

            if (success) {
                log.info("修改书籍成功: {}", book.getTitle());
            }

            return success;
        } catch (Exception e) {
            log.error("修改书籍失败: {}", e.getMessage());
            throw new RuntimeException("修改书籍失败", e);
        }
    }

    /**
     * 删除书籍
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteBooks(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return false;
        }

        try {
            // 删除书籍记录
            boolean success = removeByIds(ids);

            if (success) {
                // 异步删除相关章节数据
                for (Long id : ids) {
                    try {
                        bookChapterRepository.deleteByBookId(id);
                    } catch (Exception e) {
                        log.warn("删除书籍{}的章节数据失败: {}", id, e.getMessage());
                    }
                }

                log.info("删除书籍成功: {}", ids);
            }

            return success;
        } catch (Exception e) {
            log.error("删除书籍失败: {}", e.getMessage());
            throw new RuntimeException("删除书籍失败", e);
        }
    }

    /**
     * 上架/下架书籍
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateBookStatus(Long id, Boolean status) {
        if (id == null || status == null) {
            return false;
        }

        try {
            Book book = new Book();
            book.setId(id);
            book.setStatus(status);

            boolean success = updateById(book);

            if (success) {
                log.info("更新书籍{}状态为: {}", id, status ? "上架" : "下架");
            }

            return success;
        } catch (Exception e) {
            log.error("更新书籍状态失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 处理书籍数据（转换标签等）
     */
    private void processBookData(Book book) {
        if (book == null) {
            return;
        }

        // 处理标签字段
        if (StringUtils.isNotBlank(book.getTags())) {
            String[] tagArray = book.getTags().split(",");
            List<String> tagList = Arrays.stream(tagArray)
                .map(String::trim)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
            book.setTagList(tagList);
        } else {
            book.setTagList(new ArrayList<>());
        }

        // 处理默认值
        if (book.getReadingProgress() == null) {
            book.setReadingProgress(BigDecimal.ZERO);
        }
        if (book.getIsPurchased() == null) {
            book.setIsPurchased(book.getIsFree());
        }
    }
}
