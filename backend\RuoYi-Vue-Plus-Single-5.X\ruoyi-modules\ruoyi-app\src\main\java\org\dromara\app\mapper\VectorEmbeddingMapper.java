package org.dromara.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.dromara.app.domain.VectorEmbedding;

import java.util.List;

/**
 * 向量嵌入Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface VectorEmbeddingMapper extends BaseMapper<VectorEmbedding> {

    /**
     * 向量相似度搜索
     *
     * @param knowledgeBaseId 知识库ID
     * @param queryVector     查询向量
     * @param limit           返回数量限制
     * @param threshold       相似度阈值
     * @return 相似文档列表
     */
    @Select("SELECT *, (1 - (embedding <=> #{queryVector}::vector)) as similarity " +
        "FROM app_vector_embedding " +
        "WHERE knowledge_base_id = #{knowledgeBaseId} AND del_flag = 0 " +
        "AND (1 - (embedding <=> #{queryVector}::vector)) > #{threshold} " +
        "ORDER BY embedding <=> #{queryVector}::vector " +
        "LIMIT #{limit}")
    List<VectorEmbedding> searchSimilarVectors(@Param("knowledgeBaseId") Long knowledgeBaseId,
                                               @Param("queryVector") String queryVector,
                                               @Param("limit") Integer limit,
                                               @Param("threshold") Double threshold);

    /**
     * 混合搜索（向量相似度 + 文本匹配）
     *
     * @param knowledgeBaseId 知识库ID
     * @param queryVector     查询向量
     * @param keywords        关键词
     * @param limit           返回数量限制
     * @param threshold       相似度阈值
     * @return 相似文档列表
     */
    @Select("SELECT *, (1 - (embedding <=> #{queryVector}::vector)) as similarity " +
        "FROM app_vector_embedding " +
        "WHERE knowledge_base_id = #{knowledgeBaseId} AND del_flag = 0 " +
        "AND (content ILIKE '%' || #{keywords} || '%' OR title ILIKE '%' || #{keywords} || '%') " +
        "AND (1 - (embedding <=> #{queryVector}::vector)) > #{threshold} " +
        "ORDER BY embedding <=> #{queryVector}::vector " +
        "LIMIT #{limit}")
    List<VectorEmbedding> hybridSearch(@Param("knowledgeBaseId") Long knowledgeBaseId,
                                       @Param("queryVector") String queryVector,
                                       @Param("keywords") String keywords,
                                       @Param("limit") Integer limit,
                                       @Param("threshold") Double threshold);

    /**
     * 根据文档ID查询向量
     *
     * @param documentId 文档ID
     * @return 向量列表
     */
    @Select("SELECT * FROM app_vector_embedding WHERE document_id = #{documentId} AND del_flag = 0 ORDER BY position ASC")
    List<VectorEmbedding> selectByDocumentId(@Param("documentId") Long documentId);

    /**
     * 根据知识库ID查询向量
     *
     * @param knowledgeBaseId 知识库ID
     * @return 向量列表
     */
    @Select("SELECT * FROM app_vector_embedding WHERE knowledge_base_id = #{knowledgeBaseId} AND del_flag = 0 ORDER BY create_time DESC")
    List<VectorEmbedding> selectByKnowledgeBaseId(@Param("knowledgeBaseId") Long knowledgeBaseId);

    /**
     * 统计知识库向量数量
     *
     * @param knowledgeBaseId 知识库ID
     * @return 向量数量
     */
    @Select("SELECT COUNT(*) FROM app_vector_embedding WHERE knowledge_base_id = #{knowledgeBaseId} AND del_flag = 0")
    long countByKnowledgeBaseId(@Param("knowledgeBaseId") Long knowledgeBaseId);

    /**
     * 统计文档向量数量
     *
     * @param documentId 文档ID
     * @return 向量数量
     */
    @Select("SELECT COUNT(*) FROM app_vector_embedding WHERE document_id = #{documentId} AND del_flag = 0")
    long countByDocumentId(@Param("documentId") Long documentId);

    /**
     * 根据文档ID删除向量
     *
     * @param documentId 文档ID
     * @return 删除数量
     */
    int deleteByDocumentId(@Param("documentId") Long documentId);

    /**
     * 根据知识库ID删除向量
     *
     * @param knowledgeBaseId 知识库ID
     * @return 删除数量
     */
    int deleteByKnowledgeBaseId(@Param("knowledgeBaseId") Long knowledgeBaseId);

    /**
     * 批量插入向量
     *
     * @param embeddings 向量列表
     * @return 插入数量
     */
    int batchInsert(@Param("embeddings") List<VectorEmbedding> embeddings);

    /**
     * 根据文本块类型查询向量
     *
     * @param knowledgeBaseId 知识库ID
     * @param chunkType       文本块类型
     * @return 向量列表
     */
    @Select("SELECT * FROM app_vector_embedding WHERE knowledge_base_id = #{knowledgeBaseId} AND chunk_type = #{chunkType} AND del_flag = 0 ORDER BY position ASC")
    List<VectorEmbedding> selectByChunkType(@Param("knowledgeBaseId") Long knowledgeBaseId, @Param("chunkType") String chunkType);
}
