package org.dromara.app.service;

import com.alibaba.fastjson.JSONObject;
import org.dromara.app.domain.dto.avatar.AvatarStartDto;
import org.dromara.app.domain.dto.avatar.AvatarTextDto;
import org.dromara.app.domain.vo.avatar.AvatarSessionVo;

import java.util.function.Consumer;

/**
 * 讯飞数字人服务接口
 *
 * <AUTHOR>
 * @date 2025-07-20
 */
public interface IXunfeiAvatarService {

    /**
     * 启动数字人会话
     *
     * @param startDto 启动参数
     * @return 会话信息
     */
    AvatarSessionVo startSession(AvatarStartDto startDto);

    /**
     * 发送文本驱动
     *
     * @param sessionId 会话ID
     * @param textDto   文本参数
     * @return 是否发送成功
     */
    boolean sendTextDriver(String sessionId, AvatarTextDto textDto);

    /**
     * 发送文本交互
     *
     * @param sessionId 会话ID
     * @param textDto   文本参数
     * @return 是否发送成功
     */
    boolean sendTextInteract(String sessionId, AvatarTextDto textDto);

    /**
     * 发送音频驱动
     *
     * @param sessionId  会话ID
     * @param audioData  音频数据（Base64编码）
     * @param status     数据状态（0开始，1过渡，2结束）
     * @return 是否发送成功
     */
    boolean sendAudioDriver(String sessionId, String audioData, int status);

    /**
     * 重置（打断）数字人
     *
     * @param sessionId 会话ID
     * @return 是否重置成功
     */
    boolean resetAvatar(String sessionId);

    /**
     * 停止数字人会话
     *
     * @param sessionId 会话ID
     * @return 是否停止成功
     */
    boolean stopSession(String sessionId);

    /**
     * 发送心跳
     *
     * @param sessionId 会话ID
     * @return 是否发送成功
     */
    boolean sendHeartbeat(String sessionId);

    /**
     * 发送动作指令
     *
     * @param sessionId   会话ID
     * @param actionType  动作类型
     * @param actionValue 动作值
     * @return 是否发送成功
     */
    boolean sendAction(String sessionId, String actionType, String actionValue);

    /**
     * 设置消息处理器
     *
     * @param sessionId      会话ID
     * @param messageHandler 消息处理器
     */
    void setMessageHandler(String sessionId, Consumer<JSONObject> messageHandler);

    /**
     * 设置错误处理器
     *
     * @param sessionId    会话ID
     * @param errorHandler 错误处理器
     */
    void setErrorHandler(String sessionId, Consumer<String> errorHandler);

    /**
     * 检查会话状态
     *
     * @param sessionId 会话ID
     * @return 是否连接中
     */
    boolean isSessionActive(String sessionId);

    /**
     * 获取会话信息
     *
     * @param sessionId 会话ID
     * @return 会话信息
     */
    AvatarSessionVo getSessionInfo(String sessionId);

    /**
     * 更新会话推流地址
     *
     * @param sessionId 会话ID
     * @return 是否更新成功
     */
    boolean updateStreamUrl(String sessionId);
}
