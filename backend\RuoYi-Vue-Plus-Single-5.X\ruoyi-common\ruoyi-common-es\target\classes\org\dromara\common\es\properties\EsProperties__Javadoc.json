{"doc": "\n ES配置属性\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "enabled", "doc": "\n 是否开启\r\n"}, {"name": "hosts", "doc": "\n 集群节点地址\r\n"}, {"name": "username", "doc": "\n 用户名\r\n"}, {"name": "password", "doc": "\n 密码\r\n"}, {"name": "connectTimeout", "doc": "\n 连接超时时间（毫秒）\r\n"}, {"name": "socketTimeout", "doc": "\n 响应超时时间（毫秒）\r\n"}, {"name": "connectionRequestTimeout", "doc": "\n 连接请求超时时间（毫秒）\r\n"}, {"name": "maxConnections", "doc": "\n 最大连接数\r\n"}, {"name": "maxConnectionsPerRoute", "doc": "\n 每个路由的最大连接数\r\n"}, {"name": "defaultShards", "doc": "\n 索引默认分片数\r\n"}, {"name": "defaultReplicas", "doc": "\n 索引默认副本数\r\n"}, {"name": "sslEnabled", "doc": "\n 是否启用SSL\r\n"}, {"name": "debugEnabled", "doc": "\n 是否启用调试日志\r\n"}], "enumConstants": [], "methods": [], "constructors": []}