{"doc": "\n 角色表 sys_role\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "roleId", "doc": "\n 角色ID\r\n"}, {"name": "<PERSON><PERSON><PERSON>", "doc": "\n 角色名称\r\n"}, {"name": "<PERSON><PERSON><PERSON>", "doc": "\n 角色权限\r\n"}, {"name": "roleSort", "doc": "\n 角色排序\r\n"}, {"name": "dataScope", "doc": "\n 数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限 5：仅本人数据权限 6：部门及以下或本人数据权限）\r\n"}, {"name": "menu<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "doc": "\n 菜单树选择项是否关联显示（ 0：父子不互相关联显示 1：父子互相关联显示）\r\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "doc": "\n 部门树选择项是否关联显示（0：父子不互相关联显示 1：父子互相关联显示 ）\r\n"}, {"name": "status", "doc": "\n 角色状态（0正常 1停用）\r\n"}, {"name": "delFlag", "doc": "\n 删除标志（0代表存在 1代表删除）\r\n"}, {"name": "remark", "doc": "\n 备注\r\n"}], "enumConstants": [], "methods": [], "constructors": []}