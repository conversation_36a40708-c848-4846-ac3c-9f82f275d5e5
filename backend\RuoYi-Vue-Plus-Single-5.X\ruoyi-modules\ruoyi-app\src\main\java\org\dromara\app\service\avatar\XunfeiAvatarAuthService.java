package org.dromara.app.service.avatar;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 讯飞数字人认证服务
 *
 * <AUTHOR>
 * @date 2025-07-20
 */
@Slf4j
@Service
public class XunfeiAvatarAuthService {

    /**
     * 组装WebSocket请求URL（带认证信息）
     *
     * @param requestUrl WebSocket URL
     * @param apiKey     API Key
     * @param apiSecret  API Secret
     * @return 带认证信息的URL
     */
    public String assembleRequestUrl(String requestUrl, String apiKey, String apiSecret) {
        return assembleRequestUrl(requestUrl, apiKey, apiSecret, "GET");
    }

    /**
     * 组装WebSocket请求URL（带认证信息）
     *
     * @param requestUrl WebSocket URL
     * @param apiKey     API Key
     * @param apiSecret  API Secret
     * @param method     HTTP方法
     * @return 带认证信息的URL
     */
    public String assembleRequestUrl(String requestUrl, String apiKey, String apiSecret, String method) {
        try {
            // 转换WebSocket的URL，ws转为http，wss转为https
            String httpRequestUrl = requestUrl.replace("ws://", "http://").replace("wss://", "https://");
            URL url = new URL(httpRequestUrl);

            // 设置时间格式并设置UTC时区
            SimpleDateFormat format = new SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss z", Locale.US);
            format.setTimeZone(TimeZone.getTimeZone("UTC"));
            String date = format.format(new Date());
            String host = url.getHost();

            log.debug("认证信息 - host: {}, date: {}", host, date);

            // 构建签名字符串
            StringBuilder builder = new StringBuilder("host: ").append(host).append("\n")
                    .append("date: ").append(date).append("\n")
                    .append(method).append(" ").append(url.getPath()).append(" HTTP/1.1");

            log.debug("签名字符串: {}", builder.toString());

            Charset charset = StandardCharsets.UTF_8;
            // 生成 HMAC SHA-256 签名
            Mac mac = Mac.getInstance("hmacsha256");
            SecretKeySpec spec = new SecretKeySpec(apiSecret.getBytes(charset), "hmacsha256");
            mac.init(spec);
            byte[] hexDigits = mac.doFinal(builder.toString().getBytes(charset));
            String sha = Base64.getEncoder().encodeToString(hexDigits);

            // 生成授权头信息，将授权信息编码为 Base64，并构建最终的请求 URL
            String authorization = String.format("hmac username=\"%s\", algorithm=\"%s\", headers=\"%s\", signature=\"%s\"",
                    apiKey, "hmac-sha256", "host date request-line", sha);
            String authBase = Base64.getEncoder().encodeToString(authorization.getBytes(charset));

            log.debug("signature: {}", sha);
            log.debug("authorization: {}", authorization);
            log.debug("authBase: {}", authBase);

            return String.format("%s?authorization=%s&host=%s&date=%s",
                    requestUrl,
                    URLEncoder.encode(authBase, charset),
                    URLEncoder.encode(host, charset),
                    URLEncoder.encode(date, charset));

        } catch (Exception e) {
            log.error("组装请求URL失败", e);
            throw new RuntimeException("组装请求URL失败: " + e.getMessage(), e);
        }
    }

    /**
     * 计算签名所需要的header参数（HTTP接口）
     *
     * @param requestUrl HTTP请求URL
     * @param apiKey     API Key
     * @param apiSecret  API Secret
     * @param method     HTTP方法
     * @param body       HTTP请求体
     * @return header map，包含所有需要设置的请求头
     */
    public Map<String, String> assembleRequestHeader(String requestUrl, String apiKey, String apiSecret, String method, byte[] body) {
        try {
            URL url = new URL(requestUrl);

            // 获取日期
            SimpleDateFormat format = new SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss z", Locale.US);
            format.setTimeZone(TimeZone.getTimeZone("UTC"));
            String date = format.format(new Date());

            // 计算body摘要(SHA256)
            MessageDigest instance = MessageDigest.getInstance("SHA-256");
            instance.update(body);
            String digest = "SHA256=" + Base64.getEncoder().encodeToString(instance.digest());

            String host = url.getHost();
            int port = url.getPort();
            if (port > 0) {
                host = host + ":" + port;
            }

            String path = url.getPath();
            if ("".equals(path) || path == null) {
                path = "/";
            }

            // 构建签名计算所需参数
            StringBuilder builder = new StringBuilder()
                    .append("host: ").append(host).append("\n")
                    .append("date: ").append(date).append("\n")
                    .append(method).append(" ").append(path).append(" HTTP/1.1").append("\n")
                    .append("digest: ").append(digest);

            log.debug("HTTP认证签名字符串: {}", builder.toString());

            Charset charset = StandardCharsets.UTF_8;

            // 使用hmac-sha256计算签名
            Mac mac = Mac.getInstance("hmacsha256");
            SecretKeySpec spec = new SecretKeySpec(apiSecret.getBytes(charset), "hmacsha256");
            mac.init(spec);
            byte[] hexDigits = mac.doFinal(builder.toString().getBytes(charset));
            String sha = Base64.getEncoder().encodeToString(hexDigits);

            // 构建header
            String authorization = String.format("hmac-auth api_key=\"%s\", algorithm=\"%s\", headers=\"%s\", signature=\"%s\"",
                    apiKey, "hmac-sha256", "host date request-line digest", sha);

            Map<String, String> header = new HashMap<>();
            header.put("authorization", authorization);
            header.put("host", host);
            header.put("date", date);
            header.put("digest", digest);

            log.debug("HTTP认证header: {}", header);
            return header;

        } catch (Exception e) {
            log.error("组装请求Header失败", e);
            throw new RuntimeException("组装请求Header失败: " + e.getMessage(), e);
        }
    }
}
