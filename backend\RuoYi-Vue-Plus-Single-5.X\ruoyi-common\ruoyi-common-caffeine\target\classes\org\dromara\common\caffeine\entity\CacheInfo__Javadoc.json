{"doc": "\n 缓存信息实体\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "cacheName", "doc": "\n 缓存名称\r\n"}, {"name": "key", "doc": "\n 缓存键\r\n"}, {"name": "value", "doc": "\n 缓存值\r\n"}, {"name": "createTime", "doc": "\n 创建时间\r\n"}, {"name": "expireTime", "doc": "\n 过期时间\r\n"}, {"name": "accessCount", "doc": "\n 访问次数\r\n"}, {"name": "lastAccessTime", "doc": "\n 最后访问时间\r\n"}], "enumConstants": [], "methods": [], "constructors": []}