package org.dromara.app.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 配置管理服务
 * 提供动态配置管理功能
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
@ConfigurationProperties(prefix = "app.agent")
public class ConfigurationService {

    // 默认配置
    private Map<String, Object> defaultConfig = new HashMap<>();

    // 运行时配置
    private Map<String, Object> runtimeConfig = new HashMap<>();

    // 特性开关
    private Map<String, Boolean> featureFlags = new HashMap<>();

    public void init() {
        initDefaultConfig();
        initFeatureFlags();
        log.info("配置管理服务初始化完成");
    }

    private void initDefaultConfig() {
        // 聊天配置
        defaultConfig.put("chat.max_message_length", 4000);
        defaultConfig.put("chat.max_history_length", 20);
        defaultConfig.put("chat.response_timeout", 30000);
        defaultConfig.put("chat.stream_timeout", 60000);

        // RAG配置
        defaultConfig.put("rag.enabled", true);
        defaultConfig.put("rag.top_k", 5);
        defaultConfig.put("rag.similarity_threshold", 0.7);
        defaultConfig.put("rag.max_context_length", 4000);

        // 工具调用配置
        defaultConfig.put("tools.enabled", true);
        defaultConfig.put("tools.max_execution_time", 30000);
        defaultConfig.put("tools.max_concurrent_calls", 10);

        // 限流配置
        defaultConfig.put("rate_limit.enabled", true);
        defaultConfig.put("rate_limit.requests_per_minute", 60);
        defaultConfig.put("rate_limit.burst_capacity", 100);

        // 监控配置
        defaultConfig.put("monitoring.enabled", true);
        defaultConfig.put("monitoring.metrics_enabled", true);
        defaultConfig.put("monitoring.audit_log_enabled", true);
    }

    private void initFeatureFlags() {
        // 核心功能
        featureFlags.put("chat_enabled", true);
        featureFlags.put("stream_chat_enabled", true);
        featureFlags.put("rag_enabled", true);
        featureFlags.put("tools_enabled", true);

        // 高级功能
        featureFlags.put("advanced_rag_enabled", true);
        featureFlags.put("query_expansion_enabled", false);
        featureFlags.put("rerank_enabled", true);
        featureFlags.put("hybrid_search_enabled", true);

        // 实验性功能
        featureFlags.put("multimodal_enabled", false);
        featureFlags.put("voice_enabled", false);
        featureFlags.put("image_analysis_enabled", false);

        // 安全功能
        featureFlags.put("rate_limit_enabled", true);
        featureFlags.put("audit_log_enabled", true);
        featureFlags.put("security_monitoring_enabled", true);
    }

    /**
     * 获取配置值
     */
    @SuppressWarnings("unchecked")
    public <T> T getConfig(String key, T defaultValue) {
        try {
            // 优先从运行时配置获取
            Object value = runtimeConfig.get(key);
            if (value != null) {
                return (T) value;
            }

            // 从默认配置获取
            value = defaultConfig.get(key);
            if (value != null) {
                return (T) value;
            }

            return defaultValue;
        } catch (Exception e) {
            log.warn("获取配置失败: key={}, 使用默认值: {}", key, defaultValue, e);
            return defaultValue;
        }
    }

    /**
     * 设置运行时配置
     */
    public void setConfig(String key, Object value) {
        try {
            runtimeConfig.put(key, value);
            log.info("设置运行时配置: {}={}", key, value);
        } catch (Exception e) {
            log.error("设置配置失败: key={}, value={}", key, value, e);
        }
    }

    /**
     * 检查特性是否启用
     */
    public boolean isFeatureEnabled(String feature) {
        return featureFlags.getOrDefault(feature, false);
    }

    /**
     * 启用/禁用特性
     */
    public void setFeatureEnabled(String feature, boolean enabled) {
        featureFlags.put(feature, enabled);
        log.info("特性开关: {}={}", feature, enabled);
    }

    /**
     * 获取所有配置
     */
    public Map<String, Object> getAllConfig() {
        Map<String, Object> allConfig = new HashMap<>(defaultConfig);
        allConfig.putAll(runtimeConfig);
        return allConfig;
    }

    /**
     * 获取所有特性开关
     */
    public Map<String, Boolean> getAllFeatureFlags() {
        return new HashMap<>(featureFlags);
    }

    /**
     * 重置配置
     */
    public void resetConfig() {
        runtimeConfig.clear();
        log.info("重置运行时配置");
    }

    /**
     * 重置特性开关
     */
    public void resetFeatureFlags() {
        initFeatureFlags();
        log.info("重置特性开关");
    }

    // ========== 便捷方法 ==========

    public int getChatMaxMessageLength() {
        return getConfig("chat.max_message_length", 4000);
    }

    public int getChatMaxHistoryLength() {
        return getConfig("chat.max_history_length", 20);
    }

    public int getChatResponseTimeout() {
        return getConfig("chat.response_timeout", 30000);
    }

    public boolean isRagEnabled() {
        return isFeatureEnabled("rag_enabled") && getConfig("rag.enabled", true);
    }

    public int getRagTopK() {
        return getConfig("rag.top_k", 5);
    }

    public double getRagSimilarityThreshold() {
        return getConfig("rag.similarity_threshold", 0.7);
    }

    public boolean isToolsEnabled() {
        return isFeatureEnabled("tools_enabled") && getConfig("tools.enabled", true);
    }

    public int getToolsMaxExecutionTime() {
        return getConfig("tools.max_execution_time", 30000);
    }

    public boolean isRateLimitEnabled() {
        return isFeatureEnabled("rate_limit_enabled") && getConfig("rate_limit.enabled", true);
    }

    public int getRateLimitRequestsPerMinute() {
        return getConfig("rate_limit.requests_per_minute", 60);
    }

    public boolean isMonitoringEnabled() {
        return getConfig("monitoring.enabled", true);
    }

    public boolean isAuditLogEnabled() {
        return isFeatureEnabled("audit_log_enabled") && getConfig("monitoring.audit_log_enabled", true);
    }

    public boolean isAdvancedRagEnabled() {
        return isFeatureEnabled("advanced_rag_enabled");
    }

    public boolean isQueryExpansionEnabled() {
        return isFeatureEnabled("query_expansion_enabled");
    }

    public boolean isRerankEnabled() {
        return isFeatureEnabled("rerank_enabled");
    }

    public boolean isHybridSearchEnabled() {
        return isFeatureEnabled("hybrid_search_enabled");
    }
}
