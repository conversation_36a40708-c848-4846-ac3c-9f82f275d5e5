{"doc": "\n 用户行为 侦听器的实现\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "do<PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.String", "java.lang.Object", "java.lang.String", "cn.dev33.satoken.stp.parameter.SaLoginParameter"], "doc": "\n 每次登录时触发\r\n"}, {"name": "doLogout", "paramTypes": ["java.lang.String", "java.lang.Object", "java.lang.String"], "doc": "\n 每次注销时触发\r\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.String", "java.lang.Object", "java.lang.String"], "doc": "\n 每次被踢下线时触发\r\n"}, {"name": "doReplaced", "paramTypes": ["java.lang.String", "java.lang.Object", "java.lang.String"], "doc": "\n 每次被顶下线时触发\r\n"}, {"name": "doDisable", "paramTypes": ["java.lang.String", "java.lang.Object", "java.lang.String", "int", "long"], "doc": "\n 每次被封禁时触发\r\n"}, {"name": "doUntieDisable", "paramTypes": ["java.lang.String", "java.lang.Object", "java.lang.String"], "doc": "\n 每次被解封时触发\r\n"}, {"name": "doOpenSafe", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "long"], "doc": "\n 每次打开二级认证时触发\r\n"}, {"name": "doCloseSafe", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 每次创建Session时触发\r\n"}, {"name": "doCreateSession", "paramTypes": ["java.lang.String"], "doc": "\n 每次创建Session时触发\r\n"}, {"name": "doLogoutSession", "paramTypes": ["java.lang.String"], "doc": "\n 每次注销Session时触发\r\n"}, {"name": "doRenewTimeout", "paramTypes": ["java.lang.String", "java.lang.Object", "long"], "doc": "\n 每次Token续期时触发\r\n"}], "constructors": []}