{"doc": "\n 操作日志记录\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.system.domain.bo.SysOperLogBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 获取操作日志记录列表\r\n"}, {"name": "export", "paramTypes": ["org.dromara.system.domain.bo.SysOperLogBo", "jakarta.servlet.http.HttpServletResponse"], "doc": "\n 导出操作日志记录列表\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": "\n 批量删除操作日志记录\r\n\r\n @param operIds 日志ids\r\n"}, {"name": "clean", "paramTypes": [], "doc": "\n 清理操作日志记录\r\n"}], "constructors": []}