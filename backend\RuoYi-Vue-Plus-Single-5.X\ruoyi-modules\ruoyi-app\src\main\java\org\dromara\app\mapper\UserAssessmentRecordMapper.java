package org.dromara.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.dromara.app.domain.UserAssessmentRecord;

import java.util.List;

/**
 * 用户评估记录Mapper接口
 *
 * <AUTHOR>
 */
public interface UserAssessmentRecordMapper extends BaseMapper<UserAssessmentRecord> {

    /**
     * 根据用户ID查询评估记录列表
     *
     * @param userId 用户ID
     * @return 评估记录列表
     */
    List<UserAssessmentRecord> selectRecordsByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID和评估类型查询评估记录
     *
     * @param userId         用户ID
     * @param assessmentType 评估类型
     * @return 评估记录列表
     */
    List<UserAssessmentRecord> selectRecordsByUserIdAndType(@Param("userId") Long userId,
                                                            @Param("assessmentType") String assessmentType);

    /**
     * 根据用户ID查询最新的评估记录
     *
     * @param userId 用户ID
     * @return 最新评估记录
     */
    UserAssessmentRecord selectLatestRecordByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID和评估类型查询最新的评估记录
     *
     * @param userId         用户ID
     * @param assessmentType 评估类型
     * @return 最新评估记录
     */
    UserAssessmentRecord selectLatestRecordByUserIdAndType(@Param("userId") Long userId,
                                                           @Param("assessmentType") String assessmentType);

    /**
     * 根据用户ID查询已完成的评估记录数量
     *
     * @param userId 用户ID
     * @return 已完成评估记录数量
     */
    int countCompletedRecordsByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID和状态查询评估记录
     *
     * @param userId 用户ID
     * @param status 状态
     * @return 评估记录列表
     */
    List<UserAssessmentRecord> selectRecordsByUserIdAndStatus(@Param("userId") Long userId,
                                                              @Param("status") String status);
}
