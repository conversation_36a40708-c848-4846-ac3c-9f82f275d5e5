package org.dromara.app.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.dromara.app.domain.DocumentChunk;
import org.dromara.app.domain.KnowledgeBase;
import org.dromara.app.domain.KnowledgeDocument;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 知识库服务接口
 *
 * <AUTHOR>
 */
public interface IKnowledgeService {

    // ========== 知识库管理 ==========

    /**
     * 创建知识库
     *
     * @param knowledgeBase 知识库信息
     * @param userId        用户ID
     * @return 是否成功
     */
    boolean createKnowledgeBase(KnowledgeBase knowledgeBase, Long userId);

    /**
     * 更新知识库
     *
     * @param knowledgeBase 知识库信息
     * @param userId        用户ID
     * @return 是否成功
     */
    boolean updateKnowledgeBase(KnowledgeBase knowledgeBase, Long userId);

    /**
     * 删除知识库
     *
     * @param knowledgeBaseId 知识库ID
     * @param userId          用户ID
     * @return 是否成功
     */
    boolean deleteKnowledgeBase(Long knowledgeBaseId, Long userId);

    /**
     * 获取知识库详情
     *
     * @param knowledgeBaseId 知识库ID
     * @param userId          用户ID
     * @return 知识库详情
     */
    KnowledgeBase getKnowledgeBase(Long knowledgeBaseId, Long userId);

    /**
     * 获取用户知识库列表
     *
     * @param userId   用户ID
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @return 知识库分页结果
     */
    Page<KnowledgeBase> getUserKnowledgeBases(Long userId, Integer pageNum, Integer pageSize);

    /**
     * 获取公开知识库列表
     *
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @return 知识库分页结果
     */
    Page<KnowledgeBase> getPublicKnowledgeBases(Integer pageNum, Integer pageSize);

    /**
     * 搜索知识库
     *
     * @param keyword  搜索关键词
     * @param userId   用户ID
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @return 搜索结果
     */
    Page<KnowledgeBase> searchKnowledgeBases(String keyword, Long userId, Integer pageNum, Integer pageSize);

    // ========== 文档管理 ==========

    /**
     * 上传文档到知识库
     *
     * @param knowledgeBaseId 知识库ID
     * @param file            文件
     * @param userId          用户ID
     * @return 文档信息
     */
    KnowledgeDocument uploadDocument(Long knowledgeBaseId, MultipartFile file, Long userId);

    /**
     * 添加文本文档
     *
     * @param knowledgeBaseId 知识库ID
     * @param title           文档标题
     * @param content         文档内容
     * @param userId          用户ID
     * @return 文档信息
     */
    KnowledgeDocument addTextDocument(Long knowledgeBaseId, String title, String content, Long userId);

    /**
     * 删除文档
     *
     * @param documentId 文档ID
     * @param userId     用户ID
     * @return 是否成功
     */
    boolean deleteDocument(String documentId, Long userId);

    /**
     * 获取知识库文档列表
     *
     * @param knowledgeBaseId 知识库ID
     * @param userId          用户ID
     * @param pageNum         页码
     * @param pageSize        每页大小
     * @return 文档分页结果
     */
    Page<KnowledgeDocument> getKnowledgeBaseDocuments(Long knowledgeBaseId, Long userId,
                                                      Integer pageNum, Integer pageSize);

    /**
     * 获取文档详情
     *
     * @param documentId 文档ID
     * @param userId     用户ID
     * @return 文档详情
     */
    KnowledgeDocument getDocumentDetail(String documentId, Long userId);

    /**
     * 重新处理文档（重新分块和向量化）
     *
     * @param documentId 文档ID
     * @param userId     用户ID
     * @return 是否成功
     */
    boolean reprocessDocument(String documentId, Long userId);

    // ========== 向量搜索 ==========

    /**
     * 在知识库中搜索相关内容
     *
     * @param knowledgeBaseId 知识库ID
     * @param query           查询文本
     * @param topK            返回的top结果数
     * @param userId          用户ID
     * @return 搜索结果
     */
    List<SearchResult> searchKnowledge(Long knowledgeBaseId, String query, Integer topK, Long userId);

    /**
     * 在多个知识库中搜索
     *
     * @param knowledgeBaseIds 知识库ID列表
     * @param query            查询文本
     * @param topK             返回的top结果数
     * @param userId           用户ID
     * @return 搜索结果
     */
    List<SearchResult> searchMultipleKnowledgeBases(List<Long> knowledgeBaseIds, String query,
                                                    Integer topK, Long userId);

    /**
     * 混合搜索（向量搜索 + 关键词搜索）
     *
     * @param knowledgeBaseId 知识库ID
     * @param query           查询文本
     * @param topK            返回的top结果数
     * @param hybridAlpha     混合权重（0-1，0为纯关键词，1为纯向量）
     * @param userId          用户ID
     * @return 搜索结果
     */
    List<SearchResult> hybridSearch(Long knowledgeBaseId, String query, Integer topK,
                                    Double hybridAlpha, Long userId);

    // ========== 文档分块管理 ==========

    /**
     * 获取文档分块列表
     *
     * @param documentId 文档ID
     * @param userId     用户ID
     * @return 分块列表
     */
    List<DocumentChunk> getDocumentChunks(String documentId, Long userId);

    /**
     * 更新分块内容
     *
     * @param chunkId 分块ID
     * @param content 新内容
     * @param userId  用户ID
     * @return 是否成功
     */
    boolean updateChunkContent(String chunkId, String content, Long userId);

    /**
     * 删除分块
     *
     * @param chunkId 分块ID
     * @param userId  用户ID
     * @return 是否成功
     */
    boolean deleteChunk(String chunkId, Long userId);

    // ========== 统计和分析 ==========

    /**
     * 获取知识库统计信息
     *
     * @param knowledgeBaseId 知识库ID
     * @param userId          用户ID
     * @return 统计信息
     */
    Map<String, Object> getKnowledgeBaseStats(Long knowledgeBaseId, Long userId);

    /**
     * 获取用户知识库统计
     *
     * @param userId 用户ID
     * @return 统计信息
     */
    Map<String, Object> getUserKnowledgeStats(Long userId);

    // ========== 内部类定义 ==========

    /**
     * 搜索结果
     */
    class SearchResult {
        private String documentId;
        private String chunkId;
        private String documentTitle;
        private String content;
        private Double score;
        private String source;
        private Map<String, Object> metadata;

        public SearchResult() {
        }

        public SearchResult(String documentId, String chunkId, String content, Double score) {
            this.documentId = documentId;
            this.chunkId = chunkId;
            this.content = content;
            this.score = score;
        }

        // getters and setters
        public String getDocumentId() {
            return documentId;
        }

        public void setDocumentId(String documentId) {
            this.documentId = documentId;
        }

        public String getChunkId() {
            return chunkId;
        }

        public void setChunkId(String chunkId) {
            this.chunkId = chunkId;
        }

        public String getDocumentTitle() {
            return documentTitle;
        }

        public void setDocumentTitle(String documentTitle) {
            this.documentTitle = documentTitle;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public Double getScore() {
            return score;
        }

        public void setScore(Double score) {
            this.score = score;
        }

        public String getSource() {
            return source;
        }

        public void setSource(String source) {
            this.source = source;
        }

        public Map<String, Object> getMetadata() {
            return metadata;
        }

        public void setMetadata(Map<String, Object> metadata) {
            this.metadata = metadata;
        }
    }
}
