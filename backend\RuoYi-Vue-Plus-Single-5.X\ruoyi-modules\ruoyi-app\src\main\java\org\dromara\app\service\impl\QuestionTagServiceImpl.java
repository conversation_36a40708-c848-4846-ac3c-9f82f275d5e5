package org.dromara.app.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.QuestionTag;
import org.dromara.app.mapper.QuestionTagMapper;
import org.dromara.app.service.IQuestionTagService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 问题标签Service业务层处理
 *
 * <AUTHOR> Assistant
 * @date 2025-07-19
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class QuestionTagServiceImpl implements IQuestionTagService {

    private final QuestionTagMapper questionTagMapper;

    @Override
    public List<QuestionTag> selectByCategory(String category) {
        return questionTagMapper.selectByCategory(category);
    }

    @Override
    public List<QuestionTag> selectHotTags(Integer limit) {
        return questionTagMapper.selectHotTags(limit);
    }

    @Override
    public boolean incrementUsageCount(String tagName) {
        try {
            int result = questionTagMapper.incrementUsageCount(tagName);
            return result > 0;
        } catch (Exception e) {
            log.error("增加标签使用次数失败，tagName: {}", tagName, e);
            return false;
        }
    }

    @Override
    public List<QuestionTag> selectByNames(List<String> names) {
        if (names == null || names.isEmpty()) {
            return List.of();
        }
        return questionTagMapper.selectByNames(names);
    }

    @Override
    public TagsByCategory getAllTagsByCategory() {
        TagsByCategory result = new TagsByCategory();
        
        try {
            result.setAiTags(questionTagMapper.selectByCategory("AI"));
            result.setBigDataTags(questionTagMapper.selectByCategory("BigData"));
            result.setIotTags(questionTagMapper.selectByCategory("IoT"));
            result.setSmartSystemTags(questionTagMapper.selectByCategory("SmartSystems"));
            result.setGeneralTags(questionTagMapper.selectByCategory("General"));
        } catch (Exception e) {
            log.error("获取分类标签失败", e);
            // 返回空列表而不是null
            result.setAiTags(List.of());
            result.setBigDataTags(List.of());
            result.setIotTags(List.of());
            result.setSmartSystemTags(List.of());
            result.setGeneralTags(List.of());
        }
        
        return result;
    }

}