package org.dromara.app.mapper;

import org.apache.ibatis.annotations.Param;
import org.dromara.app.domain.Achievement;
import org.dromara.app.domain.vo.AchievementVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

/**
 * 成就定义Mapper接口
 *
 * <AUTHOR>
 */
public interface AchievementMapper extends BaseMapperPlus<Achievement, AchievementVo> {

    /**
     * 根据成就代码查询成就
     *
     * @param achievementCode 成就代码
     * @return 成就信息
     */
    Achievement selectByAchievementCode(@Param("achievementCode") String achievementCode);

    /**
     * 根据成就类型查询成就列表
     *
     * @param achievementType 成就类型
     * @return 成就列表
     */
    List<AchievementVo> selectByAchievementType(@Param("achievementType") String achievementType);

    /**
     * 查询激活的成就列表
     *
     * @return 激活的成就列表
     */
    List<AchievementVo> selectActiveAchievements();

    /**
     * 根据排序查询成就列表
     *
     * @return 按排序的成就列表
     */
    List<AchievementVo> selectOrderBySortOrder();

    /**
     * 批量更新成就状态
     *
     * @param ids      成就ID列表
     * @param isActive 是否激活
     * @return 更新数量
     */
    int batchUpdateStatus(@Param("ids") List<Long> ids, @Param("isActive") String isActive);

    /**
     * 获取总成就数
     *
     * @return 总成就数
     */
    int countTotalAchievements();

    /**
     * 根据类型获取成就数
     *
     * @param achievementType 成就类型
     * @return 该类型的成就数
     */
    int countAchievementsByType(@Param("achievementType") String achievementType);

    /**
     * 获取成就总积分
     *
     * @return 成就总积分
     */
    int sumTotalRewardPoints();

    /**
     * 根据稀有度获取成就数
     * @param rarity 稀有度
     * @return 该稀有度的成就数
     */
    int countAchievementsByRarity(String rarity);
}
