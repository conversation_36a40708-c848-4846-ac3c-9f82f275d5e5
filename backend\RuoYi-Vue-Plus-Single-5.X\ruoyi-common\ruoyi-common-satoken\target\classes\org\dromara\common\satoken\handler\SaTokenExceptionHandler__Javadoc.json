{"doc": "\n SaToken异常处理器\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "handleNotPermissionException", "paramTypes": ["cn.dev33.satoken.exception.NotPermissionException", "jakarta.servlet.http.HttpServletRequest"], "doc": "\n 权限码异常\r\n"}, {"name": "handleNotRoleException", "paramTypes": ["cn.dev33.satoken.exception.NotRoleException", "jakarta.servlet.http.HttpServletRequest"], "doc": "\n 角色权限异常\r\n"}, {"name": "handleNotLoginException", "paramTypes": ["cn.dev33.satoken.exception.NotLoginException", "jakarta.servlet.http.HttpServletRequest"], "doc": "\n 认证失败\r\n"}], "constructors": []}