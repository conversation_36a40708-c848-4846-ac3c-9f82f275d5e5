{"doc": "\n A {@link org.springframework.cache.CacheManager} implementation\r\n backed by Redisson instance.\r\n <p>\r\n 修改 RedissonSpringCacheManager 源码\r\n 重写 cacheName 处理方法 支持多参数\r\n\r\n <AUTHOR>\r\n\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["boolean"], "doc": "\n Defines possibility of storing {@code null} values.\r\n <p>\r\n Default is <code>true</code>\r\n\r\n @param allowNullValues stores if <code>true</code>\r\n"}, {"name": "setTransactionAware", "paramTypes": ["boolean"], "doc": "\n Defines if cache aware of Spring-managed transactions.\r\n If {@code true} put/evict operations are executed only for successful transaction in after-commit phase.\r\n <p>\r\n Default is <code>false</code>\r\n\r\n @param transactionAware cache is transaction aware if <code>true</code>\r\n"}, {"name": "setConfig", "paramTypes": ["java.util.Map"], "doc": "\n Set cache config mapped by cache name\r\n\r\n @param config object\r\n"}, {"name": "setCacheNames", "paramTypes": ["java.util.Collection"], "doc": "\n Defines 'fixed' cache names.\r\n A new cache instance will not be created in dynamic for non-defined names.\r\n <p>\r\n `null` parameter setups dynamic mode\r\n\r\n @param names of caches\r\n"}], "constructors": [{"name": "<init>", "paramTypes": [], "doc": "\n Creates CacheManager supplied by Redisson instance\r\n"}]}