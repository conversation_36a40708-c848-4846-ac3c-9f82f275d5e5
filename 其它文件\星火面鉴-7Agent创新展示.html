<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>星火面鉴 - 7Agent智能面试系统创新展示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .slide {
            background: white;
            margin: 30px 0;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            min-height: 600px;
        }
        
        .slide-header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 3px solid #667eea;
            padding-bottom: 20px;
        }
        
        .slide-title {
            font-size: 2.5em;
            color: #667eea;
            margin-bottom: 10px;
            font-weight: bold;
        }
        
        .slide-subtitle {
            font-size: 1.2em;
            color: #666;
            font-style: italic;
        }
        
        .agent-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }
        
        .agent-card {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            padding: 25px;
            border-radius: 12px;
            border-left: 5px solid #667eea;
            transition: transform 0.3s ease;
        }
        
        .agent-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .agent-name {
            font-size: 1.4em;
            color: #667eea;
            font-weight: bold;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        
        .agent-icon {
            width: 30px;
            height: 30px;
            background: #667eea;
            border-radius: 50%;
            margin-right: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        .feature-list {
            list-style: none;
            margin: 20px 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            padding-left: 25px;
            position: relative;
        }
        
        .feature-list li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #667eea;
            font-weight: bold;
        }
        
        .highlight-box {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .stat-item {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #667eea;
        }
        
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
        
        .innovation-section {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 10px;
            margin: 30px 0;
        }
        
        .innovation-title {
            font-size: 1.8em;
            color: #667eea;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .tech-stack {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            justify-content: center;
            margin: 20px 0;
        }
        
        .tech-item {
            background: #667eea;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 封面页 -->
        <div class="slide">
            <div class="slide-header">
                <h1 class="slide-title">星火面鉴</h1>
                <p class="slide-subtitle">基于7Agent架构的智能面试系统创新展示</p>
            </div>
            
            <div class="highlight-box">
                <h2>7个核心AI Agent协同工作</h2>
                <p>视觉Agent • 语音Agent • 文本Agent • 决策Agent • 公平性Agent • 报告Agent • 调度Agent</p>
            </div>
            
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">7</div>
                    <div class="stat-label">专业AI Agent</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">50+</div>
                    <div class="stat-label">行业模板</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">95%</div>
                    <div class="stat-label">识别准确率</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">&lt;100ms</div>
                    <div class="stat-label">实时分析延迟</div>
                </div>
            </div>
        </div>

        <!-- 模块一：智能面试管理平台 -->
        <div class="slide">
            <div class="slide-header">
                <h1 class="slide-title">模块一：智能面试管理平台</h1>
                <p class="slide-subtitle">企业体验 - 7Agent协同管理</p>
            </div>
            
            <div class="agent-grid">
                <div class="agent-card">
                    <div class="agent-name">
                        <div class="agent-icon">决</div>
                        决策Agent动态调权
                    </div>
                    <ul class="feature-list">
                        <li>销售岗自动提升表达力权重至50%</li>
                        <li>技术岗逻辑性权重至60%</li>
                        <li>岗位模型库支持50+行业模板</li>
                        <li>实时权重调整算法</li>
                        <li>多维度评估体系</li>
                    </ul>
                </div>
                
                <div class="agent-card">
                    <div class="agent-name">
                        <div class="agent-icon">文</div>
                        文本Agent深度解析
                    </div>
                    <ul class="feature-list">
                        <li>简历50+字段智能抽取</li>
                        <li>生成技能/项目/潜力三维评分</li>
                        <li>语义理解与情感分析</li>
                        <li>关键词匹配与权重计算</li>
                        <li>自然语言处理优化</li>
                    </ul>
                </div>
                
                <div class="agent-card">
                    <div class="agent-name">
                        <div class="agent-icon">公</div>
                        公平性Agent实时风控
                    </div>
                    <ul class="feature-list">
                        <li>敏感操作拦截延迟&lt;200ms</li>
                        <li>审计日志自动脱敏细粒度</li>
                        <li>权限控制，保障数据安全</li>
                        <li>偏见检测与纠正机制</li>
                        <li>合规性实时监控</li>
                    </ul>
                </div>
                
                <div class="agent-card">
                    <div class="agent-name">
                        <div class="agent-icon">调</div>
                        调度Agent智能协调
                    </div>
                    <ul class="feature-list">
                        <li>多Agent任务分配优化</li>
                        <li>资源负载均衡管理</li>
                        <li>异常处理与故障转移</li>
                        <li>性能监控与自动扩缩</li>
                        <li>服务质量保障机制</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 模块二：多模态面试体验系统 -->
        <div class="slide">
            <div class="slide-header">
                <h1 class="slide-title">模块二：多模态面试体验系统</h1>
                <p class="slide-subtitle">求职者视角 - 沉浸式智能体验</p>
            </div>

            <div class="highlight-box">
                <h3>🚀 核心创新：多模态Agent联动体验</h3>
                <p>语音Agent + 视觉Agent + 调度Agent + 报告Agent 协同工作，打造个性化面试体验</p>
            </div>

            <div class="agent-grid">
                <div class="agent-card">
                    <div class="agent-name">
                        <div class="agent-icon">语</div>
                        语音Agent - 智能语音分析师
                    </div>
                    <ul class="feature-list">
                        <li><strong>情感识别</strong>：实时分析语调变化，识别紧张、自信等情绪状态</li>
                        <li><strong>语速优化建议</strong>：动态监测语速，提供最佳表达节奏建议</li>
                        <li><strong>发音清晰度评估</strong>：专业级发音分析，提升表达专业度</li>
                        <li><strong>停顿分析</strong>：识别思考停顿vs紧张停顿，优化回答流畅度</li>
                        <li><strong>关键词提取</strong>：自动识别回答中的关键信息点</li>
                        <li><strong>语言风格分析</strong>：评估表达的专业性、逻辑性、感染力</li>
                    </ul>
                    <div class="innovation-section">
                        <strong>技术突破：</strong>基于科大讯飞语音引擎+自研情感算法，准确率>92%，支持方言识别
                    </div>
                </div>

                <div class="agent-card">
                    <div class="agent-name">
                        <div class="agent-icon">调</div>
                        调度Agent - 智能体验编排师
                    </div>
                    <ul class="feature-list">
                        <li><strong>三种面试模式</strong>：挑战型(压力↑40%)、引导型(提示↑60%)、亲和型(反馈↑80%)</li>
                        <li><strong>实时响应优化</strong>：一键面试启动延迟<1秒</li>
                        <li><strong>跨设备同步</strong>：进度同步延迟<3秒，无缝切换体验</li>
                        <li><strong>压力指数监控</strong>：30秒/次实时推送，智能调节面试强度</li>
                        <li><strong>自适应难度</strong>：根据表现动态调整问题难度</li>
                        <li><strong>时间管理</strong>：智能分配各环节时间，确保面试效率</li>
                    </ul>
                    <div class="innovation-section">
                        <strong>用户体验：</strong>个性化适配算法，用户满意度提升65%，支持断点续传
                    </div>
                </div>

                <div class="agent-card">
                    <div class="agent-name">
                        <div class="agent-icon">报</div>
                        报告Agent - 成长轨迹绘制师
                    </div>
                    <ul class="feature-list">
                        <li><strong>"能力生长树"可视化</strong>：根系展示短板，枝叶突出优势</li>
                        <li><strong>关键时刻定位</strong>：精准标记面试中的亮点与改进点</li>
                        <li><strong>成长路径规划</strong>：基于表现生成个性化提升建议</li>
                        <li><strong>对比分析</strong>：与同岗位优秀候选人的能力对比</li>
                        <li><strong>趋势分析</strong>：追踪多次面试的能力变化趋势</li>
                        <li><strong>行动计划</strong>：提供具体的能力提升行动方案</li>
                    </ul>
                    <div class="innovation-section">
                        <strong>价值创新：</strong>首创生长树可视化，让能力提升看得见，报告生成<5秒
                    </div>
                </div>
            </div>

            <div class="tech-stack">
                <div class="tech-item">沉浸体验</div>
                <div class="tech-item">个性化适配</div>
                <div class="tech-item">可视化成长</div>
                <div class="tech-item">实时反馈</div>
            </div>
        </div>

        <!-- 模块三：AI引擎-看得见的智能 -->
        <div class="slide">
            <div class="slide-header">
                <h1 class="slide-title">模块三：AI引擎 - 看得见的智能</h1>
                <p class="slide-subtitle">视频分析 - 视觉Agent三重突破</p>
            </div>

            <div class="highlight-box">
                <h3>👁️ 核心创新：视觉Agent多维度分析</h3>
                <p>人脸识别 + 表情分析 + 姿态估计 = 全方位视觉智能分析</p>
            </div>

            <div class="agent-grid">
                <div class="agent-card">
                    <div class="agent-name">
                        <div class="agent-icon">视</div>
                        视觉Agent - 多维度视觉分析师
                    </div>
                    <ul class="feature-list">
                        <li><strong>面部表情分析</strong>：7种基本表情+微表情捕捉，评估情绪真实性和稳定性</li>
                        <li><strong>眼部行为监测</strong>：眼神注视时长、眨眼频率，评估紧张度与注意力集中度</li>
                        <li><strong>肢体语言识别</strong>：手势类型识别、身体姿态分析，评估自信度与表现力</li>
                        <li><strong>整体形象评估</strong>：着装得体度、精神状态、职业形象评估</li>
                        <li><strong>实时分析能力</strong>：延迟<100ms，准确率>95%</li>
                        <li><strong>隐私保护</strong>：本地化处理，支持多种光照条件</li>
                    </ul>
                </div>

                <div class="agent-card">
                    <div class="agent-name">
                        <div class="agent-icon">技</div>
                        核心技术栈
                    </div>
                    <ul class="feature-list">
                        <li><strong>人脸识别</strong>：科大讯飞视觉Agent + OpenCV</li>
                        <li><strong>表情分析</strong>：ResNet-152+注意力机制</li>
                        <li><strong>姿态估计</strong>：OpenPose算法 + 自研优化</li>
                        <li><strong>深度学习框架</strong>：TensorFlow + PyTorch混合架构</li>
                        <li><strong>边缘计算</strong>：本地GPU加速，云端协同处理</li>
                        <li><strong>数据安全</strong>：端到端加密，符合隐私保护法规</li>
                    </ul>
                </div>

                <div class="agent-card">
                    <div class="agent-name">
                        <div class="agent-icon">优</div>
                        技术优势
                    </div>
                    <ul class="feature-list">
                        <li><strong>实时性能</strong>：视频分析延迟<100ms，支持4K高清</li>
                        <li><strong>准确性保障</strong>：多模型融合，识别准确率>95%</li>
                        <li><strong>环境适应</strong>：支持多种光照条件，自动白平衡调节</li>
                        <li><strong>设备兼容</strong>：支持PC、移动端、平板等多设备</li>
                        <li><strong>隐私安全</strong>：本地化处理，数据不上传云端</li>
                        <li><strong>可扩展性</strong>：模块化设计，支持功能定制扩展</li>
                    </ul>
                </div>
            </div>

            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">&lt;100ms</div>
                    <div class="stat-label">视频分析延迟</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">95%+</div>
                    <div class="stat-label">识别准确率</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">7+</div>
                    <div class="stat-label">表情类型识别</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">4K</div>
                    <div class="stat-label">支持视频分辨率</div>
                </div>
            </div>

            <div class="tech-stack">
                <div class="tech-item">科大讯飞AI</div>
                <div class="tech-item">OpenCV</div>
                <div class="tech-item">ResNet-152</div>
                <div class="tech-item">OpenPose</div>
                <div class="tech-item">TensorFlow</div>
                <div class="tech-item">PyTorch</div>
            </div>
        </div>
    </div>
</body>
</html>