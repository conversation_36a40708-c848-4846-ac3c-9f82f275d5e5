{"doc": "\n ES工具类\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "createIndex", "paramTypes": ["java.lang.String"], "doc": "\n 创建索引\r\n\r\n @param indexName 索引名称\r\n @return 是否成功\r\n"}, {"name": "createIndex", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 创建索引\r\n\r\n @param indexName 索引名称\r\n @param mapping   映射配置\r\n @return 是否成功\r\n"}, {"name": "deleteIndex", "paramTypes": ["java.lang.String"], "doc": "\n 删除索引\r\n\r\n @param indexName 索引名称\r\n @return 是否成功\r\n"}, {"name": "existsIndex", "paramTypes": ["java.lang.String"], "doc": "\n 判断索引是否存在\r\n\r\n @param indexName 索引名称\r\n @return 是否存在\r\n"}, {"name": "addDocument", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": "\n 添加文档\r\n\r\n @param indexName 索引名称\r\n @param document  文档内容\r\n @return 是否成功\r\n"}, {"name": "addDocument", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Object"], "doc": "\n 添加文档\r\n\r\n @param indexName 索引名称\r\n @param id        文档ID\r\n @param document  文档内容\r\n @return 是否成功\r\n"}, {"name": "batchAddDocuments", "paramTypes": ["java.lang.String", "java.util.List"], "doc": "\n 批量添加文档\r\n\r\n @param indexName 索引名称\r\n @param documents 文档列表\r\n @return 是否成功\r\n"}, {"name": "updateDocument", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Object"], "doc": "\n 更新文档\r\n\r\n @param indexName 索引名称\r\n @param id        文档ID\r\n @param document  更新内容\r\n @return 是否成功\r\n"}, {"name": "deleteDocument", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 删除文档\r\n\r\n @param indexName 索引名称\r\n @param id        文档ID\r\n @return 是否成功\r\n"}, {"name": "getDocument", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 根据ID获取文档\r\n\r\n @param indexName 索引名称\r\n @param id        文档ID\r\n @return 文档内容\r\n"}, {"name": "search", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 搜索文档\r\n\r\n @param indexName 索引名称\r\n @param keyword   关键字\r\n @return 搜索结果\r\n"}, {"name": "search", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Integer", "java.lang.Integer"], "doc": "\n 搜索文档\r\n\r\n @param indexName 索引名称\r\n @param keyword   关键字\r\n @param pageNum   页码\r\n @param pageSize  页大小\r\n @return 搜索结果\r\n"}, {"name": "search", "paramTypes": ["java.lang.String", "java.lang.String", "java.util.List"], "doc": "\n 搜索文档\r\n\r\n @param indexName 索引名称\r\n @param keyword   关键字\r\n @param fields    查询字段\r\n @return 搜索结果\r\n"}, {"name": "search", "paramTypes": ["org.dromara.common.es.entity.SearchRequest"], "doc": "\n 搜索文档\r\n\r\n @param searchRequest 搜索请求\r\n @return 搜索结果\r\n"}, {"name": "searchWithHighlight", "paramTypes": ["java.lang.String", "java.lang.String", "java.util.List"], "doc": "\n 高亮搜索\r\n\r\n @param indexName       索引名称\r\n @param keyword         关键字\r\n @param highlightFields 高亮字段\r\n @return 搜索结果\r\n"}, {"name": "searchWithFilters", "paramTypes": ["java.lang.String", "java.util.Map"], "doc": "\n 过滤搜索\r\n\r\n @param indexName 索引名称\r\n @param filters   过滤条件\r\n @return 搜索结果\r\n"}, {"name": "searchWithAggregations", "paramTypes": ["java.lang.String", "java.util.List"], "doc": "\n 聚合搜索\r\n\r\n @param indexName    索引名称\r\n @param aggregations 聚合配置\r\n @return 搜索结果\r\n"}, {"name": "builder", "paramTypes": ["java.lang.String"], "doc": "\n 构建搜索请求\r\n\r\n @param indexName 索引名称\r\n @return 搜索请求构建器\r\n"}], "constructors": []}