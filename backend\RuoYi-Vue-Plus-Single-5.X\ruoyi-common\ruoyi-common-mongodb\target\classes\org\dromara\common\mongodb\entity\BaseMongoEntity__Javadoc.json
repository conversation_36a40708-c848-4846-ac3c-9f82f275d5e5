{"doc": "\n MongoDB基础实体类\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "id", "doc": "\n 主键ID\r\n"}, {"name": "createTime", "doc": "\n 创建时间\r\n"}, {"name": "updateTime", "doc": "\n 更新时间\r\n"}, {"name": "createBy", "doc": "\n 创建者\r\n"}, {"name": "updateBy", "doc": "\n 更新者\r\n"}, {"name": "version", "doc": "\n 版本号（用于乐观锁）\r\n"}, {"name": "delFlag", "doc": "\n 是否删除（0-未删除，1-已删除）\r\n"}], "enumConstants": [], "methods": [], "constructors": []}