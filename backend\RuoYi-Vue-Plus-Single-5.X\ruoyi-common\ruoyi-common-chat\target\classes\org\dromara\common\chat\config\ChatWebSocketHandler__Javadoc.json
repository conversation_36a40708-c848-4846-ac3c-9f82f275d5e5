{"doc": "\n AI Agent聊天WebSocket处理器\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "handleStreamingMessage", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String", "java.lang.String", "org.springframework.web.socket.WebSocketSession"], "doc": "\n 处理流式消息\r\n"}, {"name": "processStreamingMessage", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String", "java.lang.String"], "doc": "\n 处理流式消息内容\r\n"}, {"name": "processMessage", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String", "cn.hutool.json.JSONObject"], "doc": "\n 处理非流式消息\r\n"}, {"name": "sendErrorMessage", "paramTypes": ["org.springframework.web.socket.WebSocketSession", "java.lang.String"], "doc": "\n 发送错误消息\r\n"}, {"name": "getActiveConnectionCount", "paramTypes": [], "doc": "\n 获取当前活跃连接数\r\n"}, {"name": "getSession", "paramTypes": ["java.lang.String"], "doc": "\n 获取指定会话\r\n"}, {"name": "broadcastMessage", "paramTypes": ["java.lang.String"], "doc": "\n 广播消息到所有连接\r\n"}], "constructors": []}