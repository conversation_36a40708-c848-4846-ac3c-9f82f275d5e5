<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>ruoyi-vue-plus</artifactId>
        <groupId>org.dromara</groupId>
        <version>5.4.0</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <modules>
        <!-- <module>dromara-generator</module> -->
        <!-- <module>dromara-job</module>       -->
        <!-- <module>dromara-workflow</module>  -->
        <!-- <module>ruoyi-demo</module>        -->
        <module>ruoyi-system</module>
        <module>ruoyi-app</module>
        <!--        <module>ruoyi-chat</module>-->
    </modules>

    <artifactId>ruoyi-modules</artifactId>
    <packaging>pom</packaging>

    <description>
        ruoyi-modules 业务模块
    </description>

</project>
