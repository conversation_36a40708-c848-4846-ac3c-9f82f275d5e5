package org.dromara.app.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pgvector.PGvector;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.text.StringEscapeUtils;
import org.dromara.app.config.OllamaConfig;
import org.dromara.app.domain.KnowledgeBase;
import org.dromara.app.domain.KnowledgeDocument;
import org.dromara.app.domain.VectorEmbedding;
import org.dromara.app.domain.bo.KnowledgeBaseBo;
import org.dromara.app.domain.bo.KnowledgeDocumentBo;
import org.dromara.app.domain.vo.KnowledgeBaseVo;
import org.dromara.app.domain.vo.KnowledgeDocumentVo;
import org.dromara.app.mapper.KnowledgeBaseMapper;
import org.dromara.app.mapper.KnowledgeDocumentMapper;
import org.dromara.app.mapper.VectorEmbeddingMapper;
import org.dromara.app.service.IEmbeddingService;
import org.dromara.app.service.IRagService;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * RAG知识库服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RagServiceImpl implements IRagService {

    // 默认配置
    private static final int DEFAULT_CHUNK_SIZE = 1000;
    private static final int DEFAULT_CHUNK_OVERLAP = 200;
    private static final int DEFAULT_TOP_K = 5;
    private final KnowledgeBaseMapper knowledgeBaseMapper;
    private final KnowledgeDocumentMapper knowledgeDocumentMapper;
    private final VectorEmbeddingMapper vectorEmbeddingMapper;
    private final IEmbeddingService embeddingService;
    private final OllamaConfig.OllamaProperties ollamaProperties;
    private final Converter converter;

    // ========== 知识库分页查询方法实现 ==========

    @Override
    public TableDataInfo<KnowledgeBaseVo> queryPageList(KnowledgeBaseBo bo) {
        LambdaQueryWrapper<KnowledgeBase> lqw = buildKnowledgeBaseQueryWrapper(bo);
        Page<KnowledgeBase> page = knowledgeBaseMapper.selectPage(bo.build(), lqw);

        // 转换分页结果
        Page<KnowledgeBaseVo> voPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        List<KnowledgeBaseVo> voList = converter.convert(page.getRecords(), KnowledgeBaseVo.class);
        voPage.setRecords(voList);

        return TableDataInfo.build(voPage);
    }

    @Override
    public List<KnowledgeBaseVo> queryList(KnowledgeBaseBo bo) {
        LambdaQueryWrapper<KnowledgeBase> lqw = buildKnowledgeBaseQueryWrapper(bo);
        List<KnowledgeBase> list = knowledgeBaseMapper.selectList(lqw);
        return converter.convert(list, KnowledgeBaseVo.class);
    }

    @Override
    public KnowledgeBaseVo queryById(Long id) {
        KnowledgeBase knowledgeBase = knowledgeBaseMapper.selectById(id);
        return converter.convert(knowledgeBase, KnowledgeBaseVo.class);
    }

    // ========== 文档分页查询方法实现 ==========

    @Override
    public TableDataInfo<KnowledgeDocumentVo> queryDocumentPageList(KnowledgeDocumentBo bo) {
        LambdaQueryWrapper<KnowledgeDocument> lqw = buildKnowledgeDocumentQueryWrapper(bo);
        Page<KnowledgeDocument> page = knowledgeDocumentMapper.selectPage(bo.build(), lqw);

        // 转换分页结果
        Page<KnowledgeDocumentVo> voPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        List<KnowledgeDocumentVo> voList = converter.convert(page.getRecords(), KnowledgeDocumentVo.class);
        voPage.setRecords(voList);

        return TableDataInfo.build(voPage);
    }

    @Override
    public List<KnowledgeDocumentVo> queryDocumentList(KnowledgeDocumentBo bo) {
        LambdaQueryWrapper<KnowledgeDocument> lqw = buildKnowledgeDocumentQueryWrapper(bo);
        List<KnowledgeDocument> list = knowledgeDocumentMapper.selectList(lqw);
        return converter.convert(list, KnowledgeDocumentVo.class);
    }

    @Override
    public KnowledgeDocumentVo queryDocumentById(Long id) {
        KnowledgeDocument document = knowledgeDocumentMapper.selectById(id);
        return converter.convert(document, KnowledgeDocumentVo.class);
    }

    // ========== 查询条件构建方法 ==========

    /**
     * 构建知识库查询条件
     */
    private LambdaQueryWrapper<KnowledgeBase> buildKnowledgeBaseQueryWrapper(KnowledgeBaseBo bo) {
        LambdaQueryWrapper<KnowledgeBase> lqw = Wrappers.lambdaQuery();

        // 基本条件
        lqw.eq(bo.getId() != null, KnowledgeBase::getId, bo.getId());
        lqw.like(StrUtil.isNotBlank(bo.getName()), KnowledgeBase::getName, bo.getName());
        lqw.like(StrUtil.isNotBlank(bo.getDescription()), KnowledgeBase::getDescription, bo.getDescription());
        lqw.eq(StrUtil.isNotBlank(bo.getType()), KnowledgeBase::getType, bo.getType());
        lqw.eq(bo.getStatus() != null, KnowledgeBase::getStatus, bo.getStatus());

        // 关键词搜索（名称或描述）
        if (StrUtil.isNotBlank(bo.getKeyword())) {
            lqw.and(wrapper -> wrapper
                .like(KnowledgeBase::getName, bo.getKeyword())
                .or()
                .like(KnowledgeBase::getDescription, bo.getKeyword())
            );
        }

        // 多选条件
        lqw.in(bo.getTypes() != null && bo.getTypes().length > 0, KnowledgeBase::getType, (Object[]) bo.getTypes());
        lqw.in(bo.getStatuses() != null && bo.getStatuses().length > 0, KnowledgeBase::getStatus, (Object[]) bo.getStatuses());

        // 时间范围
        lqw.ge(StrUtil.isNotBlank(bo.getCreateTimeStart()), KnowledgeBase::getCreateTime, bo.getCreateTimeStart());
        lqw.le(StrUtil.isNotBlank(bo.getCreateTimeEnd()), KnowledgeBase::getCreateTime, bo.getCreateTimeEnd());
        lqw.ge(StrUtil.isNotBlank(bo.getUpdateTimeStart()), KnowledgeBase::getUpdateTime, bo.getUpdateTimeStart());
        lqw.le(StrUtil.isNotBlank(bo.getUpdateTimeEnd()), KnowledgeBase::getUpdateTime, bo.getUpdateTimeEnd());

        // 数量范围
        lqw.ge(bo.getDocumentCountMin() != null, KnowledgeBase::getDocumentCount, bo.getDocumentCountMin());
        lqw.le(bo.getDocumentCountMax() != null, KnowledgeBase::getDocumentCount, bo.getDocumentCountMax());
        lqw.ge(bo.getVectorCountMin() != null, KnowledgeBase::getVectorCount, bo.getVectorCountMin());
        lqw.le(bo.getVectorCountMax() != null, KnowledgeBase::getVectorCount, bo.getVectorCountMax());

        // 排序
        lqw.orderByAsc(KnowledgeBase::getSortOrder);
        lqw.orderByDesc(KnowledgeBase::getUpdateTime);

        return lqw;
    }

    /**
     * 构建文档查询条件
     */
    private LambdaQueryWrapper<KnowledgeDocument> buildKnowledgeDocumentQueryWrapper(KnowledgeDocumentBo bo) {
        LambdaQueryWrapper<KnowledgeDocument> lqw = Wrappers.lambdaQuery();

        // 基本条件
        lqw.eq(bo.getId() != null, KnowledgeDocument::getId, bo.getId());
        lqw.eq(bo.getKnowledgeBaseId() != null, KnowledgeDocument::getKnowledgeBaseId, bo.getKnowledgeBaseId());
        lqw.like(StrUtil.isNotBlank(bo.getTitle()), KnowledgeDocument::getTitle, bo.getTitle());
        lqw.eq(StrUtil.isNotBlank(bo.getDocType()), KnowledgeDocument::getDocType, bo.getDocType());
        lqw.eq(StrUtil.isNotBlank(bo.getSource()), KnowledgeDocument::getSource, bo.getSource());
        lqw.eq(bo.getStatus() != null, KnowledgeDocument::getStatus, bo.getStatus());
        lqw.eq(bo.getProcessStatus() != null, KnowledgeDocument::getProcessStatus, bo.getProcessStatus());

        // 关键词搜索（标题或内容）
        if (StrUtil.isNotBlank(bo.getKeyword())) {
            lqw.and(wrapper -> wrapper
                .like(KnowledgeDocument::getTitle, bo.getKeyword())
                .or()
                .like(KnowledgeDocument::getContent, bo.getKeyword())
                .or()
                .like(KnowledgeDocument::getSummary, bo.getKeyword())
            );
        }

        // 多选条件
        lqw.in(bo.getDocTypes() != null && bo.getDocTypes().length > 0, KnowledgeDocument::getDocType, (Object[]) bo.getDocTypes());
        lqw.in(bo.getSources() != null && bo.getSources().length > 0, KnowledgeDocument::getSource, (Object[]) bo.getSources());
        lqw.in(bo.getStatuses() != null && bo.getStatuses().length > 0, KnowledgeDocument::getStatus, (Object[]) bo.getStatuses());
        lqw.in(bo.getProcessStatuses() != null && bo.getProcessStatuses().length > 0, KnowledgeDocument::getProcessStatus, (Object[]) bo.getProcessStatuses());

        // 时间范围
        lqw.ge(StrUtil.isNotBlank(bo.getCreateTimeStart()), KnowledgeDocument::getCreateTime, bo.getCreateTimeStart());
        lqw.le(StrUtil.isNotBlank(bo.getCreateTimeEnd()), KnowledgeDocument::getCreateTime, bo.getCreateTimeEnd());

        // 数量范围
        lqw.ge(bo.getFileSizeMin() != null, KnowledgeDocument::getFileSize, bo.getFileSizeMin());
        lqw.le(bo.getFileSizeMax() != null, KnowledgeDocument::getFileSize, bo.getFileSizeMax());
        lqw.ge(bo.getVectorCountMin() != null, KnowledgeDocument::getVectorCount, bo.getVectorCountMin());
        lqw.le(bo.getVectorCountMax() != null, KnowledgeDocument::getVectorCount, bo.getVectorCountMax());

        // 排序
        lqw.orderByAsc(KnowledgeDocument::getSortOrder);
        lqw.orderByDesc(KnowledgeDocument::getUpdateTime);

        return lqw;
    }

    // ========== 原有方法 ==========

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createKnowledgeBase(KnowledgeBase knowledgeBase) {
        try {
            // 设置默认值
            if (knowledgeBase.getVectorDimension() == null) {
                knowledgeBase.setVectorDimension(embeddingService.getVectorDimension());
            }
            if (knowledgeBase.getStatus() == null) {
                knowledgeBase.setStatus(1);
            }
            if (knowledgeBase.getDocumentCount() == null) {
                knowledgeBase.setDocumentCount(0L);
            }
            if (knowledgeBase.getVectorCount() == null) {
                knowledgeBase.setVectorCount(0L);
            }

            knowledgeBase.setLastSyncTime(LocalDateTime.now());

            return knowledgeBaseMapper.insert(knowledgeBase) > 0;

        } catch (Exception e) {
            log.error("创建知识库失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addDocument(KnowledgeDocument document) {
        try {
            // 设置默认值
            if (document.getStatus() == null) {
                document.setStatus(0); // 处理中
            }
            if (document.getProcessStatus() == null) {
                document.setProcessStatus(0); // 未处理
            }
            if (document.getVectorCount() == null) {
                document.setVectorCount(0L);
            }

            boolean success = knowledgeDocumentMapper.insert(document) > 0;

            if (success) {
                // 异步处理文档
                processDocumentAsync(document.getId());
            }

            return success;

        } catch (Exception e) {
            log.error("添加文档失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean processDocument(Long documentId) {
        try {
            KnowledgeDocument document = knowledgeDocumentMapper.selectById(documentId);
            if (document == null) {
                log.error("文档不存在: {}", documentId);
                return false;
            }

            // 更新处理状态
            knowledgeDocumentMapper.updateProcessStatus(documentId, 0);

            // 文档分块
            List<String> chunks = chunkDocument(document.getContent());

            // 生成向量并保存
            List<VectorEmbedding> embeddings = new ArrayList<>();
            for (int i = 0; i < chunks.size(); i++) {
                String chunk = chunks.get(i);
                float[] vector = embeddingService.generateEmbedding(chunk);

                if (vector.length > 0) {
                    VectorEmbedding embedding = new VectorEmbedding();
                    embedding.setKnowledgeBaseId(document.getKnowledgeBaseId());
                    embedding.setDocumentId(documentId);
                    embedding.setContent(chunk);
                    embedding.setPosition(i);
                    embedding.setContentLength(chunk.length());
                    embedding.setChunkType("paragraph");
                    embedding.setModelName(embeddingService.getModelName());
                    embedding.setDimension(vector.length);

                    // 转换向量为PGvector格式
                    embedding.setEmbedding(new PGvector(vector));

                    embeddings.add(embedding);
                }
            }

            // 批量插入向量
            if (!embeddings.isEmpty()) {
                vectorEmbeddingMapper.batchInsert(embeddings);

                // 更新文档向量数量
                knowledgeDocumentMapper.updateVectorCount(documentId, (long) embeddings.size());

                // 更新知识库向量数量
                updateKnowledgeBaseVectorCount(document.getKnowledgeBaseId());
            }

            // 更新文档状态
            knowledgeDocumentMapper.updateStatus(documentId, 1, null);
            knowledgeDocumentMapper.updateProcessStatus(documentId, 1);

            return true;

        } catch (Exception e) {
            log.error("处理文档失败: {}", documentId, e);
            knowledgeDocumentMapper.updateStatus(documentId, 2, e.getMessage());
            return false;
        }
    }

    @Override
    public List<VectorEmbedding> searchKnowledge(Long knowledgeBaseId, String query, Integer topK) {
        try {
            if (StrUtil.isBlank(query)) {
                return new ArrayList<>();
            }

            if (topK == null || topK <= 0) {
                topK = DEFAULT_TOP_K;
            }

            // 生成查询向量
            float[] queryVector = embeddingService.generateEmbedding(query);
            if (queryVector.length == 0) {
                return new ArrayList<>();
            }

            // 转换为向量字符串
            String vectorStr = vectorArrayToString(queryVector);

            // 搜索相似向量
            Double threshold = ollamaProperties.getSimilarityThreshold();
            return vectorEmbeddingMapper.searchSimilarVectors(knowledgeBaseId, vectorStr, topK, threshold);

        } catch (Exception e) {
            log.error("搜索知识失败: knowledgeBaseId={}, query={}", knowledgeBaseId, query, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<VectorEmbedding> hybridSearch(Long knowledgeBaseId, String query, Integer topK) {
        try {
            if (StrUtil.isBlank(query)) {
                return new ArrayList<>();
            }

            if (topK == null || topK <= 0) {
                topK = DEFAULT_TOP_K;
            }

            // 生成查询向量
            float[] queryVector = embeddingService.generateEmbedding(query);
            if (queryVector.length == 0) {
                return new ArrayList<>();
            }

            // 转换为向量字符串
            String vectorStr = vectorArrayToString(queryVector);

            // 混合搜索
            Double threshold = ollamaProperties.getSimilarityThreshold();
            return vectorEmbeddingMapper.hybridSearch(knowledgeBaseId, vectorStr, query, topK, threshold);

        } catch (Exception e) {
            log.error("混合搜索失败: knowledgeBaseId={}, query={}", knowledgeBaseId, query, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<KnowledgeBase> getKnowledgeBases() {
        try {
            return knowledgeBaseMapper.selectEnabledKnowledgeBases();
        } catch (Exception e) {
            log.error("获取知识库列表失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public KnowledgeBase getKnowledgeBase(Long knowledgeBaseId) {
        try {
            return knowledgeBaseMapper.selectById(knowledgeBaseId);
        } catch (Exception e) {
            log.error("获取知识库详情失败: {}", knowledgeBaseId, e);
            return null;
        }
    }

    @Override
    public List<KnowledgeDocument> getDocuments(Long knowledgeBaseId) {
        try {
            return knowledgeDocumentMapper.selectByKnowledgeBaseId(knowledgeBaseId);
        } catch (Exception e) {
            log.error("获取文档列表失败: {}", knowledgeBaseId, e);
            return new ArrayList<>();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteDocument(Long documentId) {
        try {
            KnowledgeDocument document = knowledgeDocumentMapper.selectById(documentId);
            if (document == null) {
                return false;
            }

            // 删除相关向量
            vectorEmbeddingMapper.deleteByDocumentId(documentId);

            // 删除文档
            knowledgeDocumentMapper.deleteById(documentId);

            // 更新知识库统计
            updateKnowledgeBaseStats(document.getKnowledgeBaseId());

            return true;

        } catch (Exception e) {
            log.error("删除文档失败: {}", documentId, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteKnowledgeBase(Long knowledgeBaseId) {
        try {
            // 删除所有相关向量
            vectorEmbeddingMapper.deleteByKnowledgeBaseId(knowledgeBaseId);

            // 删除所有相关文档
            List<KnowledgeDocument> documents = knowledgeDocumentMapper.selectByKnowledgeBaseId(knowledgeBaseId);
            for (KnowledgeDocument document : documents) {
                knowledgeDocumentMapper.deleteById(document.getId());
            }

            // 删除知识库
            knowledgeBaseMapper.deleteById(knowledgeBaseId);

            return true;

        } catch (Exception e) {
            log.error("删除知识库失败: {}", knowledgeBaseId, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateKnowledgeBase(KnowledgeBase knowledgeBase) {
        try {
            return knowledgeBaseMapper.updateById(knowledgeBase) > 0;
        } catch (Exception e) {
            log.error("更新知识库失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean rebuildIndex(Long knowledgeBaseId) {
        try {
            // 删除现有向量
            vectorEmbeddingMapper.deleteByKnowledgeBaseId(knowledgeBaseId);

            // 重新处理所有文档
            List<KnowledgeDocument> documents = knowledgeDocumentMapper.selectByKnowledgeBaseId(knowledgeBaseId);
            for (KnowledgeDocument document : documents) {
                processDocument(document.getId());
            }

            return true;

        } catch (Exception e) {
            log.error("重建索引失败: {}", knowledgeBaseId, e);
            return false;
        }
    }

    @Override
    public KnowledgeBaseStats getKnowledgeBaseStats(Long knowledgeBaseId) {
        try {
            KnowledgeBaseStats stats = new KnowledgeBaseStats();

            // 统计文档数量
            long documentCount = knowledgeDocumentMapper.countByKnowledgeBaseId(knowledgeBaseId);
            stats.setDocumentCount(documentCount);

            // 统计向量数量
            long vectorCount = vectorEmbeddingMapper.countByKnowledgeBaseId(knowledgeBaseId);
            stats.setVectorCount(vectorCount);

            // 获取知识库信息
            KnowledgeBase knowledgeBase = knowledgeBaseMapper.selectById(knowledgeBaseId);
            if (knowledgeBase != null) {
                stats.setLastUpdateTime(knowledgeBase.getLastSyncTime().toString());
            }

            return stats;

        } catch (Exception e) {
            log.error("获取知识库统计信息失败: {}", knowledgeBaseId, e);
            return new KnowledgeBaseStats();
        }
    }

    // ========== 私有方法 ==========

    /**
     * 异步处理文档
     */
    private void processDocumentAsync(Long documentId) {
        // 这里可以使用线程池或消息队列异步处理
        // 暂时使用同步处理
        try {
            processDocument(documentId);
        } catch (Exception e) {
            log.error("异步处理文档失败: {}", documentId, e);
        }
    }

    /**
     * 文档分块
     */
    private List<String> chunkDocument(String content) {
        if (StrUtil.isBlank(content)) {
            return new ArrayList<>();
        }

        List<String> chunks = new ArrayList<>();
        int chunkSize = DEFAULT_CHUNK_SIZE;
        int overlap = DEFAULT_CHUNK_OVERLAP;

        // 简单的分块逻辑
        int start = 0;
        while (start < content.length()) {
            int end = Math.min(start + chunkSize, content.length());
            String chunk = content.substring(start, end);

            // 避免在单词中间切断
            if (end < content.length() && !Character.isWhitespace(content.charAt(end))) {
                int lastSpace = chunk.lastIndexOf(' ');
                if (lastSpace > 0) {
                    end = start + lastSpace;
                    chunk = content.substring(start, end);
                }
            }

            chunks.add(chunk.trim());
            start = end - overlap;
        }

        return chunks;
    }

    /**
     * 更新知识库向量数量
     */
    private void updateKnowledgeBaseVectorCount(Long knowledgeBaseId) {
        try {
            long vectorCount = vectorEmbeddingMapper.countByKnowledgeBaseId(knowledgeBaseId);
            knowledgeBaseMapper.updateVectorCount(knowledgeBaseId, vectorCount);
        } catch (Exception e) {
            log.error("更新知识库向量数量失败: {}", knowledgeBaseId, e);
        }
    }

    /**
     * 更新知识库统计信息
     */
    private void updateKnowledgeBaseStats(Long knowledgeBaseId) {
        try {
            long documentCount = knowledgeDocumentMapper.countByKnowledgeBaseId(knowledgeBaseId);
            long vectorCount = vectorEmbeddingMapper.countByKnowledgeBaseId(knowledgeBaseId);

            knowledgeBaseMapper.updateDocumentCount(knowledgeBaseId, documentCount);
            knowledgeBaseMapper.updateVectorCount(knowledgeBaseId, vectorCount);
        } catch (Exception e) {
            log.error("更新知识库统计信息失败: {}", knowledgeBaseId, e);
        }
    }

    /**
     * 向量数组转换为字符串
     */
    private String vectorArrayToString(float[] vector) {
        if (vector == null || vector.length == 0) {
            return "[]";
        }
        StringBuilder sb = new StringBuilder("[");
        for (int i = 0; i < vector.length; i++) {
            if (i > 0) {
                sb.append(",");
            }
            sb.append(
                StringEscapeUtils.escapeJson(Float.toString(vector[i])
                ));
        }
        sb.append("]");
        return sb.toString();
    }
}
