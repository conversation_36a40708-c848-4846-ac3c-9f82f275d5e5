{"doc": "\n 租户配置类\r\n\r\n <AUTHOR> Li\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "tenantInit", "paramTypes": ["com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor", "org.dromara.common.tenant.properties.TenantProperties"], "doc": "\n 初始化租户配置\r\n"}, {"name": "tenantLineInnerInterceptor", "paramTypes": ["org.dromara.common.tenant.properties.TenantProperties"], "doc": "\n 多租户插件\r\n"}, {"name": "tenantCacheManager", "paramTypes": [], "doc": "\n 多租户缓存管理器\r\n"}, {"name": "tenantSaTokenDao", "paramTypes": [], "doc": "\n 多租户鉴权dao实现\r\n"}], "constructors": []}