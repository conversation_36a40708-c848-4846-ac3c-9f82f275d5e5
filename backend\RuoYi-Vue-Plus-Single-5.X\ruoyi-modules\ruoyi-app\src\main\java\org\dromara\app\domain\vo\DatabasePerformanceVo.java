package org.dromara.app.domain.vo;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 数据库性能指标视图对象
 *
 * <AUTHOR>
 * @date 2025-01-19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DatabasePerformanceVo {

    /**
     * 数据库版本
     */
    private String databaseVersion;

    /**
     * 数据库运行时间（秒）
     */
    private Long uptime;

    /**
     * 总查询数
     */
    private Long totalQueries;

    /**
     * 每秒查询数（QPS）
     */
    private Double queriesPerSecond;

    /**
     * 慢查询数量
     */
    private Long slowQueryCount;

    /**
     * 慢查询比例（百分比）
     */
    private Double slowQueryRate;

    /**
     * 平均查询时间（毫秒）
     */
    private Double averageQueryTime;

    /**
     * 连接数统计
     */
    private Map<String, Integer> connectionStats;

    /**
     * 缓冲池命中率（百分比）
     */
    private Double bufferPoolHitRate;

    /**
     * 索引使用率（百分比）
     */
    private Double indexUsageRate;

    /**
     * 表锁等待次数
     */
    private Long tableLockWaits;

    /**
     * 行锁等待次数
     */
    private Long rowLockWaits;

    /**
     * 死锁次数
     */
    private Long deadlockCount;

    /**
     * 临时表创建次数
     */
    private Long tempTableCount;

    /**
     * 磁盘临时表创建次数
     */
    private Long tempTableOnDiskCount;

    /**
     * 数据库大小（MB）
     */
    private Long databaseSize;

    /**
     * 表空间使用情况
     */
    private Map<String, Object> tablespaceUsage;

    /**
     * 最繁忙的表
     */
    private Map<String, Long> busiestTables;

    /**
     * 最大连接数
     */
    private Integer maxConnections;

    /**
     * 当前连接数
     */
    private Integer currentConnections;

    /**
     * 连接使用率（百分比）
     */
    private Double connectionUsageRate;

    /**
     * 内存使用情况
     */
    private Map<String, Object> memoryUsage;

    /**
     * 磁盘I/O统计
     */
    private Map<String, Object> diskIOStats;

    /**
     * 性能状态：excellent/good/fair/poor
     */
    private String performanceStatus;

    /**
     * 性能评分（0-100）
     */
    private Integer performanceScore;

    /**
     * 优化建议
     */
    private java.util.List<String> optimizationSuggestions;

    /**
     * 统计时间
     */
    private LocalDateTime statisticsTime;
}