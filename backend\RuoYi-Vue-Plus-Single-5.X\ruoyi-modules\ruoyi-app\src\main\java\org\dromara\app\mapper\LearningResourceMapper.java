package org.dromara.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.dromara.app.domain.LearningResource;

import java.util.List;
import java.util.Map;

/**
 * 学习资源Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface LearningResourceMapper extends BaseMapper<LearningResource> {

    /**
     * 根据技能领域和难度查询资源
     *
     * @param skillArea 技能领域
     * @param difficulty 难度等级
     * @param limit 限制数量
     * @return 资源列表
     */
    @Select("SELECT * FROM app_learning_resource " +
            "WHERE skill_area = #{skillArea} AND difficulty = #{difficulty} AND status = 'active' " +
            "ORDER BY rating DESC, rating_count DESC " +
            "LIMIT #{limit}")
    List<LearningResource> selectBySkillAreaAndDifficulty(@Param("skillArea") String skillArea, 
                                                          @Param("difficulty") String difficulty, 
                                                          @Param("limit") Integer limit);

    /**
     * 根据资源类型查询资源
     *
     * @param type 资源类型
     * @param limit 限制数量
     * @return 资源列表
     */
    @Select("SELECT * FROM app_learning_resource " +
            "WHERE type = #{type} AND status = 'active' " +
            "ORDER BY rating DESC " +
            "LIMIT #{limit}")
    List<LearningResource> selectByType(@Param("type") String type, @Param("limit") Integer limit);

    /**
     * 查询热门资源
     *
     * @param limit 限制数量
     * @return 资源列表
     */
    @Select("SELECT * FROM app_learning_resource " +
            "WHERE status = 'active' " +
            "ORDER BY JSON_EXTRACT(usage_statistics, '$.viewCount') DESC, rating DESC " +
            "LIMIT #{limit}")
    List<LearningResource> selectPopularResources(@Param("limit") Integer limit);

    /**
     * 查询免费资源
     *
     * @param skillArea 技能领域
     * @param limit 限制数量
     * @return 资源列表
     */
    @Select("SELECT * FROM app_learning_resource " +
            "WHERE skill_area = #{skillArea} AND is_free = 1 AND status = 'active' " +
            "ORDER BY rating DESC " +
            "LIMIT #{limit}")
    List<LearningResource> selectFreeResources(@Param("skillArea") String skillArea, @Param("limit") Integer limit);

    /**
     * 根据标签查询资源
     *
     * @param tag 标签
     * @param limit 限制数量
     * @return 资源列表
     */
    @Select("SELECT * FROM app_learning_resource " +
            "WHERE JSON_CONTAINS(tags, JSON_QUOTE(#{tag})) AND status = 'active' " +
            "ORDER BY rating DESC " +
            "LIMIT #{limit}")
    List<LearningResource> selectByTag(@Param("tag") String tag, @Param("limit") Integer limit);

    /**
     * 查询资源统计信息
     *
     * @return 统计信息
     */
    @Select("SELECT " +
            "COUNT(*) as total_resources, " +
            "COUNT(CASE WHEN is_free = 1 THEN 1 END) as free_resources, " +
            "COUNT(CASE WHEN rating >= 4.0 THEN 1 END) as high_quality_resources, " +
            "AVG(rating) as average_rating, " +
            "COUNT(DISTINCT skill_area) as skill_areas, " +
            "COUNT(DISTINCT provider) as providers " +
            "FROM app_learning_resource WHERE status = 'active'")
    Map<String, Object> selectResourceStatistics();

    /**
     * 查询技能领域分布
     *
     * @return 技能领域分布
     */
    @Select("SELECT skill_area, COUNT(*) as resource_count, AVG(rating) as avg_rating " +
            "FROM app_learning_resource " +
            "WHERE status = 'active' " +
            "GROUP BY skill_area " +
            "ORDER BY resource_count DESC")
    List<Map<String, Object>> selectSkillAreaDistribution();

    /**
     * 查询资源类型分布
     *
     * @return 资源类型分布
     */
    @Select("SELECT type, COUNT(*) as resource_count, AVG(rating) as avg_rating " +
            "FROM app_learning_resource " +
            "WHERE status = 'active' " +
            "GROUP BY type " +
            "ORDER BY resource_count DESC")
    List<Map<String, Object>> selectResourceTypeDistribution();

    /**
     * 查询提供者排行
     *
     * @param limit 限制数量
     * @return 提供者排行
     */
    @Select("SELECT provider, COUNT(*) as resource_count, AVG(rating) as avg_rating " +
            "FROM app_learning_resource " +
            "WHERE status = 'active' " +
            "GROUP BY provider " +
            "ORDER BY resource_count DESC, avg_rating DESC " +
            "LIMIT #{limit}")
    List<Map<String, Object>> selectTopProviders(@Param("limit") Integer limit);

    /**
     * 全文搜索资源
     *
     * @param keyword 关键词
     * @param limit 限制数量
     * @return 资源列表
     */
    @Select("SELECT * FROM app_learning_resource " +
            "WHERE (title LIKE CONCAT('%', #{keyword}, '%') " +
            "OR description LIKE CONCAT('%', #{keyword}, '%') " +
            "OR JSON_CONTAINS(tags, JSON_QUOTE(#{keyword}))) " +
            "AND status = 'active' " +
            "ORDER BY rating DESC " +
            "LIMIT #{limit}")
    List<LearningResource> searchResources(@Param("keyword") String keyword, @Param("limit") Integer limit);

    /**
     * 更新资源使用统计
     *
     * @param resourceId 资源ID
     * @param statisticsJson 统计信息JSON
     * @return 更新行数
     */
    @Select("UPDATE app_learning_resource " +
            "SET usage_statistics = #{statisticsJson} " +
            "WHERE id = #{resourceId}")
    int updateUsageStatistics(@Param("resourceId") Long resourceId, @Param("statisticsJson") String statisticsJson);

    /**
     * 查询推荐学习资源
     *
     * @param userId 用户ID（可为null，表示通用推荐）
     * @param limit 限制数量
     * @return 推荐资源列表
     */
    @Select({
        "<script>",
        "SELECT * FROM app_learning_resource",
        "WHERE status = 'active'",
        "<if test='userId != null'>",
        "AND (",
        "  -- 基于用户历史学习记录推荐相关资源",
        "  skill_area IN (",
        "    SELECT DISTINCT skill_area FROM app_learning_progress",
        "    WHERE user_id = #{userId} AND progress_percentage > 0",
        "  )",
        "  OR",
        "  -- 推荐用户未学习但相关的技能领域",
        "  skill_area IN (",
        "    SELECT DISTINCT related_skill FROM (",
        "      SELECT CASE",
        "        WHEN skill_area = 'Java' THEN 'Spring'",
        "        WHEN skill_area = 'Spring' THEN 'Spring Boot'",
        "        WHEN skill_area = 'Python' THEN '机器学习'",
        "        WHEN skill_area = '机器学习' THEN '深度学习'",
        "        WHEN skill_area = 'JavaScript' THEN 'React'",
        "        WHEN skill_area = 'React' THEN 'Node.js'",
        "        WHEN skill_area = 'SQL' THEN '数据分析'",
        "        WHEN skill_area = '数据分析' THEN '大数据'",
        "        ELSE skill_area",
        "      END as related_skill",
        "      FROM app_learning_progress",
        "      WHERE user_id = #{userId} AND progress_percentage >= 80",
        "    ) related",
        "    WHERE related_skill IS NOT NULL",
        "  )",
        ")",
        "</if>",
        "ORDER BY (",
        "  -- 综合推荐分数计算",
        "  COALESCE(rating, 0) * 0.3 +",
        "  COALESCE(JSON_EXTRACT(usage_statistics, '$.viewCount'), 0) / 1000.0 * 0.2 +",
        "  COALESCE(JSON_EXTRACT(usage_statistics, '$.completionRate'), 0) * 0.2 +",
        "  COALESCE(JSON_EXTRACT(quality_assessment, '$.overallQuality'), 0) * 0.2 +",
        "  (CASE WHEN is_free = 1 THEN 0.1 ELSE 0 END)",
        ") DESC,",
        "rating DESC,",
        "JSON_EXTRACT(usage_statistics, '$.viewCount') DESC",
        "LIMIT #{limit}",
        "</script>"
    })
    List<LearningResource> selectRecommendedResources(@Param("userId") Long userId, @Param("limit") Integer limit);

    /**
     * 根据技能差距推荐资源
     *
     * @param skillAreas 技能领域列表
     * @param difficulty 难度等级
     * @param limit 限制数量
     * @return 推荐资源列表
     */
    @Select({
        "<script>",
        "SELECT * FROM app_learning_resource",
        "WHERE status = 'active'",
        "<if test='skillAreas != null and skillAreas.size() > 0'>",
        "AND skill_area IN",
        "<foreach collection='skillAreas' item='skill' open='(' separator=',' close=')'>",
        "#{skill}",
        "</foreach>",
        "</if>",
        "<if test='difficulty != null and difficulty != \"\"'>",
        "AND difficulty = #{difficulty}",
        "</if>",
        "ORDER BY rating DESC, JSON_EXTRACT(usage_statistics, '$.completionRate') DESC",
        "LIMIT #{limit}",
        "</script>"
    })
    List<LearningResource> selectResourcesBySkillGaps(@Param("skillAreas") List<String> skillAreas, 
                                                     @Param("difficulty") String difficulty, 
                                                     @Param("limit") Integer limit);

    /**
     * 查询用户相似度推荐资源
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 推荐资源列表
     */
    @Select({
        "SELECT lr.* FROM app_learning_resource lr",
        "INNER JOIN app_learning_progress lp ON lr.skill_area = lp.skill_area",
        "WHERE lr.status = 'active'",
        "AND lp.user_id IN (",
        "  SELECT DISTINCT lp2.user_id FROM app_learning_progress lp1",
        "  INNER JOIN app_learning_progress lp2 ON lp1.skill_area = lp2.skill_area",
        "  WHERE lp1.user_id = #{userId} AND lp2.user_id != #{userId}",
        "  AND lp1.progress_percentage > 50 AND lp2.progress_percentage > 50",
        "  GROUP BY lp2.user_id",
        "  HAVING COUNT(*) >= 2",
        ")",
        "AND lr.id NOT IN (",
        "  SELECT DISTINCT resource_id FROM app_learning_progress",
        "  WHERE user_id = #{userId} AND resource_id IS NOT NULL",
        ")",
        "GROUP BY lr.id",
        "ORDER BY COUNT(*) DESC, lr.rating DESC",
        "LIMIT #{limit}"
    })
    List<LearningResource> selectCollaborativeFilteringResources(@Param("userId") Long userId, @Param("limit") Integer limit);

    /**
     * 查询趋势资源（最近热门）
     *
     * @param days 天数
     * @param limit 限制数量
     * @return 趋势资源列表
     */
    @Select({
        "SELECT * FROM app_learning_resource",
        "WHERE status = 'active'",
        "AND create_time >= DATE_SUB(NOW(), INTERVAL #{days} DAY)",
        "ORDER BY (",
        "  JSON_EXTRACT(usage_statistics, '$.viewCount') * 0.4 +",
        "  JSON_EXTRACT(usage_statistics, '$.learnerCount') * 0.3 +",
        "  rating * rating_count * 0.3",
        ") DESC",
        "LIMIT #{limit}"
    })
    List<LearningResource> selectTrendingResources(@Param("days") Integer days, @Param("limit") Integer limit);
}