package org.dromara.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.dromara.app.domain.QuestionComment;
import org.dromara.app.domain.vo.QuestionCommentVO;

import java.util.List;

/**
 * 题目评论Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface QuestionCommentMapper extends BaseMapper<QuestionComment> {

    /**
     * 分页查询题目评论列表（包含用户信息和回复列表）
     *
     * @param page           分页对象
     * @param questionId     题目ID
     * @param orderBy        排序字段
     * @param orderDirection 排序方向
     * @return 评论列表
     */
    List<QuestionCommentVO> selectCommentListWithReplies(
        @Param("page") Page<QuestionCommentVO> page,
        @Param("questionId") Long questionId,
        @Param("orderBy") String orderBy,
        @Param("orderDirection") String orderDirection
    );

    /**
     * 获取评论的回复列表
     *
     * @param parentId 父评论ID
     * @return 回复列表
     */
    List<QuestionCommentVO> selectRepliesByParentId(@Param("parentId") Long parentId);

    /**
     * 根据用户ID和评论ID查询点赞状态
     *
     * @param userId    用户ID
     * @param commentId 评论ID
     * @return 是否已点赞
     */
    Boolean selectLikeStatus(@Param("userId") Long userId, @Param("commentId") Long commentId);

    /**
     * 增加评论点赞数
     *
     * @param commentId 评论ID
     * @return 更新结果
     */
    int incrementLikeCount(@Param("commentId") Long commentId);

    /**
     * 减少评论点赞数
     *
     * @param commentId 评论ID
     * @return 更新结果
     */
    int decrementLikeCount(@Param("commentId") Long commentId);

    /**
     * 增加回复数
     *
     * @param commentId 评论ID
     * @return 更新结果
     */
    int incrementReplyCount(@Param("commentId") Long commentId);

    /**
     * 减少回复数
     *
     * @param commentId 评论ID
     * @return 更新结果
     */
    int decrementReplyCount(@Param("commentId") Long commentId);

    /**
     * 插入点赞记录
     *
     * @param commentId 评论ID
     * @param userId    用户ID
     * @return 插入结果
     */
    int insertLikeRecord(@Param("commentId") Long commentId, @Param("userId") Long userId);

    /**
     * 删除点赞记录
     *
     * @param commentId 评论ID
     * @param userId    用户ID
     * @return 删除结果
     */
    int deleteLikeRecord(@Param("commentId") Long commentId, @Param("userId") Long userId);

    /**
     * 更新题目评论数
     *
     * @param questionId 题目ID
     * @param increment  增量（正数增加，负数减少）
     * @return 更新结果
     */
    int updateQuestionCommentCount(@Param("questionId") Long questionId, @Param("increment") Integer increment);
}
