<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.app.mapper.JobMapper">

    <resultMap type="org.dromara.app.domain.vo.JobVo" id="JobVoResult">
        <result property="id" column="id"/>
        <result property="categoryId" column="category_id"/>
        <result property="name" column="name"/>
        <result property="company" column="company"/>
        <result property="logo" column="logo"/>
        <result property="difficulty" column="difficulty"/>
        <result property="duration" column="duration"/>
        <result property="questionCount" column="question_count"/>
        <result property="tags" column="tags" typeHandler="org.dromara.common.json.handler.JsonStringListTypeHandler"/>
        <result property="description" column="description"/>
        <result property="interviewers" column="interviewers"/>
        <result property="passRate" column="pass_rate"/>
        <result property="viewCount" column="view_count"/>
        <result property="favoriteCount" column="favorite_count"/>
        <result property="isFavorited" column="is_favorited"/>
        <result property="sortOrder" column="sort_order"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="selectJobPageWithFavorite" resultMap="JobVoResult">
        SELECT j.*,
        CONCAT(j.pass_rate, '%') as pass_rate,
        CASE WHEN f.id IS NOT NULL THEN 1 ELSE 0 END as is_favorited
        FROM app_job j
        LEFT JOIN app_job_favorite f ON j.id = f.job_id AND f.user_id = #{query.userId}
        <where>
            j.del_flag = '0' AND j.status = '0'
            <if test="query.categoryId != null">
                AND j.category_id = #{query.categoryId}
            </if>
            <if test="query.keyword != null and query.keyword != ''">
                AND (
                    j.name LIKE CONCAT('%', #{query.keyword}, '%')
                    OR j.company LIKE CONCAT('%', #{query.keyword}, '%')
                    OR j.description LIKE CONCAT('%', #{query.keyword}, '%')
                    OR JSON_SEARCH(j.tags, 'one', CONCAT('%', #{query.keyword}, '%')) IS NOT NULL
                )
            </if>
            <if test="query.filters != null and !query.filters.isEmpty()">
                <foreach collection="query.filters" item="filter" open="AND (" separator=" OR " close=")">
                    <if test="filter == 'hot'">j.interviewers &gt;= 50</if>
                    <if test="filter == 'new'">DATEDIFF(NOW(), j.create_time) &lt;= 7</if>
                    <if test="filter == 'easy'">j.difficulty &lt;= 2</if>
                    <if test="filter == 'hard'">j.difficulty &gt;= 4</if>
                </foreach>
            </if>
        </where>
        <choose>
            <when test="query.sortBy == 'smart'">   
                ORDER BY (j.interviewers * 0.3 + (100 - ABS(j.pass_rate - 65)) * 0.25 + (5 - ABS(j.difficulty - 3)) *
                0.2 + (60 - ABS(j.duration - 37.5)) * 0.15 + (20 - ABS(j.question_count - 12.5)) * 0.1) DESC
            </when>
            <when test="query.sortBy == 'hot'">
                ORDER BY j.interviewers DESC, j.view_count DESC
            </when>
            <when test="query.sortBy == 'difficulty' or query.sortBy == 'difficulty_desc'">
                ORDER BY j.difficulty DESC, j.interviewers DESC
            </when>
            <when test="query.sortBy == 'difficulty_asc'">
                ORDER BY j.difficulty ASC, j.interviewers DESC
            </when>
            <when test="query.sortBy == 'duration' or query.sortBy == 'duration_desc'">
                ORDER BY j.duration DESC, j.interviewers DESC
            </when>
            <when test="query.sortBy == 'duration_asc'">
                ORDER BY j.duration ASC, j.interviewers DESC
            </when>
            <when test="query.sortBy == 'pass_rate' or query.sortBy == 'pass_rate_desc'">
                ORDER BY j.pass_rate DESC, j.interviewers DESC
            </when>
            <when test="query.sortBy == 'pass_rate_asc'">
                ORDER BY j.pass_rate ASC, j.interviewers DESC
            </when>
            <when test="query.sortBy == 'created_time' or query.sortBy == 'created_time_desc'">
                ORDER BY j.create_time DESC, j.interviewers DESC
            </when>
            <when test="query.sortBy == 'created_time_asc'">
                ORDER BY j.create_time ASC, j.interviewers DESC
            </when>
            <otherwise>
                ORDER BY j.sort_order ASC, j.create_time DESC
            </otherwise>
        </choose>
    </select>

    <select id="selectRecommendedJobs" resultMap="JobVoResult">
        SELECT j.*,
               CONCAT(j.pass_rate, '%')                     as pass_rate,
               CASE WHEN f.id IS NOT NULL THEN 1 ELSE 0 END as is_favorited
        FROM app_job j
                 LEFT JOIN app_job_favorite f ON j.id = f.job_id AND f.user_id = #{userId}
        WHERE j.del_flag = '0'
          AND j.status = '0'
        ORDER BY j.interviewers DESC, j.pass_rate DESC
        LIMIT #{limit}
    </select>

    <select id="selectHotJobs" resultMap="JobVoResult">
        SELECT j.*,
               CONCAT(j.pass_rate, '%')                     as pass_rate,
               CASE WHEN f.id IS NOT NULL THEN 1 ELSE 0 END as is_favorited
        FROM app_job j
                 LEFT JOIN app_job_favorite f ON j.id = f.job_id AND f.user_id = #{userId}
        WHERE j.del_flag = '0'
          AND j.status = '0'
        ORDER BY j.view_count DESC, j.interviewers DESC
        LIMIT #{limit}
    </select>

</mapper>
