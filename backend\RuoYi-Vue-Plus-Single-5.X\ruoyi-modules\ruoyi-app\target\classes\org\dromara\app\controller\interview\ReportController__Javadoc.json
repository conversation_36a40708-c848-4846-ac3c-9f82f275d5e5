{"doc": " 面试报告控制器\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "generateReport", "paramTypes": ["java.lang.String"], "doc": " 生成面试报告\n\n @param sessionId 面试会话ID\n @return 面试报告\n"}, {"name": "generatePdfReport", "paramTypes": ["java.lang.String"], "doc": " 生成PDF报告\n\n @param sessionId 面试会话ID\n @return PDF文件路径\n"}, {"name": "downloadPdfReport", "paramTypes": ["java.lang.String", "jakarta.servlet.http.HttpServletResponse"], "doc": " 下载PDF报告\n\n @param sessionId 面试会话ID\n @param response HTTP响应\n"}, {"name": "getRadarChartData", "paramTypes": ["java.lang.String"], "doc": " 获取雷达图数据\n\n @param sessionId 面试会话ID\n @return 雷达图数据\n"}], "constructors": []}