package org.dromara.app.service;

import org.dromara.app.domain.dto.AnalysisTaskDto;
import org.dromara.app.domain.vo.TaskQueueStatusVo;

import java.util.List;

/**
 * 任务队列管理服务接口
 * 用于管理分析任务的队列和调度
 *
 * <AUTHOR>
 * @date 2025-01-19
 */
public interface ITaskQueueService {

    /**
     * 启动任务队列处理器
     */
    void startTaskProcessor();

    /**
     * 停止任务队列处理器
     */
    void stopTaskProcessor();

    /**
     * 获取队列状态
     *
     * @return 队列状态信息
     */
    TaskQueueStatusVo getQueueStatus();

    /**
     * 获取待处理任务列表
     *
     * @param limit 限制数量
     * @return 任务列表
     */
    List<AnalysisTaskDto> getPendingTasks(int limit);

    /**
     * 获取正在执行的任务列表
     *
     * @return 任务列表
     */
    List<AnalysisTaskDto> getRunningTasks();

    /**
     * 获取已完成的任务列表
     *
     * @param limit 限制数量
     * @return 任务列表
     */
    List<AnalysisTaskDto> getCompletedTasks(int limit);

    /**
     * 清理过期任务
     *
     * @return 清理的任务数量
     */
    int cleanExpiredTasks();

    /**
     * 重试失败的任务
     *
     * @param taskId 任务ID
     * @return 是否重试成功
     */
    boolean retryFailedTask(String taskId);

    /**
     * 设置队列最大容量
     *
     * @param maxCapacity 最大容量
     */
    void setMaxQueueCapacity(int maxCapacity);

    /**
     * 设置并发执行数量
     *
     * @param concurrency 并发数量
     */
    void setConcurrency(int concurrency);

    /**
     * 暂停队列处理
     */
    void pauseQueue();

    /**
     * 恢复队列处理
     */
    void resumeQueue();

    /**
     * 获取队列是否暂停
     *
     * @return 是否暂停
     */
    boolean isQueuePaused();
}