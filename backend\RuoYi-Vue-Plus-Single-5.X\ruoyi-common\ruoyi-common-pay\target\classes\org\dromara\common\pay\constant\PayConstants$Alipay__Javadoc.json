{"doc": "\n 支付宝相关常量\r\n", "fields": [{"name": "TRADE_STATUS_WAIT_BUYER_PAY", "doc": "\n 支付宝交易状态 - 交易创建，等待买家付款\r\n"}, {"name": "TRADE_STATUS_TRADE_CLOSED", "doc": "\n 支付宝交易状态 - 未付款交易超时关闭，或支付完成后全额退款\r\n"}, {"name": "TRADE_STATUS_TRADE_SUCCESS", "doc": "\n 支付宝交易状态 - 交易支付成功\r\n"}, {"name": "TRADE_STATUS_TRADE_FINISHED", "doc": "\n 支付宝交易状态 - 交易结束，不可退款\r\n"}, {"name": "PRODUCT_CODE_FAST_INSTANT_TRADE_PAY", "doc": "\n 支付宝产品码 - 快捷支付产品\r\n"}, {"name": "PRODUCT_CODE_QUICK_WAP_WAY", "doc": "\n 支付宝产品码 - 手机网站支付产品\r\n"}, {"name": "PRODUCT_CODE_QUICK_MSECURITY_PAY", "doc": "\n 支付宝产品码 - APP支付产品\r\n"}, {"name": "NOTIFY_VERIFY_SUCCESS", "doc": "\n 支付宝异步通知验证成功\r\n"}, {"name": "NOTIFY_VERIFY_FAILURE", "doc": "\n 支付宝异步通知验证失败\r\n"}], "enumConstants": [], "methods": [], "constructors": []}