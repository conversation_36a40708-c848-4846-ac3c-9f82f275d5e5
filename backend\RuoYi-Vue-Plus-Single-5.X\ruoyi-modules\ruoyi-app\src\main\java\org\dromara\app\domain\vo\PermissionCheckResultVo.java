package org.dromara.app.domain.vo;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 权限检查结果视图对象
 *
 * <AUTHOR>
 * @date 2025-01-19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PermissionCheckResultVo {

    /**
     * 检查ID
     */
    private String checkId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 资源标识
     */
    private String resource;

    /**
     * 操作类型
     */
    private String action;

    /**
     * 是否有权限
     */
    private Boolean hasPermission;

    /**
     * 检查结果：granted/denied/expired/suspended
     */
    private String result;

    /**
     * 拒绝原因
     */
    private String denyReason;

    /**
     * 权限来源：role/user/group
     */
    private String permissionSource;

    /**
     * 相关角色列表
     */
    private List<String> roles;

    /**
     * 权限过期时间
     */
    private LocalDateTime permissionExpireTime;

    /**
     * 检查耗时（毫秒）
     */
    private Long checkDuration;

    /**
     * 检查时间
     */
    private LocalDateTime checkTime;

    /**
     * 客户端IP
     */
    private String clientIp;

    /**
     * 用户代理
     */
    private String userAgent;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 是否需要额外验证
     */
    private Boolean needsAdditionalAuth;

    /**
     * 额外验证类型
     */
    private String additionalAuthType;

    /**
     * 风险评分（0-100）
     */
    private Integer riskScore;

    /**
     * 风险因素
     */
    private List<String> riskFactors;

    /**
     * 访问频率限制状态
     */
    private String rateLimitStatus;

    /**
     * 剩余访问次数
     */
    private Integer remainingAccess;

    /**
     * 限制重置时间
     */
    private LocalDateTime rateLimitResetTime;

    /**
     * 审计信息
     */
    private String auditInfo;

    /**
     * 是否记录日志
     */
    private Boolean logRecorded;
}