package org.dromara.app.event;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.dto.TrackEventDto;
import org.dromara.app.service.IAchievementService;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 简化版成就事件监听器
 * 专注于核心的成就检查功能
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SimpleAchievementEventListener {

    private final IAchievementService achievementService;

    /**
     * 监听用户登录事件
     */
    @Async
    @EventListener
    public void handleUserLoginEvent(UserLoginEvent event) {
        log.info("处理用户登录事件: userId={}", event.getUserId());

        try {
            // 创建埋点事件
            TrackEventDto trackEvent = new TrackEventDto();
            trackEvent.setUserId(Long.valueOf(event.getUserId()));
            trackEvent.setEventType("LOGIN");
            trackEvent.setTimestamp(System.currentTimeMillis());
            
            Map<String, Object> eventData = new HashMap<>();
            eventData.put("loginTime", event.getLoginTime().toString());
            eventData.put("loginSource", event.getSource());
            trackEvent.setEventData(eventData);

            // 检查登录相关成就
            achievementService.checkAchievements(Long.valueOf(event.getUserId()), trackEvent);

        } catch (Exception e) {
            log.error("处理用户登录事件失败: userId={}", event.getUserId(), e);
        }
    }

    /**
     * 监听视频观看事件
     */
    @Async
    @EventListener
    public void handleVideoWatchEvent(VideoWatchEvent event) {
        log.info("处理视频观看事件: userId={}, videoId={}", event.getUserId(), event.getVideoId());

        try {
            // 创建埋点事件
            TrackEventDto trackEvent = new TrackEventDto();
            trackEvent.setUserId(Long.valueOf(event.getUserId()));
            trackEvent.setEventType("VIDEO_WATCH");
            trackEvent.setTimestamp(System.currentTimeMillis());
            
            Map<String, Object> eventData = new HashMap<>();
            eventData.put("videoId", event.getVideoId());
            eventData.put("videoTitle", event.getVideoTitle());
            eventData.put("watchDuration", event.getWatchDuration());
            trackEvent.setEventData(eventData);

            // 检查视频观看相关成就
            achievementService.checkAchievements(Long.valueOf(event.getUserId()), trackEvent);

        } catch (Exception e) {
            log.error("处理视频观看事件失败: userId={}", event.getUserId(), e);
        }
    }

    /**
     * 监听评论事件
     */
    @Async
    @EventListener
    public void handleCommentEvent(CommentEvent event) {
        log.info("处理评论事件: userId={}, targetType={}, targetId={}", 
            event.getUserId(), event.getTargetType(), event.getTargetId());

        try {
            // 创建埋点事件
            TrackEventDto trackEvent = new TrackEventDto();
            trackEvent.setUserId(Long.valueOf(event.getUserId()));
            trackEvent.setEventType("COMMENT");
            trackEvent.setTimestamp(System.currentTimeMillis());
            
            Map<String, Object> eventData = new HashMap<>();
            eventData.put("targetType", event.getTargetType());
            eventData.put("targetId", event.getTargetId());
            eventData.put("commentContent", event.getCommentContent());
            trackEvent.setEventData(eventData);

            // 检查评论相关成就
            achievementService.checkAchievements(Long.valueOf(event.getUserId()), trackEvent);

        } catch (Exception e) {
            log.error("处理评论事件失败: userId={}", event.getUserId(), e);
        }
    }

    /**
     * 监听点赞事件
     */
    @Async
    @EventListener
    public void handleLikeEvent(LikeEvent event) {
        log.info("处理点赞事件: userId={}, targetType={}, targetId={}", 
            event.getUserId(), event.getTargetType(), event.getTargetId());

        try {
            // 创建埋点事件
            TrackEventDto trackEvent = new TrackEventDto();
            trackEvent.setUserId(Long.valueOf(event.getUserId()));
            trackEvent.setEventType("LIKE");
            trackEvent.setTimestamp(System.currentTimeMillis());
            
            Map<String, Object> eventData = new HashMap<>();
            eventData.put("targetType", event.getTargetType());
            eventData.put("targetId", event.getTargetId());
            trackEvent.setEventData(eventData);

            // 检查点赞相关成就
            achievementService.checkAchievements(Long.valueOf(event.getUserId()), trackEvent);

        } catch (Exception e) {
            log.error("处理点赞事件失败: userId={}", event.getUserId(), e);
        }
    }

    /**
     * 监听学习时长事件
     */
    @Async
    @EventListener
    public void handleStudyTimeEvent(StudyTimeEvent event) {
        log.info("处理学习时长事件: userId={}, studyMinutes={}", event.getUserId(), event.getStudyMinutes());

        try {
            // 创建埋点事件
            TrackEventDto trackEvent = new TrackEventDto();
            trackEvent.setUserId(Long.valueOf(event.getUserId()));
            trackEvent.setEventType("STUDY_TIME");
            trackEvent.setTimestamp(System.currentTimeMillis());
            
            Map<String, Object> eventData = new HashMap<>();
            eventData.put("studyMinutes", event.getStudyMinutes());
            eventData.put("studyDate", event.getStudyDate().toString());
            trackEvent.setEventData(eventData);

            // 检查学习时长相关成就
            achievementService.checkAchievements(Long.valueOf(event.getUserId()), trackEvent);

        } catch (Exception e) {
            log.error("处理学习时长事件失败: userId={}", event.getUserId(), e);
        }
    }

    // ==================== 事件类定义 ====================

    /**
     * 用户登录事件
     */
    public static class UserLoginEvent {
        private final String userId;
        private final LocalDateTime loginTime;
        private final String source;

        public UserLoginEvent(String userId, String source) {
            this.userId = userId;
            this.loginTime = LocalDateTime.now();
            this.source = source;
        }

        public String getUserId() { return userId; }
        public LocalDateTime getLoginTime() { return loginTime; }
        public String getSource() { return source; }
    }

    /**
     * 视频观看事件
     */
    public static class VideoWatchEvent {
        private final String userId;
        private final String videoId;
        private final String videoTitle;
        private final Integer watchDuration;
        private final LocalDateTime watchTime;

        public VideoWatchEvent(String userId, String videoId, String videoTitle, Integer watchDuration) {
            this.userId = userId;
            this.videoId = videoId;
            this.videoTitle = videoTitle;
            this.watchDuration = watchDuration;
            this.watchTime = LocalDateTime.now();
        }

        public String getUserId() { return userId; }
        public String getVideoId() { return videoId; }
        public String getVideoTitle() { return videoTitle; }
        public Integer getWatchDuration() { return watchDuration; }
        public LocalDateTime getWatchTime() { return watchTime; }
    }

    /**
     * 评论事件
     */
    public static class CommentEvent {
        private final String userId;
        private final String targetType;
        private final String targetId;
        private final String commentContent;
        private final LocalDateTime commentTime;

        public CommentEvent(String userId, String targetType, String targetId, String commentContent) {
            this.userId = userId;
            this.targetType = targetType;
            this.targetId = targetId;
            this.commentContent = commentContent;
            this.commentTime = LocalDateTime.now();
        }

        public String getUserId() { return userId; }
        public String getTargetType() { return targetType; }
        public String getTargetId() { return targetId; }
        public String getCommentContent() { return commentContent; }
        public LocalDateTime getCommentTime() { return commentTime; }
    }

    /**
     * 点赞事件
     */
    public static class LikeEvent {
        private final String userId;
        private final String targetType;
        private final String targetId;
        private final LocalDateTime likeTime;

        public LikeEvent(String userId, String targetType, String targetId) {
            this.userId = userId;
            this.targetType = targetType;
            this.targetId = targetId;
            this.likeTime = LocalDateTime.now();
        }

        public String getUserId() { return userId; }
        public String getTargetType() { return targetType; }
        public String getTargetId() { return targetId; }
        public LocalDateTime getLikeTime() { return likeTime; }
    }

    /**
     * 学习时长事件
     */
    public static class StudyTimeEvent {
        private final String userId;
        private final Integer studyMinutes;
        private final LocalDateTime studyDate;

        public StudyTimeEvent(String userId, Integer studyMinutes) {
            this.userId = userId;
            this.studyMinutes = studyMinutes;
            this.studyDate = LocalDateTime.now();
        }

        public String getUserId() { return userId; }
        public Integer getStudyMinutes() { return studyMinutes; }
        public LocalDateTime getStudyDate() { return studyDate; }
    }

}
