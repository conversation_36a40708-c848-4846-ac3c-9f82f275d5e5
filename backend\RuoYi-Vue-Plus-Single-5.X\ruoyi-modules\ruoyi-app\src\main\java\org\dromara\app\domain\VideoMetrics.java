package org.dromara.app.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 视频指标对象 app_video_metrics
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@Data
@TableName("app_video_metrics")
public class VideoMetrics implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 视频指标ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 结果ID
     */
    private String resultId;

    /**
     * 眼神交流
     */
    private Integer eyeContact;

    /**
     * 姿态
     */
    private Integer posture;

    /**
     * 表情
     */
    private Integer expressions;

    /**
     * 手势
     */
    private Integer gestures;

    /**
     * 总体评分
     */
    private Integer overall;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

}
