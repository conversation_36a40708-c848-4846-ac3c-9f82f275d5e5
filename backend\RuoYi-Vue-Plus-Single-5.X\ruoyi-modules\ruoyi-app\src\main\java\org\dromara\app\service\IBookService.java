package org.dromara.app.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.app.domain.Book;

import java.util.List;
import java.util.Map;

/**
 * 面试书籍Service接口
 *
 * <AUTHOR>
 */
public interface IBookService extends IService<Book> {

    /**
     * 分页查询书籍列表
     *
     * @param pageNum     页码
     * @param pageSize    每页大小
     * @param category    分类筛选
     * @param searchQuery 搜索关键词
     * @param userId      用户ID
     * @return 分页结果
     */
    Page<Book> queryBookPage(Integer pageNum, Integer pageSize, String category, String searchQuery, Long userId);

    /**
     * 根据ID查询书籍详情
     *
     * @param id     书籍ID
     * @param userId 用户ID
     * @return 书籍详情
     */
    Book queryBookDetail(Long id, Long userId);

    /**
     * 查询热门书籍列表
     *
     * @param limit 限制数量
     * @return 热门书籍列表
     */
    List<Book> queryHotBooks(Integer limit);

    /**
     * 查询推荐书籍列表
     *
     * @param userId 用户ID
     * @param limit  限制数量
     * @return 推荐书籍列表
     */
    List<Book> queryRecommendedBooks(Long userId, Integer limit);

    /**
     * 查询分类统计信息
     *
     * @return 分类统计
     */
    Map<String, Object> queryCategoryStats();

    /**
     * 增加书籍阅读次数
     *
     * @param bookId 书籍ID
     * @return 是否成功
     */
    boolean incrementReadCount(Long bookId);

    /**
     * 新增书籍
     *
     * @param book 书籍信息
     * @return 是否成功
     */
    boolean insertBook(Book book);

    /**
     * 修改书籍
     *
     * @param book 书籍信息
     * @return 是否成功
     */
    boolean updateBook(Book book);

    /**
     * 删除书籍
     *
     * @param ids 书籍ID列表
     * @return 是否成功
     */
    boolean deleteBooks(List<Long> ids);

    /**
     * 上架/下架书籍
     *
     * @param id     书籍ID
     * @param status 状态
     * @return 是否成功
     */
    boolean updateBookStatus(Long id, Boolean status);
}
