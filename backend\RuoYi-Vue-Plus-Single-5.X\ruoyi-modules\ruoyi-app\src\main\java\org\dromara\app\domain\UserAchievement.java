package org.dromara.app.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 用户成就关联对象 app_user_achievement
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_user_achievement")
public class UserAchievement extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 成就ID
     */
    private Long achievementId;

    /**
     * 解锁时间
     */
    private Date unlockTime;

    /**
     * 进度百分比(0-100)
     */
    private BigDecimal progress;

    /**
     * 当前数值
     */
    private Long currentValue;

    /**
     * 目标数值
     */
    private Long targetValue;

    /**
     * 是否完成(0否 1是)
     */
    private String isCompleted;

    /**
     * 是否已通知(0否 1是)
     */
    private String isNotified;

}
