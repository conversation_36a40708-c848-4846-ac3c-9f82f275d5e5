package org.dromara.app.service.impl;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.vo.PaymentOrderVo;
import org.dromara.app.event.PaymentEvent;
import org.dromara.app.service.IPaymentService;
import org.dromara.app.service.IPaymentSseService;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 支付SSE服务实现类
 * 管理SSE连接和推送支付状态消息
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PaymentSseServiceImpl implements IPaymentSseService {

    /**
     * SSE连接超时时间（毫秒）- 5分钟
     */
    private static final long SSE_TIMEOUT = 5 * 60 * 1000L;
    private final IPaymentService paymentService;
    private final ObjectMapper objectMapper;
    /**
     * SSE连接存储Map
     * Key: 订单号, Value: SSE连接信息
     */
    private final Map<String, SseConnectionInfo> sseConnections = new ConcurrentHashMap<>();

    /**
     * 验证支付token
     */
    @Override
    public boolean validatePaymentToken(String orderNo, String payToken) {
        if (StrUtil.isBlank(orderNo) || StrUtil.isBlank(payToken)) {
            return false;
        }
        return paymentService.validatePayToken(orderNo, payToken);
    }

    /**
     * 创建SSE连接
     */
    @Override
    public SseEmitter createConnection(String orderNo, String payToken) {
        try {
            // 如果已存在连接，先关闭旧连接
            if (sseConnections.containsKey(orderNo)) {
                closeConnection(orderNo);
            }

            // 创建新的SSE发射器
            SseEmitter emitter = new SseEmitter(SSE_TIMEOUT);
            SseConnectionInfo connectionInfo = new SseConnectionInfo(emitter, orderNo, payToken);

            // 设置连接回调
            emitter.onCompletion(() -> {
                log.info("SSE连接完成，订单号：{}", orderNo);
                sseConnections.remove(orderNo);
            });

            emitter.onTimeout(() -> {
                log.info("SSE连接超时，订单号：{}", orderNo);
                sseConnections.remove(orderNo);
            });

            emitter.onError((throwable) -> {
                log.error("SSE连接错误，订单号：{}，错误：{}", orderNo, throwable.getMessage());
                sseConnections.remove(orderNo);
            });

            // 存储连接信息
            sseConnections.put(orderNo, connectionInfo);

            // 发送连接成功消息
            sendMessage(emitter, "connected", "连接建立成功", null);

            // 发送心跳消息
            sendHeartbeat(emitter);

            log.info("SSE连接创建成功，订单号：{}，当前连接数：{}", orderNo, sseConnections.size());

            return emitter;

        } catch (Exception e) {
            log.error("创建SSE连接失败，订单号：{}，错误：{}", orderNo, e.getMessage(), e);
            throw new RuntimeException("创建SSE连接失败");
        }
    }

    /**
     * 推送支付成功消息
     */
    @Override
    public void pushPaymentSuccess(String orderNo) {
        SseConnectionInfo connectionInfo = sseConnections.get(orderNo);
        if (connectionInfo != null) {
            try {
                // 查询最新订单信息
                PaymentOrderVo orderInfo = paymentService.queryOrderStatus(orderNo);

                sendMessage(connectionInfo.getEmitter(), "payment-success", "支付成功", orderInfo);
                log.info("推送支付成功消息，订单号：{}", orderNo);

                // 支付成功后延迟关闭连接
                setTimeout(() -> closeConnection(orderNo), 3000);

            } catch (Exception e) {
                log.error("推送支付成功消息失败，订单号：{}，错误：{}", orderNo, e.getMessage(), e);
            }
        } else {
            log.warn("未找到SSE连接，无法推送支付成功消息，订单号：{}", orderNo);
        }
    }

    /**
     * 推送支付失败消息
     */
    @Override
    public void pushPaymentFailed(String orderNo, String reason) {
        SseConnectionInfo connectionInfo = sseConnections.get(orderNo);
        if (connectionInfo != null) {
            try {
                PaymentFailedData failedData = new PaymentFailedData();
                failedData.setOrderNo(orderNo);
                failedData.setReason(reason);
                failedData.setTimestamp(LocalDateTime.now());

                sendMessage(connectionInfo.getEmitter(), "payment-failed", "支付失败", failedData);
                log.info("推送支付失败消息，订单号：{}，原因：{}", orderNo, reason);

            } catch (Exception e) {
                log.error("推送支付失败消息失败，订单号：{}，错误：{}", orderNo, e.getMessage(), e);
            }
        }
    }

    /**
     * 推送支付取消消息
     */
    @Override
    public void pushPaymentCancelled(String orderNo) {
        SseConnectionInfo connectionInfo = sseConnections.get(orderNo);
        if (connectionInfo != null) {
            try {
                PaymentCancelledData cancelledData = new PaymentCancelledData();
                cancelledData.setOrderNo(orderNo);
                cancelledData.setTimestamp(LocalDateTime.now());

                sendMessage(connectionInfo.getEmitter(), "payment-cancelled", "支付已取消", cancelledData);
                log.info("推送支付取消消息，订单号：{}", orderNo);

            } catch (Exception e) {
                log.error("推送支付取消消息失败，订单号：{}，错误：{}", orderNo, e.getMessage(), e);
            }
        }
    }

    /**
     * 手动查询订单状态
     */
    @Override
    public void manualQueryOrderStatus(String orderNo, String payToken) {
        try {
            // 验证token
            if (!validatePaymentToken(orderNo, payToken)) {
                log.warn("手动查询订单状态token验证失败，订单号：{}", orderNo);
                return;
            }

            // 查询订单状态
            PaymentOrderVo orderInfo = paymentService.queryOrderStatus(orderNo);

            SseConnectionInfo connectionInfo = sseConnections.get(orderNo);
            if (connectionInfo != null) {
                // 根据订单状态推送相应消息
                switch (orderInfo.getStatus()) {
                    case "paid":
                        pushPaymentSuccess(orderNo);
                        break;
                    case "cancelled":
                        pushPaymentCancelled(orderNo);
                        break;
                    case "expired":
                        pushPaymentFailed(orderNo, "订单已过期");
                        break;
                    default:
                        // 推送查询结果
                        sendMessage(connectionInfo.getEmitter(), "query-result", "查询结果", orderInfo);
                        break;
                }
            }

            log.info("手动查询订单状态完成，订单号：{}，状态：{}", orderNo, orderInfo.getStatus());

        } catch (Exception e) {
            log.error("手动查询订单状态失败，订单号：{}，错误：{}", orderNo, e.getMessage(), e);
        }
    }

    /**
     * 关闭SSE连接
     */
    @Override
    public void closeConnection(String orderNo) {
        SseConnectionInfo connectionInfo = sseConnections.remove(orderNo);
        if (connectionInfo != null) {
            try {
                connectionInfo.getEmitter().complete();
                log.info("关闭SSE连接，订单号：{}", orderNo);
            } catch (Exception e) {
                log.error("关闭SSE连接失败，订单号：{}，错误：{}", orderNo, e.getMessage(), e);
            }
        }
    }

    /**
     * 清理过期连接
     */
    @Override
    @Scheduled(fixedRate = 60000) // 每分钟执行一次
    public void cleanupExpiredConnections() {
        try {
            LocalDateTime cutoffTime = LocalDateTime.now().minusMinutes(5);

            sseConnections.entrySet().removeIf(entry -> {
                SseConnectionInfo connectionInfo = entry.getValue();
                if (connectionInfo.getCreateTime().isBefore(cutoffTime)) {
                    try {
                        connectionInfo.getEmitter().complete();
                        log.info("清理过期SSE连接，订单号：{}", entry.getKey());
                        return true;
                    } catch (Exception e) {
                        log.error("清理过期SSE连接失败，订单号：{}，错误：{}", entry.getKey(), e.getMessage());
                        return true; // 即使清理失败也要移除
                    }
                }
                return false;
            });

            if (!sseConnections.isEmpty()) {
                log.debug("SSE连接清理完成，当前连接数：{}", sseConnections.size());
            }

        } catch (Exception e) {
            log.error("清理过期SSE连接失败：{}", e.getMessage(), e);
        }
    }

    /**
     * 发送SSE消息
     */
    private void sendMessage(SseEmitter emitter, String event, String message, Object data) {
        try {
            SseMessage sseMessage = new SseMessage();
            sseMessage.setEvent(event);
            sseMessage.setMessage(message);
            sseMessage.setData(data);
            sseMessage.setTimestamp(LocalDateTime.now());

            String jsonMessage = objectMapper.writeValueAsString(sseMessage);

            emitter.send(SseEmitter.event()
                .name(event)
                .data(jsonMessage));

        } catch (IOException e) {
            log.error("发送SSE消息失败，事件：{}，错误：{}", event, e.getMessage(), e);
            throw new RuntimeException("发送SSE消息失败", e);
        }
    }

    /**
     * 发送心跳消息
     */
    private void sendHeartbeat(SseEmitter emitter) {
        try {
            emitter.send(SseEmitter.event()
                .name("heartbeat")
                .data("ping"));
        } catch (IOException e) {
            log.error("发送心跳消息失败：{}", e.getMessage(), e);
        }
    }

    /**
     * 延迟执行任务
     */
    private void setTimeout(Runnable task, long delayMs) {
        new Thread(() -> {
            try {
                Thread.sleep(delayMs);
                task.run();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }).start();
    }

    /**
     * 监听支付成功事件
     */
    @EventListener
    @Async("paymentEventExecutor")
    public void handlePaymentSuccessEvent(PaymentEvent.PaymentSuccessEvent event) {
        try {
            log.info("收到支付成功事件，订单号：{}", event.getOrderNo());
            pushPaymentSuccess(event.getOrderNo());
        } catch (Exception e) {
            log.error("处理支付成功事件失败，订单号：{}，错误：{}", event.getOrderNo(), e.getMessage(), e);
        }
    }

    /**
     * 监听支付失败事件
     */
    @EventListener
    @Async("paymentEventExecutor")
    public void handlePaymentFailedEvent(PaymentEvent.PaymentFailedEvent event) {
        try {
            log.info("收到支付失败事件，订单号：{}，原因：{}", event.getOrderNo(), event.getReason());
            pushPaymentFailed(event.getOrderNo(), event.getReason());
        } catch (Exception e) {
            log.error("处理支付失败事件失败，订单号：{}，错误：{}", event.getOrderNo(), e.getMessage(), e);
        }
    }

    /**
     * 监听支付取消事件
     */
    @EventListener
    @Async("paymentEventExecutor")
    public void handlePaymentCancelledEvent(PaymentEvent.PaymentCancelledEvent event) {
        try {
            log.info("收到支付取消事件，订单号：{}", event.getOrderNo());
            pushPaymentCancelled(event.getOrderNo());
        } catch (Exception e) {
            log.error("处理支付取消事件失败，订单号：{}，错误：{}", event.getOrderNo(), e.getMessage(), e);
        }
    }

    /**
     * SSE连接信息
     */
    @Data
    private static class SseConnectionInfo {
        private SseEmitter emitter;
        private String orderNo;
        private String payToken;
        private LocalDateTime createTime;
        private LocalDateTime lastHeartbeat;

        public SseConnectionInfo(SseEmitter emitter, String orderNo, String payToken) {
            this.emitter = emitter;
            this.orderNo = orderNo;
            this.payToken = payToken;
            this.createTime = LocalDateTime.now();
            this.lastHeartbeat = LocalDateTime.now();
        }
    }

    // ==================== 事件监听器 ====================

    /**
     * SSE消息格式
     */
    @Data
    private static class SseMessage {
        private String event;
        private String message;
        private Object data;
        private LocalDateTime timestamp;
    }

    /**
     * 支付失败数据
     */
    @Data
    private static class PaymentFailedData {
        private String orderNo;
        private String reason;
        private LocalDateTime timestamp;
    }

    /**
     * 支付取消数据
     */
    @Data
    private static class PaymentCancelledData {
        private String orderNo;
        private LocalDateTime timestamp;
    }
}
