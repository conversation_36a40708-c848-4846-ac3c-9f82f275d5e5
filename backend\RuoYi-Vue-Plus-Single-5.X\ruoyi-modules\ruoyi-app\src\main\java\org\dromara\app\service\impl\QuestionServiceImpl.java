package org.dromara.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.Question;
import org.dromara.app.domain.QuestionBank;
import org.dromara.app.domain.bo.QuestionBo;
import org.dromara.app.domain.vo.QuestionVo;
import org.dromara.app.mapper.QuestionMapper;
import org.dromara.app.mapper.QuestionBankMapper;
import org.dromara.app.service.IQuestionService;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 题目管理Service业务层处理
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class QuestionServiceImpl extends ServiceImpl<QuestionMapper, Question> implements IQuestionService {

    private final QuestionMapper baseMapper;
    private final QuestionBankMapper questionBankMapper;

    /**
     * 查询题目
     */
    @Override
    public QuestionVo queryById(Long questionId) {
        return baseMapper.selectVoById(questionId);
    }

    /**
     * 查询题目列表
     */
    @Override
    public TableDataInfo<QuestionVo> queryPageList(QuestionBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<Question> lqw = buildQueryWrapper(bo);
        Page<QuestionVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询题目列表
     */
    @Override
    public List<QuestionVo> queryList(QuestionBo bo) {
        LambdaQueryWrapper<Question> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<Question> buildQueryWrapper(QuestionBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Question> lqw = Wrappers.lambdaQuery();

        // 基础查询条件
        lqw.eq(bo.getBankId() != null, Question::getBankId, bo.getBankId());
        lqw.like(StringUtils.isNotBlank(bo.getQuestionCode()), Question::getQuestionCode, bo.getQuestionCode());
        lqw.like(StringUtils.isNotBlank(bo.getTitle()), Question::getTitle, bo.getTitle());
        lqw.like(StringUtils.isNotBlank(bo.getDescription()), Question::getDescription, bo.getDescription());
        lqw.like(StringUtils.isNotBlank(bo.getContent()), Question::getContent, bo.getContent());
        lqw.eq(bo.getDifficulty() != null, Question::getDifficulty, bo.getDifficulty());
        lqw.eq(StringUtils.isNotBlank(bo.getCategory()), Question::getCategory, bo.getCategory());
        lqw.eq(bo.getType() != null, Question::getType, bo.getType());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), Question::getStatus, bo.getStatus());

        // 批量查询条件
        lqw.in(bo.getStatusArray() != null && bo.getStatusArray().length > 0, Question::getStatus, (Object[]) bo.getStatusArray());
        lqw.in(bo.getDifficultyArray() != null && bo.getDifficultyArray().length > 0, Question::getDifficulty, (Object[]) bo.getDifficultyArray());
        lqw.in(bo.getTypeArray() != null && bo.getTypeArray().length > 0, Question::getType, (Object[]) bo.getTypeArray());
        lqw.in(bo.getCategoryArray() != null && bo.getCategoryArray().length > 0, Question::getCategory, (Object[]) bo.getCategoryArray());

        // 关键词搜索
        if (StringUtils.isNotBlank(bo.getKeyword())) {
            lqw.and(wrapper -> wrapper
                .like(Question::getTitle, bo.getKeyword())
                .or()
                .like(Question::getDescription, bo.getKeyword())
                .or()
                .like(Question::getContent, bo.getKeyword())
                .or()
                .like(Question::getQuestionCode, bo.getKeyword())
                .or()
                .like(Question::getTags, bo.getKeyword())
            );
        }

        // 标签搜索
        if (StringUtils.isNotBlank(bo.getTags())) {
            lqw.like(Question::getTags, bo.getTags());
        }

        // 时间范围查询
        lqw.ge(StringUtils.isNotBlank(bo.getCreateTimeStart()), Question::getCreateTime, bo.getCreateTimeStart());
        lqw.le(StringUtils.isNotBlank(bo.getCreateTimeEnd()), Question::getCreateTime, bo.getCreateTimeEnd());

        // 排序
        if (StringUtils.isNotBlank(bo.getOrderByColumn())) {
            String orderBy = bo.getOrderByColumn();
            boolean isAsc = "asc".equals(bo.getIsAsc());

            switch (orderBy) {
                case "questionId" -> lqw.orderBy(true, isAsc, Question::getQuestionId);
                case "title" -> lqw.orderBy(true, isAsc, Question::getTitle);
                case "difficulty" -> lqw.orderBy(true, isAsc, Question::getDifficulty);
                case "type" -> lqw.orderBy(true, isAsc, Question::getType);
                case "practiceCount" -> lqw.orderBy(true, isAsc, Question::getPracticeCount);
                case "correctRate" -> lqw.orderBy(true, isAsc, Question::getCorrectRate);
                case "sort" -> lqw.orderBy(true, isAsc, Question::getSort);
                case "createTime" -> lqw.orderBy(true, isAsc, Question::getCreateTime);
                default -> lqw.orderByDesc(Question::getCreateTime);
            }
        } else {
            // 默认排序：按排序字段升序，创建时间降序
            lqw.orderByAsc(Question::getSort).orderByDesc(Question::getCreateTime);
        }

        return lqw;
    }

    /**
     * 新增题目
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(QuestionBo bo) {
        Question add = BeanUtil.toBean(bo, Question.class);
        validEntityBeforeSave(add);

        // 检查题目编码是否重复
        if (existsQuestionCode(add.getQuestionCode(), null)) {
            throw new RuntimeException("题目编码已存在");
        }

        // 设置默认值
        setDefaultValues(add);

        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setQuestionId(add.getQuestionId());
            // 更新题库题目数量
            updateBankQuestionCount(add.getBankId());
        }
        return flag;
    }

    /**
     * 修改题目
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(QuestionBo bo) {
        Question update = BeanUtil.toBean(bo, Question.class);
        validEntityBeforeSave(update);

        // 检查题目编码是否重复
        if (existsQuestionCode(update.getQuestionCode(), update.getQuestionId())) {
            throw new RuntimeException("题目编码已存在");
        }

        // 检查题库是否变更，如果变更需要更新题库题目数量
        Question oldQuestion = baseMapper.selectById(update.getQuestionId());
        boolean bankChanged = oldQuestion != null && !Objects.equals(oldQuestion.getBankId(), update.getBankId());

        boolean flag = baseMapper.updateById(update) > 0;

        if (flag && bankChanged) {
            // 更新新旧题库的题目数量
            updateBankQuestionCount(oldQuestion.getBankId());
            updateBankQuestionCount(update.getBankId());
        }

        return flag;
    }

    /**
     * 校验并批量删除题目信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 可以添加删除前的业务校验逻辑
            // 例如：检查题目是否被引用等
        }

        // 获取要删除的题目信息，用于更新题库数量
        List<Question> questions = baseMapper.selectBatchIds(ids);
        Set<Long> bankIds = questions.stream()
            .map(Question::getBankId)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());

        boolean flag = baseMapper.deleteBatchIds(ids) > 0;

        if (flag) {
            // 更新相关题库的题目数量
            bankIds.forEach(this::updateBankQuestionCount);
        }

        return flag;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(Question entity) {
        if (StringUtils.isBlank(entity.getQuestionCode())) {
            throw new RuntimeException("题目编码不能为空");
        }
        if (StringUtils.isBlank(entity.getTitle())) {
            throw new RuntimeException("题目标题不能为空");
        }
        if (StringUtils.isBlank(entity.getContent())) {
            throw new RuntimeException("题目内容不能为空");
        }
        if (entity.getBankId() == null) {
            throw new RuntimeException("题库ID不能为空");
        }
        if (entity.getDifficulty() == null || entity.getDifficulty() < 1 || entity.getDifficulty() > 3) {
            throw new RuntimeException("难度等级必须在1-3之间");
        }
        if (entity.getType() == null || entity.getType() < 1 || entity.getType() > 5) {
            throw new RuntimeException("题目类型必须在1-5之间");
        }
        if (StringUtils.isBlank(entity.getCategory())) {
            throw new RuntimeException("题目分类不能为空");
        }

        // 验证题库是否存在
        QuestionBank bank = questionBankMapper.selectById(entity.getBankId());
        if (bank == null) {
            throw new RuntimeException("指定的题库不存在");
        }
    }

    /**
     * 设置默认值
     */
    private void setDefaultValues(Question entity) {
        if (entity.getPracticeCount() == null) {
            entity.setPracticeCount(0);
        }
        if (entity.getCorrectRate() == null) {
            entity.setCorrectRate(0);
        }
        if (entity.getAcceptanceRate() == null) {
            entity.setAcceptanceRate(0.0);
        }
        if (entity.getCommentCount() == null) {
            entity.setCommentCount(0);
        }
        if (entity.getSort() == null) {
            entity.setSort(0);
        }
        if (StringUtils.isBlank(entity.getStatus())) {
            entity.setStatus("0");
        }
    }

    /**
     * 检查题目编码是否存在
     */
    private boolean existsQuestionCode(String questionCode, Long excludeId) {
        LambdaQueryWrapper<Question> lqw = Wrappers.lambdaQuery();
        lqw.eq(Question::getQuestionCode, questionCode);
        if (excludeId != null) {
            lqw.ne(Question::getQuestionId, excludeId);
        }
        return baseMapper.selectCount(lqw) > 0;
    }

    /**
     * 更新题库题目数量
     */
    private void updateBankQuestionCount(Long bankId) {
        if (bankId == null) {
            return;
        }

        try {
            // 统计题库下的题目数量
            LambdaQueryWrapper<Question> lqw = Wrappers.lambdaQuery();
            lqw.eq(Question::getBankId, bankId);
            lqw.eq(Question::getStatus, "0"); // 只统计正常状态的题目
            Long count = baseMapper.selectCount(lqw);

            // 更新题库的题目数量
            QuestionBank bank = new QuestionBank();
            bank.setBankId(bankId);
            bank.setTotalQuestions(count.intValue());
            questionBankMapper.updateById(bank);

        } catch (Exception e) {
            log.error("更新题库题目数量失败，bankId: {}", bankId, e);
        }
    }

    /**
     * 批量导入题目
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importQuestion(List<QuestionVo> questionList, Long bankId, Boolean isUpdateSupport, String operName) {
        if (ObjectUtil.isNull(questionList) || questionList.isEmpty()) {
            throw new RuntimeException("导入题目数据不能为空！");
        }

        // 验证题库是否存在
        QuestionBank bank = questionBankMapper.selectById(bankId);
        if (bank == null) {
            throw new RuntimeException("指定的题库不存在！");
        }

        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        for (QuestionVo questionVo : questionList) {
            try {
                // 设置题库ID
                questionVo.setBankId(bankId);

                // 验证是否存在这个题目
                Question existQuestion = null;
                if (StringUtils.isNotBlank(questionVo.getQuestionCode())) {
                    existQuestion = baseMapper.selectOne(
                        Wrappers.lambdaQuery(Question.class)
                            .eq(Question::getQuestionCode, questionVo.getQuestionCode())
                    );
                }

                if (ObjectUtil.isNull(existQuestion)) {
                    // 新增题目
                    Question question = BeanUtil.toBean(questionVo, Question.class);
                    setDefaultValues(question);
                    validEntityBeforeSave(question);

                    baseMapper.insert(question);
                    successNum++;
                    successMsg.append("<br/>").append(successNum).append("、题目 ").append(question.getTitle()).append(" 导入成功");

                } else if (isUpdateSupport) {
                    // 更新题目
                    Question question = BeanUtil.toBean(questionVo, Question.class);
                    question.setQuestionId(existQuestion.getQuestionId());
                    validEntityBeforeSave(question);

                    baseMapper.updateById(question);
                    successNum++;
                    successMsg.append("<br/>").append(successNum).append("、题目 ").append(question.getTitle()).append(" 更新成功");

                } else {
                    failureNum++;
                    failureMsg.append("<br/>").append(failureNum).append("、题目 ").append(questionVo.getTitle()).append(" 已存在");
                }

            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、题目 " + questionVo.getTitle() + " 导入失败：";
                failureMsg.append(msg).append(e.getMessage());
                log.error("导入题目失败", e);
            }
        }

        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new RuntimeException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
            // 更新题库题目数量
            updateBankQuestionCount(bankId);
        }
        return successMsg.toString();
    }

    /**
     * 导出题目列表
     */
    @Override
    public List<QuestionVo> exportQuestionList(QuestionBo bo) {
        LambdaQueryWrapper<Question> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 更新题目状态
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateStatus(Long questionId, String status) {
        Question question = new Question();
        question.setQuestionId(questionId);
        question.setStatus(status);
        return baseMapper.updateById(question) > 0;
    }

    /**
     * 批量更新题目状态
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchUpdateStatus(List<Long> questionIds, String status) {
        if (questionIds == null || questionIds.isEmpty()) {
            return false;
        }

        for (Long questionId : questionIds) {
            Question question = new Question();
            question.setQuestionId(questionId);
            question.setStatus(status);
            baseMapper.updateById(question);
        }
        return true;
    }

    /**
     * 更新题目难度
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateDifficulty(Long questionId, String difficulty) {
        Integer difficultyInt = Integer.valueOf(difficulty);
        if (difficultyInt < 1 || difficultyInt > 3) {
            throw new RuntimeException("难度等级必须在1-3之间");
        }

        Question question = new Question();
        question.setQuestionId(questionId);
        question.setDifficulty(difficultyInt);
        return baseMapper.updateById(question) > 0;
    }

    /**
     * 批量更新题目难度
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchUpdateDifficulty(List<Long> questionIds, String difficulty) {
        if (questionIds == null || questionIds.isEmpty()) {
            return false;
        }

        Integer difficultyInt = Integer.valueOf(difficulty);
        if (difficultyInt < 1 || difficultyInt > 3) {
            throw new RuntimeException("难度等级必须在1-3之间");
        }

        for (Long questionId : questionIds) {
            Question question = new Question();
            question.setQuestionId(questionId);
            question.setDifficulty(difficultyInt);
            baseMapper.updateById(question);
        }
        return true;
    }

    // 继续添加剩余的方法实现...
    // 由于文件长度限制，剩余方法将在后续添加
}
