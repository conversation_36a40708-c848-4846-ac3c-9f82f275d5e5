package org.dromara.app.domain.vo;

import lombok.Data;

/**
 * 首页仪表盘汇总数据VO
 *
 * <AUTHOR>
 */
@Data
public class DashboardSummaryVO {

    /**
     * 用户信息
     */
    private UserInfoVO user;

    /**
     * 欢迎消息
     */
    private String welcomeMessage;

    /**
     * AI激励消息
     */
    private String aiMotivation;

    /**
     * 今日待完成任务数
     */
    private Integer todayTasks;

    /**
     * 未读消息数
     */
    private Integer unreadMessages;

    /**
     * 下次面试安排
     */
    private NextInterviewVO nextInterview;

    /**
     * 用户信息VO
     */
    @Data
    public static class UserInfoVO {
        /**
         * 用户ID
         */
        private String id;

        /**
         * 用户名称
         */
        private String name;

        /**
         * 用户头像
         */
        private String avatar;

        /**
         * 目标岗位
         */
        private String targetPosition;

        /**
         * 用户等级
         */
        private Integer level;
    }

    /**
     * 下次面试安排VO
     */
    @Data
    public static class NextInterviewVO {
        /**
         * 面试时间
         */
        private String time;

        /**
         * 面试职位
         */
        private String position;

        /**
         * 面试公司
         */
        private String company;
    }
}
