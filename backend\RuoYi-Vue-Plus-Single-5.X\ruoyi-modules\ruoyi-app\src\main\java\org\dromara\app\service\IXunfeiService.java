package org.dromara.app.service;

import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * 讯飞服务接口
 *
 * <AUTHOR>
 */
public interface IXunfeiService {

    /**
     * 星火大模型聊天
     *
     * @param message 消息内容
     * @param context 上下文信息
     * @return 响应内容
     */
    String sparkChat(String message, Map<String, Object> context);

    /**
     * 星火大模型流式聊天
     *
     * @param message  消息内容
     * @param context  上下文信息
     * @param callback 回调处理器
     */
    void sparkChatStream(String message, Map<String, Object> context, StreamCallback callback);

    /**
     * 智能体星火大模型流式聊天
     *
     * @param message   消息内容
     * @param agentType 智能体类型
     * @param context   上下文信息
     * @param callback  回调处理器
     */
    void sparkChatAgent(String message, String agentType, Map<String, Object> context, StreamCallback callback);

    /**
     * 智能体星火大模型聊天(非流式)
     *
     * @param message   消息内容
     * @param agentType 智能体类型
     * @param context   上下文信息
     * @return 响应内容
     */
    String sparkChatAgentSync(String message, String agentType, Map<String, Object> context);

    /**
     * 语音识别
     *
     * @param audioFile 音频文件
     * @return 语音识别结果
     */
    SpeechRecognitionResult speechRecognition(MultipartFile audioFile);

    /**
     * 通过URL进行语音识别
     *
     * @param audioUrl 音频URL
     * @return 语音识别结果
     */
    SpeechRecognitionResult speechRecognition(String audioUrl);

    /**
     * 情感分析
     *
     * @param text 待分析文本
     * @return 情感分析结果
     */
    EmotionAnalysisResult emotionAnalysis(String text);

    /**
     * 语音情感分析
     *
     * @param audioFile 音频文件
     * @return 语音情感分析结果
     */
    VoiceEmotionResult voiceEmotionAnalysis(MultipartFile audioFile);

    /**
     * 图像情感分析
     *
     * @param imageData base64编码的图像数据
     * @return 图像情感分析结果
     */
    EmotionAnalysisResult imageEmotionAnalysis(String imageData);

    /**
     * 生成面试智能建议
     *
     * @param context 面试上下文
     * @param analysisData 分析数据
     * @return 智能建议
     */
    String generateInterviewSuggestion(Map<String, Object> context, Map<String, Object> analysisData);

    /**
     * 文本转语音
     *
     * @param text        待合成文本
     * @param voiceConfig 语音配置
     * @return 语音数据
     */
    byte[] textToSpeech(String text, VoiceConfig voiceConfig);

    /**
     * 流式回调接口
     */
    interface StreamCallback {
        /**
         * 收到token回调
         *
         * @param token 文本片段
         */
        void onToken(String token);

        /**
         * 完成回调
         *
         * @param fullResponse 完整响应
         */
        void onComplete(String fullResponse);

        /**
         * 错误回调
         *
         * @param throwable 异常信息
         */
        void onError(Throwable throwable);
    }

    /**
     * 语音配置
     */
    class VoiceConfig {
        private String voiceName; // 发音人
        private Integer speed;    // 语速
        private Integer volume;   // 音量
        private Integer pitch;    // 音调
        private String audioFormat; // 音频格式

        // getter和setter方法

        public String getVoiceName() {
            return voiceName;
        }

        public void setVoiceName(String voiceName) {
            this.voiceName = voiceName;
        }

        public Integer getSpeed() {
            return speed;
        }

        public void setSpeed(Integer speed) {
            this.speed = speed;
        }

        public Integer getVolume() {
            return volume;
        }

        public void setVolume(Integer volume) {
            this.volume = volume;
        }

        public Integer getPitch() {
            return pitch;
        }

        public void setPitch(Integer pitch) {
            this.pitch = pitch;
        }

        public String getAudioFormat() {
            return audioFormat;
        }

        public void setAudioFormat(String audioFormat) {
            this.audioFormat = audioFormat;
        }
    }

    /**
     * 语音识别结果
     */
    class SpeechRecognitionResult {
        private boolean success;
        private String text;
        private String language;
        private Double confidence;
        private Long duration;
        private String errorMessage;

        // 静态工厂方法和getter/setter方法

        public static SpeechRecognitionResult success(String text, String language, Double confidence, Long duration) {
            SpeechRecognitionResult result = new SpeechRecognitionResult();
            result.success = true;
            result.text = text;
            result.language = language;
            result.confidence = confidence;
            result.duration = duration;
            return result;
        }

        public static SpeechRecognitionResult error(String errorMessage) {
            SpeechRecognitionResult result = new SpeechRecognitionResult();
            result.success = false;
            result.errorMessage = errorMessage;
            return result;
        }

        public boolean isSuccess() {
            return success;
        }

        public String getText() {
            return text;
        }

        public String getLanguage() {
            return language;
        }

        public Double getConfidence() {
            return confidence;
        }

        public Long getDuration() {
            return duration;
        }

        public String getErrorMessage() {
            return errorMessage;
        }
    }

    /**
     * 情感分析结果
     */
    class EmotionAnalysisResult {
        private boolean success;
        private String emotion;
        private Double confidence;
        private Map<String, Double> emotionScores;
        private String sentiment; // 情感
        private Double sentimentScore;
        private String errorMessage;

        // 静态工厂方法和getter/setter方法

        public static EmotionAnalysisResult success(String emotion, Double confidence,
                                                   Map<String, Double> emotionScores,
                                                   String sentiment, Double sentimentScore) {
            EmotionAnalysisResult result = new EmotionAnalysisResult();
            result.success = true;
            result.emotion = emotion;
            result.confidence = confidence;
            result.emotionScores = emotionScores;
            result.sentiment = sentiment;
            result.sentimentScore = sentimentScore;
            return result;
        }

        public static EmotionAnalysisResult error(String errorMessage) {
            EmotionAnalysisResult result = new EmotionAnalysisResult();
            result.success = false;
            result.errorMessage = errorMessage;
            return result;
        }


        public boolean isSuccess() {
            return success;
        }

        public String getEmotion() {
            return emotion;
        }

        public Double getConfidence() {
            return confidence;
        }

        public Map<String, Double> getEmotionScores() {
            return emotionScores;
        }

        public String getSentiment() {
            return sentiment;
        }

        public Double getSentimentScore() {
            return sentimentScore;
        }

        public String getErrorMessage() {
            return errorMessage;
        }
    }

    /**
     * 语音情感分析结果
     */
    class VoiceEmotionResult {
        private boolean success;
        private String text;
        private EmotionAnalysisResult textEmotionResult;
        private Map<String, Double> voiceFeatures;
        private String overallEmotion;
        private Double overallConfidence;
        private String errorMessage;

        // 静态工厂方法和getter/setter方法

        public static VoiceEmotionResult success(String text,
                                                EmotionAnalysisResult textEmotionResult,
                                                Map<String, Double> voiceFeatures,
                                                String overallEmotion,
                                                Double overallConfidence) {
            VoiceEmotionResult result = new VoiceEmotionResult();
            result.success = true;
            result.text = text;
            result.textEmotionResult = textEmotionResult;
            result.voiceFeatures = voiceFeatures;
            result.overallEmotion = overallEmotion;
            result.overallConfidence = overallConfidence;
            return result;
        }

        public static VoiceEmotionResult error(String errorMessage) {
            VoiceEmotionResult result = new VoiceEmotionResult();
            result.success = false;
            result.errorMessage = errorMessage;
            return result;
        }

        public boolean isSuccess() {
            return success;
        }

        public String getText() {
            return text;
        }

        public EmotionAnalysisResult getTextEmotionResult() {
            return textEmotionResult;
        }

        public Map<String, Double> getVoiceFeatures() {
            return voiceFeatures;
        }

        public String getOverallEmotion() {
            return overallEmotion;
        }

        public Double getOverallConfidence() {
            return overallConfidence;
        }

        public String getErrorMessage() {
            return errorMessage;
        }
    }
}
