package org.dromara.app.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.time.LocalDateTime;

/**
 * 知识库实体
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_knowledge_base")
public class KnowledgeBase extends BaseEntity {

    /**
     * 知识库ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 知识库名称
     */
    private String name;

    /**
     * 知识库描述
     */
    private String description;

    /**
     * 知识库类型 (general/technical/business/etc.)
     */
    private String type;

    /**
     * 知识库状态 (0=禁用 1=启用)
     */
    private Integer status;

    /**
     * 向量维度 (默认1024)
     */
    private Integer vectorDimension;

    /**
     * 文档数量
     */
    private Long documentCount;

    /**
     * 向量数量
     */
    private Long vectorCount;

    /**
     * 索引配置 (JSON格式)
     */
    private String indexConfig;

    /**
     * 扩展配置 (JSON格式)
     */
    private String extendConfig;

    /**
     * 最后更新时间
     */
    private LocalDateTime lastSyncTime;

    /**
     * 排序字段
     */
    private Integer sortOrder;

    /**
     * 备注
     */
    private String remark;
}
