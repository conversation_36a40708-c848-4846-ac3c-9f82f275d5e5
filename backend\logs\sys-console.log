2025-07-24 20:05:19 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-07-24 20:05:19 [main] INFO  org.dromara.DromaraApplication - Starting DromaraApplication using Java 17.0.10 with PID 29264 (C:\Users\<USER>\Desktop\softwart-xunfei-code\backend\RuoYi-Vue-Plus-Single-5.X\ruoyi-admin\target\classes started by lenovo in C:\Users\<USER>\Desktop\softwart-xunfei-code\backend)
2025-07-24 20:05:19 [main] INFO  org.dromara.DromaraApplication - The following 1 profile is active: "dev"
2025-07-24 20:05:38 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-07-24 20:05:40 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-07-24 20:05:40 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-07-24 20:06:01 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@31f96cf
2025-07-24 20:06:01 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-07-24 20:06:01 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-24 20:06:01 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-07-24 20:06:06 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatMessageMapper.searchMessages] is ignored, because it exists, maybe from xml file
2025-07-24 20:06:06 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatMessageMapper.getMessageStats] is ignored, because it exists, maybe from xml file
2025-07-24 20:06:06 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatMessageMapper.selectRecentMessages] is ignored, because it exists, maybe from xml file
2025-07-24 20:06:06 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatMessageMapper.countSessionMessages] is ignored, because it exists, maybe from xml file
2025-07-24 20:06:06 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.countUserSessions] is ignored, because it exists, maybe from xml file
2025-07-24 20:06:06 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.getPopularAgentTypes] is ignored, because it exists, maybe from xml file
2025-07-24 20:06:06 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.selectRecentSessions] is ignored, because it exists, maybe from xml file
2025-07-24 20:06:06 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.selectActiveSessions] is ignored, because it exists, maybe from xml file
2025-07-24 20:06:06 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.selectUserSessionsByAgent] is ignored, because it exists, maybe from xml file
2025-07-24 20:06:06 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ChatSessionMapper.selectSessionStats] is ignored, because it exists, maybe from xml file
2025-07-24 20:06:06 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[org.dromara.app.mapper.ToolCallMapper.getUserToolStats] is ignored, because it exists, maybe from xml file
2025-07-24 20:06:08 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-07-24 20:06:08 [main] INFO  org.redisson.Version - Redisson 3.45.1
2025-07-24 20:06:09 [redisson-netty-1-1] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for 42.193.227.58/42.193.227.58:6379
2025-07-24 20:06:10 [redisson-netty-1-3] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for 42.193.227.58/42.193.227.58:6379
2025-07-24 20:06:11 [main] INFO  o.d.app.service.impl.ToolServiceImpl - 初始化默认AI工具
2025-07-24 20:06:12 [main] INFO  o.d.app.service.impl.ToolServiceImpl - 默认AI工具已存在，跳过初始化
2025-07-24 20:06:12 [main] INFO  o.d.app.service.impl.ToolServiceImpl - 注册默认工具执行器
2025-07-24 20:06:12 [main] INFO  org.dromara.app.tool.ToolRegistry - 注册工具执行器: text_processor - 文本处理器
2025-07-24 20:06:12 [main] INFO  org.dromara.app.tool.ToolRegistry - 注册工具执行器: datetime_helper - 时间助手
2025-07-24 20:06:12 [main] INFO  o.d.app.service.impl.ToolServiceImpl - 默认工具执行器注册完成
2025-07-24 20:06:13 [main] INFO  o.d.a.service.impl.AgentServiceImpl - 数据库中已存在 11 个代理，跳过初始化
2025-07-24 20:06:13 [main] INFO  o.d.app.service.ChatMemoryManager - 聊天内存管理器初始化完成，清理间隔: 30 分钟
2025-07-24 20:06:13 [main] INFO  o.d.app.service.SseConnectionManager - SSE连接管理器初始化完成，清理间隔: 5 分钟
2025-07-24 20:06:14 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class org.dromara.app.domain.DimensionScore ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-07-24 20:06:16 [main] INFO  o.d.c.mongodb.config.MongoConfig - 初始化 MongoDB 配置
2025-07-24 20:06:16 [main] INFO  org.mongodb.driver.client - MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync", "version": "5.2.1"}, "os": {"type": "Windows", "name": "Windows 11", "architecture": "amd64", "version": "10.0"}, "platform": "Java/Oracle Corporation/17.0.10+11-LTS-240"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=null, transportSettings=null, commandListeners=[], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@61ee1773, com.mongodb.Jep395RecordCodecProvider@268467ab, com.mongodb.KotlinCodecProvider@30aeb2b3]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[**************:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=30000, readTimeoutMS=15000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=30000, readTimeoutMS=30000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=50, minSize=5, maxWaitTimeMS=30000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=600000, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=UNSPECIFIED, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
2025-07-24 20:06:20 [main] INFO  o.d.c.caffeine.config.CaffeineConfig - 初始化Caffeine缓存成功，最大容量: 1000, 过期时间: 3600秒
2025-07-24 20:06:20 [main] INFO  org.dromara.app.config.AsyncConfig - 分析任务执行器已初始化，核心线程数: 5, 最大线程数: 10, 队列容量: 100
2025-07-24 20:06:20 [main] INFO  org.dromara.app.config.AsyncConfig - 报告生成执行器已初始化
2025-07-24 20:06:20 [main] INFO  org.dromara.app.config.AsyncConfig - 缓存优化执行器已初始化
2025-07-24 20:06:20 [main] INFO  org.dromara.app.config.AsyncConfig - 数据清理执行器已初始化
2025-07-24 20:06:22 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@4d74c3ba
2025-07-24 20:06:24 [main] INFO  o.d.c.p.config.PayAutoConfiguration - 支付组件自动配置已启用
2025-07-24 20:06:26 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-07-24 20:06:26 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-07-24 20:06:26 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-07-24 20:06:27 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-07-24 20:06:37 [cluster-ClusterId{value='688221b8f2eda47baab0211a', description='null'}-**************:27017] INFO  org.mongodb.driver.cluster - Exception in monitor thread while connecting to server **************:27017
com.mongodb.MongoSocketOpenException: Exception opening socket
	at com.mongodb.internal.connection.SocketStream.lambda$open$0(SocketStream.java:85)
	at java.base/java.util.Optional.orElseThrow(Optional.java:403)
	at com.mongodb.internal.connection.SocketStream.open(SocketStream.java:85)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:233)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitor.lookupServerDescription(DefaultServerMonitor.java:219)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitor.run(DefaultServerMonitor.java:176)
Caused by: java.net.ConnectException: Connection timed out: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:554)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:602)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at com.mongodb.internal.connection.SocketStreamHelper.initialize(SocketStreamHelper.java:76)
	at com.mongodb.internal.connection.SocketStream.initializeSocket(SocketStream.java:104)
	at com.mongodb.internal.connection.SocketStream.open(SocketStream.java:79)
	... 3 common frames omitted
2025-07-24 20:07:14 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1] WARN  o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: no further information
2025-07-24 20:07:56 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-2] WARN  o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: no further information
2025-07-24 20:08:17 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#1-1] WARN  o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: no further information
2025-07-24 20:08:38 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-3] WARN  o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: no further information
2025-07-24 20:09:20 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#1-2] WARN  o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: no further information
2025-07-24 20:09:41 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-4] WARN  o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: no further information
2025-07-24 20:09:54 [main] ERROR o.s.a.r.l.SimpleMessageListenerContainer - Consumer failed to start in 60000 milliseconds; does the task executor have enough threads to support the container concurrency?
2025-07-24 20:10:02 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#2-1] WARN  o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: no further information
2025-07-24 20:10:23 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#1-3] WARN  o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: no further information
2025-07-24 20:10:44 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-5] WARN  o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: no further information
2025-07-24 20:11:26 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#2-2] WARN  o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: no further information
2025-07-24 20:11:47 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#1-4] WARN  o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: no further information
2025-07-24 20:12:00 [main] ERROR o.s.a.r.l.SimpleMessageListenerContainer - Consumer failed to start in 60000 milliseconds; does the task executor have enough threads to support the container concurrency?
2025-07-24 20:12:00 [main] INFO  org.dromara.DromaraApplication - Started DromaraApplication in 404.486 seconds (process running for 407.515)
2025-07-24 20:12:00 [schedule-pool-3] INFO  o.d.a.t.InterviewSessionCleanupTask - 开始清理过期的面试会话
2025-07-24 20:12:00 [main] INFO  o.d.c.sse.listener.SseTopicListener - 初始化SSE主题订阅监听器成功
2025-07-24 20:12:00 [main] INFO  o.d.a.c.ToolInitializationConfig - 开始初始化AI工具系统...
2025-07-24 20:12:00 [main] INFO  o.d.app.service.impl.ToolServiceImpl - 开始初始化系统工具
2025-07-24 20:12:00 [main] INFO  o.d.app.service.impl.ToolServiceImpl - 初始化默认AI工具
2025-07-24 20:12:00 [schedule-pool-3] INFO  o.d.a.t.InterviewSessionCleanupTask - 没有找到过期的面试会话
2025-07-24 20:12:00 [schedule-pool-6] INFO  o.d.a.t.InterviewSessionCleanupTask - 面试会话统计 - 总数: 47, 已创建: 0, 进行中: 0, 暂停: 0, 已完成: 0, 已过期: 47
2025-07-24 20:12:00 [main] INFO  o.d.app.service.impl.ToolServiceImpl - 默认AI工具已存在，跳过初始化
2025-07-24 20:12:00 [main] INFO  o.d.app.service.impl.ToolServiceImpl - 注册默认工具执行器
2025-07-24 20:12:00 [main] INFO  org.dromara.app.tool.ToolRegistry - 注册工具执行器: text_processor - 文本处理器
2025-07-24 20:12:00 [main] INFO  org.dromara.app.tool.ToolRegistry - 注册工具执行器: datetime_helper - 时间助手
2025-07-24 20:12:00 [main] INFO  o.d.app.service.impl.ToolServiceImpl - 默认工具执行器注册完成
2025-07-24 20:12:00 [main] INFO  o.d.app.service.impl.ToolServiceImpl - 系统工具初始化完成
2025-07-24 20:12:00 [main] INFO  o.d.a.c.ToolInitializationConfig - AI工具系统初始化完成
2025-07-24 20:12:00 [main] INFO  o.d.a.config.AppStartupConfiguration - 开始初始化Agent应用...
2025-07-24 20:12:00 [main] INFO  o.d.app.service.ConfigurationService - 配置管理服务初始化完成
2025-07-24 20:12:00 [main] INFO  o.d.a.config.AppStartupConfiguration - 配置服务初始化完成
2025-07-24 20:12:00 [main] INFO  o.d.a.config.AppStartupConfiguration - 工具执行器注册完成
2025-07-24 20:12:00 [main] INFO  o.d.a.config.AppStartupConfiguration - ========================================
2025-07-24 20:12:00 [main] INFO  o.d.a.config.AppStartupConfiguration - Agent应用启动信息:
2025-07-24 20:12:00 [main] INFO  o.d.a.config.AppStartupConfiguration - - 聊天功能: 启用
2025-07-24 20:12:00 [main] INFO  o.d.a.config.AppStartupConfiguration - - 流式聊天: 启用
2025-07-24 20:12:00 [main] INFO  o.d.a.config.AppStartupConfiguration - - RAG功能: 启用
2025-07-24 20:12:00 [main] INFO  o.d.a.config.AppStartupConfiguration - - 高级RAG: 启用
2025-07-24 20:12:00 [main] INFO  o.d.a.config.AppStartupConfiguration - - 工具调用: 启用
2025-07-24 20:12:00 [main] INFO  o.d.a.config.AppStartupConfiguration - - 限流功能: 启用
2025-07-24 20:12:00 [main] INFO  o.d.a.config.AppStartupConfiguration - - 监控功能: 启用
2025-07-24 20:12:00 [main] INFO  o.d.a.config.AppStartupConfiguration - - 审计日志: 启用
2025-07-24 20:12:00 [main] INFO  o.d.a.config.AppStartupConfiguration - ========================================
2025-07-24 20:12:00 [main] INFO  o.d.a.config.AppStartupConfiguration - Agent应用初始化完成
2025-07-24 20:12:01 [main] INFO  o.d.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-07-24 20:12:01 [main] INFO  o.d.app.config.PaymentTimeoutConfig - 初始化支付超时队列监听器，队列名：payment:timeout:queue
2025-07-24 20:12:01 [main] INFO  o.d.app.config.PaymentTimeoutConfig - 支付超时队列监听器初始化成功
2025-07-24 20:12:01 [main] ERROR o.s.b.c.p.m.PropertiesMigrationListener - 
The use of configuration keys that are no longer supported was found in the environment:

Property source 'Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/' (document #10)':
	Key: xunfei.spark.api-key
		Line: 421
		Reason: none
	Key: xunfei.spark.api-secret
		Line: 422
		Reason: none

Property source 'Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' (document #19)':
	Key: xunfei.spark.api-key
		Line: 872
		Reason: none
	Key: xunfei.spark.api-secret
		Line: 873
		Reason: none


Please refer to the release notes or reference guide for potential alternatives.

2025-07-24 20:12:08 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-6] WARN  o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: no further information
2025-07-24 20:12:29 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#3-1] WARN  o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: no further information
2025-07-24 20:12:50 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#2-3] WARN  o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: no further information
2025-07-24 20:13:11 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#1-5] WARN  o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: no further information
2025-07-24 20:13:33 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-7] WARN  o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: no further information
2025-07-24 20:13:54 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#3-2] WARN  o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: no further information
2025-07-24 20:14:15 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#2-4] WARN  o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: no further information
2025-07-24 20:14:36 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#1-6] WARN  o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: no further information
2025-07-24 20:14:57 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-8] WARN  o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: no further information
2025-07-24 20:15:18 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#3-3] WARN  o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: no further information
2025-07-24 20:15:39 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#2-5] WARN  o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: no further information
2025-07-24 20:16:00 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#1-7] WARN  o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: no further information
2025-07-24 20:16:21 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-9] WARN  o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: no further information
2025-07-24 20:16:42 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#3-4] WARN  o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: no further information
2025-07-24 20:17:03 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#2-6] WARN  o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: no further information
2025-07-24 20:17:24 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#1-8] WARN  o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: no further information
2025-07-24 20:17:45 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-10] WARN  o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: no further information
2025-07-24 20:18:06 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#3-5] WARN  o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: no further information
2025-07-24 20:18:27 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#2-7] WARN  o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: no further information
2025-07-24 20:18:48 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#1-9] WARN  o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: no further information
2025-07-24 20:19:09 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-11] WARN  o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: no further information
2025-07-24 20:19:30 [org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#3-6] WARN  o.s.a.r.l.SimpleMessageListenerContainer - Consumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: no further information
2025-07-24 20:19:46 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
