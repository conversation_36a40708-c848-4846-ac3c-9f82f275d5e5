{"doc": "\n ES分页结果\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "pageNum", "doc": "\n 当前页码\r\n"}, {"name": "pageSize", "doc": "\n 每页大小\r\n"}, {"name": "total", "doc": "\n 总记录数\r\n"}, {"name": "pages", "doc": "\n 总页数\r\n"}, {"name": "records", "doc": "\n 数据列表\r\n"}, {"name": "aggregations", "doc": "\n 聚合结果\r\n"}, {"name": "isFirstPage", "doc": "\n 是否为第一页\r\n"}, {"name": "isLastPage", "doc": "\n 是否为最后一页\r\n"}, {"name": "has<PERSON>revious", "doc": "\n 是否有前一页\r\n"}, {"name": "hasNext", "doc": "\n 是否有下一页\r\n"}], "enumConstants": [], "methods": [{"name": "of", "paramTypes": ["java.lang.Integer", "java.lang.Integer", "java.lang.Long", "java.util.List"], "doc": "\n 构造分页结果\r\n"}, {"name": "calculatePageInfo", "paramTypes": [], "doc": "\n 计算分页信息\r\n"}], "constructors": []}