package org.dromara.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.dromara.app.domain.ActivitySession;
import org.dromara.app.domain.enums.ActivityType;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户活动会话Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Mapper
public interface ActivitySessionMapper extends BaseMapper<ActivitySession> {

    /**
     * 根据会话ID查询活动会话
     *
     * @param sessionId 会话ID
     * @return 活动会话
     */
    @Select("SELECT * FROM app_activity_session WHERE session_id = #{sessionId} AND del_flag = '0'")
    ActivitySession selectBySessionId(@Param("sessionId") String sessionId);

    /**
     * 查询用户的活跃会话
     *
     * @param userId 用户ID
     * @return 活跃会话列表
     */
    @Select("SELECT * FROM app_activity_session WHERE user_id = #{userId} AND is_active = 1 AND del_flag = '0'")
    List<ActivitySession> selectActiveSessionsByUserId(@Param("userId") Long userId);

    /**
     * 更新会话状态为非活跃
     *
     * @param sessionId 会话ID
     * @param endTime   结束时间
     * @param duration  持续时长
     * @return 更新行数
     */
    @Update("UPDATE app_activity_session SET is_active = 0, end_time = #{endTime}, duration = #{duration}, update_time = NOW() WHERE session_id = #{sessionId}")
    int updateSessionToInactive(@Param("sessionId") String sessionId,
                                @Param("endTime") LocalDateTime endTime,
                                @Param("duration") Long duration);

    /**
     * 更新会话持续时长
     *
     * @param sessionId 会话ID
     * @param duration  持续时长
     * @return 更新行数
     */
    @Update("UPDATE app_activity_session SET duration = #{duration}, update_time = NOW() WHERE session_id = #{sessionId}")
    int updateSessionDuration(@Param("sessionId") String sessionId, @Param("duration") Long duration);

    /**
     * 分页查询用户活动历史记录
     *
     * @param page         分页对象
     * @param userId       用户ID
     * @param activityType 活动类型
     * @param startDate    开始时间
     * @param endDate      结束时间
     * @return 分页结果
     */
    Page<ActivitySession> selectUserActivityHistory(Page<ActivitySession> page,
                                                    @Param("userId") Long userId,
                                                    @Param("activityType") ActivityType activityType,
                                                    @Param("startDate") LocalDateTime startDate,
                                                    @Param("endDate") LocalDateTime endDate);

    /**
     * 查询用户今日活动时长
     *
     * @param userId       用户ID
     * @param activityType 活动类型
     * @return 今日活动时长(毫秒)
     */
    @Select("SELECT COALESCE(SUM(duration), 0) FROM app_activity_session " +
        "WHERE user_id = #{userId} " +
        "AND (#{activityType} IS NULL OR activity_type = #{activityType}) " +
        "AND DATE(start_time) = CURDATE() " +
        "AND del_flag = '0'")
    Long selectTodayDuration(@Param("userId") Long userId, @Param("activityType") ActivityType activityType);

    /**
     * 查询用户本周活动时长
     *
     * @param userId       用户ID
     * @param activityType 活动类型
     * @return 本周活动时长(毫秒)
     */
    @Select("SELECT COALESCE(SUM(duration), 0) FROM app_activity_session " +
        "WHERE user_id = #{userId} " +
        "AND (#{activityType} IS NULL OR activity_type = #{activityType}) " +
        "AND start_time >= DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY) " +
        "AND start_time <= CURDATE() " +
        "AND del_flag = '0'")
    Long selectWeekDuration(@Param("userId") Long userId, @Param("activityType") ActivityType activityType);

    /**
     * 查询用户本月活动时长
     *
     * @param userId       用户ID
     * @param activityType 活动类型
     * @return 本月活动时长(毫秒)
     */
    @Select("SELECT COALESCE(SUM(duration), 0) FROM app_activity_session " +
        "WHERE user_id = #{userId} " +
        "AND (#{activityType} IS NULL OR activity_type = #{activityType}) " +
        "AND start_time >= DATE_FORMAT(CURDATE(), '%Y-%m-01') " +
        "AND start_time <= CURDATE() " +
        "AND del_flag = '0'")
    Long selectMonthDuration(@Param("userId") Long userId, @Param("activityType") ActivityType activityType);

    /**
     * 查询用户总活动时长
     *
     * @param userId       用户ID
     * @param activityType 活动类型
     * @return 总活动时长(毫秒)
     */
    @Select("SELECT COALESCE(SUM(duration), 0) FROM app_activity_session " +
        "WHERE user_id = #{userId} " +
        "AND (#{activityType} IS NULL OR activity_type = #{activityType}) " +
        "AND del_flag = '0'")
    Long selectTotalDuration(@Param("userId") Long userId, @Param("activityType") ActivityType activityType);

    /**
     * 删除用户指定类型的活动记录
     *
     * @param userId       用户ID
     * @param activityType 活动类型
     * @return 删除行数
     */
    @Update("UPDATE app_activity_session SET del_flag = '2', update_time = NOW() " +
        "WHERE user_id = #{userId} " +
        "AND (#{activityType} IS NULL OR activity_type = #{activityType}) " +
        "AND del_flag = '0'")
    int deleteUserActivityRecords(@Param("userId") Long userId, @Param("activityType") ActivityType activityType);
}
