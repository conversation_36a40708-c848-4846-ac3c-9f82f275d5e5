package org.dromara.app.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 学习进度实体
 *
 * <AUTHOR>
 */
@Data
@TableName(value = "app_learning_progress", autoResultMap = true)
public class LearningProgress extends BaseEntity {

    /**
     * 进度ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 学习路径ID
     */
    private String learningPathId;

    /**
     * 资源ID
     */
    private Long resourceId;

    /**
     * 进度状态：not_started-未开始, in_progress-进行中, completed-已完成, paused-暂停
     */
    private String status;

    /**
     * 完成百分比（0-100）
     */
    private Integer completionPercentage;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 最后学习时间
     */
    private LocalDateTime lastStudyTime;

    /**
     * 完成时间
     */
    private LocalDateTime completionTime;

    /**
     * 预计完成时间
     */
    private LocalDateTime estimatedCompletionTime;

    /**
     * 实际学习时长（分钟）
     */
    private Integer actualStudyMinutes;

    /**
     * 预计学习时长（分钟）
     */
    private Integer estimatedStudyMinutes;

    /**
     * 学习效果评分（1-5分）
     */
    private Double effectivenessRating;

    /**
     * 难度感受评分（1-5分）
     */
    private Double difficultyRating;

    /**
     * 满意度评分（1-5分）
     */
    private Double satisfactionRating;

    /**
     * 学习笔记
     */
    private String notes;

    /**
     * 学习目标
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> learningGoals;

    /**
     * 已完成的里程碑
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> completedMilestones;

    /**
     * 学习统计
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private LearningStatistics learningStatistics;

    /**
     * 学习计划
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private LearningPlan learningPlan;

    /**
     * 学习反馈
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<LearningFeedback> feedbackList;

    /**
     * 扩展数据
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> extendedData;

    /**
     * 学习统计
     */
    @Data
    public static class LearningStatistics {
        /**
         * 总学习天数
         */
        private Integer totalStudyDays;

        /**
         * 连续学习天数
         */
        private Integer consecutiveStudyDays;

        /**
         * 平均每日学习时长（分钟）
         */
        private Integer averageDailyMinutes;

        /**
         * 最长单次学习时长（分钟）
         */
        private Integer longestSessionMinutes;

        /**
         * 学习频率（次/周）
         */
        private Double studyFrequency;

        /**
         * 完成率趋势
         */
        private List<Double> completionTrend;

        /**
         * 学习效率评分
         */
        private Double efficiencyScore;

        /**
         * 最后更新时间
         */
        private String lastUpdated;
    }

    /**
     * 学习计划
     */
    @Data
    public static class LearningPlan {
        /**
         * 计划开始日期
         */
        private String plannedStartDate;

        /**
         * 计划完成日期
         */
        private String plannedEndDate;

        /**
         * 每日学习时长（分钟）
         */
        private Integer dailyStudyMinutes;

        /**
         * 每周学习天数
         */
        private Integer weeklyStudyDays;

        /**
         * 学习提醒设置
         */
        private ReminderSettings reminderSettings;

        /**
         * 阶段性目标
         */
        private List<PhaseGoal> phaseGoals;

        /**
         * 计划调整记录
         */
        private List<PlanAdjustment> adjustments;
    }

    /**
     * 提醒设置
     */
    @Data
    public static class ReminderSettings {
        /**
         * 是否启用提醒
         */
        private Boolean enabled;

        /**
         * 提醒时间
         */
        private List<String> reminderTimes;

        /**
         * 提醒方式
         */
        private List<String> reminderMethods;

        /**
         * 提醒频率
         */
        private String frequency;
    }

    /**
     * 阶段性目标
     */
    @Data
    public static class PhaseGoal {
        /**
         * 目标名称
         */
        private String goalName;

        /**
         * 目标描述
         */
        private String description;

        /**
         * 目标日期
         */
        private String targetDate;

        /**
         * 完成状态
         */
        private Boolean completed;

        /**
         * 完成日期
         */
        private String completionDate;

        /**
         * 目标权重
         */
        private Double weight;
    }

    /**
     * 计划调整记录
     */
    @Data
    public static class PlanAdjustment {
        /**
         * 调整日期
         */
        private String adjustmentDate;

        /**
         * 调整原因
         */
        private String reason;

        /**
         * 调整内容
         */
        private String adjustmentDetails;

        /**
         * 调整类型
         */
        private String adjustmentType;
    }

    /**
     * 学习反馈
     */
    @Data
    public static class LearningFeedback {
        /**
         * 反馈日期
         */
        private String feedbackDate;

        /**
         * 反馈类型：daily-日常, milestone-里程碑, completion-完成
         */
        private String feedbackType;

        /**
         * 学习感受
         */
        private String learningExperience;

        /**
         * 遇到的困难
         */
        private List<String> difficulties;

        /**
         * 收获和心得
         */
        private List<String> insights;

        /**
         * 改进建议
         */
        private List<String> improvements;

        /**
         * 情绪状态
         */
        private String emotionalState;

        /**
         * 自信心评分
         */
        private Double confidenceRating;
    }
}