package org.dromara.app.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.*;
import org.dromara.app.domain.dto.QuestionBankQueryDto;
import org.dromara.app.domain.dto.QuestionQueryDto;
import org.dromara.app.domain.enums.QuestionDifficultyEnum;
import org.dromara.app.domain.vo.*;
import org.dromara.app.mapper.*;
import org.dromara.app.service.ILearningService;
import org.dromara.app.utils.QuestionUtils;
import org.dromara.common.caffeine.annotation.CaffeineCache;
import org.dromara.common.caffeine.annotation.CaffeineEvict;
import org.dromara.common.caffeine.annotation.TimeUnit;
import org.dromara.common.core.enums.FormatsType;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 学习资源Service实现
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class LearningServiceImpl implements ILearningService {

    private final MajorMapper majorMapper;
    private final QuestionBankMapper questionBankMapper;
    private final QuestionBankBookmarkMapper bookmarkMapper;
    private final QuestionMapper questionMapper;
    private final QuestionCommentMapper questionCommentMapper;

    @Override
    @CaffeineCache(cacheName = "majorList", key = "getMajorList", expire = "12", timeUnit = TimeUnit.HOURS)
    public List<MajorVo> getMajorList() {
        // 查询所有启用的专业
        LambdaQueryWrapper<Major> wrapper = Wrappers.lambdaQuery(Major.class)
            .eq(Major::getStatus, "0")
            .orderByAsc(Major::getSort);

        List<Major> majorList = majorMapper.selectList(wrapper);

        // 转换为VO对象
        return majorList.stream().map(major -> {
            MajorVo vo = new MajorVo();
            vo.setId(major.getMajorCode());
            vo.setName(major.getMajorName());
            vo.setIcon(major.getIcon());
            vo.setColor(major.getColor());
            vo.setCount(major.getQuestionBankCount());
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    @CaffeineCache(
        cacheName = "questionBankList",
        key = "#queryDto.majorId + '_' + #queryDto.filter + '_' + #queryDto.keyword + '_' + #queryDto.page + '_' + #queryDto.pageSize",
        expire = "6",
        timeUnit = TimeUnit.HOURS
    )
    public TableDataInfo<QuestionBankVo> getQuestionBankList(QuestionBankQueryDto queryDto) {
        // 构建查询条件
        LambdaQueryWrapper<QuestionBank> wrapper = buildQueryWrapper(queryDto);

        // 分页查询
        Page<QuestionBank> page = new Page<>(queryDto.getPage(), queryDto.getPageSize());
        Page<QuestionBank> result = questionBankMapper.selectPage(page, wrapper);

        // 转换为VO对象
        List<QuestionBankVo> voList = result.getRecords().stream()
            .map(bank -> convertToVo(bank, null))
            .collect(Collectors.toList());

        // 构建分页结果
        TableDataInfo<QuestionBankVo> tableDataInfo = new TableDataInfo<>();
        tableDataInfo.setRows(voList);
        tableDataInfo.setTotal(result.getTotal());
        tableDataInfo.setCode(200);
        tableDataInfo.setMsg("查询成功");

        return tableDataInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Boolean> toggleBookmark(Long userId, String bankId, Boolean isBookmarked) {
        Map<String, Boolean> result = new HashMap<>();

        if (isBookmarked) {
            // 添加收藏
            QuestionBankBookmark bookmark = new QuestionBankBookmark();
            bookmark.setUserId(userId);
            bookmark.setBankId(Long.valueOf(bankId));
            bookmarkMapper.insert(bookmark);
        } else {
            // 取消收藏
            LambdaQueryWrapper<QuestionBankBookmark> wrapper = Wrappers.lambdaQuery(QuestionBankBookmark.class)
                .eq(QuestionBankBookmark::getUserId, userId)
                .eq(QuestionBankBookmark::getBankId, Long.valueOf(bankId));
            bookmarkMapper.delete(wrapper);
        }

        result.put("isBookmarked", isBookmarked);
        return result;
    }

    @Override
    @CaffeineCache(
        cacheName = "searchQuestionBanks",
        key = "#keyword + '_' + #majorId",
        expire = "30",
        timeUnit = TimeUnit.MINUTES,
        condition = "#keyword != null || #majorId != null"
    )
    public List<QuestionBankVo> searchQuestionBanks(String keyword, String majorId) {
        LambdaQueryWrapper<QuestionBank> wrapper = Wrappers.lambdaQuery(QuestionBank.class)
            .eq(QuestionBank::getStatus, "0");

        // 添加关键词搜索条件
        if (StrUtil.isNotBlank(keyword)) {
            wrapper.and(w -> w.like(QuestionBank::getTitle, keyword)
                .or().like(QuestionBank::getDescription, keyword));
        }

        // 添加专业筛选
        if (StrUtil.isNotBlank(majorId)) {
            Major major = majorMapper.selectOne(Wrappers.lambdaQuery(Major.class)
                .eq(Major::getMajorCode, majorId));
            if (major != null) {
                wrapper.eq(QuestionBank::getMajorId, major.getMajorId());
            }
        }

        List<QuestionBank> banks = questionBankMapper.selectList(wrapper);
        return banks.stream()
            .map(bank -> convertToVo(bank, null))
            .collect(Collectors.toList());
    }

    @Override
    @CaffeineCache(
        key = "#bankId + '_' + #majorId + '_' + T(String).valueOf(#userId)",
        cacheName = "questionBankDetail",
        expire = "1",
        timeUnit = TimeUnit.HOURS
    )
    public QuestionBankVo getQuestionBankDetail(String bankId, String majorId, Long userId) {
        QuestionBank bank = questionBankMapper.selectById(Long.valueOf(bankId));
        if (bank == null) {
            return null;
        }

        return convertToVo(bank, userId);
    }

    @Override
    @CaffeineCache(cacheName = "hotQuestionBanks", key = "#limit", expire = "1", timeUnit = TimeUnit.HOURS)
    public List<QuestionBankVo> getHotQuestionBanks(Integer limit) {
        if (limit == null || limit <= 0) {
            limit = 10;
        }

        LambdaQueryWrapper<QuestionBank> wrapper = Wrappers.lambdaQuery(QuestionBank.class)
            .eq(QuestionBank::getStatus, "0")
            .orderByDesc(QuestionBank::getPracticeCount)
            .last("LIMIT " + limit);

        List<QuestionBank> banks = questionBankMapper.selectList(wrapper);
        return banks.stream()
            .map(bank -> convertToVo(bank, null))
            .collect(Collectors.toList());
    }

    @Override
    @CaffeineCache(
        cacheName = "newQuestionBanks",
        key = "#limit",
        expire = "10",
        timeUnit = TimeUnit.MINUTES
    )
    public List<QuestionBankVo> getNewQuestionBanks(Integer limit) {
        if (limit == null || limit <= 0) {
            limit = 10;
        }

        LambdaQueryWrapper<QuestionBank> wrapper = Wrappers.lambdaQuery(QuestionBank.class)
            .eq(QuestionBank::getStatus, "0")
            .orderByDesc(QuestionBank::getCreateTime)
            .last("LIMIT " + limit);

        List<QuestionBank> banks = questionBankMapper.selectList(wrapper);
        return banks.stream()
            .map(bank -> convertToVo(bank, null))
            .collect(Collectors.toList());
    }

    @Override
    @CaffeineCache(
        cacheName = "questionBankFullDetail",
        key = "#bankId + '_' + #userId",
        expire = "2",
        timeUnit = TimeUnit.HOURS
    )
    public QuestionBankDetailVO getQuestionBankFullDetail(String bankId, Long userId) {
        QuestionBank bank = questionBankMapper.selectById(Long.valueOf(bankId));
        if (bank == null) {
            return null;
        }

        QuestionBankDetailVO vo = new QuestionBankDetailVO();
        vo.setId(bank.getBankId());
        vo.setCode(bank.getBankCode());
        vo.setTitle(bank.getTitle());
        vo.setDescription(bank.getDescription());
        vo.setIcon(bank.getIcon());
        vo.setColor(bank.getColor());

        // 转换难度：使用枚举类方法替代硬编码
        vo.setDifficulty(QuestionUtils.getDifficultyDescription(bank.getDifficulty()));

        vo.setTotalQuestions(bank.getTotalQuestions());
        vo.setPracticeCount(bank.getPracticeCount());

        // 解析分类标签
        if (StrUtil.isNotBlank(bank.getCategories())) {
            vo.setCategories(JSONUtil.toList(bank.getCategories(), String.class));
        } else {
            vo.setCategories(new ArrayList<>());
        }

        // 查询用户学习进度（这里暂时返回模拟数据）
        vo.setProgress(68);

        // 查询收藏状态
        if (userId != null) {
            LambdaQueryWrapper<QuestionBankBookmark> wrapper = Wrappers.lambdaQuery(QuestionBankBookmark.class)
                .eq(QuestionBankBookmark::getUserId, userId)
                .eq(QuestionBankBookmark::getBankId, bank.getBankId());
            long count = bookmarkMapper.selectCount(wrapper);
            vo.setIsBookmarked(count > 0);
        } else {
            vo.setIsBookmarked(false);
        }

        return vo;
    }

    @Override
    @CaffeineCache(
        cacheName = "questionsByCategory",
        key = "#bankId + '_' + #category + '_' + #pageNum + '_' + #pageSize",
        expire = "30",
        timeUnit = TimeUnit.MINUTES
    )
    public Map<String, Object> getQuestionsByCategory(String bankId, String category, Integer pageNum, Integer pageSize) {
        Map<String, Object> result = new HashMap<>();

        // 参数校验和默认值处理
        pageNum = pageNum != null && pageNum > 0 ? pageNum : 1;
        pageSize = pageSize != null && pageSize > 0 ? pageSize : 10;

        // 构建查询条件
        LambdaQueryWrapper<Question> wrapper = Wrappers.lambdaQuery(Question.class)
            .eq(Question::getBankId, Long.valueOf(bankId))
            .eq(Question::getStatus, "0")
            .orderByDesc(Question::getCreateTime);

        // 如果指定了分类，则添加分类筛选条件
        if (!"all".equals(category) && StrUtil.isNotBlank(category)) {
            wrapper.eq(Question::getCategory, category);
        }

        // 创建分页对象
        Page<Question> page = new Page<>(pageNum, pageSize);

        // 执行分页查询
        Page<Question> questionPage = questionMapper.selectPage(page, wrapper);

        // 转换为VO对象
        List<QuestionVO> voList = questionPage.getRecords().stream()
            .map(this::convertQuestionToVo)
            .collect(Collectors.toList());

        // 使用分类名称作为key，如果没有指定分类则使用"all"
        String categoryKey = StrUtil.isNotBlank(category) ? category : "all";
        result.put(categoryKey, voList);
        // 添加分页信息
        result.put("page", questionPage.getTotal());
        // 返回总数
        return result;
    }

    @Override
    @CaffeineCache(
        cacheName = "recommendedQuestions",
        key = "#bankId + '_' + #limit",
        expire = "1",
        timeUnit = TimeUnit.HOURS
    )
    public List<QuestionVO> getRecommendedQuestions(String bankId, Integer limit) {
        if (limit == null || limit <= 0) {
            limit = 5;
        }

        List<Question> questions = questionMapper.selectRecommendedQuestions(Long.valueOf(bankId), limit);
        return questions.stream()
            .map(this::convertQuestionToVo)
            .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public Map<String, Object> toggleQuestionBankBookmark(Long userId, String bankId) {
        Map<String, Object> result = new HashMap<>();

        // 查询当前收藏状态
        LambdaQueryWrapper<QuestionBankBookmark> wrapper = Wrappers.lambdaQuery(QuestionBankBookmark.class)
            .eq(QuestionBankBookmark::getUserId, userId)
            .eq(QuestionBankBookmark::getBankId, Long.valueOf(bankId));

        QuestionBankBookmark bookmark = bookmarkMapper.selectOne(wrapper);

        if (bookmark != null) {
            // 已收藏，取消收藏
            bookmarkMapper.delete(wrapper);
            result.put("isBookmarked", false);
            result.put("message", "取消收藏成功");
        } else {
            // 未收藏，添加收藏
            bookmark = new QuestionBankBookmark();
            bookmark.setUserId(userId);
            bookmark.setBankId(Long.valueOf(bankId));
            bookmarkMapper.insert(bookmark);
            result.put("isBookmarked", true);
            result.put("message", "收藏成功");
        }

        return result;
    }

    @Override
    public TableDataInfo<QuestionVO> getQuestionList(String bankId, QuestionQueryDto queryDto) {
        // 构建查询条件
        LambdaQueryWrapper<Question> wrapper = Wrappers.lambdaQuery(Question.class)
            .eq(Question::getBankId, Long.valueOf(bankId))
            .eq(Question::getStatus, "0");

        // 添加搜索条件
        if (StrUtil.isNotBlank(queryDto.getKeyword())) {
            wrapper.and(w -> w.like(Question::getTitle, queryDto.getKeyword())
                .or().like(Question::getContent, queryDto.getKeyword()));
        }

        // 添加难度筛选：将英文难度转换为数字代码
        if (StrUtil.isNotBlank(queryDto.getDifficulty())) {
            Integer difficultyCode = switch (queryDto.getDifficulty().toLowerCase()) {
                case "easy" -> QuestionDifficultyEnum.EASY.getCode();
                case "medium" -> QuestionDifficultyEnum.MEDIUM.getCode();
                case "hard" -> QuestionDifficultyEnum.HARD.getCode();
                default -> null;
            };
            if (difficultyCode != null) {
                wrapper.eq(Question::getDifficulty, difficultyCode);
            }
        }

        // 添加分类筛选
        if (StrUtil.isNotBlank(queryDto.getCategory())) {
            wrapper.eq(Question::getCategory, queryDto.getCategory());
        }

        // 添加完成状态筛选（需要根据用户ID查询用户答题记录）
        if (queryDto.getCompleted() != null) {
            // 这里需要根据实际的用户答题记录表来实现
            // 暂时跳过此筛选条件
        }

        // 添加排序条件
        if (StrUtil.isNotBlank(queryDto.getOrderBy())) {
            boolean isAsc = "asc".equalsIgnoreCase(queryDto.getOrderDirection());
            switch (queryDto.getOrderBy()) {
                case "difficulty":
                    // 难度排序：使用枚举类代码进行排序（简单=1，中等=2，困难=3）
                    wrapper.orderBy(true, isAsc, Question::getDifficulty);
                    break;
                case "practiceCount":
                    wrapper.orderBy(true, isAsc, Question::getPracticeCount);
                    break;
                case "createTime":
                    wrapper.orderBy(true, isAsc, Question::getCreateTime);
                    break;
                default:
                    wrapper.orderByDesc(Question::getCreateTime);
            }
        } else {
            wrapper.orderByDesc(Question::getCreateTime);
        }

        // 分页查询
        Page<Question> page = new Page<>(queryDto.getPageNum(), queryDto.getPageSize());
        Page<Question> result = questionMapper.selectPage(page, wrapper);

        // 转换为VO对象
        List<QuestionVO> voList = result.getRecords().stream()
            .map(this::convertQuestionToVo)
            .collect(Collectors.toList());

        // 构建分页结果
        TableDataInfo<QuestionVO> tableDataInfo = new TableDataInfo<>();
        tableDataInfo.setRows(voList);
        tableDataInfo.setTotal(result.getTotal());
        tableDataInfo.setCode(200);
        tableDataInfo.setMsg("查询成功");

        return tableDataInfo;
    }

    @Override
    @CaffeineCache(
        cacheName = "questionDetail",
        key = "#questionId + '_' + #userId",
        expire = "4",
        timeUnit = TimeUnit.HOURS
    )
    public QuestionDetailVO getQuestionDetail(String questionId, Long userId) {
        Question question = questionMapper.selectById(Long.valueOf(questionId));
        if (question == null) {
            return null;
        }

        QuestionDetailVO vo = new QuestionDetailVO();
        vo.setId(question.getQuestionId().toString());
        vo.setTitle(question.getTitle());
        vo.setDescription(question.getDescription());
        vo.setContent(question.getContent());
        vo.setAnswer(question.getAnswer());
        vo.setAnalysis(question.getAnalysis());

        // 转换难度：使用工具类将数字代码转换为中文描述
        vo.setDifficulty(QuestionUtils.getDifficultyDescription(question.getDifficulty()));

        // 转换题目类型：使用工具类将数字代码转换为中文描述
        vo.setQuestionType(QuestionUtils.getTypeDescription(question.getType()));

        // 解析标签
        if (StrUtil.isNotBlank(question.getTags())) {
            vo.setTags(JSONUtil.toList(question.getTags(), String.class));
        } else {
            vo.setTags(new ArrayList<>());
        }

        vo.setAcceptanceRate(question.getAcceptanceRate());
        vo.setPracticeCount(question.getPracticeCount());
        vo.setCorrectRate(question.getCorrectRate());
        vo.setCommentCount(question.getCommentCount());
        vo.setCategory(question.getCategory());

        // 获取题库信息
        QuestionBank bank = questionBankMapper.selectById(question.getBankId());
        if (bank != null) {
            vo.setBankId(bank.getBankId().toString());
            vo.setBankTitle(bank.getTitle());
        }

        // TODO: 查询用户完成状态
        vo.setIsCompleted(false);

        // 时间格式转换
        vo.setCreateTime(DateUtils.parseDateToStr(FormatsType.YYYY_MM_DD_HH_MM_SS, question.getCreateTime()));
        vo.setUpdateTime(DateUtils.parseDateToStr(FormatsType.YYYY_MM_DD_HH_MM_SS, question.getUpdateTime()));

        return vo;
    }

    @Override
    public List<QuestionVO> searchQuestions(String bankId, String keyword, String difficulty,
                                            String category, Boolean completed, Long userId) {
        // 构建查询条件
        LambdaQueryWrapper<Question> wrapper = Wrappers.lambdaQuery(Question.class)
            .eq(Question::getBankId, Long.valueOf(bankId))
            .eq(Question::getStatus, "0");

        // 关键词搜索
        if (StrUtil.isNotBlank(keyword)) {
            wrapper.and(w -> w.like(Question::getTitle, keyword)
                .or().like(Question::getDescription, keyword)
                .or().like(Question::getTags, keyword));
        }

        // 难度筛选（使用枚举类进行转换）
        if (StrUtil.isNotBlank(difficulty)) {
            Integer difficultyValue = switch (difficulty) {
                case "easy" -> QuestionDifficultyEnum.EASY.getCode();
                case "medium" -> QuestionDifficultyEnum.MEDIUM.getCode();
                case "hard" -> QuestionDifficultyEnum.HARD.getCode();
                default -> null;
            };
            if (difficultyValue != null) {
                wrapper.eq(Question::getDifficulty, difficultyValue);
            }
        }

        // 分类筛选
        if (StrUtil.isNotBlank(category)) {
            wrapper.eq(Question::getCategory, category);
        }

        // TODO: 根据完成状态筛选（需要关联用户学习记录表）

        List<Question> questions = questionMapper.selectList(wrapper);
        return questions.stream()
            .map(this::convertQuestionToVo)
            .collect(Collectors.toList());
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<QuestionBank> buildQueryWrapper(QuestionBankQueryDto queryDto) {
        LambdaQueryWrapper<QuestionBank> wrapper = Wrappers.lambdaQuery(QuestionBank.class)
            .eq(QuestionBank::getStatus, "0");

        // 专业筛选
        if (StrUtil.isNotBlank(queryDto.getMajorId())) {
            Major major = majorMapper.selectOne(Wrappers.lambdaQuery(Major.class)
                .eq(Major::getMajorCode, queryDto.getMajorId()));
            if (major != null) {
                wrapper.eq(QuestionBank::getMajorId, major.getMajorId());
            }
        }

        // 筛选条件（使用枚举类进行转换）
        switch (queryDto.getFilter()) {
            case "easy":
                wrapper.eq(QuestionBank::getDifficulty, QuestionDifficultyEnum.EASY.getCode());
                break;
            case "medium":
                wrapper.eq(QuestionBank::getDifficulty, QuestionDifficultyEnum.MEDIUM.getCode());
                break;
            case "hard":
                wrapper.eq(QuestionBank::getDifficulty, QuestionDifficultyEnum.HARD.getCode());
                break;
            case "hot":
                wrapper.orderByDesc(QuestionBank::getPracticeCount);
                break;
            case "new":
                wrapper.orderByDesc(QuestionBank::getCreateTime);
                break;
            default:
                wrapper.orderByAsc(QuestionBank::getSort);
                break;
        }

        // 关键词搜索
        if (StrUtil.isNotBlank(queryDto.getKeyword())) {
            wrapper.and(w -> w.like(QuestionBank::getTitle, queryDto.getKeyword())
                .or().like(QuestionBank::getDescription, queryDto.getKeyword()));
        }

        return wrapper;
    }

    /**
     * 转换为VO对象
     */
    private QuestionBankVo convertToVo(QuestionBank bank, Long userId) {
        QuestionBankVo vo = new QuestionBankVo();
        vo.setId(bank.getBankId().toString());
        vo.setTitle(bank.getTitle());
        vo.setDescription(bank.getDescription());
        vo.setIcon(bank.getIcon());
        vo.setColor(bank.getColor());
        vo.setDifficulty(QuestionUtils.getDifficultyDescription(bank.getDifficulty()));
        vo.setTotalQuestions(bank.getTotalQuestions());
        vo.setPracticeCount(bank.getPracticeCount());
        if (StrUtil.isNotBlank(bank.getCategories())) {
            vo.setCategories(JSONUtil.toList(bank.getCategories(), String.class));
        } else {
            vo.setCategories(new ArrayList<>());
        }

        // TODO:查询用户学习进度（这里暂时返回默认值）
        vo.setProgress(0);

        // 用户是否收藏
        if (userId != null) {
            LambdaQueryWrapper<QuestionBankBookmark> wrapper = Wrappers.lambdaQuery(QuestionBankBookmark.class)
                .eq(QuestionBankBookmark::getUserId, userId)
                .eq(QuestionBankBookmark::getBankId, bank.getBankId());
            long count = bookmarkMapper.selectCount(wrapper);
            vo.setIsBookmarked(count > 0);
        } else {
            vo.setIsBookmarked(false);
        }

        // 获取专业信息
        Major major = majorMapper.selectById(bank.getMajorId());
        if (major != null) {
            vo.setMajorId(major.getMajorCode());
        }

        // 时间格式转换
        vo.setCreatedAt(DateUtils.parseDateToStr(FormatsType.YYYY_MM_DD_HH_MM_SS, bank.getCreateTime()));
        vo.setUpdatedAt(DateUtils.parseDateToStr(FormatsType.YYYY_MM_DD_HH_MM_SS, bank.getUpdateTime()));

        return vo;
    }

    /**
     * 转换题目为VO对象
     *
     * @param question 题目实体
     * @return 题目VO
     */
    private QuestionVO convertQuestionToVo(Question question) {
        QuestionVO vo = new QuestionVO();
        vo.setId(question.getQuestionId().toString());
        vo.setTitle(question.getTitle());
        vo.setContent(question.getContent());
        vo.setDescription(question.getContent());

        // 转换难度：使用工具类将数字代码转换为中文描述
        vo.setDifficulty(QuestionUtils.getDifficultyDescription(question.getDifficulty()));

        // 转换题目类型：使用工具类将数字代码转换为中文描述
        vo.setType(QuestionUtils.getTypeDescription(question.getType()));

        vo.setPracticeCount(question.getPracticeCount());
        vo.setCorrectRate(question.getCorrectRate());
        vo.setCommentCount(question.getCommentCount());
        vo.setCategory(question.getCategory());
        vo.setCreateTime(question.getCreateTime());

        // 处理标签 - 从JSON字符串转换为List
        if (StrUtil.isNotBlank(question.getTags())) {
            try {
                List<String> tagList = JSONUtil.toList(question.getTags(), String.class);
                vo.setTags(tagList);
            } catch (Exception e) {
                log.warn("解析题目标签失败: {}", e.getMessage());
                vo.setTags(new ArrayList<>());
            }
        } else {
            vo.setTags(new ArrayList<>());
        }

        // 计算通过率
        if (question.getPracticeCount() > 0) {
            double acceptanceRate = (double) question.getCorrectRate();
            vo.setAcceptanceRate(acceptanceRate);
        } else {
            vo.setAcceptanceRate(0.0);
        }

        // 设置预计完成时间：使用工具类计算
        vo.setEstimatedTime(QuestionUtils.calculateEstimatedTime(question.getDifficulty(), question.getType()));

        // 设置默认状态（实际使用时应该根据用户ID查询）
        vo.setIsCompleted(false);
        vo.setIsBookmarked(false);

        // 设置题库信息
        vo.setBankId(question.getBankId().toString());

        // 可以根据需要查询题库标题
        try {
            QuestionBank bank = questionBankMapper.selectById(question.getBankId());
            if (bank != null) {
                vo.setBankTitle(bank.getTitle());
            }
        } catch (Exception e) {
            log.warn("查询题库标题失败: {}", e.getMessage());
        }

        return vo;
    }

    @Override
    public Map<String, Object> toggleQuestionBookmark(Long userId, String questionId, Boolean isBookmarked) {
        // TODO: 实现题目收藏功能
        return Map.of("isBookmarked", false, "message", "功能暂未实现");
    }

    @Override
    public Map<String, Object> getQuestionComments(String questionId, Integer page, Integer pageSize, String orderBy, String orderDirection) {
        try {
            // 参数校验
            if (StrUtil.isBlank(questionId)) {
                throw new IllegalArgumentException("题目ID不能为空");
            }

            // 分页参数处理
            page = page != null && page > 0 ? page : 1;
            pageSize = pageSize != null && pageSize > 0 ? pageSize : 10;
            pageSize = Math.min(pageSize, 50);

            // 排序参数处理
            orderBy = StrUtil.isNotBlank(orderBy) ? orderBy : "createTime";
            orderDirection = StrUtil.isNotBlank(orderDirection) ? orderDirection : "desc";

            // 创建分页对象
            Page<QuestionCommentVO> pageObj = new Page<>(page, pageSize);

            // 查询主评论列表（不包含回复）
            List<QuestionCommentVO> comments = questionCommentMapper.selectCommentListWithReplies(
                pageObj, Long.valueOf(questionId), orderBy, orderDirection);

            // 为每个主评论加载回复
            for (QuestionCommentVO comment : comments) {
                if (comment.getReplyCount() > 0) {
                    List<QuestionCommentVO> replies = questionCommentMapper.selectRepliesByParentId(
                        Long.valueOf(comment.getId()));
                    comment.setReplies(replies);
                } else {
                    comment.setReplies(new ArrayList<>());
                }
            }
            // 获取当前用户ID
            Long userId = StpUtil.getLoginIdAsLong();
            // 查询每个评论是否点赞
            for (QuestionCommentVO comment : comments) {
                // 判断当前用户是否点赞
                comment.setLiked(questionCommentMapper.selectLikeStatus(userId, Long.valueOf(comment.getId())));
                comment.getReplies().forEach(reply -> {
                    reply.setLiked(questionCommentMapper.selectLikeStatus(userId, Long.valueOf(reply.getId())));
                });
            }

            // 计算总数
            long total = pageObj.getTotal();

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("list", comments);
            result.put("total", total);
            result.put("page", page);
            result.put("pageSize", pageSize);
            result.put("hasMore", page * pageSize < total);

            return result;
        } catch (Exception e) {
            log.error("获取题目评论失败：questionId={}, error={}", questionId, e.getMessage(), e);
            throw new RuntimeException("获取评论失败", e);
        }
    }

    @Override
    @CaffeineEvict(
        cacheName = "questionComments",
        key = "#questionId"
    )
    public QuestionCommentVO createQuestionComment(Long userId, String questionId, String content, Long parentId) {
        try {
            // 参数校验
            if (userId == null || userId <= 0) {
                throw new IllegalArgumentException("用户ID无效");
            }
            if (StrUtil.isBlank(questionId)) {
                throw new IllegalArgumentException("题目ID不能为空");
            }
            if (StrUtil.isBlank(content)) {
                throw new IllegalArgumentException("评论内容不能为空");
            }
            if (content.length() > 500) {
                throw new IllegalArgumentException("评论内容不能超过500个字符");
            }
            // 检查题目是否存在
            Question question = questionMapper.selectById(Long.valueOf(questionId));
            if (question == null) {
                throw new IllegalArgumentException("题目不存在");
            }
            // 如果是回复，检查父评论是否存在
            if (parentId != null && parentId > 0) {
                QuestionComment parentComment = questionCommentMapper.selectById(parentId);
                if (parentComment == null || !Objects.equals(parentComment.getQuestionId(), Long.valueOf(questionId))) {
                    throw new IllegalArgumentException("父评论不存在或不属于当前题目");
                }
            }
            // 创建评论实体
            QuestionComment comment = new QuestionComment();
            comment.setCommentId(IdUtil.getSnowflakeNextId());
            comment.setQuestionId(Long.valueOf(questionId));
            comment.setUserId(userId);
            comment.setParentId(parentId);
            comment.setContent(content);
            comment.setLikeCount(0);
            comment.setReplyCount(0);
            comment.setStatus("0");
            comment.setSort(0);
            comment.setCreateBy(userId);
            comment.setCreateTime(new Date());
            comment.setUpdateBy(userId);
            comment.setUpdateTime(new Date());

            // 插入评论
            int insertResult = questionCommentMapper.insert(comment);
            if (insertResult <= 0) {
                throw new RuntimeException("评论创建失败");
            }

            // 更新相关计数
            if (parentId != null && parentId > 0) {
                // 如果是回复，增加父评论的回复数
                questionCommentMapper.incrementReplyCount(parentId);
            } else {
                // 如果是主评论，增加题目的评论数
                questionCommentMapper.updateQuestionCommentCount(Long.valueOf(questionId), 1);
            }

            // 查询并返回创建的评论信息
            return convertCommentToVO(comment, userId);

        } catch (Exception e) {
            log.error("创建题目评论失败：userId={}, questionId={}, error={}", userId, questionId, e.getMessage(), e);
            throw new RuntimeException("评论失败", e);
        }
    }

    @Override
    @CaffeineEvict(
        cacheName = "questionComments",
        allEntries = true
    )
    public boolean deleteQuestionComment(Long userId, String commentId) {
        try {
            // 参数校验
            if (userId == null || userId <= 0) {
                throw new IllegalArgumentException("用户ID无效");
            }
            if (StrUtil.isBlank(commentId)) {
                throw new IllegalArgumentException("评论ID不能为空");
            }

            // 查询评论是否存在
            QuestionComment comment = questionCommentMapper.selectById(Long.valueOf(commentId));
            if (comment == null) {
                return false;
            }

            // 检查权限（只能删除自己的评论）
            if (!Objects.equals(comment.getUserId(), userId)) {
                throw new IllegalArgumentException("无权限删除此评论");
            }

            // 标记删除（软删除）
            comment.setStatus("1");
            comment.setUpdateBy(userId);
            comment.setUpdateTime(new Date());

            int updateResult = questionCommentMapper.updateById(comment);
            if (updateResult <= 0) {
                return false;
            }

            // 更新相关计数
            if (comment.getParentId() != null && comment.getParentId() > 0) {
                // 如果是回复，减少父评论的回复数
                questionCommentMapper.decrementReplyCount(comment.getParentId());
            } else {
                // 如果是主评论，减少题目的评论数
                questionCommentMapper.updateQuestionCommentCount(comment.getQuestionId(), -1);
            }

            return true;

        } catch (Exception e) {
            log.error("删除题目评论失败：userId={}, commentId={}, error={}", userId, commentId, e.getMessage(), e);
            throw new RuntimeException("删除评论失败", e);
        }
    }

    @Override
    @CaffeineEvict(
        cacheName = "questionComments",
        allEntries = true
    )
    public Map<String, Object> likeQuestionComment(Long userId, String commentId) {
        try {
            // 参数校验
            if (userId == null || userId <= 0) {
                throw new IllegalArgumentException("用户ID无效");
            }
            if (StrUtil.isBlank(commentId)) {
                throw new IllegalArgumentException("评论ID不能为空");
            }

            // 查询评论是否存在
            QuestionComment comment = questionCommentMapper.selectById(Long.valueOf(commentId));
            if (comment == null) {
                throw new IllegalArgumentException("评论不存在");
            }

            // 查询当前用户是否已点赞
            Boolean isLiked = questionCommentMapper.selectLikeStatus(userId, Long.valueOf(commentId));
            boolean currentLikeStatus = isLiked != null && isLiked;

            Map<String, Object> result = new HashMap<>();

            if (currentLikeStatus) {
                // 已点赞，执行取消点赞操作
                questionCommentMapper.deleteLikeRecord(Long.valueOf(commentId), userId);
                questionCommentMapper.decrementLikeCount(Long.valueOf(commentId));

                result.put("isLiked", false);
                result.put("likeCount", Math.max(comment.getLikeCount() - 1, 0));
                result.put("message", "取消点赞");
            } else {
                // 未点赞，执行点赞操作
                questionCommentMapper.insertLikeRecord(Long.valueOf(commentId), userId);
                questionCommentMapper.incrementLikeCount(Long.valueOf(commentId));

                result.put("isLiked", true);
                result.put("likeCount", comment.getLikeCount() + 1);
                result.put("message", "点赞成功");
            }

            return result;

        } catch (Exception e) {
            log.error("点赞评论失败：userId={}, commentId={}, error={}", userId, commentId, e.getMessage(), e);
            throw new RuntimeException("点赞操作失败", e);
        }
    }

    @Override
    public Map<String, Object> submitPracticeRecord(Long userId, String questionId, String userAnswer, Integer timeSpent) {
        // TODO: 实现练习记录提交功能
        return Map.of("success", false, "message", "功能暂未实现");
    }

    @Override
    public Map<String, Object> getQuestionStats(String questionId) {
        // TODO: 实现题目统计功能
        return Map.of();
    }

    @Override
    public List<QuestionDetailVO> getRelatedQuestions(String questionId, Integer limit) {
        // TODO: 实现相关题目推荐功能
        return List.of();
    }

    @Override
    public boolean reportContent(Long userId, String targetId, String targetType, String reason, String description) {
        // TODO: 实现内容举报功能
        log.info("用户{}举报{}内容{}，原因：{}，描述：{}", userId, targetType, targetId, reason, description);
        return true;
    }

    /**
     * 转换评论实体为VO对象
     *
     * @param comment       评论实体
     * @param currentUserId 当前用户ID
     * @return 评论VO
     */
    private QuestionCommentVO convertCommentToVO(QuestionComment comment, Long currentUserId) {
        QuestionCommentVO vo = new QuestionCommentVO();
        vo.setId(comment.getCommentId().toString());
        vo.setQuestionId(comment.getQuestionId().toString());
        vo.setUserId(comment.getUserId().toString());
        vo.setContent(comment.getContent());
        vo.setLikes(comment.getLikeCount());
        vo.setReplyCount(comment.getReplyCount());
        vo.setParentId(comment.getParentId() != null ? comment.getParentId().toString() : null);
        vo.setCreateTime(DateUtil.formatDateTime(comment.getCreateTime()));
        vo.setUpdateTime(DateUtil.formatDateTime(comment.getUpdateTime()));

        // 设置时间显示
        vo.setTime(formatTimeDisplay(comment.getCreateTime()));

        // 查询用户信息
        // 这里应该查询用户表获取用户昵称和头像，简化处理
        vo.setAuthor("用户" + comment.getUserId());
        vo.setAvatar("👤");

        return vo;
    }

    /**
     * 格式化时间显示
     *
     * @param createTime 创建时间
     * @return 格式化后的时间字符串
     */
    private String formatTimeDisplay(Date createTime) {
        if (createTime == null) {
            return "未知时间";
        }

        long diff = System.currentTimeMillis() - createTime.getTime();
        long minutes = diff / (60 * 1000);
        long hours = diff / (60 * 60 * 1000);
        long days = diff / (24 * 60 * 60 * 1000);

        if (minutes < 1) {
            return "刚刚";
        } else if (minutes < 60) {
            return minutes + "分钟前";
        } else if (hours < 24) {
            return hours + "小时前";
        } else if (days < 30) {
            return days + "天前";
        } else {
            return DateUtil.format(createTime, "yyyy-MM-dd");
        }
    }

    /**
     * 获取题库题目统计信息
     *
     * @param bankId 题库ID
     * @return 统计信息
     */
    @CaffeineCache(
        cacheName = "questionStatistics",
        key = "#bankId",
        expire = "2",
        timeUnit = TimeUnit.HOURS
    )
    @Override
    public Map<String, Object> getQuestionStatistics(String bankId) {
        Map<String, Object> statistics = new HashMap<>();

        try {
            // 查询题库下的所有题目
            LambdaQueryWrapper<Question> wrapper = Wrappers.lambdaQuery(Question.class)
                .eq(Question::getBankId, Long.valueOf(bankId))
                .eq(Question::getStatus, "0");

            List<Question> questions = questionMapper.selectList(wrapper);

            // 计算统计数据（使用枚举类进行难度比较）
            int totalQuestions = questions.size();
            int easyCount = (int) questions.stream()
                .filter(q -> QuestionDifficultyEnum.EASY.getCode().equals(q.getDifficulty()))
                .count();
            int mediumCount = (int) questions.stream()
                .filter(q -> QuestionDifficultyEnum.MEDIUM.getCode().equals(q.getDifficulty()))
                .count();
            int hardCount = (int) questions.stream()
                .filter(q -> QuestionDifficultyEnum.HARD.getCode().equals(q.getDifficulty()))
                .count();

            // 计算平均正确率
            double averageCorrectRate = questions.stream()
                .mapToInt(Question::getCorrectRate)
                .average()
                .orElse(0.0);

            statistics.put("totalQuestions", totalQuestions);
            statistics.put("easyCount", easyCount);
            statistics.put("mediumCount", mediumCount);
            statistics.put("hardCount", hardCount);
            statistics.put("averageCorrectRate", Math.round(averageCorrectRate));

        } catch (Exception e) {
            log.error("获取题目统计信息失败：{}", e.getMessage(), e);
            // 返回默认值
            statistics.put("totalQuestions", 0);
            statistics.put("easyCount", 0);
            statistics.put("mediumCount", 0);
            statistics.put("hardCount", 0);
            statistics.put("averageCorrectRate", 0);
        }

        return statistics;
    }

    /**
     * 获取专业下的所有题库列表（增强版 - 支持更多筛选和排序）
     *
     * @param queryDto 查询参数
     * @param userId   用户ID
     * @return 题库列表
     */
    @Override
    public TableDataInfo<QuestionBankVo> getMajorQuestionBankList(QuestionBankQueryDto queryDto, Long userId) {
        try {
            // 构建增强的查询条件
            LambdaQueryWrapper<QuestionBank> wrapper = buildEnhancedQueryWrapper(queryDto, userId);

            // 应用排序
            applySorting(wrapper, queryDto.getSortType());

            // 分页查询
            Page<QuestionBank> page = new Page<>(queryDto.getPage(), queryDto.getPageSize());
            Page<QuestionBank> result = questionBankMapper.selectPage(page, wrapper);

            // 转换为VO对象
            List<QuestionBankVo> voList = result.getRecords().stream()
                .map(bank -> convertToVo(bank, userId))
                .collect(Collectors.toList());

            // 构建分页结果
            TableDataInfo<QuestionBankVo> tableDataInfo = new TableDataInfo<>();
            tableDataInfo.setRows(voList);
            tableDataInfo.setTotal(result.getTotal());
            tableDataInfo.setCode(200);
            tableDataInfo.setMsg("查询成功");

            return tableDataInfo;
        } catch (Exception e) {
            log.error("获取专业题库列表失败：{}", e.getMessage(), e);
            throw new RuntimeException("获取题库列表失败", e);
        }
    }

    /**
     * 获取专业题库统计信息
     *
     * @param majorId 专业ID
     * @param userId  用户ID
     * @return 统计信息
     */
    @Override
    @CaffeineCache(
        cacheName = "majorQuestionBankStatistics",
        key = "T(String).valueOf(#majorId) + '_' + T(String).valueOf(#userId)",
        expire = "4",
        timeUnit = TimeUnit.HOURS
    )
    public Map<String, Object> getMajorQuestionBankStatistics(String majorId, Long userId) {
        Map<String, Object> statistics = new HashMap<>();

        try {
            // 获取专业信息
            Major major = null;
            if (StrUtil.isNotBlank(majorId)) {
                major = majorMapper.selectOne(Wrappers.lambdaQuery(Major.class)
                    .eq(Major::getMajorCode, majorId));
            }

            // 构建查询条件
            LambdaQueryWrapper<QuestionBank> wrapper = Wrappers.lambdaQuery(QuestionBank.class)
                .eq(QuestionBank::getStatus, "0");

            if (major != null) {
                wrapper.eq(QuestionBank::getMajorId, major.getMajorId());
            }

            List<QuestionBank> banks = questionBankMapper.selectList(wrapper);

            // 计算基础统计
            int totalBanks = banks.size();
            int totalQuestions = banks.stream()
                .mapToInt(QuestionBank::getTotalQuestions)
                .sum();

            // 计算平均进度（需要考虑用户的学习进度）
            int averageProgress = 0;
            if (userId != null) {
                // TODO: 这里需要根据实际的用户学习进度来计算
                // 暂时使用模拟数据
                averageProgress = 45; // 示例数据
            }

            // 计算收藏和完成状态（需要用户登录）
            int bookmarkedBanks = 0;
            int completedBanks = 0;
            if (userId != null) {
                // 查询用户收藏的题库数量
                bookmarkedBanks = Math.toIntExact(bookmarkMapper.selectCount(
                    Wrappers.lambdaQuery(QuestionBankBookmark.class)
                        .eq(QuestionBankBookmark::getUserId, userId)
                        .in(QuestionBankBookmark::getBankId,
                            banks.stream().map(QuestionBank::getBankId).collect(Collectors.toList()))));

                // TODO: 查询用户完成的题库数量
                // 暂时使用模拟数据
                completedBanks = 2; // 示例数据
            }

            statistics.put("totalBanks", totalBanks);
            statistics.put("totalQuestions", totalQuestions);
            statistics.put("averageProgress", averageProgress);
            statistics.put("bookmarkedBanks", bookmarkedBanks);
            statistics.put("completedBanks", completedBanks);

        } catch (Exception e) {
            log.error("获取专业题库统计信息失败：{}", e.getMessage(), e);
            // 返回默认值
            statistics.put("totalBanks", 0);
            statistics.put("totalQuestions", 0);
            statistics.put("averageProgress", 0);
            statistics.put("bookmarkedBanks", 0);
            statistics.put("completedBanks", 0);
        }

        return statistics;
    }

    /**
     * 获取专业题库筛选选项计数
     *
     * @param majorId 专业ID
     * @param userId  用户ID
     * @return 筛选选项计数
     */
    @Override
    @CaffeineCache(
        cacheName = "majorQuestionBankFilterCounts",
        key = "T(String).valueOf(#majorId) + '_' + T(String).valueOf(#userId)",
        expire = "6",
        timeUnit = TimeUnit.HOURS
    )
    public Map<String, Object> getMajorQuestionBankFilterCounts(String majorId, Long userId) {
        Map<String, Object> filterCounts = new HashMap<>();

        try {
            // 获取专业信息
            Major major = null;
            if (StrUtil.isNotBlank(majorId)) {
                major = majorMapper.selectOne(Wrappers.lambdaQuery(Major.class)
                    .eq(Major::getMajorCode, majorId));
            }

            // 构建基本查询条件
            LambdaQueryWrapper<QuestionBank> baseWrapper = Wrappers.lambdaQuery(QuestionBank.class)
                .eq(QuestionBank::getStatus, "0");

            if (major != null) {
                baseWrapper.eq(QuestionBank::getMajorId, major.getMajorId());
            }

            List<QuestionBank> allBanks = questionBankMapper.selectList(baseWrapper);

            // 计算各个筛选选项的数量
            int allCount = allBanks.size();
            int easyCount = (int) allBanks.stream()
                .filter(bank -> bank.getDifficulty() == 1) // 简单
                .count();
            int mediumCount = (int) allBanks.stream()
                .filter(bank -> bank.getDifficulty() == 2) // 中等
                .count();
            int hardCount = (int) allBanks.stream()
                .filter(bank -> bank.getDifficulty() == 3) // 困难
                .count();

            // 计算收藏和完成状态（需要用户登录）
            int bookmarkedCount = 0;
            int completedCount = 0;
            if (userId != null) {
                // 查询用户收藏的题库
                List<QuestionBankBookmark> bookmarks = bookmarkMapper.selectList(
                    Wrappers.lambdaQuery(QuestionBankBookmark.class)
                        .eq(QuestionBankBookmark::getUserId, userId)
                        .in(QuestionBankBookmark::getBankId,
                            allBanks.stream().map(QuestionBank::getBankId).collect(Collectors.toList())));
                bookmarkedCount = bookmarks.size();

                // TODO: 查询用户完成的题库
                // 暂时使用模拟数据
                completedCount = 2; // 示例数据
            }

            filterCounts.put("all", allCount);
            filterCounts.put("easy", easyCount);
            filterCounts.put("medium", mediumCount);
            filterCounts.put("hard", hardCount);
            filterCounts.put("bookmarked", bookmarkedCount);
            filterCounts.put("completed", completedCount);

        } catch (Exception e) {
            log.error("获取专业题库筛选计数失败：{}", e.getMessage(), e);
            // 返回默认值
            filterCounts.put("all", 0);
            filterCounts.put("easy", 0);
            filterCounts.put("medium", 0);
            filterCounts.put("hard", 0);
            filterCounts.put("bookmarked", 0);
            filterCounts.put("completed", 0);
        }

        return filterCounts;
    }

    /**
     * 构建增强的查询条件
     *
     * @param queryDto 查询参数
     * @param userId   用户ID
     * @return 查询条件包装器
     */
    private LambdaQueryWrapper<QuestionBank> buildEnhancedQueryWrapper(QuestionBankQueryDto queryDto, Long userId) {
        LambdaQueryWrapper<QuestionBank> wrapper = Wrappers.lambdaQuery(QuestionBank.class)
            .eq(QuestionBank::getStatus, "0");

        // 专业筛选
        if (StrUtil.isNotBlank(queryDto.getMajorId())) {
            Major major = majorMapper.selectOne(Wrappers.lambdaQuery(Major.class)
                .eq(Major::getMajorCode, queryDto.getMajorId()));
            if (major != null) {
                wrapper.eq(QuestionBank::getMajorId, major.getMajorId());
            }
        }

        // 关键词搜索
        if (StrUtil.isNotBlank(queryDto.getKeyword())) {
            wrapper.and(w -> w.like(QuestionBank::getTitle, queryDto.getKeyword())
                .or().like(QuestionBank::getDescription, queryDto.getKeyword())
                .or().like(QuestionBank::getCategories, queryDto.getKeyword()));
        }

        // 应用筛选条件
        applyEnhancedFilter(wrapper, queryDto.getFilter(), userId);

        return wrapper;
    }

    /**
     * 应用增强的筛选条件
     *
     * @param wrapper 查询条件包装器
     * @param filter  筛选条件
     * @param userId  用户ID
     */
    private void applyEnhancedFilter(LambdaQueryWrapper<QuestionBank> wrapper, String filter, Long userId) {
        if (StrUtil.isBlank(filter) || "all".equals(filter)) {
            return;
        }

        switch (filter) {
            case "easy":
                wrapper.eq(QuestionBank::getDifficulty, 1);
                break;
            case "medium":
                wrapper.eq(QuestionBank::getDifficulty, 2);
                break;
            case "hard":
                wrapper.eq(QuestionBank::getDifficulty, 3);
                break;
            case "bookmarked":
                if (userId != null) {
                    // 查询用户收藏的题库ID列表
                    List<QuestionBankBookmark> bookmarks = bookmarkMapper.selectList(
                        Wrappers.lambdaQuery(QuestionBankBookmark.class)
                            .eq(QuestionBankBookmark::getUserId, userId));
                    if (!bookmarks.isEmpty()) {
                        List<Long> bookmarkedBankIds = bookmarks.stream()
                            .map(QuestionBankBookmark::getBankId)
                            .collect(Collectors.toList());
                        wrapper.in(QuestionBank::getBankId, bookmarkedBankIds);
                    } else {
                        // 如果没有收藏的题库，返回空结果
                        wrapper.eq(QuestionBank::getBankId, -1);
                    }
                } else {
                    // 未登录用户，返回空结果
                    wrapper.eq(QuestionBank::getBankId, -1);
                }
                break;
            case "completed":
                if (userId != null) {
                    // TODO: 实现完成状态筛选
                    // 暂时返回空结果
                    wrapper.eq(QuestionBank::getBankId, -1);
                } else {
                    // 未登录用户，返回空结果
                    wrapper.eq(QuestionBank::getBankId, -1);
                }
                break;
            case "hot":
                wrapper.orderByDesc(QuestionBank::getPracticeCount);
                break;
            case "new":
                wrapper.orderByDesc(QuestionBank::getCreateTime);
                break;
        }
    }

    /**
     * 应用排序条件
     *
     * @param wrapper  查询条件包装器
     * @param sortType 排序类型
     */
    private void applySorting(LambdaQueryWrapper<QuestionBank> wrapper, String sortType) {
        if (StrUtil.isBlank(sortType) || "default".equals(sortType)) {
            wrapper.orderByAsc(QuestionBank::getSort)
                .orderByDesc(QuestionBank::getCreateTime);
            return;
        }

        switch (sortType) {
            case "difficulty":
                wrapper.orderByAsc(QuestionBank::getDifficulty)
                    .orderByDesc(QuestionBank::getCreateTime);
                break;
            case "progress":
                // TODO: 根据用户学习进度排序
                // 暂时按创建时间排序
                wrapper.orderByDesc(QuestionBank::getCreateTime);
                break;
            case "popularity":
                wrapper.orderByDesc(QuestionBank::getPracticeCount)
                    .orderByDesc(QuestionBank::getCreateTime);
                break;
            case "latest":
                wrapper.orderByDesc(QuestionBank::getCreateTime);
                break;
        }
    }

    @Override
    @CaffeineCache(
        cacheName = "learningStats",
        key = "#userId",
        expire = "10",
        timeUnit = TimeUnit.MINUTES
    )
    public Map<String, Object> getLearningStats(Long userId) {
        Map<String, Object> stats = new HashMap<>();

        try {
            // 这里需要注入 LearningProgressMapper，暂时使用模拟数据
            // 查询用户学习进度记录
            // LambdaQueryWrapper<LearningProgress> progressWrapper = Wrappers.lambdaQuery(LearningProgress.class)
            //     .eq(LearningProgress::getUserId, userId);
            // List<LearningProgress> progressList = learningProgressMapper.selectList(progressWrapper);

            // 计算学习统计数据
            int totalHours = calculateTotalStudyHours(userId);
            int completedCourses = calculateCompletedCourses(userId);
            int currentStreak = calculateCurrentStreak(userId);
            int weeklyProgress = calculateWeeklyProgress(userId);

            stats.put("totalHours", totalHours);
            stats.put("completedCourses", completedCourses);
            stats.put("currentStreak", currentStreak);
            stats.put("weeklyProgress", weeklyProgress);

        } catch (Exception e) {
            log.error("计算学习统计数据失败：{}", e.getMessage(), e);
            // 返回默认数据
            stats.put("totalHours", 0);
            stats.put("completedCourses", 0);
            stats.put("currentStreak", 0);
            stats.put("weeklyProgress", 0);
        }

        return stats;
    }

    @Override
    @CaffeineCache(
        cacheName = "todayRecommendation",
        key = "#userId",
        expire = "30",
        timeUnit = TimeUnit.MINUTES
    )
    public Map<String, Object> getTodayRecommendation(Long userId) {
        Map<String, Object> recommendation = new HashMap<>();

        try {
            // 查询用户最近的学习记录，推荐继续学习的内容
            QuestionBank recommendedBank = findRecommendedQuestionBank(userId);

            if (recommendedBank != null) {
                recommendation.put("type", "continue");
                recommendation.put("title", recommendedBank.getTitle());
                recommendation.put("description", "继续学习 " + recommendedBank.getDescription());
                recommendation.put("progress", calculateBankProgress(userId, recommendedBank.getBankId()));
                recommendation.put("nextLesson", "下一个题目");
                recommendation.put("courseId", recommendedBank.getBankId().toString());
            } else {
                // 如果没有找到推荐内容，返回默认推荐
                recommendation.put("type", "explore");
                recommendation.put("title", "开始新的学习之旅");
                recommendation.put("description", "探索更多学习资源，提升你的技能");
                recommendation.put("progress", 0);
                recommendation.put("nextLesson", "选择感兴趣的题库");
                recommendation.put("courseId", null);
            }

        } catch (Exception e) {
            log.error("获取今日推荐失败：{}", e.getMessage(), e);
            // 返回默认推荐
            recommendation.put("type", "explore");
            recommendation.put("title", "开始学习");
            recommendation.put("description", "选择一个题库开始学习");
            recommendation.put("progress", 0);
            recommendation.put("nextLesson", "第一个题目");
            recommendation.put("courseId", null);
        }

        return recommendation;
    }

    @Override
    @CaffeineCache(
        cacheName = "resourceCategoryStats",
        key = "#majorId + '_' + #userId",
        expire = "15",
        timeUnit = TimeUnit.MINUTES
    )
    public List<Map<String, Object>> getResourceCategoryStats(String majorId, Long userId) {
        List<Map<String, Object>> stats = new ArrayList<>();

        try {
            // 构建查询条件
            LambdaQueryWrapper<QuestionBank> wrapper = Wrappers.lambdaQuery(QuestionBank.class)
                .eq(QuestionBank::getStatus, "0");

            // 如果指定了专业ID，添加专业筛选
            if (StrUtil.isNotBlank(majorId)) {
                Major major = majorMapper.selectOne(Wrappers.lambdaQuery(Major.class)
                    .eq(Major::getMajorCode, majorId));
                if (major != null) {
                    wrapper.eq(QuestionBank::getMajorId, major.getMajorId());
                }
            }

            // 统计题库数量（代表题库资源）
            long questionBankCount = questionBankMapper.selectCount(wrapper);

            // 创建资源分类统计
            Map<String, Object> bookStats = new HashMap<>();
            bookStats.put("id", "book");
            bookStats.put("name", "面试宝典");
            bookStats.put("count", calculateBookCount(majorId)); // 书籍数量
            stats.add(bookStats);

            Map<String, Object> videoStats = new HashMap<>();
            videoStats.put("id", "video");
            videoStats.put("name", "视频课程");
            videoStats.put("count", calculateVideoCount(majorId)); // 视频数量
            stats.add(videoStats);

            Map<String, Object> questionStats = new HashMap<>();
            questionStats.put("id", "question");
            questionStats.put("name", "题库资源");
            questionStats.put("count", (int) questionBankCount); // 题库数量
            stats.add(questionStats);

        } catch (Exception e) {
            log.error("获取资源分类统计失败：{}", e.getMessage(), e);
            // 返回默认统计数据
            stats.add(Map.of("id", "book", "name", "面试宝典", "count", 0));
            stats.add(Map.of("id", "video", "name", "视频课程", "count", 0));
            stats.add(Map.of("id", "question", "name", "题库资源", "count", 0));
        }

        return stats;
    }

    /**
     * 计算用户总学习时长（小时）
     */
    private int calculateTotalStudyHours(Long userId) {
        try {
            // TODO: 这里应该查询用户的学习记录表，计算总学习时长
            // 暂时返回模拟数据
            return 24 + (int) (userId % 100); // 基础24小时 + 用户ID的变化
        } catch (Exception e) {
            log.warn("计算学习时长失败：{}", e.getMessage());
            return 0;
        }
    }

    /**
     * 计算用户完成的课程数
     */
    private int calculateCompletedCourses(Long userId) {
        try {
            // 查询用户收藏的题库数量作为参与的课程数
            LambdaQueryWrapper<QuestionBankBookmark> wrapper = Wrappers.lambdaQuery(QuestionBankBookmark.class)
                .eq(QuestionBankBookmark::getUserId, userId);
            long bookmarkedCount = bookmarkMapper.selectCount(wrapper);

            // 假设完成率为60%
            return (int) (bookmarkedCount * 0.6);
        } catch (Exception e) {
            log.warn("计算完成课程数失败：{}", e.getMessage());
            return 0;
        }
    }

    /**
     * 计算连续学习天数
     */
    private int calculateCurrentStreak(Long userId) {
        try {
            // TODO: 这里应该查询用户的学习记录，计算连续学习天数
            // 暂时返回模拟数据
            return 7 + (int) (userId % 20); // 基础7天 + 用户ID的变化
        } catch (Exception e) {
            log.warn("计算连续学习天数失败：{}", e.getMessage());
            return 0;
        }
    }

    /**
     * 计算本周学习进度
     */
    private int calculateWeeklyProgress(Long userId) {
        try {
            // TODO: 这里应该查询用户本周的学习记录，计算进度百分比
            // 暂时返回模拟数据
            return 65 + (int) (userId % 30); // 基础65% + 用户ID的变化
        } catch (Exception e) {
            log.warn("计算周进度失败：{}", e.getMessage());
            return 0;
        }
    }

    /**
     * 查找推荐的题库
     */
    private QuestionBank findRecommendedQuestionBank(Long userId) {
        try {
            // 查询用户收藏但未完成的题库
            List<QuestionBankBookmark> bookmarks = bookmarkMapper.selectList(
                Wrappers.lambdaQuery(QuestionBankBookmark.class)
                    .eq(QuestionBankBookmark::getUserId, userId));

            if (!bookmarks.isEmpty()) {
                // 随机选择一个收藏的题库
                QuestionBankBookmark bookmark = bookmarks.get(0);
                return questionBankMapper.selectById(bookmark.getBankId());
            }

            // 如果没有收藏的题库，推荐一个热门题库
            List<QuestionBank> hotBanks = questionBankMapper.selectList(
                Wrappers.lambdaQuery(QuestionBank.class)
                    .eq(QuestionBank::getStatus, "0")
                    .orderByDesc(QuestionBank::getPracticeCount)
                    .last("LIMIT 1"));

            return hotBanks.isEmpty() ? null : hotBanks.get(0);
        } catch (Exception e) {
            log.warn("查找推荐题库失败：{}", e.getMessage());
            return null;
        }
    }

    /**
     * 计算题库学习进度
     */
    private int calculateBankProgress(Long userId, Long bankId) {
        try {
            // TODO: 这里应该查询用户在该题库的学习进度
            // 暂时返回模拟数据
            return 30 + (int) ((userId + bankId) % 50); // 30-80%的进度
        } catch (Exception e) {
            log.warn("计算题库进度失败：{}", e.getMessage());
            return 0;
        }
    }

    /**
     * 计算书籍数量
     */
    private int calculateBookCount(String majorId) {
        try {
            // TODO: 这里应该查询书籍表的数量
            // 暂时返回模拟数据
            return StrUtil.isNotBlank(majorId) ? 15 : 50;
        } catch (Exception e) {
            log.warn("计算书籍数量失败：{}", e.getMessage());
            return 0;
        }
    }

    /**
     * 计算视频数量
     */
    private int calculateVideoCount(String majorId) {
        try {
            // TODO: 这里应该查询视频表的数量
            // 暂时返回模拟数据
            return StrUtil.isNotBlank(majorId) ? 25 : 80;
        } catch (Exception e) {
            log.warn("计算视频数量失败：{}", e.getMessage());
            return 0;
        }
    }
}
