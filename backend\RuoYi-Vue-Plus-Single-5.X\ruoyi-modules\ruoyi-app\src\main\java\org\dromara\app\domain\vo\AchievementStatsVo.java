package org.dromara.app.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 成就统计数据视图对象
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Data
@Schema(description = "成就统计数据视图对象")
public class AchievementStatsVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 成就总数
     */
    @Schema(description = "成就总数")
    private Integer totalAchievements;

    /**
     * 已解锁成就数量
     */
    @Schema(description = "已解锁成就数量")
    private Integer unlockedAchievements;

    /**
     * 已完成成就数量
     */
    @Schema(description = "已完成成就数量")
    private Integer completedAchievements;

    /**
     * 完成百分比
     */
    @Schema(description = "完成百分比")
    private Double completionPercentage;

    /**
     * 完成率
     */
    @Schema(description = "完成率")
    private Double completionRate;

    /**
     * 徽章总数
     */
    @Schema(description = "徽章总数")
    private Integer totalBadges;

    /**
     * 已解锁徽章数量
     */
    @Schema(description = "已解锁徽章数量")
    private Integer unlockedBadges;

    /**
     * 徽章完成百分比
     */
    @Schema(description = "徽章完成百分比")
    private Double badgeCompletionPercentage;

    /**
     * 总成就点数
     */
    @Schema(description = "总成就点数")
    private Integer totalPoints;

    /**
     * 已获得成就点数
     */
    @Schema(description = "已获得成就点数")
    private Integer earnedPoints;

    /**
     * 各类别成就统计（Map格式）
     */
    @Schema(description = "各类别成就统计（Map格式）")
    private Map<String, Integer> categoryStats;

    /**
     * 各类别成就统计（详细）
     */
    @Schema(description = "各类别成就统计（详细）")
    private List<CategoryStats> categoryStatsDetail;

    /**
     * 各稀有度成就统计
     */
    @Schema(description = "各稀有度成就统计")
    private List<RarityStats> rarityStats;

    /**
     * 最近解锁的成就
     */
    @Schema(description = "最近解锁的成就")
    private List<AchievementVo> recentAchievements;

    /**
     * 推荐接下来完成的成就
     */
    @Schema(description = "推荐接下来完成的成就")
    private List<UserAchievementVo> recommendedAchievements;

    /**
     * 成就解锁趋势（按日期统计）
     */
    @Schema(description = "成就解锁趋势")
    private Map<String, Integer> unlockTrend;

    /**
     * 成就排行榜信息
     */
    @Schema(description = "成就排行榜信息")
    private LeaderboardInfo leaderboardInfo;

    /**
     * 类别统计
     */
    @Data
    @Schema(description = "类别统计")
    public static class CategoryStats {
        /**
         * 类别名称
         */
        @Schema(description = "类别名称")
        private String category;

        /**
         * 类别显示名称
         */
        @Schema(description = "类别显示名称")
        private String displayName;

        /**
         * 总成就数
         */
        @Schema(description = "总成就数")
        private Integer total;

        /**
         * 已解锁成就数
         */
        @Schema(description = "已解锁成就数")
        private Integer unlocked;

        /**
         * 完成百分比
         */
        @Schema(description = "完成百分比")
        private Double percentage;
    }

    /**
     * 稀有度统计
     */
    @Data
    @Schema(description = "稀有度统计")
    public static class RarityStats {
        /**
         * 稀有度
         */
        @Schema(description = "稀有度")
        private String rarity;

        /**
         * 稀有度显示名称
         */
        @Schema(description = "稀有度显示名称")
        private String displayName;

        /**
         * 总成就数
         */
        @Schema(description = "总成就数")
        private Integer total;

        /**
         * 已解锁成就数
         */
        @Schema(description = "已解锁成就数")
        private Integer unlocked;

        /**
         * 完成百分比
         */
        @Schema(description = "完成百分比")
        private Double percentage;
    }

    /**
     * 排行榜信息
     */
    @Data
    @Schema(description = "排行榜信息")
    public static class LeaderboardInfo {
        /**
         * 用户排名
         */
        @Schema(description = "用户排名")
        private Integer rank;

        /**
         * 前10名用户
         */
        @Schema(description = "前10名用户")
        private List<LeaderboardEntry> topUsers;
    }

    /**
     * 排行榜条目
     */
    @Data
    @Schema(description = "排行榜条目")
    public static class LeaderboardEntry {
        /**
         * 用户ID
         */
        @Schema(description = "用户ID")
        private String userId;

        /**
         * 用户昵称
         */
        @Schema(description = "用户昵称")
        private String nickname;

        /**
         * 用户头像
         */
        @Schema(description = "用户头像")
        private String avatar;

        /**
         * 解锁成就数
         */
        @Schema(description = "解锁成就数")
        private Integer unlockedCount;

        /**
         * 成就点数
         */
        @Schema(description = "成就点数")
        private Integer points;

        /**
         * 排名
         */
        @Schema(description = "排名")
        private Integer rank;

        /**
         * 排名
         */
        private Integer ranking;

        /**
         * 总用户数
         */
        private Integer totalUsers;

        /**
         * 类别
         */
        private String category;
    }

}
