package org.dromara.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.AppUser;
import org.dromara.app.domain.dto.AppUserProfileDto;
import org.dromara.app.domain.vo.AppUserProfileVo;
import org.dromara.app.mapper.AppUserMapper;
import org.dromara.app.service.IAppUserProfileService;
import org.dromara.common.core.exception.user.UserException;
import org.dromara.common.core.utils.DateUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 应用用户Service业务层处理
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class AppUserProfileServiceImpl implements IAppUserProfileService {

    private final AppUserMapper appUserMapper;

    @Override
    public AppUserProfileVo getUserProfile(Long userId) {
        // 根据用户ID查询用户信息
        AppUser user = appUserMapper.selectById(userId);
        if (ObjectUtil.isNull(user) || "1".equals(user.getDelFlag())) {
            throw new UserException("用户不存在");
        }

        // 转换为VO对象返回
        AppUserProfileVo profileVo = BeanUtil.toBean(user, AppUserProfileVo.class);
        profileVo.setName(user.getRealName());
        return profileVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUserProfile(Long userId, AppUserProfileDto profileDto) {
        // 查询用户是否存在
        AppUser user = appUserMapper.selectById(userId);
        if (ObjectUtil.isNull(user) || "1".equals(user.getDelFlag())) {
            throw new UserException("用户不存在");
        }

        // 更新用户信息
        AppUser updateUser = new AppUser();
        updateUser.setUserId(userId);
        updateUser.setRealName(profileDto.getName());
        updateUser.setGender(profileDto.getGender());
        updateUser.setSchool(profileDto.getSchool());
        updateUser.setMajor(profileDto.getMajor());
        updateUser.setGrade(profileDto.getGrade());
        updateUser.setIntroduction(profileDto.getIntroduction());
        updateUser.setAvatar(profileDto.getAvatar());
        updateUser.setUpdateTime(DateUtils.getNowDate());

        return appUserMapper.updateById(updateUser) > 0;
    }
}
