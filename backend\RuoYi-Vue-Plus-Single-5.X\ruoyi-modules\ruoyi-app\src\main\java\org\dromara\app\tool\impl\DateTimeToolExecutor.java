package org.dromara.app.tool.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.service.IToolService;
import org.dromara.app.tool.ToolExecutor;
import org.springframework.stereotype.Component;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 时间助手工具执行器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class DateTimeToolExecutor implements ToolExecutor {

    @Override
    public IToolService.ToolCallResult execute(Map<String, Object> parameters, IToolService.ToolCallContext context) {
        try {
            String operation = (String) parameters.get("operation");

            if (StrUtil.isBlank(operation)) {
                return new IToolService.ToolCallResult(false, null, "操作类型不能为空");
            }

            Object result = processDateTime(operation, parameters);

            return new IToolService.ToolCallResult(true, result, "时间操作成功");

        } catch (Exception e) {
            log.error("时间操作失败", e);
            return new IToolService.ToolCallResult(false, null, "时间操作失败: " + e.getMessage());
        }
    }

    @Override
    public String getToolId() {
        return "datetime_helper";
    }

    @Override
    public String getToolName() {
        return "时间助手";
    }

    @Override
    public String getDescription() {
        return "处理日期时间相关操作，包括格式化、计算、转换等";
    }

    /**
     * 处理日期时间操作
     */
    private Object processDateTime(String operation, Map<String, Object> parameters) {
        switch (operation.toLowerCase()) {
            case "current":
                return getCurrentDateTime(parameters);
            case "format":
                return formatDateTime(parameters);
            case "parse":
                return parseDateTime(parameters);
            case "calculate":
                return calculateDateTime(parameters);
            case "timezone":
                return convertTimezone(parameters);
            default:
                throw new IllegalArgumentException("不支持的操作类型: " + operation);
        }
    }

    /**
     * 获取当前时间
     */
    private Map<String, Object> getCurrentDateTime(Map<String, Object> parameters) {
        Map<String, Object> result = new HashMap<>();

        LocalDateTime now = LocalDateTime.now();
        String timezone = (String) parameters.get("timezone");

        if (StrUtil.isNotBlank(timezone)) {
            ZoneId zoneId = ZoneId.of(timezone);
            now = LocalDateTime.now(zoneId);
        }

        String format = (String) parameters.get("format");
        if (StrUtil.isBlank(format)) {
            format = "yyyy-MM-dd HH:mm:ss";
        }

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);

        result.put("timestamp", now.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
        result.put("formatted", now.format(formatter));
        result.put("iso", now.toString());
        result.put("year", now.getYear());
        result.put("month", now.getMonthValue());
        result.put("day", now.getDayOfMonth());
        result.put("hour", now.getHour());
        result.put("minute", now.getMinute());
        result.put("second", now.getSecond());
        result.put("dayOfWeek", now.getDayOfWeek().getValue());
        result.put("dayOfYear", now.getDayOfYear());

        return result;
    }

    /**
     * 格式化日期时间
     */
    private String formatDateTime(Map<String, Object> parameters) {
        String datetime = (String) parameters.get("datetime");
        String format = (String) parameters.get("format");

        if (StrUtil.isBlank(datetime)) {
            throw new IllegalArgumentException("日期时间不能为空");
        }

        if (StrUtil.isBlank(format)) {
            format = "yyyy-MM-dd HH:mm:ss";
        }

        // 尝试多种解析方式
        LocalDateTime dateTime = parseToLocalDateTime(datetime);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);

        return dateTime.format(formatter);
    }

    /**
     * 解析日期时间
     */
    private Map<String, Object> parseDateTime(Map<String, Object> parameters) {
        String datetime = (String) parameters.get("datetime");

        if (StrUtil.isBlank(datetime)) {
            throw new IllegalArgumentException("日期时间不能为空");
        }

        LocalDateTime dateTime = parseToLocalDateTime(datetime);

        Map<String, Object> result = new HashMap<>();
        result.put("timestamp", dateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
        result.put("iso", dateTime.toString());
        result.put("year", dateTime.getYear());
        result.put("month", dateTime.getMonthValue());
        result.put("day", dateTime.getDayOfMonth());
        result.put("hour", dateTime.getHour());
        result.put("minute", dateTime.getMinute());
        result.put("second", dateTime.getSecond());
        result.put("dayOfWeek", dateTime.getDayOfWeek().getValue());
        result.put("dayOfYear", dateTime.getDayOfYear());

        return result;
    }

    /**
     * 计算日期时间
     */
    private Map<String, Object> calculateDateTime(Map<String, Object> parameters) {
        String datetime = (String) parameters.get("datetime");
        Integer amount = (Integer) parameters.get("amount");
        String unit = (String) parameters.get("unit");

        if (StrUtil.isBlank(datetime)) {
            datetime = LocalDateTime.now().toString();
        }

        if (amount == null) {
            amount = 1;
        }

        if (StrUtil.isBlank(unit)) {
            unit = "days";
        }

        LocalDateTime dateTime = parseToLocalDateTime(datetime);
        LocalDateTime result;

        switch (unit.toLowerCase()) {
            case "years":
                result = dateTime.plusYears(amount);
                break;
            case "months":
                result = dateTime.plusMonths(amount);
                break;
            case "days":
                result = dateTime.plusDays(amount);
                break;
            case "hours":
                result = dateTime.plusHours(amount);
                break;
            case "minutes":
                result = dateTime.plusMinutes(amount);
                break;
            case "seconds":
                result = dateTime.plusSeconds(amount);
                break;
            default:
                throw new IllegalArgumentException("不支持的时间单位: " + unit);
        }

        Map<String, Object> response = new HashMap<>();
        response.put("original", datetime);
        response.put("result", result.toString());
        response.put("formatted", result.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        response.put("timestamp", result.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
        response.put("operation", amount + " " + unit);

        return response;
    }

    /**
     * 时区转换
     */
    private Map<String, Object> convertTimezone(Map<String, Object> parameters) {
        String datetime = (String) parameters.get("datetime");
        String fromTimezone = (String) parameters.get("fromTimezone");
        String toTimezone = (String) parameters.get("timezone");

        if (StrUtil.isBlank(datetime)) {
            datetime = LocalDateTime.now().toString();
        }

        if (StrUtil.isBlank(fromTimezone)) {
            fromTimezone = ZoneId.systemDefault().getId();
        }

        if (StrUtil.isBlank(toTimezone)) {
            toTimezone = "UTC";
        }

        LocalDateTime dateTime = parseToLocalDateTime(datetime);
        ZoneId fromZone = ZoneId.of(fromTimezone);
        ZoneId toZone = ZoneId.of(toTimezone);

        ZonedDateTime fromZonedDateTime = dateTime.atZone(fromZone);
        ZonedDateTime toZonedDateTime = fromZonedDateTime.withZoneSameInstant(toZone);

        Map<String, Object> result = new HashMap<>();
        result.put("original", datetime);
        result.put("fromTimezone", fromTimezone);
        result.put("toTimezone", toTimezone);
        result.put("result", toZonedDateTime.toLocalDateTime().toString());
        result.put("formatted", toZonedDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        result.put("timestamp", toZonedDateTime.toInstant().toEpochMilli());
        result.put("offset", toZonedDateTime.getOffset().toString());

        return result;
    }

    /**
     * 解析字符串为LocalDateTime
     */
    private LocalDateTime parseToLocalDateTime(String datetime) {
        try {
            // 尝试时间戳
            if (datetime.matches("\\d{10}")) {
                return LocalDateTime.ofInstant(Instant.ofEpochSecond(Long.parseLong(datetime)), ZoneId.systemDefault());
            }

            if (datetime.matches("\\d{13}")) {
                return LocalDateTime.ofInstant(Instant.ofEpochMilli(Long.parseLong(datetime)), ZoneId.systemDefault());
            }

            // 尝试ISO格式
            if (datetime.contains("T")) {
                return LocalDateTime.parse(datetime, DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            }

            // 尝试常见格式
            String[] patterns = {
                "yyyy-MM-dd HH:mm:ss",
                "yyyy-MM-dd HH:mm",
                "yyyy-MM-dd",
                "yyyy/MM/dd HH:mm:ss",
                "yyyy/MM/dd HH:mm",
                "yyyy/MM/dd",
                "MM/dd/yyyy HH:mm:ss",
                "MM/dd/yyyy HH:mm",
                "MM/dd/yyyy",
                "dd/MM/yyyy HH:mm:ss",
                "dd/MM/yyyy HH:mm",
                "dd/MM/yyyy"
            };

            for (String pattern : patterns) {
                try {
                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
                    if (pattern.contains("HH")) {
                        return LocalDateTime.parse(datetime, formatter);
                    } else {
                        return LocalDate.parse(datetime, formatter).atStartOfDay();
                    }
                } catch (Exception ignored) {
                    // 继续尝试下一个格式
                }
            }

            // 使用Hutool工具类作为后备
            Date date = DateUtil.parse(datetime);
            return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();

        } catch (Exception e) {
            throw new IllegalArgumentException("无法解析日期时间: " + datetime, e);
        }
    }
}
