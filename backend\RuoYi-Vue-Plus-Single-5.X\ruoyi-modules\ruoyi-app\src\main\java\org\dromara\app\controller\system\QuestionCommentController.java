package org.dromara.app.controller.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.app.domain.bo.QuestionCommentBo;
import org.dromara.app.domain.vo.QuestionCommentVO;
import org.dromara.app.service.IQuestionCommentService;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 题目评论管理
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/questioncomment")
public class QuestionCommentController extends BaseController {

    private final IQuestionCommentService questionCommentService;

    /**
     * 查询题目评论列表
     */
    @SaCheckPermission("system:questioncomment:list")
    @GetMapping("/list")
    public TableDataInfo<QuestionCommentVO> list(QuestionCommentBo bo, PageQuery pageQuery) {
        return questionCommentService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出题目评论列表
     */
    @SaCheckPermission("system:questioncomment:export")
    @Log(title = "题目评论", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(QuestionCommentBo bo, HttpServletResponse response) {
        List<QuestionCommentVO> list = questionCommentService.exportCommentList(bo);
        ExcelUtil.exportExcel(list, "题目评论", QuestionCommentVO.class, response);
    }

    /**
     * 获取题目评论详细信息
     */
    @SaCheckPermission("system:questioncomment:query")
    @GetMapping("/{commentId}")
    public R<QuestionCommentVO> getInfo(@NotNull(message = "主键不能为空")
                                        @PathVariable Long commentId) {
        return R.ok(questionCommentService.queryById(commentId));
    }

    /**
     * 新增题目评论
     */
    @SaCheckPermission("system:questioncomment:add")
    @Log(title = "题目评论", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping
    public R<Void> add(@Validated(AddGroup.class) @RequestBody QuestionCommentBo bo) {
        return toAjax(questionCommentService.insertByBo(bo));
    }

    /**
     * 修改题目评论
     */
    @SaCheckPermission("system:questioncomment:edit")
    @Log(title = "题目评论", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody QuestionCommentBo bo) {
        return toAjax(questionCommentService.updateByBo(bo));
    }

    /**
     * 删除题目评论
     */
    @SaCheckPermission("system:questioncomment:remove")
    @Log(title = "题目评论", businessType = BusinessType.DELETE)
    @DeleteMapping("/{commentIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] commentIds) {
        return toAjax(questionCommentService.deleteWithValidByIds(List.of(commentIds)));
    }

    /**
     * 审核题目评论
     */
    @SaCheckPermission("system:questioncomment:audit")
    @Log(title = "题目评论审核", businessType = BusinessType.UPDATE)
    @PostMapping("/{commentId}/audit")
    public R<Void> auditComment(@PathVariable Long commentId,
                                @RequestBody Map<String, Object> request) {
        String status = (String) request.get("status");
        String reason = (String) request.get("reason");
        Long operatorId = getUserId();
        
        Boolean success = questionCommentService.auditComment(commentId, status, operatorId, reason);
        return success ? R.ok() : R.fail("审核失败");
    }

    /**
     * 置顶/取消置顶题目评论
     */
    @SaCheckPermission("system:questioncomment:top")
    @Log(title = "题目评论置顶", businessType = BusinessType.UPDATE)
    @PostMapping("/{commentId}/toggle-top")
    public R<Void> toggleTop(@PathVariable Long commentId,
                             @RequestBody Map<String, Object> request) {
        Boolean isTop = (Boolean) request.get("isTop");
        Long operatorId = getUserId();
        
        Boolean success = questionCommentService.toggleCommentTop(commentId, isTop, operatorId);
        return success ? R.ok() : R.fail("操作失败");
    }

    /**
     * 批量删除题目评论
     */
    @SaCheckPermission("system:questioncomment:remove")
    @Log(title = "题目评论批量删除", businessType = BusinessType.DELETE)
    @PostMapping("/batch-delete")
    public R<Void> batchDelete(@RequestBody List<Long> commentIds) {
        Long operatorId = getUserId();
        Boolean success = questionCommentService.batchDeleteComments(commentIds, operatorId);
        return success ? R.ok() : R.fail("批量删除失败");
    }

    /**
     * 获取题目评论统计信息
     */
    @SaCheckPermission("system:questioncomment:query")
    @GetMapping("/statistics/{questionId}")
    public R<Map<String, Object>> getStatistics(@PathVariable String questionId) {
        Map<String, Object> statistics = questionCommentService.getCommentStatistics(questionId);
        return R.ok(statistics);
    }

    /**
     * 搜索题目评论
     */
    @SaCheckPermission("system:questioncomment:list")
    @GetMapping("/search")
    public TableDataInfo<QuestionCommentVO> search(@RequestParam String keyword, PageQuery pageQuery) {
        return questionCommentService.searchComments(keyword, pageQuery);
    }

    /**
     * 获取题目的评论列表（包含回复）
     */
    @SaCheckPermission("system:questioncomment:query")
    @GetMapping("/question/{questionId}")
    public R<Map<String, Object>> getQuestionComments(
        @PathVariable String questionId,
        @RequestParam(defaultValue = "1") Integer page,
        @RequestParam(defaultValue = "10") Integer pageSize,
        @RequestParam(defaultValue = "createTime") String orderBy,
        @RequestParam(defaultValue = "desc") String orderDirection) {
        
        Map<String, Object> result = questionCommentService.getQuestionComments(
            questionId, page, pageSize, orderBy, orderDirection);
        return R.ok(result);
    }

    /**
     * 获取评论的回复列表
     */
    @SaCheckPermission("system:questioncomment:query")
    @GetMapping("/{commentId}/replies")
    public R<List<QuestionCommentVO>> getReplies(@PathVariable Long commentId) {
        List<QuestionCommentVO> replies = questionCommentService.getRepliesByParentId(commentId);
        return R.ok(replies);
    }
}
