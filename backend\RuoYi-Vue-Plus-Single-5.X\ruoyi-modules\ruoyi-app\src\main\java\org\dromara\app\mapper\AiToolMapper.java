package org.dromara.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.dromara.app.domain.AiTool;

import java.util.List;
import java.util.Map;

/**
 * AI工具Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface AiToolMapper extends BaseMapper<AiTool> {

    /**
     * 分页查询可用工具列表
     *
     * @param page     分页对象
     * @param category 工具分类（可选）
     * @param enabled  是否启用
     * @return 工具分页结果
     */
    Page<AiTool> selectAvailableTools(Page<AiTool> page,
                                      @Param("category") String category,
                                      @Param("enabled") Boolean enabled);

    /**
     * 根据分类查询启用的工具
     *
     * @param category 工具分类
     * @return 工具列表
     */
    List<AiTool> selectEnabledByCategory(@Param("category") String category);

    /**
     * 根据权限级别查询工具
     *
     * @param maxPermissionLevel 最大权限级别
     * @return 工具列表
     */
    List<AiTool> selectByPermissionLevel(@Param("maxPermissionLevel") Integer maxPermissionLevel);

    /**
     * 查询系统内置工具
     *
     * @return 系统工具列表
     */
    List<AiTool> selectSystemTools();

    /**
     * 查询热门工具
     *
     * @param limit 返回数量限制
     * @return 热门工具列表
     */
    List<AiTool> selectPopularTools(@Param("limit") Integer limit);

    /**
     * 根据使用次数查询工具
     *
     * @param minUsageCount 最小使用次数
     * @return 工具列表
     */
    List<AiTool> selectToolsByUsage(@Param("minUsageCount") Long minUsageCount);

    /**
     * 根据分类列表查询工具
     *
     * @param categories 分类列表
     * @return 工具列表
     */
    List<AiTool> selectToolsByCategory(@Param("categories") List<String> categories);

    /**
     * 搜索工具
     *
     * @param keyword 关键词
     * @return 工具列表
     */
    List<AiTool> searchTools(@Param("keyword") String keyword);

    /**
     * 获取工具统计信息
     *
     * @return 统计信息
     */
    Map<String, Object> getToolStats();

    /**
     * 获取分类统计信息
     *
     * @return 分类统计列表
     */
    List<Map<String, Object>> getCategoryStats();

    /**
     * 增加工具使用次数
     *
     * @param toolId   工具ID
     * @param lastUsed 最后使用时间
     * @return 影响行数
     */
    int incrementUsageCount(@Param("toolId") String toolId, @Param("lastUsed") Long lastUsed);

    /**
     * 更新工具统计信息
     *
     * @param toolId           工具ID
     * @param usageCount       使用次数
     * @param avgExecutionTime 平均执行时间
     * @param successRate      成功率
     * @param lastUsed         最后使用时间
     * @return 影响行数
     */
    int updateToolStats(@Param("toolId") String toolId,
                        @Param("usageCount") Long usageCount,
                        @Param("avgExecutionTime") Long avgExecutionTime,
                        @Param("successRate") Double successRate,
                        @Param("lastUsed") Long lastUsed);

    /**
     * 批量更新工具状态
     *
     * @param toolIds 工具ID列表
     * @param enabled 是否启用
     * @return 影响行数
     */
    int batchUpdateToolStatus(@Param("toolIds") List<String> toolIds, @Param("enabled") Boolean enabled);

    /**
     * 批量删除工具
     *
     * @param toolIds 工具ID列表
     * @return 影响行数
     */
    int batchDeleteTools(@Param("toolIds") List<String> toolIds);

    /**
     * 批量插入默认工具
     *
     * @param tools 工具列表
     * @return 影响行数
     */
    int batchInsertDefaultTools(@Param("tools") List<AiTool> tools);

    /**
     * 查询所有启用的工具
     *
     * @return 启用的工具列表
     */
    List<AiTool> selectEnabledTools();

    /**
     * 根据工具名称查询工具
     *
     * @param toolName 工具名称
     * @return 工具对象
     */
    AiTool selectByName(String toolName);
}
