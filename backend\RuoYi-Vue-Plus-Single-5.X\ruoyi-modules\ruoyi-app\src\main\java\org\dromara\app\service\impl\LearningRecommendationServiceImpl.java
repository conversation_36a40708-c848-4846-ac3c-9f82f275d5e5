package org.dromara.app.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.InterviewReport;
import org.dromara.app.service.ILearningRecommendationService;
import org.dromara.app.service.IMultimodalAnalysisService.DimensionScore;
import org.dromara.common.core.exception.ServiceException;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 学习推荐服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LearningRecommendationServiceImpl implements ILearningRecommendationService {

    // 技能领域映射
    private static final Map<String, List<String>> SKILL_AREA_MAPPING = new HashMap<>();
    
    // 岗位技能要求映射
    private static final Map<String, List<String>> JOB_SKILL_MAPPING = new HashMap<>();
    
    // 学习资源库
    private static final Map<String, List<LearningResource>> RESOURCE_LIBRARY = new HashMap<>();

    static {
        initializeSkillAreaMapping();
        initializeJobSkillMapping();
        initializeResourceLibrary();
    }

    @Override
    public List<LearningPathRecommendation> generateLearningPaths(InterviewReport report) {
        try {
            log.info("基于面试报告生成学习路径推荐，报告ID: {}", report.getId());
            
            List<LearningPathRecommendation> recommendations = new ArrayList<>();
            
            // 1. 基于弱项生成推荐
            if (report.getWeaknesses() != null && !report.getWeaknesses().isEmpty()) {
                List<LearningPathRecommendation> weaknessPaths = generatePathsByWeaknesses(
                    report.getWeaknesses(), report.getJobPosition());
                recommendations.addAll(weaknessPaths);
            }
            
            // 2. 基于维度评分生成推荐
            if (report.getDimensionScores() != null && !report.getDimensionScores().isEmpty()) {
                List<DimensionScore> lowScores = convertAndFilterLowScores(report.getDimensionScores());
                if (!lowScores.isEmpty()) {
                    List<LearningPathRecommendation> dimensionPaths = generatePathsByDimensionScores(
                        lowScores, report.getJobPosition());
                    recommendations.addAll(dimensionPaths);
                }
            }
            
            // 3. 基于岗位生成通用推荐
            String userLevel = determineUserLevel(report.getTotalScore());
            List<LearningPathRecommendation> jobPaths = generatePathsByJobPosition(
                report.getJobPosition(), userLevel);
            recommendations.addAll(jobPaths);
            
            // 4. 去重、排序和限制数量
            List<LearningPathRecommendation> finalRecommendations = recommendations.stream()
                .collect(Collectors.toMap(
                    LearningPathRecommendation::getTitle,
                    path -> path,
                    (existing, replacement) -> existing.getPriority() > replacement.getPriority() ? existing : replacement))
                .values()
                .stream()
                .sorted((p1, p2) -> p2.getPriority().compareTo(p1.getPriority()))
                .limit(5)
                .collect(Collectors.toList());
            
            log.info("生成学习路径推荐完成，共{}条", finalRecommendations.size());
            return finalRecommendations;
            
        } catch (Exception e) {
            log.error("生成学习路径推荐失败", e);
            throw new ServiceException("生成学习路径推荐失败: " + e.getMessage());
        }
    }

    @Override
    public List<LearningPathRecommendation> generatePathsByWeaknesses(List<String> weaknesses, String jobPosition) {
        List<LearningPathRecommendation> paths = new ArrayList<>();
        
        for (String weakness : weaknesses) {
            if (weakness.contains("专业知识") || weakness.contains("技术")) {
                paths.add(createTechnicalSkillPath(jobPosition, 90));
            } else if (weakness.contains("表达") || weakness.contains("沟通") || weakness.contains("语速")) {
                paths.add(createCommunicationSkillPath(85));
            } else if (weakness.contains("逻辑") || weakness.contains("思维")) {
                paths.add(createLogicalThinkingPath(80));
            } else if (weakness.contains("眼神") || weakness.contains("肢体") || weakness.contains("表情")) {
                paths.add(createBodyLanguagePath(75));
            } else if (weakness.contains("创新") || weakness.contains("创造")) {
                paths.add(createInnovationPath(70));
            } else if (weakness.contains("STAR") || weakness.contains("结构")) {
                paths.add(createInterviewSkillsPath(85));
            }
        }
        
        return paths.stream().distinct().collect(Collectors.toList());
    }

    @Override
    public List<LearningPathRecommendation> generatePathsByJobPosition(String jobPosition, String userLevel) {
        List<LearningPathRecommendation> paths = new ArrayList<>();
        
        if (jobPosition == null) {
            return paths;
        }
        
        String normalizedPosition = jobPosition.toLowerCase();
        
        if (normalizedPosition.contains("java") || normalizedPosition.contains("后端")) {
            paths.add(createJavaBackendPath(userLevel, 85));
            paths.add(createSystemDesignPath(userLevel, 80));
        } else if (normalizedPosition.contains("前端") || normalizedPosition.contains("javascript")) {
            paths.add(createFrontendPath(userLevel, 85));
            paths.add(createUIUXPath(userLevel, 75));
        } else if (normalizedPosition.contains("算法") || normalizedPosition.contains("ai") || normalizedPosition.contains("机器学习")) {
            paths.add(createAlgorithmPath(userLevel, 90));
            paths.add(createMachineLearningPath(userLevel, 85));
        } else if (normalizedPosition.contains("数据") || normalizedPosition.contains("分析")) {
            paths.add(createDataAnalysisPath(userLevel, 85));
            paths.add(createDataSciencePath(userLevel, 80));
        } else if (normalizedPosition.contains("测试") || normalizedPosition.contains("qa")) {
            paths.add(createTestingPath(userLevel, 80));
            paths.add(createAutomationPath(userLevel, 75));
        } else {
            // 通用技术路径
            paths.add(createGeneralTechPath(userLevel, 75));
        }
        
        return paths;
    }

    @Override
    public List<LearningPathRecommendation> generatePathsByDimensionScores(List<DimensionScore> dimensionScores, String jobPosition) {
        List<LearningPathRecommendation> paths = new ArrayList<>();
        
        for (DimensionScore score : dimensionScores) {
            if (score.getScore() < 60) { // 低于60分的维度需要重点提升
                String dimension = score.getDimension();
                
                if (dimension.contains("专业知识")) {
                    paths.add(createTechnicalSkillPath(jobPosition, 90));
                } else if (dimension.contains("表达") || dimension.contains("沟通")) {
                    paths.add(createCommunicationSkillPath(85));
                } else if (dimension.contains("逻辑")) {
                    paths.add(createLogicalThinkingPath(80));
                } else if (dimension.contains("肢体") || dimension.contains("表情")) {
                    paths.add(createBodyLanguagePath(75));
                } else if (dimension.contains("创新")) {
                    paths.add(createInnovationPath(70));
                }
            }
        }
        
        return paths.stream().distinct().collect(Collectors.toList());
    }

    @Override
    public List<LearningResource> getRecommendedResources(String skillArea, String difficulty, String resourceType) {
        String key = skillArea + "_" + difficulty;
        List<LearningResource> resources = RESOURCE_LIBRARY.getOrDefault(key, new ArrayList<>());
        
        if (resourceType != null && !resourceType.isEmpty()) {
            resources = resources.stream()
                .filter(resource -> resourceType.equals(resource.getType()))
                .collect(Collectors.toList());
        }
        
        return resources.stream()
            .sorted((r1, r2) -> Double.compare(r2.getRating(), r1.getRating()))
            .limit(10)
            .collect(Collectors.toList());
    }

    @Override
    public Map<String, Integer> calculateLearningPriorities(List<String> weaknesses, 
                                                           List<DimensionScore> dimensionScores, 
                                                           String jobPosition) {
        Map<String, Integer> priorities = new HashMap<>();
        
        // 基于弱项计算优先级
        if (weaknesses != null) {
            for (String weakness : weaknesses) {
                if (weakness.contains("专业知识")) {
                    priorities.put("技术能力", priorities.getOrDefault("技术能力", 0) + 30);
                } else if (weakness.contains("表达")) {
                    priorities.put("沟通表达", priorities.getOrDefault("沟通表达", 0) + 25);
                } else if (weakness.contains("逻辑")) {
                    priorities.put("逻辑思维", priorities.getOrDefault("逻辑思维", 0) + 20);
                }
            }
        }
        
        // 基于维度评分计算优先级
        if (dimensionScores != null) {
            for (DimensionScore score : dimensionScores) {
                int priority = Math.max(0, 100 - score.getScore()); // 分数越低优先级越高
                String area = mapDimensionToSkillArea(score.getDimension());
                priorities.put(area, priorities.getOrDefault(area, 0) + priority);
            }
        }
        
        // 基于岗位调整优先级
        if (jobPosition != null) {
            List<String> jobSkills = JOB_SKILL_MAPPING.getOrDefault(jobPosition.toLowerCase(), new ArrayList<>());
            for (String skill : jobSkills) {
                priorities.put(skill, priorities.getOrDefault(skill, 0) + 10);
            }
        }
        
        return priorities;
    }

    @Override
    public List<LearningPathRecommendation> personalizelearningPaths(List<LearningPathRecommendation> basePaths, 
                                                                    UserProfile userProfile) {
        if (userProfile == null) {
            return basePaths;
        }
        
        return basePaths.stream().map(path -> {
            LearningPathRecommendation personalizedPath = clonePath(path);
            
            // 根据用户可用时间调整学习时长
            if (userProfile.getAvailableHoursPerWeek() != null) {
                int adjustedHours = adjustLearningHours(path.getEstimatedHours(), userProfile.getAvailableHoursPerWeek());
                personalizedPath.setEstimatedHours(adjustedHours);
            }
            
            // 根据学习风格调整资源类型
            if (userProfile.getLearningStyle() != null) {
                List<LearningResource> adjustedResources = adjustResourcesByLearningStyle(
                    path.getResources(), userProfile.getLearningStyle());
                personalizedPath.setResources(adjustedResources);
            }
            
            // 根据经验水平调整难度
            if (userProfile.getExperienceLevel() != null) {
                String adjustedDifficulty = adjustDifficultyByExperience(
                    path.getDifficulty(), userProfile.getExperienceLevel());
                personalizedPath.setDifficulty(adjustedDifficulty);
            }
            
            return personalizedPath;
        }).collect(Collectors.toList());
    }

    @Override
    public LearningPathRecommendation getLearningPathDetail(String pathId) {
        // 这里应该从数据库获取，暂时返回模拟数据
        return createTechnicalSkillPath("Java开发工程师", 85);
    }

    // ========== 私有辅助方法 ==========

    /**
     * 初始化技能领域映射
     */
    private static void initializeSkillAreaMapping() {
        SKILL_AREA_MAPPING.put("技术能力", Arrays.asList("编程", "算法", "数据结构", "系统设计"));
        SKILL_AREA_MAPPING.put("沟通表达", Arrays.asList("口语表达", "演讲技巧", "团队协作"));
        SKILL_AREA_MAPPING.put("逻辑思维", Arrays.asList("问题分析", "结构化思考", "批判性思维"));
        SKILL_AREA_MAPPING.put("创新能力", Arrays.asList("创意思维", "产品设计", "用户体验"));
        SKILL_AREA_MAPPING.put("项目管理", Arrays.asList("敏捷开发", "团队管理", "风险控制"));
    }

    /**
     * 初始化岗位技能映射
     */
    private static void initializeJobSkillMapping() {
        JOB_SKILL_MAPPING.put("java开发工程师", Arrays.asList("技术能力", "系统设计", "数据库"));
        JOB_SKILL_MAPPING.put("前端开发工程师", Arrays.asList("技术能力", "用户体验", "视觉设计"));
        JOB_SKILL_MAPPING.put("算法工程师", Arrays.asList("技术能力", "数学基础", "机器学习"));
        JOB_SKILL_MAPPING.put("产品经理", Arrays.asList("产品设计", "用户研究", "项目管理"));
        JOB_SKILL_MAPPING.put("数据分析师", Arrays.asList("数据分析", "统计学", "业务理解"));
    }

    /**
     * 初始化资源库
     */
    private static void initializeResourceLibrary() {
        // 技术能力资源
        List<LearningResource> techResources = Arrays.asList(
            createResource("Java核心技术", "course", "深入学习Java编程语言", "https://example.com/java", "40小时", "中等", 4.8, "慕课网", true),
            createResource("算法与数据结构", "video", "计算机算法基础教程", "https://example.com/algorithm", "30小时", "困难", 4.9, "B站", true),
            createResource("Spring Boot实战", "practice", "企业级应用开发实践", "https://example.com/springboot", "25小时", "中等", 4.7, "实验楼", false)
        );
        RESOURCE_LIBRARY.put("技术能力_中等", techResources);
        
        // 沟通表达资源
        List<LearningResource> commResources = Arrays.asList(
            createResource("高效沟通技巧", "course", "职场沟通与表达艺术", "https://example.com/communication", "15小时", "简单", 4.6, "网易云课堂", false),
            createResource("演讲与口才训练", "video", "提升公众演讲能力", "https://example.com/speech", "20小时", "简单", 4.5, "腾讯课堂", true),
            createResource("STAR面试法", "article", "结构化面试回答技巧", "https://example.com/star", "2小时", "简单", 4.8, "知乎", true)
        );
        RESOURCE_LIBRARY.put("沟通表达_简单", commResources);
    }

    /**
     * 创建学习资源
     */
    private static LearningResource createResource(String title, String type, String description, 
                                                 String url, String duration, String difficulty, 
                                                 Double rating, String provider, Boolean isFree) {
        LearningResource resource = new LearningResource();
        resource.setId(UUID.randomUUID().toString());
        resource.setTitle(title);
        resource.setType(type);
        resource.setDescription(description);
        resource.setUrl(url);
        resource.setDuration(duration);
        resource.setDifficulty(difficulty);
        resource.setRating(rating);
        resource.setProvider(provider);
        resource.setIsFree(isFree);
        resource.setLanguage("中文");
        resource.setTags(Arrays.asList("推荐", "热门"));
        return resource;
    }

    /**
     * 创建技术技能学习路径
     */
    private LearningPathRecommendation createTechnicalSkillPath(String jobPosition, int priority) {
        LearningPathRecommendation path = new LearningPathRecommendation();
        path.setId("tech_skill_" + UUID.randomUUID().toString().substring(0, 8));
        path.setTitle("技术能力提升计划");
        path.setDescription("针对" + (jobPosition != null ? jobPosition : "技术岗位") + "的核心技术能力提升");
        path.setCategory("技术能力");
        path.setSkillArea("编程技术");
        path.setEstimatedHours(40);
        path.setDifficulty("中等");
        path.setPriority(priority);
        
        // 设置学习资源
        List<LearningResource> resources = getRecommendedResources("技术能力", "中等", null);
        if (resources.isEmpty()) {
            resources = createDefaultTechResources();
        }
        path.setResources(resources);
        
        // 设置学习里程碑
        path.setMilestones(Arrays.asList(
            "掌握基础编程概念 (10小时)",
            "完成核心技术学习 (15小时)",
            "实践项目开发 (10小时)",
            "技术面试准备 (5小时)"
        ));
        
        // 设置前置条件
        path.setPrerequisites(Arrays.asList("基础计算机知识", "编程语言基础"));
        
        return path;
    }

    /**
     * 创建沟通技能学习路径
     */
    private LearningPathRecommendation createCommunicationSkillPath(int priority) {
        LearningPathRecommendation path = new LearningPathRecommendation();
        path.setId("comm_skill_" + UUID.randomUUID().toString().substring(0, 8));
        path.setTitle("沟通表达能力提升");
        path.setDescription("提升职场沟通与面试表达技巧");
        path.setCategory("软技能");
        path.setSkillArea("沟通表达");
        path.setEstimatedHours(20);
        path.setDifficulty("简单");
        path.setPriority(priority);
        
        List<LearningResource> resources = getRecommendedResources("沟通表达", "简单", null);
        if (resources.isEmpty()) {
            resources = createDefaultCommResources();
        }
        path.setResources(resources);
        
        path.setMilestones(Arrays.asList(
            "学习沟通基础理论 (5小时)",
            "练习表达技巧 (8小时)",
            "模拟面试训练 (5小时)",
            "实际应用反馈 (2小时)"
        ));
        
        path.setPrerequisites(Arrays.asList("基本语言表达能力"));
        
        return path;
    }

    /**
     * 创建逻辑思维学习路径
     */
    private LearningPathRecommendation createLogicalThinkingPath(int priority) {
        LearningPathRecommendation path = new LearningPathRecommendation();
        path.setId("logic_skill_" + UUID.randomUUID().toString().substring(0, 8));
        path.setTitle("逻辑思维能力提升");
        path.setDescription("培养结构化思考和问题分析能力");
        path.setCategory("思维能力");
        path.setSkillArea("逻辑思维");
        path.setEstimatedHours(25);
        path.setDifficulty("中等");
        path.setPriority(priority);
        
        path.setResources(createDefaultLogicResources());
        
        path.setMilestones(Arrays.asList(
            "掌握逻辑思维基础 (8小时)",
            "学习结构化思考方法 (10小时)",
            "案例分析练习 (5小时)",
            "实际问题解决 (2小时)"
        ));
        
        path.setPrerequisites(Arrays.asList("基本分析能力"));
        
        return path;
    }

    /**
     * 创建肢体语言学习路径
     */
    private LearningPathRecommendation createBodyLanguagePath(int priority) {
        LearningPathRecommendation path = new LearningPathRecommendation();
        path.setId("body_lang_" + UUID.randomUUID().toString().substring(0, 8));
        path.setTitle("肢体语言与非语言沟通");
        path.setDescription("改善面试中的肢体语言表现");
        path.setCategory("软技能");
        path.setSkillArea("肢体语言");
        path.setEstimatedHours(15);
        path.setDifficulty("简单");
        path.setPriority(priority);
        
        path.setResources(createDefaultBodyLanguageResources());
        
        path.setMilestones(Arrays.asList(
            "了解肢体语言基础 (3小时)",
            "练习眼神交流技巧 (5小时)",
            "改善姿态和手势 (5小时)",
            "综合表现训练 (2小时)"
        ));
        
        path.setPrerequisites(Arrays.asList("基本自我认知"));
        
        return path;
    }

    /**
     * 创建创新能力学习路径
     */
    private LearningPathRecommendation createInnovationPath(int priority) {
        LearningPathRecommendation path = new LearningPathRecommendation();
        path.setId("innovation_" + UUID.randomUUID().toString().substring(0, 8));
        path.setTitle("创新思维培养");
        path.setDescription("培养创新意识和创造性思维");
        path.setCategory("思维能力");
        path.setSkillArea("创新思维");
        path.setEstimatedHours(30);
        path.setDifficulty("中等");
        path.setPriority(priority);
        
        path.setResources(createDefaultInnovationResources());
        
        path.setMilestones(Arrays.asList(
            "理解创新思维理论 (8小时)",
            "学习创新方法工具 (12小时)",
            "创新实践项目 (8小时)",
            "创新成果展示 (2小时)"
        ));
        
        path.setPrerequisites(Arrays.asList("开放性思维", "基础专业知识"));
        
        return path;
    }

    /**
     * 创建面试技巧学习路径
     */
    private LearningPathRecommendation createInterviewSkillsPath(int priority) {
        LearningPathRecommendation path = new LearningPathRecommendation();
        path.setId("interview_" + UUID.randomUUID().toString().substring(0, 8));
        path.setTitle("面试技巧全面提升");
        path.setDescription("掌握STAR方法和面试核心技巧");
        path.setCategory("面试技巧");
        path.setSkillArea("面试准备");
        path.setEstimatedHours(18);
        path.setDifficulty("简单");
        path.setPriority(priority);
        
        path.setResources(createDefaultInterviewResources());
        
        path.setMilestones(Arrays.asList(
            "学习STAR回答法 (5小时)",
            "常见问题准备 (8小时)",
            "模拟面试练习 (4小时)",
            "面试心态调整 (1小时)"
        ));
        
        path.setPrerequisites(Arrays.asList("基本沟通能力"));
        
        return path;
    }

    // 创建各种岗位特定的学习路径
    private LearningPathRecommendation createJavaBackendPath(String userLevel, int priority) {
        LearningPathRecommendation path = new LearningPathRecommendation();
        path.setId("java_backend_" + UUID.randomUUID().toString().substring(0, 8));
        path.setTitle("Java后端开发进阶");
        path.setDescription("Java后端开发核心技能提升");
        path.setCategory("技术能力");
        path.setSkillArea("后端开发");
        path.setEstimatedHours(50);
        path.setDifficulty(getDifficultyByLevel(userLevel));
        path.setPriority(priority);
        
        path.setResources(createJavaBackendResources());
        path.setMilestones(Arrays.asList(
            "Java基础巩固 (12小时)",
            "Spring框架学习 (15小时)",
            "数据库设计优化 (10小时)",
            "微服务架构 (8小时)",
            "项目实战 (5小时)"
        ));
        
        return path;
    }

    private LearningPathRecommendation createFrontendPath(String userLevel, int priority) {
        LearningPathRecommendation path = new LearningPathRecommendation();
        path.setId("frontend_" + UUID.randomUUID().toString().substring(0, 8));
        path.setTitle("前端开发技能提升");
        path.setDescription("现代前端开发技术栈学习");
        path.setCategory("技术能力");
        path.setSkillArea("前端开发");
        path.setEstimatedHours(45);
        path.setDifficulty(getDifficultyByLevel(userLevel));
        path.setPriority(priority);
        
        path.setResources(createFrontendResources());
        path.setMilestones(Arrays.asList(
            "HTML/CSS/JS基础 (10小时)",
            "现代框架学习 (15小时)",
            "工程化工具 (8小时)",
            "性能优化 (7小时)",
            "项目实战 (5小时)"
        ));
        
        return path;
    }

    private LearningPathRecommendation createAlgorithmPath(String userLevel, int priority) {
        LearningPathRecommendation path = new LearningPathRecommendation();
        path.setId("algorithm_" + UUID.randomUUID().toString().substring(0, 8));
        path.setTitle("算法与数据结构强化");
        path.setDescription("算法工程师核心技能训练");
        path.setCategory("技术能力");
        path.setSkillArea("算法");
        path.setEstimatedHours(60);
        path.setDifficulty("困难");
        path.setPriority(priority);
        
        path.setResources(createAlgorithmResources());
        path.setMilestones(Arrays.asList(
            "数据结构基础 (15小时)",
            "经典算法学习 (20小时)",
            "算法题练习 (15小时)",
            "算法优化技巧 (8小时)",
            "面试算法准备 (2小时)"
        ));
        
        return path;
    }

    // 其他辅助方法
    private List<DimensionScore> convertAndFilterLowScores(List<org.dromara.app.domain.DimensionScore> domainScores) {
        return domainScores.stream()
            .filter(score -> score.getScore() < 70) // 过滤低分维度
            .map(this::convertDomainScore)
            .collect(Collectors.toList());
    }

    private DimensionScore convertDomainScore(org.dromara.app.domain.DimensionScore domainScore) {
        DimensionScore score = new DimensionScore();
        score.setDimension(domainScore.getDimension());
        score.setScore(domainScore.getScore());
        score.setMaxScore(domainScore.getMaxScore());
        score.setPercentile(domainScore.getPercentile());
        score.setDescription(domainScore.getDescription());
        return score;
    }

    private String determineUserLevel(Integer totalScore) {
        if (totalScore == null) return "初级";
        if (totalScore >= 85) return "高级";
        if (totalScore >= 70) return "中级";
        return "初级";
    }

    private String mapDimensionToSkillArea(String dimension) {
        if (dimension.contains("专业知识")) return "技术能力";
        if (dimension.contains("表达") || dimension.contains("沟通")) return "沟通表达";
        if (dimension.contains("逻辑")) return "逻辑思维";
        if (dimension.contains("创新")) return "创新能力";
        return "综合能力";
    }

    private String getDifficultyByLevel(String userLevel) {
        switch (userLevel) {
            case "高级": return "困难";
            case "中级": return "中等";
            default: return "简单";
        }
    }

    private LearningPathRecommendation clonePath(LearningPathRecommendation original) {
        // 简化的克隆实现
        LearningPathRecommendation clone = new LearningPathRecommendation();
        clone.setId(original.getId());
        clone.setTitle(original.getTitle());
        clone.setDescription(original.getDescription());
        clone.setCategory(original.getCategory());
        clone.setSkillArea(original.getSkillArea());
        clone.setEstimatedHours(original.getEstimatedHours());
        clone.setDifficulty(original.getDifficulty());
        clone.setPriority(original.getPriority());
        clone.setResources(new ArrayList<>(original.getResources()));
        clone.setMilestones(new ArrayList<>(original.getMilestones()));
        clone.setPrerequisites(new ArrayList<>(original.getPrerequisites()));
        return clone;
    }

    private int adjustLearningHours(Integer originalHours, Integer availableHoursPerWeek) {
        if (originalHours == null || availableHoursPerWeek == null) return originalHours;
        
        // 根据可用时间调整学习时长
        if (availableHoursPerWeek < 5) {
            return (int) (originalHours * 1.5); // 时间少，延长学习周期
        } else if (availableHoursPerWeek > 15) {
            return (int) (originalHours * 0.8); // 时间充足，可以加快进度
        }
        return originalHours;
    }

    private List<LearningResource> adjustResourcesByLearningStyle(List<LearningResource> resources, String learningStyle) {
        if (resources == null || learningStyle == null) return resources;
        
        // 根据学习风格调整资源类型优先级
        return resources.stream()
            .sorted((r1, r2) -> {
                int score1 = getResourceScoreByLearningStyle(r1.getType(), learningStyle);
                int score2 = getResourceScoreByLearningStyle(r2.getType(), learningStyle);
                return Integer.compare(score2, score1);
            })
            .collect(Collectors.toList());
    }

    private int getResourceScoreByLearningStyle(String resourceType, String learningStyle) {
        switch (learningStyle) {
            case "visual":
                return resourceType.equals("video") ? 3 : (resourceType.equals("article") ? 2 : 1);
            case "auditory":
                return resourceType.equals("course") ? 3 : (resourceType.equals("video") ? 2 : 1);
            case "kinesthetic":
                return resourceType.equals("practice") ? 3 : (resourceType.equals("course") ? 2 : 1);
            case "reading":
                return resourceType.equals("article") ? 3 : (resourceType.equals("book") ? 2 : 1);
            default:
                return 1;
        }
    }

    private String adjustDifficultyByExperience(String originalDifficulty, String experienceLevel) {
        if (originalDifficulty == null || experienceLevel == null) return originalDifficulty;
        
        // 根据经验水平调整难度
        switch (experienceLevel) {
            case "初级":
                return "简单";
            case "中级":
                return "中等";
            case "高级":
                return "困难";
            default:
                return originalDifficulty;
        }
    }

    // 创建默认资源的方法
    private List<LearningResource> createDefaultTechResources() {
        return Arrays.asList(
            createResource("编程基础教程", "course", "计算机编程入门", "https://example.com/programming", "20小时", "简单", 4.5, "慕课网", true),
            createResource("算法训练营", "practice", "算法题目练习", "https://example.com/algorithm-practice", "30小时", "中等", 4.7, "LeetCode", false)
        );
    }

    private List<LearningResource> createDefaultCommResources() {
        return Arrays.asList(
            createResource("职场沟通技巧", "video", "提升沟通表达能力", "https://example.com/communication", "10小时", "简单", 4.4, "腾讯课堂", true),
            createResource("演讲训练", "practice", "公众演讲能力提升", "https://example.com/speech", "8小时", "简单", 4.3, "演讲俱乐部", false)
        );
    }

    private List<LearningResource> createDefaultLogicResources() {
        return Arrays.asList(
            createResource("逻辑思维训练", "course", "结构化思考方法", "https://example.com/logic", "15小时", "中等", 4.6, "网易云课堂", false),
            createResource("批判性思维", "book", "思维方式改进指南", "https://example.com/critical-thinking", "12小时", "中等", 4.5, "图书馆", true)
        );
    }

    private List<LearningResource> createDefaultBodyLanguageResources() {
        return Arrays.asList(
            createResource("肢体语言密码", "video", "非语言沟通技巧", "https://example.com/body-language", "6小时", "简单", 4.2, "B站", true),
            createResource("面试形象管理", "article", "面试中的形象塑造", "https://example.com/interview-image", "2小时", "简单", 4.0, "知乎", true)
        );
    }

    private List<LearningResource> createDefaultInnovationResources() {
        return Arrays.asList(
            createResource("创新思维方法", "course", "设计思维与创新", "https://example.com/innovation", "20小时", "中等", 4.7, "斯坦福在线", false),
            createResource("创意工具箱", "practice", "创新方法实践", "https://example.com/creative-tools", "15小时", "中等", 4.4, "创新工坊", false)
        );
    }

    private List<LearningResource> createDefaultInterviewResources() {
        return Arrays.asList(
            createResource("STAR面试法详解", "article", "结构化面试回答技巧", "https://example.com/star-method", "3小时", "简单", 4.8, "面试指南", true),
            createResource("模拟面试训练", "practice", "真实面试场景练习", "https://example.com/mock-interview", "10小时", "简单", 4.6, "面试平台", false)
        );
    }

    private List<LearningResource> createJavaBackendResources() {
        return Arrays.asList(
            createResource("Java高级编程", "course", "Java企业级开发", "https://example.com/java-advanced", "25小时", "中等", 4.8, "极客时间", false),
            createResource("Spring Boot微服务", "video", "微服务架构实战", "https://example.com/microservices", "20小时", "困难", 4.7, "慕课网", false),
            createResource("数据库优化实战", "practice", "MySQL性能调优", "https://example.com/db-optimization", "15小时", "中等", 4.5, "实验楼", false)
        );
    }

    private List<LearningResource> createFrontendResources() {
        return Arrays.asList(
            createResource("React全栈开发", "course", "现代前端框架学习", "https://example.com/react", "30小时", "中等", 4.9, "React官网", true),
            createResource("前端工程化", "video", "构建工具与部署", "https://example.com/frontend-engineering", "15小时", "中等", 4.6, "前端大学", false),
            createResource("性能优化实战", "practice", "Web性能优化技巧", "https://example.com/performance", "12小时", "困难", 4.7, "性能专家", false)
        );
    }

    private List<LearningResource> createAlgorithmResources() {
        return Arrays.asList(
            createResource("算法导论", "book", "经典算法教材", "https://example.com/algorithms", "50小时", "困难", 4.9, "MIT出版社", false),
            createResource("LeetCode刷题", "practice", "算法题目训练", "https://leetcode.com", "100小时", "困难", 4.8, "LeetCode", true),
            createResource("算法竞赛入门", "course", "ACM算法竞赛", "https://example.com/acm", "40小时", "困难", 4.6, "算法学院", false)
        );
    }

    // 其他岗位路径创建方法（简化实现）
    private LearningPathRecommendation createSystemDesignPath(String userLevel, int priority) {
        LearningPathRecommendation path = new LearningPathRecommendation();
        path.setId("system_design_" + UUID.randomUUID().toString().substring(0, 8));
        path.setTitle("系统设计能力提升");
        path.setDescription("大型系统架构设计学习");
        path.setCategory("技术能力");
        path.setSkillArea("系统设计");
        path.setEstimatedHours(35);
        path.setDifficulty("困难");
        path.setPriority(priority);
        path.setResources(createSystemDesignResources());
        path.setMilestones(Arrays.asList("基础架构理论", "分布式系统", "高可用设计", "性能优化", "案例分析"));
        return path;
    }

    private LearningPathRecommendation createUIUXPath(String userLevel, int priority) {
        LearningPathRecommendation path = new LearningPathRecommendation();
        path.setId("uiux_" + UUID.randomUUID().toString().substring(0, 8));
        path.setTitle("UI/UX设计思维");
        path.setDescription("用户体验设计基础");
        path.setCategory("设计能力");
        path.setSkillArea("用户体验");
        path.setEstimatedHours(25);
        path.setDifficulty("中等");
        path.setPriority(priority);
        path.setResources(createUIUXResources());
        path.setMilestones(Arrays.asList("设计基础", "用户研究", "原型设计", "交互设计", "设计评估"));
        return path;
    }

    private LearningPathRecommendation createMachineLearningPath(String userLevel, int priority) {
        LearningPathRecommendation path = new LearningPathRecommendation();
        path.setId("ml_" + UUID.randomUUID().toString().substring(0, 8));
        path.setTitle("机器学习基础");
        path.setDescription("AI算法工程师必备技能");
        path.setCategory("技术能力");
        path.setSkillArea("机器学习");
        path.setEstimatedHours(60);
        path.setDifficulty("困难");
        path.setPriority(priority);
        path.setResources(createMLResources());
        path.setMilestones(Arrays.asList("数学基础", "机器学习算法", "深度学习", "实际项目", "模型部署"));
        return path;
    }

    private LearningPathRecommendation createDataAnalysisPath(String userLevel, int priority) {
        LearningPathRecommendation path = new LearningPathRecommendation();
        path.setId("data_analysis_" + UUID.randomUUID().toString().substring(0, 8));
        path.setTitle("数据分析技能");
        path.setDescription("数据分析师核心技能");
        path.setCategory("技术能力");
        path.setSkillArea("数据分析");
        path.setEstimatedHours(40);
        path.setDifficulty("中等");
        path.setPriority(priority);
        path.setResources(createDataAnalysisResources());
        path.setMilestones(Arrays.asList("统计基础", "数据处理", "可视化", "业务分析", "报告撰写"));
        return path;
    }

    private LearningPathRecommendation createDataSciencePath(String userLevel, int priority) {
        LearningPathRecommendation path = new LearningPathRecommendation();
        path.setId("data_science_" + UUID.randomUUID().toString().substring(0, 8));
        path.setTitle("数据科学进阶");
        path.setDescription("数据科学家技能树");
        path.setCategory("技术能力");
        path.setSkillArea("数据科学");
        path.setEstimatedHours(55);
        path.setDifficulty("困难");
        path.setPriority(priority);
        path.setResources(createDataScienceResources());
        path.setMilestones(Arrays.asList("数据科学基础", "高级统计", "机器学习应用", "大数据技术", "项目实战"));
        return path;
    }

    private LearningPathRecommendation createTestingPath(String userLevel, int priority) {
        LearningPathRecommendation path = new LearningPathRecommendation();
        path.setId("testing_" + UUID.randomUUID().toString().substring(0, 8));
        path.setTitle("软件测试技能");
        path.setDescription("测试工程师专业技能");
        path.setCategory("技术能力");
        path.setSkillArea("软件测试");
        path.setEstimatedHours(30);
        path.setDifficulty("中等");
        path.setPriority(priority);
        path.setResources(createTestingResources());
        path.setMilestones(Arrays.asList("测试理论", "测试设计", "自动化测试", "性能测试", "测试管理"));
        return path;
    }

    private LearningPathRecommendation createAutomationPath(String userLevel, int priority) {
        LearningPathRecommendation path = new LearningPathRecommendation();
        path.setId("automation_" + UUID.randomUUID().toString().substring(0, 8));
        path.setTitle("自动化测试");
        path.setDescription("测试自动化技术栈");
        path.setCategory("技术能力");
        path.setSkillArea("自动化测试");
        path.setEstimatedHours(35);
        path.setDifficulty("中等");
        path.setPriority(priority);
        path.setResources(createAutomationResources());
        path.setMilestones(Arrays.asList("自动化基础", "工具选择", "脚本编写", "持续集成", "维护优化"));
        return path;
    }

    private LearningPathRecommendation createGeneralTechPath(String userLevel, int priority) {
        LearningPathRecommendation path = new LearningPathRecommendation();
        path.setId("general_tech_" + UUID.randomUUID().toString().substring(0, 8));
        path.setTitle("通用技术技能");
        path.setDescription("IT从业者基础技能");
        path.setCategory("技术能力");
        path.setSkillArea("通用技术");
        path.setEstimatedHours(25);
        path.setDifficulty("简单");
        path.setPriority(priority);
        path.setResources(createGeneralTechResources());
        path.setMilestones(Arrays.asList("计算机基础", "编程入门", "数据库基础", "网络知识", "项目管理"));
        return path;
    }

    // 创建各种资源的简化方法
    private List<LearningResource> createSystemDesignResources() {
        return Arrays.asList(
            createResource("系统设计面试", "course", "大型系统架构设计", "https://example.com/system-design", "30小时", "困难", 4.8, "系统架构师", false)
        );
    }

    private List<LearningResource> createUIUXResources() {
        return Arrays.asList(
            createResource("用户体验设计", "course", "UX设计基础", "https://example.com/ux-design", "20小时", "中等", 4.6, "设计学院", false)
        );
    }

    private List<LearningResource> createMLResources() {
        return Arrays.asList(
            createResource("机器学习实战", "course", "ML算法与应用", "https://example.com/ml", "50小时", "困难", 4.9, "AI学院", false)
        );
    }

    private List<LearningResource> createDataAnalysisResources() {
        return Arrays.asList(
            createResource("Python数据分析", "course", "数据分析工具", "https://example.com/data-analysis", "35小时", "中等", 4.7, "数据学院", false)
        );
    }

    private List<LearningResource> createDataScienceResources() {
        return Arrays.asList(
            createResource("数据科学全栈", "course", "端到端数据科学", "https://example.com/data-science", "45小时", "困难", 4.8, "数据科学家", false)
        );
    }

    private List<LearningResource> createTestingResources() {
        return Arrays.asList(
            createResource("软件测试基础", "course", "测试理论与实践", "https://example.com/testing", "25小时", "中等", 4.5, "测试学院", false)
        );
    }

    private List<LearningResource> createAutomationResources() {
        return Arrays.asList(
            createResource("自动化测试实战", "course", "测试自动化工具", "https://example.com/automation", "30小时", "中等", 4.6, "自动化专家", false)
        );
    }

    private List<LearningResource> createGeneralTechResources() {
        return Arrays.asList(
            createResource("计算机基础", "course", "IT基础知识", "https://example.com/cs-basics", "20小时", "简单", 4.4, "计算机学院", true)
        );
    }
}