package org.dromara.app.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 专业视图对象
 *
 * <AUTHOR>
 */
@Data
public class MajorVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 专业ID
     */
    private String id;

    /**
     * 专业名称
     */
    private String name;

    /**
     * 专业图标
     */
    private String icon;

    /**
     * 专业颜色
     */
    private String color;

    /**
     * 题库数量
     */
    private Integer count;
}
