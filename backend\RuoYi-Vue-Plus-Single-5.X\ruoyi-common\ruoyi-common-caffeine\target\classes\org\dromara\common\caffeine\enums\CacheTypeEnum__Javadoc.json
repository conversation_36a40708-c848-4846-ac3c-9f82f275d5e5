{"doc": "\n 缓存类型枚举\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "code", "doc": "\n 缓存类型代码\r\n"}, {"name": "description", "doc": "\n 缓存类型描述\r\n"}], "enumConstants": [{"name": "USER", "doc": "\n 用户缓存\r\n"}, {"name": "SYSTEM", "doc": "\n 系统缓存\r\n"}, {"name": "CONFIG", "doc": "\n 配置缓存\r\n"}, {"name": "DICT", "doc": "\n 字典缓存\r\n"}, {"name": "PERMISSION", "doc": "\n 权限缓存\r\n"}, {"name": "TEMP", "doc": "\n 临时缓存\r\n"}], "methods": [{"name": "getByCode", "paramTypes": ["java.lang.String"], "doc": "\n 根据代码获取枚举\r\n\r\n @param code 代码\r\n @return 枚举\r\n"}], "constructors": []}