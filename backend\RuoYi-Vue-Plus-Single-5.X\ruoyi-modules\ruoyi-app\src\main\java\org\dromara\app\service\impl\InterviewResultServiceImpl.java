package org.dromara.app.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.*;
import org.dromara.app.domain.dto.InterviewReportData;
import org.dromara.app.domain.vo.InterviewResultResponseVo;
import org.dromara.app.mapper.*;
import org.dromara.app.service.IInterviewResultService;
import org.dromara.common.core.exception.ServiceException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Interview Result Service Implementation
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class InterviewResultServiceImpl implements IInterviewResultService {

    private final InterviewResultMapper interviewResultMapper;
    private final DimensionScoreMapper dimensionScoreMapper;
    private final QuestionAnalysisMapper questionAnalysisMapper;
    private final AudioMetricsMapper audioMetricsMapper;
    private final VideoMetricsMapper videoMetricsMapper;
    private final PerformanceMetricsMapper performanceMetricsMapper;
    private final LearningResourceMapper learningResourceMapper;
    private final ImprovementPlanMapper improvementPlanMapper;
    private final InterviewHistoryMapper interviewHistoryMapper;
    private final ResultShareMapper resultShareMapper;

    @Override
    public InterviewResultResponseVo.InterviewResultSummary getResultSummary(String resultId) {
        log.info("获取面试结果摘要，resultId: {}", resultId);

        // 先尝试按结果ID查询，如果没有则按会话ID查询
        InterviewResult result = interviewResultMapper.selectById(resultId);
        if (result == null) {
            result = interviewResultMapper.selectBySessionId(resultId);
        }

        if (result == null) {
            throw new ServiceException("Interview result not found");
        }

        // Convert to response object
        InterviewResultResponseVo.InterviewResultSummary summary = new InterviewResultResponseVo.InterviewResultSummary();
        summary.setId(result.getId());
        summary.setSessionId(result.getSessionId());
        summary.setJobName(result.getJobName());
        summary.setCompany(result.getCompany());
        summary.setMode(result.getMode());
        summary.setDate(result.getDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")));
        summary.setDuration(result.getDuration());
        summary.setTotalScore(result.getTotalScore());
        summary.setRank(result.getRank());
        summary.setRankText(result.getRankText());
        summary.setPercentile(result.getPercentile());
        summary.setAnsweredQuestions(result.getAnsweredQuestions());
        summary.setTotalQuestions(result.getTotalQuestions());
        summary.setStatus(result.getStatus());
        summary.setTopStrengths(result.getTopStrengths());
        summary.setTopWeaknesses(result.getTopWeaknesses());

        return summary;
    }

    @Override
    public InterviewResultResponseVo.InterviewResultDetail getResultDetail(String resultId) {
        log.info("获取面试结果详情，resultId: {}", resultId);

        // 获取基本信息
        InterviewResultResponseVo.InterviewResultSummary summary = getResultSummary(resultId);

        // 创建详细结果对象
        InterviewResultResponseVo.InterviewResultDetail detail = new InterviewResultResponseVo.InterviewResultDetail();
        // 复制基本信息
        detail.setId(summary.getId());
        detail.setSessionId(summary.getSessionId());
        detail.setJobName(summary.getJobName());
        detail.setCompany(summary.getCompany());
        detail.setMode(summary.getMode());
        detail.setDate(summary.getDate());
        detail.setDuration(summary.getDuration());
        detail.setTotalScore(summary.getTotalScore());
        detail.setRank(summary.getRank());
        detail.setRankText(summary.getRankText());
        detail.setPercentile(summary.getPercentile());
        detail.setAnsweredQuestions(summary.getAnsweredQuestions());
        detail.setTotalQuestions(summary.getTotalQuestions());
        detail.setStatus(summary.getStatus());
        detail.setTopStrengths(summary.getTopStrengths());
        detail.setTopWeaknesses(summary.getTopWeaknesses());

        // 获取维度评分
        List<DimensionScore> dimensionScores = dimensionScoreMapper.selectByResultId(summary.getId());
        detail.setDimensionScores(dimensionScores.stream().map(this::convertToDimensionScoreVo).collect(Collectors.toList()));

        // 获取问题分析
        List<QuestionAnalysis> questionAnalyses = questionAnalysisMapper.selectByResultId(summary.getId());
        detail.setQuestionAnalyses(questionAnalyses.stream().map(this::convertToQuestionAnalysisVo).collect(Collectors.toList()));

        // 获取音频指标
        AudioMetrics audioMetrics = audioMetricsMapper.selectByResultId(summary.getId());
        if (audioMetrics != null) {
            detail.setAudioMetrics(convertToAudioMetricsVo(audioMetrics));
        }

        // 获取视频指标
        VideoMetrics videoMetrics = videoMetricsMapper.selectByResultId(summary.getId());
        if (videoMetrics != null) {
            detail.setVideoMetrics(convertToVideoMetricsVo(videoMetrics));
        }

        // 获取总体反馈
        InterviewResult result = interviewResultMapper.selectById(summary.getId());
        detail.setOverallFeedback(result.getOverallFeedback());

        return detail;
    }

    @Override
    public InterviewResultResponseVo.PerformanceMetrics getPerformanceMetrics(String resultId) {
        log.info("获取性能指标，resultId: {}", resultId);

        PerformanceMetrics metrics = performanceMetricsMapper.selectByResultId(resultId);
        if (metrics == null) {
            throw new ServiceException("性能指标不存在");
        }

        return convertToPerformanceMetricsVo(metrics);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public InterviewResultResponseVo.SaveHistoryResponse saveToHistory(String resultId, String title) {
        log.info("保存到历史记录，resultId: {}, title: {}", resultId, title);

        if (!StpUtil.isLogin()) {
            throw new ServiceException("请先登录");
        }

        Long userId = StpUtil.getLoginIdAsLong();

        // 检查结果是否存在
        InterviewResult result = interviewResultMapper.selectById(resultId);
        if (result == null) {
            throw new ServiceException("面试结果不存在");
        }

        // 检查是否已经保存过
        InterviewHistory existingHistory = interviewHistoryMapper.selectByUserIdAndResultId(userId, resultId);
        if (existingHistory != null) {
            throw new ServiceException("该结果已保存到历史记录");
        }

        // 创建历史记录
        InterviewHistory history = new InterviewHistory();
        history.setUserId(userId);
        history.setResultId(resultId);
        history.setTitle(StrUtil.isNotBlank(title) ? title : result.getJobName());
        history.setIsFavorite(false);
        history.setCreateTime(LocalDateTime.now());
        history.setUpdateTime(LocalDateTime.now());

        int rows = interviewHistoryMapper.insert(history);

        InterviewResultResponseVo.SaveHistoryResponse response = new InterviewResultResponseVo.SaveHistoryResponse();
        response.setSuccess(rows > 0);
        response.setHistoryId(history.getId().toString());

        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public InterviewResultResponseVo.ShareResultResponse shareResult(String resultId, String platform, String content) {
        log.info("分享结果，resultId: {}, platform: {}", resultId, platform);

        if (!StpUtil.isLogin()) {
            throw new ServiceException("请先登录");
        }

        Long userId = StpUtil.getLoginIdAsLong();

        // 检查结果是否存在
        InterviewResult result = interviewResultMapper.selectById(resultId);
        if (result == null) {
            throw new ServiceException("面试结果不存在");
        }

        // 生成分享链接
        String shareUrl = generateShareUrl(resultId, platform);

        // 创建分享记录
        ResultShare share = new ResultShare();
        share.setResultId(resultId);
        share.setUserId(userId);
        share.setPlatform(platform);
        share.setShareUrl(shareUrl);
        share.setContent(content);
        share.setViewCount(0);
        share.setExpiresAt(LocalDateTime.now().plusDays(30)); // 30天后过期
        share.setStatus("active");
        share.setCreateTime(LocalDateTime.now());
        share.setUpdateTime(LocalDateTime.now());

        int rows = resultShareMapper.insert(share);

        InterviewResultResponseVo.ShareResultResponse response = new InterviewResultResponseVo.ShareResultResponse();
        response.setSuccess(rows > 0);
        response.setShareUrl(shareUrl);

        return response;
    }

    @Override
    public InterviewResultResponseVo.ImprovementPlan getImprovementPlan(String resultId, Long userId) {
        log.info("获取提升计划，resultId: {}, userId: {}", resultId, userId);

        ImprovementPlan plan = improvementPlanMapper.selectByResultId(resultId);
        if (plan == null) {
            // 如果没有现成的计划，生成一个默认计划
            plan = generateDefaultImprovementPlan(resultId, userId);
        }

        return convertToImprovementPlanVo(plan);
    }

    @Override
    public List<InterviewResultResponseVo.LearningResource> getLearningResources(String resultId, Integer limit) {
        log.info("获取学习资源推荐，resultId: {}, limit: {}", resultId, limit);

        if (limit == null || limit <= 0) {
            limit = 10;
        }

        // 根据结果分析推荐相关资源
        List<LearningResource> resources = learningResourceMapper.selectRecommendedResources(null, limit);

        return resources.stream().map(this::convertToLearningResourceVo).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createInterviewResult(String sessionId, Object resultData) {
        log.info("创建面试结果，sessionId: {}", sessionId);

        if (StrUtil.isBlank(sessionId)) {
            throw new ServiceException("会话ID不能为空");
        }

        if (resultData == null) {
            throw new ServiceException("结果数据不能为空");
        }

        if (!StpUtil.isLogin()) {
            throw new ServiceException("请先登录");
        }

        Long userId = StpUtil.getLoginIdAsLong();

        try {
            // 解析结果数据
            InterviewResultData data = parseResultData(resultData);

            // 生成结果ID
            String resultId = IdUtil.simpleUUID();

            // 创建主结果记录
            InterviewResult result = new InterviewResult();
            result.setId(resultId);
            result.setSessionId(sessionId);
            result.setUserId(userId);
            result.setJobId(data.getJobId());
            result.setJobName(data.getJobName());
            result.setCompany(data.getCompany());
            result.setMode(data.getMode());
            result.setDate(LocalDateTime.now());
            result.setDuration(data.getDuration());
            result.setTotalScore(data.getTotalScore());
            result.setRank(data.getRank());
            result.setRankText(data.getRankText());
            result.setPercentile(data.getPercentile());
            result.setAnsweredQuestions(data.getAnsweredQuestions());
            result.setTotalQuestions(data.getTotalQuestions());
            result.setStatus(data.getStatus());
            result.setTopStrengths(data.getTopStrengths());
            result.setTopWeaknesses(data.getTopWeaknesses());
            result.setOverallFeedback(data.getOverallFeedback());
            result.setCreateTime(DateTime.now());
            result.setUpdateTime(DateTime.now());

            int rows = interviewResultMapper.insert(result);
            if (rows == 0) {
                throw new ServiceException("创建面试结果失败");
            }

            // 创建关联数据
            createRelatedData(resultId, data);

            log.info("面试结果创建成功，resultId: {}", resultId);
            return resultId;

        } catch (Exception e) {
            log.error("创建面试结果失败，sessionId: {}", sessionId, e);
            throw new ServiceException("创建面试结果失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateInterviewResult(String resultId, Object resultData) {
        log.info("更新面试结果，resultId: {}", resultId);

        if (StrUtil.isBlank(resultId)) {
            throw new ServiceException("结果ID不能为空");
        }

        if (resultData == null) {
            throw new ServiceException("结果数据不能为空");
        }

        if (!StpUtil.isLogin()) {
            throw new ServiceException("请先登录");
        }

        Long userId = StpUtil.getLoginIdAsLong();

        try {
            // 检查结果是否存在
            InterviewResult existingResult = interviewResultMapper.selectById(resultId);
            if (existingResult == null) {
                throw new ServiceException("面试结果不存在");
            }

            // 检查权限（只能更新自己的结果）
            if (!existingResult.getUserId().equals(userId)) {
                throw new ServiceException("无权限更新此面试结果");
            }

            // 解析更新数据
            InterviewResultData data = parseResultData(resultData);

            // 更新主结果记录
            InterviewResult updateResult = new InterviewResult();
            updateResult.setId(resultId);
            updateResult.setJobId(data.getJobId());
            updateResult.setJobName(data.getJobName());
            updateResult.setCompany(data.getCompany());
            updateResult.setMode(data.getMode());
            updateResult.setDuration(data.getDuration());
            updateResult.setTotalScore(data.getTotalScore());
            updateResult.setRank(data.getRank());
            updateResult.setRankText(data.getRankText());
            updateResult.setPercentile(data.getPercentile());
            updateResult.setAnsweredQuestions(data.getAnsweredQuestions());
            updateResult.setTotalQuestions(data.getTotalQuestions());
            updateResult.setStatus(data.getStatus());
            updateResult.setTopStrengths(data.getTopStrengths());
            updateResult.setTopWeaknesses(data.getTopWeaknesses());
            updateResult.setOverallFeedback(data.getOverallFeedback());
            updateResult.setUpdateTime(DateTime.now());

            int rows = interviewResultMapper.updateById(updateResult);
            if (rows == 0) {
                throw new ServiceException("更新面试结果失败");
            }

            // 更新关联数据
            updateRelatedData(resultId, data);

            log.info("面试结果更新成功，resultId: {}", resultId);
            return true;

        } catch (Exception e) {
            log.error("更新面试结果失败，resultId: {}", resultId, e);
            throw new ServiceException("更新面试结果失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteInterviewResult(String resultId) {
        log.info("删除面试结果，resultId: {}", resultId);

        if (StrUtil.isBlank(resultId)) {
            throw new ServiceException("结果ID不能为空");
        }

        if (!StpUtil.isLogin()) {
            throw new ServiceException("请先登录");
        }

        Long userId = StpUtil.getLoginIdAsLong();

        try {
            // 检查结果是否存在
            InterviewResult existingResult = interviewResultMapper.selectById(resultId);
            if (existingResult == null) {
                throw new ServiceException("面试结果不存在");
            }

            // 检查权限（只能删除自己的结果）
            if (!existingResult.getUserId().equals(userId)) {
                throw new ServiceException("无权限删除此面试结果");
            }

            // 删除关联数据
            deleteRelatedData(resultId);

            // 删除主结果记录
            int rows = interviewResultMapper.deleteById(resultId);
            if (rows == 0) {
                throw new ServiceException("删除面试结果失败");
            }

            log.info("面试结果删除成功，resultId: {}", resultId);
            return true;

        } catch (Exception e) {
            log.error("删除面试结果失败，resultId: {}", resultId, e);
            throw new ServiceException("删除面试结果失败：" + e.getMessage());
        }
    }

    @Override
    public List<InterviewResultResponseVo.InterviewResultSummary> getUserResults(Long userId, Integer limit) {
        log.info("获取用户面试结果列表，userId: {}, limit: {}", userId, limit);

        if (userId == null) {
            throw new ServiceException("用户ID不能为空");
        }

        if (limit == null || limit <= 0) {
            limit = 20; // 默认返回20条
        }

        try {
            // 查询用户的面试结果列表
            List<InterviewResult> results = interviewResultMapper.selectByUserId(userId, limit);

            if (results.isEmpty()) {
                return new ArrayList<>();
            }

            // 转换为响应对象
            List<InterviewResultResponseVo.InterviewResultSummary> summaries = new ArrayList<>();
            for (InterviewResult result : results) {
                InterviewResultResponseVo.InterviewResultSummary summary = new InterviewResultResponseVo.InterviewResultSummary();
                summary.setId(result.getId());
                summary.setSessionId(result.getSessionId());
                summary.setJobName(result.getJobName());
                summary.setCompany(result.getCompany());
                summary.setMode(result.getMode());
                summary.setDate(result.getDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")));
                summary.setDuration(result.getDuration());
                summary.setTotalScore(result.getTotalScore());
                summary.setRank(result.getRank());
                summary.setRankText(result.getRankText());
                summary.setPercentile(result.getPercentile());
                summary.setAnsweredQuestions(result.getAnsweredQuestions());
                summary.setTotalQuestions(result.getTotalQuestions());
                summary.setStatus(result.getStatus());
                summary.setTopStrengths(result.getTopStrengths());
                summary.setTopWeaknesses(result.getTopWeaknesses());
                summaries.add(summary);
            }

            return summaries;

        } catch (Exception e) {
            log.error("获取用户面试结果列表失败，userId: {}", userId, e);
            throw new ServiceException("获取用户面试结果列表失败：" + e.getMessage());
        }
    }

    @Override
    public UserStatistics getUserStatistics(Long userId) {
        log.info("获取用户面试统计，userId: {}", userId);

        if (userId == null) {
            throw new ServiceException("用户ID不能为空");
        }

        try {
            UserStatistics statistics = new UserStatistics();

            // 获取总面试次数
            Integer totalInterviews = interviewResultMapper.selectCountByUserId(userId);
            statistics.setTotalInterviews(totalInterviews != null ? totalInterviews : 0);

            // 获取平均分数
            Double averageScore = interviewResultMapper.selectAvgScoreByUserId(userId);
            statistics.setAverageScore(averageScore != null ? averageScore : 0.0);

            // 获取最近30天的面试记录计算月度改进
            List<InterviewResult> recentResults = interviewResultMapper.selectRecentByUserId(userId, 30);
            calculateMonthlyImprovement(statistics, recentResults);

            // 计算当前等级和进度
            calculateCurrentLevelAndProgress(statistics);

            // 获取最高分数
            Integer highestScore = 0;
            if (!recentResults.isEmpty()) {
                highestScore = recentResults.stream()
                    .mapToInt(InterviewResult::getTotalScore)
                    .max()
                    .orElse(0);
            }
            statistics.setHighestScore(highestScore);

            // 获取最近面试时间
            if (!recentResults.isEmpty()) {
                LocalDateTime lastInterviewTime = recentResults.get(0).getDate();
                statistics.setLastInterviewDate(lastInterviewTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            }

            return statistics;

        } catch (Exception e) {
            log.error("获取用户面试统计失败，userId: {}", userId, e);
            throw new ServiceException("获取用户面试统计失败：" + e.getMessage());
        }
    }

    @Override
    public InterviewReportData generateReport(String resultId) {
        log.info("生成面试报告，resultId: {}", resultId);

        if (StrUtil.isBlank(resultId)) {
            throw new ServiceException("结果ID不能为空");
        }

        try {
            // 获取面试结果详情
            InterviewResultResponseVo.InterviewResultDetail detail = getResultDetail(resultId);
            if (detail == null) {
                throw new ServiceException("面试结果不存在");
            }

            // 创建报告数据
            InterviewReportData reportData = new InterviewReportData();
            reportData.setSessionId(detail.getSessionId());
            reportData.setGenerateTime(System.currentTimeMillis());

            // 设置基本信息
            InterviewReportData.BasicInfo basicInfo = new InterviewReportData.BasicInfo();
            basicInfo.setJobPosition(detail.getJobName());
            basicInfo.setCompany(detail.getCompany());
            basicInfo.setInterviewDate(detail.getDate());
            basicInfo.setDuration(detail.getDuration());
            basicInfo.setTotalQuestions(detail.getTotalQuestions());
            basicInfo.setAnsweredQuestions(detail.getAnsweredQuestions());
            reportData.setBasicInfo(basicInfo);

            // 设置总体评分
            reportData.setOverallScore(detail.getTotalScore());
            reportData.setRank(detail.getRank());
            reportData.setPercentile(detail.getPercentile());

            // 设置维度评分
            if (detail.getDimensionScores() != null) {
                List<InterviewReportData.DimensionScore> dimensionScores = detail.getDimensionScores().stream()
                    .map(this::convertToReportDimensionScore)
                    .collect(Collectors.toList());
                reportData.setDimensionScores(dimensionScores);
            }

            // 设置优势和劣势
            reportData.setStrengths(detail.getTopStrengths());
            reportData.setWeaknesses(detail.getTopWeaknesses());
            reportData.setOverallFeedback(detail.getOverallFeedback());

            // 生成雷达图数据
            reportData.setRadarChartData(generateRadarChartData(detail));

            // 生成改进建议
            reportData.setImprovementSuggestions(generateImprovementSuggestions(detail));

            // 生成学习路径推荐
            reportData.setLearningPaths(generateLearningPathRecommendations(detail));

            return reportData;

        } catch (Exception e) {
            log.error("生成面试报告失败，resultId: {}", resultId, e);
            throw new ServiceException("生成面试报告失败：" + e.getMessage());
        }
    }

    // 私有辅助方法
    private String generateShareUrl(String resultId, String platform) {
        return "https://example.com/interview/result/share/" + resultId + "?platform=" + platform + "&token=" + IdUtil.simpleUUID();
    }

    private ImprovementPlan generateDefaultImprovementPlan(String resultId, Long userId) {
        log.info("生成默认提升计划，resultId: {}, userId: {}", resultId, userId);

        try {
            // 获取面试结果详情
            InterviewResult result = interviewResultMapper.selectById(resultId);
            if (result == null) {
                return new ImprovementPlan();
            }

            ImprovementPlan plan = new ImprovementPlan();
            plan.setResultId(resultId);
            plan.setUserId(userId);
            plan.setStatus("active");
            plan.setProgress(0);
            plan.setCreateTime(LocalDateTime.now());
            plan.setUpdateTime(LocalDateTime.now());

            // 根据分数生成提升领域
            List<ImprovementPlan.ImprovementArea> areas = new ArrayList<>();

            // 基于总分生成通用提升建议
            if (result.getTotalScore() < 60) {
                areas.add(createImprovementArea("基础技能", 2, 4, "3个月",
                    List.of("加强基础知识学习", "多做练习题", "参加培训课程")));
                areas.add(createImprovementArea("沟通表达", 2, 4, "2个月",
                    List.of("练习口语表达", "参加演讲训练", "模拟面试练习")));
            } else if (result.getTotalScore() < 80) {
                areas.add(createImprovementArea("专业技能", 3, 4, "2个月",
                    List.of("深入学习专业知识", "实践项目经验", "关注行业动态")));
                areas.add(createImprovementArea("问题解决", 3, 4, "1个月",
                    List.of("练习逻辑思维", "学习解决问题的方法", "多做案例分析")));
            } else {
                areas.add(createImprovementArea("领导力", 3, 5, "3个月",
                    List.of("学习管理技能", "培养团队协作能力", "提升决策能力")));
            }

            plan.setAreas(areas);

            // 设置目标
            plan.setShortTermGoals(List.of("提升面试表现", "加强薄弱环节", "增强自信心"));
            plan.setMediumTermGoals(List.of("全面提升专业技能", "建立知识体系", "培养核心竞争力"));
            plan.setLongTermGoals(List.of("成为行业专家", "实现职业目标", "持续学习成长"));

            // 推荐学习资源
            List<LearningResource> resources = learningResourceMapper.selectPopularResources(5);
            plan.setRecommendedResources(resources);

            // 保存到数据库
            improvementPlanMapper.insert(plan);

            return plan;

        } catch (Exception e) {
            log.error("生成默认提升计划失败，resultId: {}", resultId, e);
            return new ImprovementPlan();
        }
    }

    // 辅助方法

    /**
     * 解析结果数据
     */
    private InterviewResultData parseResultData(Object resultData) {
        try {
            if (resultData instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> dataMap = (Map<String, Object>) resultData;
                return convertMapToResultData(dataMap);
            } else if (resultData instanceof String) {
                // 如果是JSON字符串，先解析为Map
                Map<String, Object> dataMap = cn.hutool.json.JSONUtil.toBean((String) resultData, Map.class);
                return convertMapToResultData(dataMap);
            } else {
                throw new ServiceException("不支持的数据格式");
            }
        } catch (Exception e) {
            log.error("解析结果数据失败", e);
            throw new ServiceException("解析结果数据失败：" + e.getMessage());
        }
    }

    /**
     * 将Map转换为InterviewResultData
     */
    @SuppressWarnings("unchecked")
    private InterviewResultData convertMapToResultData(Map<String, Object> dataMap) {
        InterviewResultData data = new InterviewResultData();

        // 基本信息
        data.setJobId(getLongValue(dataMap, "jobId"));
        data.setJobName(getStringValue(dataMap, "jobName"));
        data.setCompany(getStringValue(dataMap, "company"));
        data.setMode(getStringValue(dataMap, "mode"));
        data.setDuration(getStringValue(dataMap, "duration"));
        data.setTotalScore(getIntegerValue(dataMap, "totalScore"));
        data.setRank(getStringValue(dataMap, "rank"));
        data.setRankText(getStringValue(dataMap, "rankText"));
        data.setPercentile(getIntegerValue(dataMap, "percentile"));
        data.setAnsweredQuestions(getIntegerValue(dataMap, "answeredQuestions"));
        data.setTotalQuestions(getIntegerValue(dataMap, "totalQuestions"));
        data.setStatus(getStringValue(dataMap, "status"));
        data.setOverallFeedback(getStringValue(dataMap, "overallFeedback"));

        // 列表数据
        data.setTopStrengths(getListValue(dataMap, "topStrengths"));
        data.setTopWeaknesses(getListValue(dataMap, "topWeaknesses"));

        // 关联数据
        if (dataMap.containsKey("dimensionScores")) {
            data.setDimensionScores(parseDimensionScores((List<Map<String, Object>>) dataMap.get("dimensionScores")));
        }

        if (dataMap.containsKey("questionAnalyses")) {
            data.setQuestionAnalyses(parseQuestionAnalyses((List<Map<String, Object>>) dataMap.get("questionAnalyses")));
        }

        if (dataMap.containsKey("audioMetrics")) {
            data.setAudioMetrics(parseAudioMetrics((Map<String, Object>) dataMap.get("audioMetrics")));
        }

        if (dataMap.containsKey("videoMetrics")) {
            data.setVideoMetrics(parseVideoMetrics((Map<String, Object>) dataMap.get("videoMetrics")));
        }

        if (dataMap.containsKey("performanceMetrics")) {
            data.setPerformanceMetrics(parsePerformanceMetrics((Map<String, Object>) dataMap.get("performanceMetrics")));
        }

        return data;
    }

    // 转换方法
    private InterviewResultResponseVo.DimensionScore convertToDimensionScoreVo(DimensionScore dimensionScore) {
        InterviewResultResponseVo.DimensionScore vo = new InterviewResultResponseVo.DimensionScore();
        vo.setDimension(dimensionScore.getDimension());
        vo.setScore(dimensionScore.getScore());
        vo.setMaxScore(dimensionScore.getMaxScore());
        vo.setPercentile(dimensionScore.getPercentile());
        vo.setDescription(dimensionScore.getDescription());
        vo.setStrengths(dimensionScore.getStrengths());
        vo.setWeaknesses(dimensionScore.getWeaknesses());
        vo.setRecommendations(dimensionScore.getRecommendations());
        return vo;
    }

    private InterviewResultResponseVo.QuestionAnalysis convertToQuestionAnalysisVo(QuestionAnalysis questionAnalysis) {
        InterviewResultResponseVo.QuestionAnalysis vo = new InterviewResultResponseVo.QuestionAnalysis();
        vo.setId(questionAnalysis.getId());
        vo.setQuestionId(questionAnalysis.getQuestionId());
        vo.setQuestion(questionAnalysis.getQuestion());
        vo.setCategory(questionAnalysis.getCategory());
        vo.setDifficulty(questionAnalysis.getDifficulty());
        vo.setAnswer(questionAnalysis.getAnswer());
        vo.setScore(questionAnalysis.getScore());
        vo.setAudioScore(questionAnalysis.getAudioScore());
        vo.setVideoScore(questionAnalysis.getVideoScore());
        vo.setTextScore(questionAnalysis.getTextScore());
        vo.setFeedback(questionAnalysis.getFeedback());
        vo.setStrengths(questionAnalysis.getStrengths());
        vo.setWeaknesses(questionAnalysis.getWeaknesses());
        vo.setKeywordMatches(questionAnalysis.getKeywordMatches());
        vo.setIdealAnswer(questionAnalysis.getIdealAnswer());
        vo.setTimeSpent(questionAnalysis.getTimeSpent());
        vo.setTimeLimit(questionAnalysis.getTimeLimit());
        return vo;
    }

    private InterviewResultResponseVo.AudioMetrics convertToAudioMetricsVo(AudioMetrics audioMetrics) {
        InterviewResultResponseVo.AudioMetrics vo = new InterviewResultResponseVo.AudioMetrics();
        vo.setClarity(audioMetrics.getClarity());
        vo.setFluency(audioMetrics.getFluency());
        vo.setConfidence(audioMetrics.getConfidence());
        vo.setPace(audioMetrics.getPace());
        vo.setOverall(audioMetrics.getOverall());
        return vo;
    }

    private InterviewResultResponseVo.VideoMetrics convertToVideoMetricsVo(VideoMetrics videoMetrics) {
        InterviewResultResponseVo.VideoMetrics vo = new InterviewResultResponseVo.VideoMetrics();
        vo.setEyeContact(videoMetrics.getEyeContact());
        vo.setPosture(videoMetrics.getPosture());
        vo.setExpressions(videoMetrics.getExpressions());
        vo.setGestures(videoMetrics.getGestures());
        vo.setOverall(videoMetrics.getOverall());
        return vo;
    }

    private InterviewResultResponseVo.PerformanceMetrics convertToPerformanceMetricsVo(PerformanceMetrics performanceMetrics) {
        InterviewResultResponseVo.PerformanceMetrics vo = new InterviewResultResponseVo.PerformanceMetrics();
        vo.setTechnical(performanceMetrics.getTechnical());
        vo.setCommunication(performanceMetrics.getCommunication());
        vo.setProblemSolving(performanceMetrics.getProblemSolving());
        vo.setTeamwork(performanceMetrics.getTeamwork());
        vo.setLeadership(performanceMetrics.getLeadership());
        vo.setCreativity(performanceMetrics.getCreativity());
        vo.setDetailedMetrics(performanceMetrics.getDetailedMetrics());
        vo.setIndustryAverage(performanceMetrics.getIndustryAverage());
        vo.setSkillGaps(performanceMetrics.getSkillGaps());
        vo.setSkillStrengths(performanceMetrics.getSkillStrengths());
        return vo;
    }

    private InterviewResultResponseVo.LearningResource convertToLearningResourceVo(LearningResource learningResource) {
        InterviewResultResponseVo.LearningResource vo = new InterviewResultResponseVo.LearningResource();
        vo.setId(String.valueOf(learningResource.getId()));
        vo.setTitle(learningResource.getTitle());
        vo.setType(learningResource.getType());
        vo.setDescription(learningResource.getDescription());
        vo.setDuration(learningResource.getDuration());
        vo.setDifficulty(learningResource.getDifficulty());
        vo.setUrl(learningResource.getUrl());
        vo.setImageUrl(learningResource.getUrl());
        vo.setTags(learningResource.getTags());
//        vo.setRelevanceScore(learningResource.getRelevanceScore());
        return vo;
    }

    private InterviewResultResponseVo.ImprovementPlan convertToImprovementPlanVo(ImprovementPlan improvementPlan) {
        InterviewResultResponseVo.ImprovementPlan vo = new InterviewResultResponseVo.ImprovementPlan();

        // 转换提升领域
        if (improvementPlan.getAreas() != null) {
            List<InterviewResultResponseVo.ImprovementArea> areas = improvementPlan.getAreas().stream()
                .map(area -> {
                    InterviewResultResponseVo.ImprovementArea areaVo = new InterviewResultResponseVo.ImprovementArea();
                    areaVo.setName(area.getName());
                    areaVo.setCurrentLevel(area.getCurrentLevel());
                    areaVo.setTargetLevel(area.getTargetLevel());
                    areaVo.setTimeframe(area.getTimeframe());
                    areaVo.setActions(area.getActions());
                    return areaVo;
                })
                .collect(Collectors.toList());
            vo.setAreas(areas);
        }

        vo.setShortTermGoals(improvementPlan.getShortTermGoals());
        vo.setMediumTermGoals(improvementPlan.getMediumTermGoals());
        vo.setLongTermGoals(improvementPlan.getLongTermGoals());

        // 转换推荐资源
        if (improvementPlan.getRecommendedResources() != null) {
            List<InterviewResultResponseVo.LearningResource> resources = improvementPlan.getRecommendedResources().stream()
                .map(this::convertToLearningResourceVo)
                .collect(Collectors.toList());
            vo.setRecommendedResources(resources);
        }

        return vo;
    }

    // 数据操作辅助方法

    /**
     * 创建关联数据
     */
    private void createRelatedData(String resultId, InterviewResultData data) {
        try {
            // 创建维度评分
            if (data.getDimensionScores() != null && !data.getDimensionScores().isEmpty()) {
                for (DimensionScore score : data.getDimensionScores()) {
//                    score.setResultId(resultId);
//                    score.setCreateTime(LocalDateTime.now());
//                    score.setUpdateTime(LocalDateTime.now());
                }
                dimensionScoreMapper.batchInsert(data.getDimensionScores());
            }

            // 创建问题分析
            if (data.getQuestionAnalyses() != null && !data.getQuestionAnalyses().isEmpty()) {
                for (QuestionAnalysis analysis : data.getQuestionAnalyses()) {
                    analysis.setResultId(resultId);
                    analysis.setCreateTime(LocalDateTime.now());
                    analysis.setUpdateTime(LocalDateTime.now());
                }
                questionAnalysisMapper.batchInsert(data.getQuestionAnalyses());
            }

            // 创建音频指标
            if (data.getAudioMetrics() != null) {
                data.getAudioMetrics().setResultId(resultId);
                data.getAudioMetrics().setCreateTime(LocalDateTime.now());
                data.getAudioMetrics().setUpdateTime(LocalDateTime.now());
                audioMetricsMapper.insert(data.getAudioMetrics());
            }

            // 创建视频指标
            if (data.getVideoMetrics() != null) {
                data.getVideoMetrics().setResultId(resultId);
                data.getVideoMetrics().setCreateTime(LocalDateTime.now());
                data.getVideoMetrics().setUpdateTime(LocalDateTime.now());
                videoMetricsMapper.insert(data.getVideoMetrics());
            }

            // 创建性能指标
            if (data.getPerformanceMetrics() != null) {
                data.getPerformanceMetrics().setResultId(resultId);
                data.getPerformanceMetrics().setCreateTime(LocalDateTime.now());
                data.getPerformanceMetrics().setUpdateTime(LocalDateTime.now());
                performanceMetricsMapper.insert(data.getPerformanceMetrics());
            }

        } catch (Exception e) {
            log.error("创建关联数据失败，resultId: {}", resultId, e);
            throw new ServiceException("创建关联数据失败：" + e.getMessage());
        }
    }

    /**
     * 更新关联数据
     */
    private void updateRelatedData(String resultId, InterviewResultData data) {
        try {
            // 删除旧的关联数据
            deleteRelatedData(resultId);

            // 创建新的关联数据
            createRelatedData(resultId, data);

        } catch (Exception e) {
            log.error("更新关联数据失败，resultId: {}", resultId, e);
            throw new ServiceException("更新关联数据失败：" + e.getMessage());
        }
    }

    /**
     * 删除关联数据
     */
    private void deleteRelatedData(String resultId) {
        try {
            // 删除维度评分
            dimensionScoreMapper.deleteByResultId(resultId);

            // 删除问题分析
            questionAnalysisMapper.deleteByResultId(resultId);

            // 删除音频指标
            audioMetricsMapper.deleteByResultId(resultId);

            // 删除视频指标
            videoMetricsMapper.deleteByResultId(resultId);

            // 删除性能指标
            performanceMetricsMapper.deleteByResultId(resultId);

            // 删除提升计划
            improvementPlanMapper.deleteByResultId(resultId);

            // 删除分享记录
            resultShareMapper.deleteByResultId(resultId);

        } catch (Exception e) {
            log.error("删除关联数据失败，resultId: {}", resultId, e);
            throw new ServiceException("删除关联数据失败：" + e.getMessage());
        }
    }

    // 数据解析辅助方法

    private String getStringValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        return value != null ? value.toString() : null;
    }

    private Integer getIntegerValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) return null;
        if (value instanceof Integer) return (Integer) value;
        if (value instanceof Number) return ((Number) value).intValue();
        try {
            return Integer.parseInt(value.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    private Long getLongValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) return null;
        if (value instanceof Long) return (Long) value;
        if (value instanceof Number) return ((Number) value).longValue();
        try {
            return Long.parseLong(value.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    @SuppressWarnings("unchecked")
    private List<String> getListValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) return new ArrayList<>();
        if (value instanceof List) {
            return ((List<?>) value).stream()
                .map(Object::toString)
                .collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    // 数据解析方法

    private List<DimensionScore> parseDimensionScores(List<Map<String, Object>> scoresList) {
        if (scoresList == null) return new ArrayList<>();

        return scoresList.stream().map(scoreMap -> {
            DimensionScore score = new DimensionScore();
            score.setDimension(getStringValue(scoreMap, "dimension"));
            score.setScore(getIntegerValue(scoreMap, "score"));
            score.setMaxScore(getIntegerValue(scoreMap, "maxScore"));
            score.setPercentile(getIntegerValue(scoreMap, "percentile"));
            score.setDescription(getStringValue(scoreMap, "description"));
            score.setStrengths(getListValue(scoreMap, "strengths"));
            score.setWeaknesses(getListValue(scoreMap, "weaknesses"));
            score.setRecommendations(getListValue(scoreMap, "recommendations"));
            return score;
        }).collect(Collectors.toList());
    }

    private List<QuestionAnalysis> parseQuestionAnalyses(List<Map<String, Object>> analysesList) {
        if (analysesList == null) return new ArrayList<>();

        return analysesList.stream().map(analysisMap -> {
            QuestionAnalysis analysis = new QuestionAnalysis();
            analysis.setId(IdUtil.simpleUUID());
            analysis.setQuestionId(getStringValue(analysisMap, "questionId"));
            analysis.setQuestion(getStringValue(analysisMap, "question"));
            analysis.setCategory(getStringValue(analysisMap, "category"));
            analysis.setDifficulty(getIntegerValue(analysisMap, "difficulty"));
            analysis.setAnswer(getStringValue(analysisMap, "answer"));
            analysis.setScore(getIntegerValue(analysisMap, "score"));
            analysis.setAudioScore(getIntegerValue(analysisMap, "audioScore"));
            analysis.setVideoScore(getIntegerValue(analysisMap, "videoScore"));
            analysis.setTextScore(getIntegerValue(analysisMap, "textScore"));
            analysis.setFeedback(getStringValue(analysisMap, "feedback"));
            analysis.setStrengths(getListValue(analysisMap, "strengths"));
            analysis.setWeaknesses(getListValue(analysisMap, "weaknesses"));
            analysis.setKeywordMatches(getListValue(analysisMap, "keywordMatches"));
            analysis.setIdealAnswer(getStringValue(analysisMap, "idealAnswer"));
            analysis.setTimeSpent(getIntegerValue(analysisMap, "timeSpent"));
            analysis.setTimeLimit(getIntegerValue(analysisMap, "timeLimit"));
            return analysis;
        }).collect(Collectors.toList());
    }

    private AudioMetrics parseAudioMetrics(Map<String, Object> metricsMap) {
        if (metricsMap == null) return null;

        AudioMetrics metrics = new AudioMetrics();
        metrics.setClarity(getIntegerValue(metricsMap, "clarity"));
        metrics.setFluency(getIntegerValue(metricsMap, "fluency"));
        metrics.setConfidence(getIntegerValue(metricsMap, "confidence"));
        metrics.setPace(getIntegerValue(metricsMap, "pace"));
        metrics.setOverall(getIntegerValue(metricsMap, "overall"));
        return metrics;
    }

    private VideoMetrics parseVideoMetrics(Map<String, Object> metricsMap) {
        if (metricsMap == null) return null;

        VideoMetrics metrics = new VideoMetrics();
        metrics.setEyeContact(getIntegerValue(metricsMap, "eyeContact"));
        metrics.setPosture(getIntegerValue(metricsMap, "posture"));
        metrics.setExpressions(getIntegerValue(metricsMap, "expressions"));
        metrics.setGestures(getIntegerValue(metricsMap, "gestures"));
        metrics.setOverall(getIntegerValue(metricsMap, "overall"));
        return metrics;
    }

    @SuppressWarnings("unchecked")
    private PerformanceMetrics parsePerformanceMetrics(Map<String, Object> metricsMap) {
        if (metricsMap == null) return null;

        PerformanceMetrics metrics = new PerformanceMetrics();
        metrics.setTechnical(getIntegerValue(metricsMap, "technical"));
        metrics.setCommunication(getIntegerValue(metricsMap, "communication"));
        metrics.setProblemSolving(getIntegerValue(metricsMap, "problemSolving"));
        metrics.setTeamwork(getIntegerValue(metricsMap, "teamwork"));
        metrics.setLeadership(getIntegerValue(metricsMap, "leadership"));
        metrics.setCreativity(getIntegerValue(metricsMap, "creativity"));

        // 处理Map类型字段
        Object detailedMetrics = metricsMap.get("detailedMetrics");
        if (detailedMetrics instanceof Map) {
            metrics.setDetailedMetrics((Map<String, Integer>) detailedMetrics);
        }

        Object industryAverage = metricsMap.get("industryAverage");
        if (industryAverage instanceof Map) {
            metrics.setIndustryAverage((Map<String, Integer>) industryAverage);
        }

        metrics.setSkillGaps(getListValue(metricsMap, "skillGaps"));
        metrics.setSkillStrengths(getListValue(metricsMap, "skillStrengths"));

        return metrics;
    }

    // 统计计算辅助方法

    private void calculateMonthlyImprovement(UserStatistics statistics, List<InterviewResult> recentResults) {
        if (recentResults.size() < 2) {
            statistics.setMonthlyImprovement(0.0);
            statistics.setImprovementPercent(0.0);
            return;
        }

        // 计算最近一次和之前的平均分差异
        int latestScore = recentResults.get(0).getTotalScore();
        double previousAverage = recentResults.subList(1, recentResults.size()).stream()
            .mapToInt(InterviewResult::getTotalScore)
            .average()
            .orElse(0.0);

        double improvement = latestScore - previousAverage;
        statistics.setMonthlyImprovement(improvement);

        if (previousAverage > 0) {
            double improvementPercent = (improvement / previousAverage) * 100;
            statistics.setImprovementPercent(improvementPercent);
        } else {
            statistics.setImprovementPercent(0.0);
        }
    }

    private void calculateCurrentLevelAndProgress(UserStatistics statistics) {
        double averageScore = statistics.getAverageScore();

        if (averageScore >= 90) {
            statistics.setCurrentLevel("专家");
            statistics.setNextLevelProgress(100);
        } else if (averageScore >= 80) {
            statistics.setCurrentLevel("高级");
            statistics.setNextLevelProgress((int) ((averageScore - 80) / 10 * 100));
        } else if (averageScore >= 70) {
            statistics.setCurrentLevel("中级");
            statistics.setNextLevelProgress((int) ((averageScore - 70) / 10 * 100));
        } else if (averageScore >= 60) {
            statistics.setCurrentLevel("初级");
            statistics.setNextLevelProgress((int) ((averageScore - 60) / 10 * 100));
        } else {
            statistics.setCurrentLevel("入门");
            statistics.setNextLevelProgress((int) (averageScore / 60 * 100));
        }
    }

    private ImprovementPlan.ImprovementArea createImprovementArea(String name, Integer currentLevel,
                                                                  Integer targetLevel, String timeframe,
                                                                  List<String> actions) {
        ImprovementPlan.ImprovementArea area = new ImprovementPlan.ImprovementArea();
        area.setName(name);
        area.setCurrentLevel(currentLevel);
        area.setTargetLevel(targetLevel);
        area.setTimeframe(timeframe);
        area.setActions(actions);
        return area;
    }

    // 报告生成辅助方法

    private InterviewReportData.DimensionScore convertToReportDimensionScore(
            InterviewResultResponseVo.DimensionScore dimensionScore) {
        InterviewReportData.DimensionScore reportScore = new InterviewReportData.DimensionScore();
        reportScore.setDimension(dimensionScore.getDimension());
        reportScore.setScore(dimensionScore.getScore());
        reportScore.setMaxScore(dimensionScore.getMaxScore());
        reportScore.setPercentile(dimensionScore.getPercentile());
        reportScore.setDescription(dimensionScore.getDescription());
        reportScore.setStrengths(dimensionScore.getStrengths());
        reportScore.setWeaknesses(dimensionScore.getWeaknesses());
        reportScore.setRecommendations(dimensionScore.getRecommendations());
        return reportScore;
    }

    private InterviewReportData.RadarChartData generateRadarChartData(
            InterviewResultResponseVo.InterviewResultDetail detail) {
        InterviewReportData.RadarChartData radarData = new InterviewReportData.RadarChartData();

        List<String> dimensions = new ArrayList<>();
        List<Integer> scores = new ArrayList<>();
        List<Integer> maxScores = new ArrayList<>();
        List<Integer> industryAverages = new ArrayList<>();

        if (detail.getDimensionScores() != null) {
            for (InterviewResultResponseVo.DimensionScore score : detail.getDimensionScores()) {
                dimensions.add(score.getDimension());
                scores.add(score.getScore());
                maxScores.add(score.getMaxScore());
                // 设置行业平均分（这里使用模拟数据）
                industryAverages.add((int) (score.getMaxScore() * 0.7));
            }
        }

        radarData.setDimensions(dimensions);
        radarData.setScores(scores);
        radarData.setMaxScores(maxScores);
        radarData.setIndustryAverages(industryAverages);

        return radarData;
    }

    private List<InterviewReportData.ImprovementSuggestion> generateImprovementSuggestions(
            InterviewResultResponseVo.InterviewResultDetail detail) {
        List<InterviewReportData.ImprovementSuggestion> suggestions = new ArrayList<>();

        // 基于薄弱环节生成建议
        if (detail.getTopWeaknesses() != null) {
            for (String weakness : detail.getTopWeaknesses()) {
                InterviewReportData.ImprovementSuggestion suggestion =
                    new InterviewReportData.ImprovementSuggestion();
                suggestion.setTitle("改进" + weakness);
                suggestion.setPriority("高");
                suggestion.setDescription("针对" + weakness + "进行专项提升");
                suggestion.setActionItems(List.of("制定学习计划", "寻找相关资源", "定期练习评估"));
                suggestion.setEstimatedTime(30); // 30天
                suggestion.setDifficulty("中等");
                suggestions.add(suggestion);
            }
        }

        return suggestions;
    }

    private List<InterviewReportData.LearningPathRecommendation> generateLearningPathRecommendations(
            InterviewResultResponseVo.InterviewResultDetail detail) {
        List<InterviewReportData.LearningPathRecommendation> paths = new ArrayList<>();

        // 基于职位和薄弱环节推荐学习路径
        InterviewReportData.LearningPathRecommendation path =
            new InterviewReportData.LearningPathRecommendation();
        path.setTitle("综合能力提升路径");
        path.setDescription("基于面试结果定制的个性化学习路径");
        path.setEstimatedHours(120); // 120小时
        path.setDifficulty("中等");
        path.setPriority(1);

        // 设置里程碑
        List<String> milestones = new ArrayList<>();
        milestones.add("完成基础知识学习");
        milestones.add("完成实践项目");
        milestones.add("通过技能评估");
        path.setMilestones(milestones);

        // 设置学习资源（这里使用空列表，实际应该从数据库获取）
        path.setResources(new ArrayList<>());

        paths.add(path);

        return paths;
    }

    // 内部类定义

    /**
     * 面试结果数据传输对象
     */
    @Data
    public static class InterviewResultData {
        private Long jobId;
        private String jobName;
        private String company;
        private String mode;
        private String duration;
        private Integer totalScore;
        private String rank;
        private String rankText;
        private Integer percentile;
        private Integer answeredQuestions;
        private Integer totalQuestions;
        private String status;
        private List<String> topStrengths;
        private List<String> topWeaknesses;
        private String overallFeedback;

        // 关联数据
        private List<DimensionScore> dimensionScores;
        private List<QuestionAnalysis> questionAnalyses;
        private AudioMetrics audioMetrics;
        private VideoMetrics videoMetrics;
        private PerformanceMetrics performanceMetrics;
    }

    /**
     * 用户统计数据
     */
    @Data
    public static class UserStatistics {
        private Integer totalInterviews;
        private Double averageScore;
        private Double monthlyImprovement;
        private Double improvementPercent;
        private String currentLevel;
        private Integer nextLevelProgress;
        private Integer highestScore;
        private String lastInterviewDate;
    }

}
