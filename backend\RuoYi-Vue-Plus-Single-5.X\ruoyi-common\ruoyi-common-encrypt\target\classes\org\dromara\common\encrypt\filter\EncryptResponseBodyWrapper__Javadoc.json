{"doc": "\n 加密响应参数包装类\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "getEncryptContent", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "java.lang.String", "java.lang.String"], "doc": "\n 获取加密内容\r\n\r\n @param servletResponse response\r\n @param publicKey       RSA公钥 (用于加密 AES 秘钥)\r\n @param headerFlag      请求头标志\r\n @return 加密内容\r\n"}], "constructors": []}