package org.dromara.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.dromara.app.domain.Job;
import org.dromara.app.domain.bo.JobQueryBo;
import org.dromara.app.domain.vo.JobVo;

import java.util.List;

/**
 * 岗位信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Mapper
public interface JobMapper extends BaseMapper<Job> {

    /**
     * 分页查询岗位列表（包含收藏状态）
     *
     * @param page    分页参数
     * @param queryBo 查询条件
     * @return 岗位列表
     */
    Page<JobVo> selectJobPageWithFavorite(Page<JobVo> page, @Param("query") JobQueryBo queryBo);

    /**
     * 查询推荐岗位
     *
     * @param limit  限制数量
     * @param userId 用户ID
     * @return 推荐岗位列表
     */
    List<JobVo> selectRecommendedJobs(@Param("limit") Integer limit, @Param("userId") Long userId);

    /**
     * 查询热门岗位
     *
     * @param limit  限制数量
     * @param userId 用户ID
     * @return 热门岗位列表
     */
    List<JobVo> selectHotJobs(@Param("limit") Integer limit, @Param("userId") Long userId);

    /**
     * 增加浏览次数
     *
     * @param jobId 岗位ID
     * @return 影响行数
     */
    @Update("UPDATE app_job SET view_count = view_count + 1 WHERE id = #{jobId}")
    int incrementViewCount(@Param("jobId") Long jobId);

    /**
     * 更新收藏次数
     *
     * @param jobId     岗位ID
     * @param increment 增量（1或-1）
     * @return 影响行数
     */
    @Update("UPDATE app_job SET favorite_count = favorite_count + #{increment} WHERE id = #{jobId}")
    int updateFavoriteCount(@Param("jobId") Long jobId, @Param("increment") Integer increment);

    /**
     * 查询岗位统计信息
     *
     * @return 统计信息
     */
    @Select({
        "SELECT ",
        "COUNT(*) as total_jobs, ",
        "SUM(interviewers) as total_interviews, ",
        "AVG(pass_rate) as avg_pass_rate ",
        "FROM app_job ",
        "WHERE del_flag = '0' AND status = '0'"
    })
    JobStatistics selectJobStatistics();

    /**
     * 岗位统计信息内部类
     */
    class JobStatistics {
        private Integer totalJobs;
        private Integer totalInterviews;
        private Double avgPassRate;

        // getters and setters
        public Integer getTotalJobs() {
            return totalJobs;
        }

        public void setTotalJobs(Integer totalJobs) {
            this.totalJobs = totalJobs;
        }

        public Integer getTotalInterviews() {
            return totalInterviews;
        }

        public void setTotalInterviews(Integer totalInterviews) {
            this.totalInterviews = totalInterviews;
        }

        public Double getAvgPassRate() {
            return avgPassRate;
        }

        public void setAvgPassRate(Double avgPassRate) {
            this.avgPassRate = avgPassRate;
        }
    }

}
