package org.dromara.app.service.tool.impl;

import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.ToolCall;
import org.dromara.app.service.tool.ToolExecutor;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 获取当前时间工具
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class CurrentTimeToolExecutor implements ToolExecutor {

    @Override
    public String getToolName() {
        return "current_time";
    }

    @Override
    public String getDisplayName() {
        return "获取当前时间";
    }

    @Override
    public String getDescription() {
        return "获取当前的日期和时间信息";
    }

    @Override
    public String getCategory() {
        return "system";
    }

    @Override
    public Map<String, Object> getParameterSchema() {
        Map<String, Object> schema = new HashMap<>();
        schema.put("type", "object");

        Map<String, Object> properties = new HashMap<>();

        // format参数
        Map<String, Object> formatParam = new HashMap<>();
        formatParam.put("type", "string");
        formatParam.put("description", "时间格式，可选值：datetime, date, time");
        formatParam.put("enum", new String[]{"datetime", "date", "time"});
        formatParam.put("default", "datetime");
        properties.put("format", formatParam);

        // timezone参数
        Map<String, Object> timezoneParam = new HashMap<>();
        timezoneParam.put("type", "string");
        timezoneParam.put("description", "时区，默认为系统时区");
        timezoneParam.put("default", "Asia/Shanghai");
        properties.put("timezone", timezoneParam);

        schema.put("properties", properties);
        schema.put("required", new String[]{});

        return schema;
    }

    @Override
    public ValidationResult validateParameters(Map<String, Object> parameters) {
        if (parameters == null) {
            return ValidationResult.success();
        }

        String format = (String) parameters.get("format");
        if (format != null && !isValidFormat(format)) {
            return ValidationResult.failure("无效的时间格式: " + format);
        }

        return ValidationResult.success();
    }

    @Override
    public ToolCall.ToolCallResult execute(Map<String, Object> parameters, ExecutionContext context) {
        try {
            String format = parameters != null ? (String) parameters.get("format") : "datetime";
            if (format == null) {
                format = "datetime";
            }

            LocalDateTime now = LocalDateTime.now();
            String timeString = formatTime(now, format);

            Map<String, Object> result = new HashMap<>();
            result.put("current_time", timeString);
            result.put("timestamp", System.currentTimeMillis());
            result.put("format", format);

            Map<String, Object> metadata = new HashMap<>();
            metadata.put("tool_name", getToolName());
            metadata.put("execution_time", System.currentTimeMillis());

            return ToolCall.ToolCallResult.builder()
                .success(true)
                .data(result)
                .message("获取当前时间成功")
                .type("json")
                .metadata(metadata)
                .build();

        } catch (Exception e) {
            log.error("获取当前时间失败", e);
            return ToolCall.ToolCallResult.builder()
                .success(false)
                .message("获取当前时间失败: " + e.getMessage())
                .type("error")
                .build();
        }
    }

    @Override
    public int getTimeoutSeconds() {
        return 5;
    }

    private boolean isValidFormat(String format) {
        return "datetime".equals(format) || "date".equals(format) || "time".equals(format);
    }

    private String formatTime(LocalDateTime dateTime, String format) {
        return switch (format) {
            case "date" -> dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            case "time" -> dateTime.format(DateTimeFormatter.ofPattern("HH:mm:ss"));
            default -> dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        };
    }
}
