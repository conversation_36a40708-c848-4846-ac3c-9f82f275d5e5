package org.dromara.app.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.util.List;
import java.util.Map;

/**
 * 面试报告实体
 *
 * <AUTHOR>
 */
@Data
@TableName(value = "app_interview_report", autoResultMap = true)
public class InterviewReport extends BaseEntity {

    /**
     * 报告ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 面试会话ID
     */
    private String sessionId;

    /**
     * 报告标题
     */
    private String title;

    /**
     * 总分
     */
    private Integer totalScore;

    /**
     * 等级
     */
    private String level;

    /**
     * 百分位
     */
    private Integer percentile;

    /**
     * 岗位信息
     */
    private String jobPosition;

    /**
     * 报告状态：draft-草稿, published-已发布, archived-已归档
     */
    private String status;

    /**
     * 报告PDF文件URL
     */
    private String pdfUrl;

    /**
     * 维度评分
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<DimensionScore> dimensionScores;

    /**
     * 优势列表
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> strengths;

    /**
     * 劣势列表
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> weaknesses;

    /**
     * 改进建议
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<ImprovementSuggestion> improvementSuggestions;

    /**
     * 学习路径推荐
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<LearningPathRecommendation> learningPaths;

    /**
     * 总体反馈
     */
    private String overallFeedback;

    /**
     * 雷达图数据
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private RadarChartData radarChartData;

    /**
     * 报告元数据
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> metadata;

    /**
     * 改进建议
     */
    @Data
    public static class ImprovementSuggestion {
        /**
         * 标题
         */
        private String title;

        /**
         * 描述
         */
        private String description;

        /**
         * 行动项
         */
        private List<String> actionItems;

        /**
         * 优先级
         */
        private String priority;

        /**
         * 预计时间（天）
         */
        private Integer estimatedTime;

        /**
         * 难度
         */
        private String difficulty;
    }

    /**
     * 学习路径推荐
     */
    @Data
    public static class LearningPathRecommendation {
        /**
         * 标题
         */
        private String title;

        /**
         * 描述
         */
        private String description;

        /**
         * 预计学习时间（小时）
         */
        private Integer estimatedHours;

        /**
         * 难度
         */
        private String difficulty;

        /**
         * 资源列表
         */
        private List<LearningResource> resources;

        /**
         * 里程碑
         */
        private List<String> milestones;

        /**
         * 优先级
         */
        private Integer priority;
    }

    /**
     * 学习资源
     */
    @Data
    public static class LearningResource {
        /**
         * 标题
         */
        private String title;

        /**
         * 类型：video, article, course, practice
         */
        private String type;

        /**
         * 描述
         */
        private String description;

        /**
         * 链接
         */
        private String url;

        /**
         * 时长
         */
        private String duration;

        /**
         * 难度
         */
        private String difficulty;
    }

    /**
     * 雷达图数据
     */
    @Data
    public static class RadarChartData {
        /**
         * 维度列表
         */
        private List<String> dimensions;

        /**
         * 分数列表
         */
        private List<Integer> scores;

        /**
         * 最大分数列表
         */
        private List<Integer> maxScores;

        /**
         * 行业平均分
         */
        private List<Integer> industryAverages;

        /**
         * 图表配置
         */
        private Map<String, Object> chartConfig;
    }
}