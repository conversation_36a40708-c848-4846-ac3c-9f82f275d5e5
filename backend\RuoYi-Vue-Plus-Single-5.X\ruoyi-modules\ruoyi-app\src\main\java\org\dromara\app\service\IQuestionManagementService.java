package org.dromara.app.service;

import org.dromara.app.domain.InterviewQuestion;

import java.util.List;
import java.util.Map;

/**
 * 问题管理增强Service接口
 *
 * <AUTHOR> Assistant
 * @date 2025-07-19
 */
public interface IQuestionManagementService {

    /**
     * 智能推荐面试问题
     *
     * @param jobId           岗位ID
     * @param technicalDomain 技术领域
     * @param difficulty      目标难度
     * @param questionCount   问题数量
     * @param includeMultimodal 是否包含多模态问题
     * @return 推荐问题列表
     */
    List<InterviewQuestion> recommendQuestions(Long jobId, String technicalDomain, 
                                             Integer difficulty, Integer questionCount, 
                                             Boolean includeMultimodal);

    /**
     * 根据用户技能水平推荐问题
     *
     * @param userId        用户ID
     * @param jobId         岗位ID
     * @param questionCount 问题数量
     * @return 个性化推荐问题
     */
    List<InterviewQuestion> recommendQuestionsForUser(Long userId, Long jobId, Integer questionCount);

    /**
     * 获取问题难度分级系统
     *
     * @param technicalDomain 技术领域
     * @return 难度分级信息
     */
    DifficultyGradingSystem getDifficultyGradingSystem(String technicalDomain);

    /**
     * 分析问题覆盖度
     *
     * @param jobId 岗位ID
     * @return 覆盖度分析结果
     */
    QuestionCoverageAnalysis analyzeQuestionCoverage(Long jobId);

    /**
     * 获取多模态评估配置
     *
     * @param questionId 问题ID
     * @return 多模态评估配置
     */
    MultimodalEvaluationConfig getMultimodalConfig(Long questionId);

    /**
     * 批量更新问题标签
     *
     * @param questionIds 问题ID列表
     * @param tags        标签列表
     * @return 更新结果
     */
    boolean batchUpdateQuestionTags(List<Long> questionIds, List<String> tags);

    /**
     * 难度分级系统
     */
    class DifficultyGradingSystem {
        private String technicalDomain;
        private Map<Integer, DifficultyLevel> levels;
        private String description;

        // getters and setters
        public String getTechnicalDomain() {
            return technicalDomain;
        }

        public void setTechnicalDomain(String technicalDomain) {
            this.technicalDomain = technicalDomain;
        }

        public Map<Integer, DifficultyLevel> getLevels() {
            return levels;
        }

        public void setLevels(Map<Integer, DifficultyLevel> levels) {
            this.levels = levels;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }
    }

    /**
     * 难度级别定义
     */
    class DifficultyLevel {
        private Integer level;
        private String name;
        private String description;
        private List<String> characteristics;
        private Integer timeLimit;
        private Double passRate;

        // getters and setters
        public Integer getLevel() {
            return level;
        }

        public void setLevel(Integer level) {
            this.level = level;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public List<String> getCharacteristics() {
            return characteristics;
        }

        public void setCharacteristics(List<String> characteristics) {
            this.characteristics = characteristics;
        }

        public Integer getTimeLimit() {
            return timeLimit;
        }

        public void setTimeLimit(Integer timeLimit) {
            this.timeLimit = timeLimit;
        }

        public Double getPassRate() {
            return passRate;
        }

        public void setPassRate(Double passRate) {
            this.passRate = passRate;
        }
    }

    /**
     * 问题覆盖度分析
     */
    class QuestionCoverageAnalysis {
        private Long jobId;
        private String jobName;
        private Integer totalQuestions;
        private Map<String, Integer> categoryDistribution;
        private Map<Integer, Integer> difficultyDistribution;
        private Map<String, Integer> typeDistribution;
        private List<String> missingAreas;
        private Double coverageScore;

        // getters and setters
        public Long getJobId() {
            return jobId;
        }

        public void setJobId(Long jobId) {
            this.jobId = jobId;
        }

        public String getJobName() {
            return jobName;
        }

        public void setJobName(String jobName) {
            this.jobName = jobName;
        }

        public Integer getTotalQuestions() {
            return totalQuestions;
        }

        public void setTotalQuestions(Integer totalQuestions) {
            this.totalQuestions = totalQuestions;
        }

        public Map<String, Integer> getCategoryDistribution() {
            return categoryDistribution;
        }

        public void setCategoryDistribution(Map<String, Integer> categoryDistribution) {
            this.categoryDistribution = categoryDistribution;
        }

        public Map<Integer, Integer> getDifficultyDistribution() {
            return difficultyDistribution;
        }

        public void setDifficultyDistribution(Map<Integer, Integer> difficultyDistribution) {
            this.difficultyDistribution = difficultyDistribution;
        }

        public Map<String, Integer> getTypeDistribution() {
            return typeDistribution;
        }

        public void setTypeDistribution(Map<String, Integer> typeDistribution) {
            this.typeDistribution = typeDistribution;
        }

        public List<String> getMissingAreas() {
            return missingAreas;
        }

        public void setMissingAreas(List<String> missingAreas) {
            this.missingAreas = missingAreas;
        }

        public Double getCoverageScore() {
            return coverageScore;
        }

        public void setCoverageScore(Double coverageScore) {
            this.coverageScore = coverageScore;
        }
    }

    /**
     * 多模态评估配置
     */
    class MultimodalEvaluationConfig {
        private Long questionId;
        private Boolean audioRequired;
        private Boolean videoRequired;
        private Boolean textRequired;
        private List<String> requiredElements;
        private Map<String, Integer> evaluationWeights;
        private Integer timeLimit;
        private String instructions;

        // getters and setters
        public Long getQuestionId() {
            return questionId;
        }

        public void setQuestionId(Long questionId) {
            this.questionId = questionId;
        }

        public Boolean getAudioRequired() {
            return audioRequired;
        }

        public void setAudioRequired(Boolean audioRequired) {
            this.audioRequired = audioRequired;
        }

        public Boolean getVideoRequired() {
            return videoRequired;
        }

        public void setVideoRequired(Boolean videoRequired) {
            this.videoRequired = videoRequired;
        }

        public Boolean getTextRequired() {
            return textRequired;
        }

        public void setTextRequired(Boolean textRequired) {
            this.textRequired = textRequired;
        }

        public List<String> getRequiredElements() {
            return requiredElements;
        }

        public void setRequiredElements(List<String> requiredElements) {
            this.requiredElements = requiredElements;
        }

        public Map<String, Integer> getEvaluationWeights() {
            return evaluationWeights;
        }

        public void setEvaluationWeights(Map<String, Integer> evaluationWeights) {
            this.evaluationWeights = evaluationWeights;
        }

        public Integer getTimeLimit() {
            return timeLimit;
        }

        public void setTimeLimit(Integer timeLimit) {
            this.timeLimit = timeLimit;
        }

        public String getInstructions() {
            return instructions;
        }

        public void setInstructions(String instructions) {
            this.instructions = instructions;
        }
    }

}