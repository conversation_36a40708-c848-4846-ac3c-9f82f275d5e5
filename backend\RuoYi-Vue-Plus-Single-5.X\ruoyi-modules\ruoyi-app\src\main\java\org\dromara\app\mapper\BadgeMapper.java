package org.dromara.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.dromara.app.domain.Badge;
import org.dromara.app.domain.vo.BadgeVo;

import java.util.List;

/**
 * 徽章数据层
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
public interface BadgeMapper extends BaseMapper<Badge> {

    /**
     * 获取总徽章数
     *
     * @return 总徽章数
     */
    int countTotalBadges();

    /**
     * 根据类别获取徽章数
     *
     * @param category 类别
     * @return 该类别的徽章数
     */
    int countBadgesByCategory(@Param("category") String category);

    /**
     * 根据稀有度获取徽章数
     *
     * @param rarity 稀有度
     * @return 该稀有度的徽章数
     */
    int countBadgesByRarity(@Param("rarity") String rarity);

    /**
     * 根据成就ID查询徽章
     *
     * @param achievementId 成就ID
     * @return 相关徽章列表
     */
    List<Badge> selectByAchievementId(@Param("achievementId") String achievementId);

    /**
     * 根据多个ID批量查询徽章
     *
     * @param ids 徽章ID列表
     * @return 徽章列表
     */
    List<Badge> selectByIds(@Param("ids") List<String> ids);

    /**
     * 获取所有推荐的徽章
     *
     * @return 推荐徽章列表
     */
    List<Badge> selectRecommendedBadges();

    /**
     * 获取指定类别的徽章
     *
     * @param category 类别
     * @return 徽章列表
     */
    List<Badge> selectByCategory(@Param("category") String category);

    /**
     * 获取指定稀有度的徽章
     *
     * @param rarity 稀有度
     * @return 徽章列表
     */
    List<Badge> selectByRarity(@Param("rarity") String rarity);

    /**
     * 获取特殊徽章（限时、活动等）
     *
     * @param specialFlag 特殊标志
     * @return 特殊徽章列表
     */
    List<Badge> selectBySpecialFlag(@Param("specialFlag") String specialFlag);

    /**
     * 根据标签查询徽章
     *
     * @param tag 标签
     * @return 徽章列表
     */
    List<Badge> selectByTag(@Param("tag") String tag);

    /**
     * 获取所有启用的徽章
     *
     * @return 启用的徽章列表
     */
    List<Badge> selectAllEnabled();

    /**
     * 根据解锁条件查询徽章
     *
     * @param unlockCriteria 解锁条件关键词
     * @return 徽章列表
     */
    List<Badge> selectByUnlockCriteria(@Param("unlockCriteria") String unlockCriteria);
}
