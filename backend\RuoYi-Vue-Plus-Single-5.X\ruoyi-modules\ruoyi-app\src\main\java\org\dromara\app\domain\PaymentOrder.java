package org.dromara.app.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 支付订单实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_payment_order")
public class PaymentOrder extends BaseEntity {

    /**
     * 订单ID
     */
    @TableId(value = "order_id", type = IdType.AUTO)
    private Long orderId;

    /**
     * 订单号
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * 商品ID
     */
    @TableField("product_id")
    private Long productId;

    /**
     * 商品类型
     */
    @TableField("product_type")
    private String productType;

    /**
     * 商品标题
     */
    @TableField("product_title")
    private String productTitle;

    /**
     * 支付金额
     */
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 支付方式
     */
    @TableField("payment_method")
    private String paymentMethod;

    /**
     * 订单状态：pending-待支付，paid-已支付，cancelled-已取消，expired-已过期
     */
    @TableField("status")
    private String status;

    /**
     * 支付宝交易号
     */
    @TableField("alipay_trade_no")
    private String alipayTradeNo;

    /**
     * 支付时间
     */
    @TableField("pay_time")
    private LocalDateTime payTime;

    /**
     * 过期时间
     */
    @TableField("expire_time")
    private LocalDateTime expireTime;

    /**
     * 客户端IP
     */
    @TableField("client_ip")
    private String clientIp;

    /**
     * 用户代理
     */
    @TableField("user_agent")
    private String userAgent;

    /**
     * 回调通知次数
     */
    @TableField("notify_count")
    private Integer notifyCount;

    /**
     * 最后通知时间
     */
    @TableField("last_notify_time")
    private LocalDateTime lastNotifyTime;

    /**
     * 通知结果
     */
    @TableField("notify_result")
    private String notifyResult;

    /**
     * 备注信息
     */
    @TableField("remark")
    private String remark;

    /**
     * 支付token
     */
    @TableField("pay_token")
    private String payToken;

    /**
     * 支付token过期时间
     */
    @TableField("pay_token_expire_time")
    private LocalDateTime payTokenExpireTime;

    /**
     * 支付token是否已使用
     */
    @TableField("pay_token_used")
    private Boolean payTokenUsed;
}
