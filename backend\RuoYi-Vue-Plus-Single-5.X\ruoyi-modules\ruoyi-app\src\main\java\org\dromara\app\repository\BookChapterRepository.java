package org.dromara.app.repository;

import org.dromara.app.domain.BookChapter;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 书籍章节Repository接口
 *
 * <AUTHOR>
 */
@Repository
public interface BookChapterRepository extends MongoRepository<BookChapter, String> {

    /**
     * 根据书籍ID查询章节列表
     *
     * @param bookId 书籍ID
     * @return 章节列表（按顺序排序）
     */
    @Query("{'book_id': ?0, 'status': true}")
    List<BookChapter> findByBookIdAndStatusTrueOrderByChapterOrderAsc(Long bookId);

    /**
     * 根据书籍ID和章节序号查询章节
     *
     * @param bookId       书籍ID
     * @param chapterOrder 章节序号
     * @return 章节信息
     */
    BookChapter findByBookIdAndChapterOrder(Long bookId, Integer chapterOrder);

    /**
     * 根据书籍ID查询试读章节列表
     *
     * @param bookId 书籍ID
     * @return 试读章节列表
     */
    @Query("{'book_id': ?0, 'is_preview': true, 'status': true}")
    List<BookChapter> findPreviewChaptersByBookId(Long bookId);

    /**
     * 根据书籍ID查询已解锁章节列表
     *
     * @param bookId 书籍ID
     * @return 已解锁章节列表
     */
    @Query("{'book_id': ?0, 'is_unlocked': true, 'status': true}")
    List<BookChapter> findUnlockedChaptersByBookId(Long bookId);

    /**
     * 根据书籍ID统计章节数量
     *
     * @param bookId 书籍ID
     * @return 章节数量
     */
    @Query(value = "{'book_id': ?0, 'status': true}", count = true)
    long countByBookIdAndStatusTrue(Long bookId);

    /**
     * 根据书籍ID删除所有章节
     *
     * @param bookId 书籍ID
     */
    void deleteByBookId(Long bookId);

    /**
     * 根据书籍ID查询最大章节序号
     *
     * @param bookId 书籍ID
     * @return 最大章节序号
     */
    @Query(value = "{'book_id': ?0}", fields = "{'chapter_order': 1}", sort = "{'chapter_order': -1}")
    BookChapter findMaxChapterOrderByBookId(Long bookId);
}
