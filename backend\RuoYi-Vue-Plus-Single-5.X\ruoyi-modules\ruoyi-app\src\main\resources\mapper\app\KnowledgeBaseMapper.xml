<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.app.mapper.KnowledgeBaseMapper">

    <!-- 批量更新知识库状态 -->
    <update id="updateStatusByIds">
        UPDATE app_knowledge_base
        SET status = #{status}, update_time = CURRENT_TIMESTAMP
        WHERE id IN
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        AND del_flag = '0'
    </update>

</mapper>
