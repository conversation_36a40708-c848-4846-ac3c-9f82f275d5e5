{"doc": "\n 增强网络相关工具类\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "isIPv6", "paramTypes": ["java.lang.String"], "doc": "\n 判断是否为IPv6地址\r\n\r\n @param ip IP地址\r\n @return 是否为IPv6地址\r\n"}, {"name": "isInnerIPv6", "paramTypes": ["java.lang.String"], "doc": "\n 判断IPv6地址是否为内网地址\r\n <br><br>\r\n 以下地址将归类为本地地址，如有业务场景有需要，请根据需求自行处理：\r\n <pre>\r\n 通配符地址 0:0:0:0:0:0:0:0\r\n 链路本地地址 fe80::/10\r\n 唯一本地地址 fec0::/10\r\n 环回地址 ::1\r\n </pre>\r\n\r\n @param ip IP地址\r\n @return 是否为内网地址\r\n"}, {"name": "isIPv4", "paramTypes": ["java.lang.String"], "doc": "\n 判断是否为IPv4地址\r\n\r\n @param ip IP地址\r\n @return 是否为IPv4地址\r\n"}], "constructors": []}