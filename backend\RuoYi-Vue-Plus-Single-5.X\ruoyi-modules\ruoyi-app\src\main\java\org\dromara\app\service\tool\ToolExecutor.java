package org.dromara.app.service.tool;

import org.dromara.app.domain.ToolCall;

import java.util.Map;

/**
 * 工具执行器接口
 * 所有工具实现都需要实现此接口
 *
 * <AUTHOR>
 */
public interface ToolExecutor {

    /**
     * 获取工具名称
     *
     * @return 工具名称
     */
    String getToolName();

    /**
     * 获取工具显示名称
     *
     * @return 显示名称
     */
    String getDisplayName();

    /**
     * 获取工具描述
     *
     * @return 工具描述
     */
    String getDescription();

    /**
     * 获取工具分类
     *
     * @return 工具分类
     */
    String getCategory();

    /**
     * 获取参数Schema
     *
     * @return 参数Schema
     */
    Map<String, Object> getParameterSchema();

    /**
     * 验证参数
     *
     * @param parameters 参数
     * @return 验证结果
     */
    ValidationResult validateParameters(Map<String, Object> parameters);

    /**
     * 执行工具
     *
     * @param parameters 参数
     * @param context    执行上下文
     * @return 执行结果
     */
    ToolCall.ToolCallResult execute(Map<String, Object> parameters, ExecutionContext context);

    /**
     * 是否支持异步执行
     *
     * @return 是否支持异步
     */
    default boolean supportsAsync() {
        return false;
    }

    /**
     * 获取执行超时时间（秒）
     *
     * @return 超时时间
     */
    default int getTimeoutSeconds() {
        return 30;
    }

    /**
     * 获取所需权限
     *
     * @return 权限列表
     */
    default String[] getRequiredPermissions() {
        return new String[0];
    }

    /**
     * 验证结果
     */
    class ValidationResult {
        private final boolean valid;
        private final String errorMessage;
        private final Map<String, String> fieldErrors;

        public ValidationResult(boolean valid, String errorMessage, Map<String, String> fieldErrors) {
            this.valid = valid;
            this.errorMessage = errorMessage;
            this.fieldErrors = fieldErrors;
        }

        public static ValidationResult success() {
            return new ValidationResult(true, null, null);
        }

        public static ValidationResult failure(String errorMessage) {
            return new ValidationResult(false, errorMessage, null);
        }

        public static ValidationResult failure(String errorMessage, Map<String, String> fieldErrors) {
            return new ValidationResult(false, errorMessage, fieldErrors);
        }

        public boolean isValid() {
            return valid;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public Map<String, String> getFieldErrors() {
            return fieldErrors;
        }
    }

    /**
     * 执行上下文
     */
    class ExecutionContext {
        private final Long userId;
        private final String sessionId;
        private final String messageId;
        private final Map<String, Object> metadata;

        public ExecutionContext(Long userId, String sessionId, String messageId, Map<String, Object> metadata) {
            this.userId = userId;
            this.sessionId = sessionId;
            this.messageId = messageId;
            this.metadata = metadata;
        }

        public Long getUserId() {
            return userId;
        }

        public String getSessionId() {
            return sessionId;
        }

        public String getMessageId() {
            return messageId;
        }

        public Map<String, Object> getMetadata() {
            return metadata;
        }

        public Object getMetadata(String key) {
            return metadata != null ? metadata.get(key) : null;
        }
    }
}
