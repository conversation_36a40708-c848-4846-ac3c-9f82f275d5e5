{"doc": "\n RabbitMQ 工具类\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "createExchange", "paramTypes": ["java.lang.String", "java.lang.String", "boolean", "boolean"], "doc": "\n 创建交换机\r\n\r\n @param exchangeName 交换机名称\r\n @param exchangeType 交换机类型\r\n @param durable      是否持久化\r\n @param autoDelete   是否自动删除\r\n"}, {"name": "createQueue", "paramTypes": ["java.lang.String", "boolean", "boolean", "boolean"], "doc": "\n 创建队列\r\n\r\n @param queueName  队列名称\r\n @param durable    是否持久化\r\n @param exclusive  是否独占\r\n @param autoDelete 是否自动删除\r\n @return Queue对象\r\n"}, {"name": "createQueue", "paramTypes": ["java.lang.String", "boolean", "boolean", "boolean", "java.util.Map"], "doc": "\n 创建队列（带参数）\r\n\r\n @param queueName  队列名称\r\n @param durable    是否持久化\r\n @param exclusive  是否独占\r\n @param autoDelete 是否自动删除\r\n @param args       队列参数\r\n @return Queue对象\r\n"}, {"name": "createDeadLetterQueue", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String", "long"], "doc": "\n 创建死信队列\r\n\r\n @param queueName      队列名称\r\n @param deadExchange   死信交换机\r\n @param deadRoutingKey 死信路由键\r\n @param messageTtl     消息过期时间（毫秒）\r\n @return Queue对象\r\n"}, {"name": "createDelayQueue", "paramTypes": ["java.lang.String"], "doc": "\n 创建延迟队列\r\n\r\n @param queueName 队列名称\r\n @return Queue对象\r\n"}, {"name": "bindQueue", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 绑定队列到交换机\r\n\r\n @param queueName    队列名称\r\n @param exchangeName 交换机名称\r\n @param routingKey   路由键\r\n"}, {"name": "unbindQueue", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 解除队列绑定\r\n\r\n @param queueName    队列名称\r\n @param exchangeName 交换机名称\r\n @param routingKey   路由键\r\n"}, {"name": "deleteExchange", "paramTypes": ["java.lang.String"], "doc": "\n 删除交换机\r\n\r\n @param exchangeName 交换机名称\r\n"}, {"name": "deleteQueue", "paramTypes": ["java.lang.String"], "doc": "\n 删除队列\r\n\r\n @param queueName 队列名称\r\n"}, {"name": "purge<PERSON><PERSON>ue", "paramTypes": ["java.lang.String"], "doc": "\n 清空队列消息\r\n\r\n @param queueName 队列名称\r\n"}, {"name": "getQueueMessageCount", "paramTypes": ["java.lang.String"], "doc": "\n 获取队列消息数量\r\n\r\n @param queueName 队列名称\r\n @return 消息数量\r\n"}, {"name": "queueExists", "paramTypes": ["java.lang.String"], "doc": "\n 检查队列是否存在\r\n\r\n @param queueName 队列名称\r\n @return 是否存在\r\n"}], "constructors": []}