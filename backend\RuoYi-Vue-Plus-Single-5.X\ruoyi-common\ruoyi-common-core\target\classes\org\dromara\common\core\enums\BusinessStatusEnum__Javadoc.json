{"doc": "\n 业务状态枚举\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "status", "doc": "\n 状态\r\n"}, {"name": "desc", "doc": "\n 描述\r\n"}], "enumConstants": [{"name": "CANCEL", "doc": "\n 已撤销\r\n"}, {"name": "DRAFT", "doc": "\n 草稿\r\n"}, {"name": "WAITING", "doc": "\n 待审核\r\n"}, {"name": "FINISH", "doc": "\n 已完成\r\n"}, {"name": "INVALID", "doc": "\n 已作废\r\n"}, {"name": "BACK", "doc": "\n 已退回\r\n"}, {"name": "TERMINATION", "doc": "\n 已终止\r\n"}], "methods": [{"name": "getByStatus", "paramTypes": ["java.lang.String"], "doc": "\n 根据状态获取对应的 BusinessStatusEnum 枚举\r\n\r\n @param status 业务状态码\r\n @return 对应的 BusinessStatusEnum 枚举，如果找不到则返回 null\r\n"}, {"name": "findByStatus", "paramTypes": ["java.lang.String"], "doc": "\n 根据状态获取对应的业务状态描述信息\r\n\r\n @param status 业务状态码\r\n @return 返回业务状态描述，若状态码为空或未找到对应的枚举，返回空字符串\r\n"}, {"name": "isDraftOrCancelOrBack", "paramTypes": ["java.lang.String"], "doc": "\n 判断是否为指定的状态之一：草稿、已撤销或已退回\r\n\r\n @param status 要检查的状态\r\n @return 如果状态为草稿、已撤销或已退回之一，则返回 true；否则返回 false\r\n"}, {"name": "initialState", "paramTypes": ["java.lang.String"], "doc": "\n 判断是否为撤销，退回，作废，终止\r\n\r\n @param status status\r\n @return 结果\r\n"}, {"name": "runningStatus", "paramTypes": [], "doc": "\n 获取运行中的实例状态列表\r\n\r\n @return 包含运行中实例状态的不可变列表\r\n （包含 DRAFT、WAITING、BACK 和 CANCEL 状态）\r\n"}, {"name": "finishStatus", "paramTypes": [], "doc": "\n 获取结束实例的状态列表\r\n\r\n @return 包含结束实例状态的不可变列表\r\n （包含 FINISH、INVALID 和 TERMINATION 状态）\r\n"}, {"name": "checkStartStatus", "paramTypes": ["java.lang.String"], "doc": "\n 启动流程校验\r\n\r\n @param status 状态\r\n"}, {"name": "checkCancelStatus", "paramTypes": ["java.lang.String"], "doc": "\n 撤销流程校验\r\n\r\n @param status 状态\r\n"}, {"name": "checkBackStatus", "paramTypes": ["java.lang.String"], "doc": "\n 驳回流程校验\r\n\r\n @param status 状态\r\n"}, {"name": "checkInvalidStatus", "paramTypes": ["java.lang.String"], "doc": "\n 作废,终止流程校验\r\n\r\n @param status 状态\r\n"}], "constructors": []}