package org.dromara.app.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.AiTool;
import org.dromara.app.domain.ToolCall;
import org.dromara.app.mapper.AiToolMapper;
import org.dromara.app.mapper.ToolCallMapper;
import org.dromara.app.service.IToolService;
import org.dromara.app.tool.ToolExecutor;
import org.dromara.app.tool.ToolRegistry;
import org.dromara.common.caffeine.annotation.CaffeineCache;
import org.dromara.common.caffeine.annotation.TimeUnit;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * AI工具服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ToolServiceImpl implements IToolService {

    // 异步执行器，使用单例模式避免重复创建
    private static final java.util.concurrent.ExecutorService ASYNC_EXECUTOR =
        java.util.concurrent.Executors.newCachedThreadPool(r -> {
            Thread thread = new Thread(r, "tool-async-" + System.currentTimeMillis());
            thread.setDaemon(true);
            return thread;
        });
    private final AiToolMapper aiToolMapper;
    private final ToolCallMapper toolCallMapper;
    private final ToolRegistry toolRegistry;

    @PostConstruct
    public void init() {
        // 初始化默认工具
        initDefaultTools();

        // 注册工具执行器
        registerDefaultToolExecutors();
    }

    // ========== 工具管理 ==========

    @Override
    @CaffeineCache(
        key = "#category + '-' + #userId",
        expire = "10",
        timeUnit = TimeUnit.MINUTES
    )
    public List<AiTool> getAvailableTools(String category, Long userId) {
        try {

            // 从数据库查询，使用分页避免一次性加载过多数据
            Page<AiTool> page = new Page<>(1, 1000); // 设置合理的上限
            List<AiTool> tools = aiToolMapper.selectAvailableTools(page, category, true).getRecords();

            if (tools.isEmpty()) {
                return Collections.emptyList();
            }

            // 并行检查权限以提高性能
            List<AiTool> authorizedTools = tools.parallelStream()
                .filter(tool -> {
                    try {
                        return checkToolPermission(tool.getId(), userId);
                    } catch (Exception e) {
                        log.warn("检查工具权限失败: toolId={}, userId={}", tool.getId(), userId, e);
                        return false;
                    }
                })
                .collect(java.util.stream.Collectors.toList());

            // 按排序序号和名称排序
            authorizedTools.sort(Comparator
                .comparing(AiTool::getSortOrder, Comparator.nullsLast(Comparator.naturalOrder()))
                .thenComparing(AiTool::getName, Comparator.nullsLast(Comparator.naturalOrder())));

            // 处理JSON字段
            authorizedTools.forEach(this::processToolData);

            return authorizedTools;
        } catch (Exception e) {
            log.error("获取可用工具列表失败: category={}, userId={}", category, userId, e);
            return Collections.emptyList();
        }
    }

    @Override
    @CaffeineCache(
        key = "#toolId",
        expire = "30",
        timeUnit = TimeUnit.MINUTES
    )
    public AiTool getToolDetail(String toolId, Long userId) {
        try {

            if (StrUtil.isBlank(toolId)) {
                return null;
            }

            // 从数据库查询
            AiTool tool = aiToolMapper.selectById(toolId);
            if (tool == null) {
                return null;
            }

            // 检查工具是否启用
            if (!Boolean.TRUE.equals(tool.getEnabled())) {
                return null;
            }

            // 检查权限
            if (!checkToolPermission(toolId, userId)) {
                return null;
            }

            // 处理JSON字段
            processToolData(tool);

            // 验证工具配置完整性
            if (!validateToolConfiguration(tool)) {
                return null;
            }

            return tool;
        } catch (Exception e) {
            log.error("获取工具详情失败: toolId={}, userId={}", toolId, userId, e);
            return null;
        }
    }

    @Override
    public boolean checkToolPermission(String toolId, Long userId) {
        try {
            if (StrUtil.isBlank(toolId)) {
                return false;
            }

            AiTool tool = aiToolMapper.selectById(toolId);
            if (tool == null) {
                return false;
            }

            // 检查工具是否启用
            if (!Boolean.TRUE.equals(tool.getEnabled())) {
                return false;
            }

            // 检查权限级别
            Integer permissionLevel = tool.getPermissionLevel();
            if (permissionLevel == null) {
                permissionLevel = 0; // 默认为公开
            }

            switch (permissionLevel) {
                case 0: // 公开工具
                    return true;
                case 1: // 登录用户
                    if (userId == null) {
                        return false;
                    }
                    return true;
                case 2: // 特定权限
                    if (userId == null) {
                        return false;
                    }
                    return checkSpecificPermission(tool, userId);
                default:
                    log.warn("未知权限级别: toolId={}, permissionLevel={}", toolId, permissionLevel);
                    return false;
            }
        } catch (Exception e) {
            log.error("检查工具权限失败: toolId={}, userId={}", toolId, userId, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean registerTool(AiTool tool) {
        try {

            // 参数验证
            if (tool == null) {
                return false;
            }

            if (StrUtil.isBlank(tool.getId())) {
                return false;
            }

            if (StrUtil.isBlank(tool.getName())) {
                return false;
            }

            // 检查工具是否已存在
            AiTool existingTool = aiToolMapper.selectById(tool.getId());
            if (existingTool != null) {
                return false;
            }

            // 设置默认值
            if (tool.getEnabled() == null) {
                tool.setEnabled(true);
            }
            if (tool.getIsSystem() == null) {
                tool.setIsSystem(false);
            }
            if (tool.getPermissionLevel() == null) {
                tool.setPermissionLevel(0);
            }
            if (tool.getUsageCount() == null) {
                tool.setUsageCount(0L);
            }
            if (tool.getAvgExecutionTime() == null) {
                tool.setAvgExecutionTime(0L);
            }
            if (tool.getSuccessRate() == null) {
                tool.setSuccessRate(1.0);
            }
            if (tool.getSortOrder() == null) {
                tool.setSortOrder(999);
            }

            // 验证工具配置
            if (!validateToolConfiguration(tool)) {
                return false;
            }

            // 设置创建时间
            tool.setCreateTime(DateTime.now());
            tool.setUpdateTime(DateTime.now());

            // 保存到数据库
            int result = aiToolMapper.insert(tool);
            if (result <= 0) {
                return false;
            }

            // 注册到工具注册表
            toolRegistry.registerTool(tool);

            return true;
        } catch (Exception e) {
            log.error("注册工具失败: toolId={}", tool != null ? tool.getId() : "null", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTool(AiTool tool) {
        try {

            // 参数验证
            if (tool == null) {
                return false;
            }

            if (StrUtil.isBlank(tool.getId())) {
                return false;
            }

            // 检查工具是否存在
            AiTool existingTool = aiToolMapper.selectById(tool.getId());
            if (existingTool == null) {
                return false;
            }

            // 系统工具的某些字段不允许修改
            if (Boolean.TRUE.equals(existingTool.getIsSystem())) {
                tool.setIsSystem(true);
                tool.setImplementationClass(existingTool.getImplementationClass());
            }

            // 验证工具配置
            if (!validateToolConfiguration(tool)) {
                log.error("工具配置验证失败: toolId={}", tool.getId());
                return false;
            }

            // 设置更新时间
            tool.setUpdateTime(DateTime.now());

            // 更新数据库
            int result = aiToolMapper.updateById(tool);
            if (result <= 0) {
                log.error("更新工具到数据库失败: toolId={}", tool.getId());
                return false;
            }

            // 更新工具注册表
            toolRegistry.updateTool(tool);

            return true;
        } catch (Exception e) {
            log.error("更新工具失败: toolId={}", tool != null ? tool.getId() : "null", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteTool(String toolId) {
        try {

            if (StrUtil.isBlank(toolId)) {
                log.error("工具ID不能为空");
                return false;
            }

            // 检查工具是否存在
            AiTool existingTool = aiToolMapper.selectById(toolId);
            if (existingTool == null) {
                log.warn("工具不存在: toolId={}", toolId);
                return true; // 已经不存在，认为删除成功
            }

            // 系统工具不允许删除
            if (Boolean.TRUE.equals(existingTool.getIsSystem())) {
                log.error("系统工具不允许删除: toolId={}", toolId);
                return false;
            }

            // 检查是否有正在进行的调用
            long runningCalls = toolCallMapper.selectCount(
                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<ToolCall>()
                    .eq(ToolCall::getToolId, toolId)
                    .eq(ToolCall::getStatus, 0) // 调用中状态
            );

            if (runningCalls > 0) {
                log.error("工具有正在进行的调用，无法删除: toolId={}, runningCalls={}", toolId, runningCalls);
                return false;
            }

            // 从数据库删除
            int result = aiToolMapper.deleteById(toolId);
            if (result <= 0) {
                log.error("从数据库删除工具失败: toolId={}", toolId);
                return false;
            }

            // 从工具注册表移除
            toolRegistry.unregisterTool(toolId);

            return true;
        } catch (Exception e) {
            log.error("删除工具失败: toolId={}", toolId, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean toggleTool(String toolId, Boolean enabled) {
        try {

            if (StrUtil.isBlank(toolId)) {
                log.error("工具ID不能为空");
                return false;
            }

            if (enabled == null) {
                log.error("启用状态不能为空");
                return false;
            }

            // 检查工具是否存在
            AiTool existingTool = aiToolMapper.selectById(toolId);
            if (existingTool == null) {
                log.error("工具不存在: toolId={}", toolId);
                return false;
            }

            // 如果状态没有变化，直接返回成功
            if (enabled.equals(existingTool.getEnabled())) {
                log.debug("工具状态未变化: toolId={}, enabled={}", toolId, enabled);
                return true;
            }

            // 如果要禁用工具，检查是否有正在进行的调用
            if (!enabled) {
                long runningCalls = toolCallMapper.selectCount(
                    new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<ToolCall>()
                        .eq(ToolCall::getToolId, toolId)
                        .eq(ToolCall::getStatus, 0) // 调用中状态
                );

                if (runningCalls > 0) {
                    log.warn("工具有正在进行的调用，建议稍后禁用: toolId={}, runningCalls={}", toolId, runningCalls);
                    // 这里可以选择是否强制禁用，目前允许禁用但记录警告
                }
            }

            // 更新数据库
            AiTool tool = new AiTool();
            tool.setId(toolId);
            tool.setEnabled(enabled);
            tool.setUpdateTime(DateTime.now());

            int result = aiToolMapper.updateById(tool);
            if (result <= 0) {
                log.error("更新工具状态到数据库失败: toolId={}", toolId);
                return false;
            }

            // 清除相关缓存
            clearToolCache(toolId);

            return true;
        } catch (Exception e) {
            log.error("切换工具状态失败: toolId={}, enabled={}", toolId, enabled, e);
            return false;
        }
    }

    // ========== 工具调用 ==========

    @Override
    public ToolCallResult executeTool(String toolId, Map<String, Object> parameters,
                                      ToolCallContext context, Long userId) {
        long startTime = System.currentTimeMillis();
        String callId = generateCallId();


        // 创建调用记录
        ToolCall toolCall = createToolCall(callId, toolId, parameters, context, userId, startTime);

        try {
            // 参数验证
            if (StrUtil.isBlank(toolId)) {
                return createFailureResult(toolCall, startTime, "工具ID不能为空");
            }

            if (parameters == null) {
                parameters = new HashMap<>();
            }

            // 检查权限
            if (!checkToolPermission(toolId, userId)) {
                return createFailureResult(toolCall, startTime, "无权限调用该工具");
            }

            // 获取工具信息
            AiTool tool = getToolDetail(toolId, userId);
            if (tool == null) {
                return createFailureResult(toolCall, startTime, "工具不存在或已禁用");
            }

            // 验证参数
            ParameterValidationResult validation = validateParameters(toolId, parameters);
            if (!validation.isValid()) {
                String errorMsg = "参数验证失败: " + String.join(", ", validation.getErrors());
                return createFailureResult(toolCall, startTime, errorMsg);
            }

            // 使用标准化后的参数
            Map<String, Object> normalizedParams = validation.getNormalizedParameters();
            if (normalizedParams != null) {
                parameters = normalizedParams;
                toolCall.setParameters(JSONUtil.toJsonStr(parameters));
            }

            // 获取工具执行器
            ToolExecutor executor = toolRegistry.getExecutor(toolId);
            if (executor == null) {
                return createFailureResult(toolCall, startTime, "工具执行器不存在");
            }

            // 检查执行器是否可用
            if (!executor.isAvailable()) {
                return createFailureResult(toolCall, startTime, "工具执行器当前不可用");
            }

            // 应用工具配置（超时等）
            ToolCallResult result = executeWithConfig(executor, parameters, context, tool);

            // 更新调用记录
            long endTime = System.currentTimeMillis();
            toolCall.setStatus(result.isSuccess() ? 1 : 2);
            toolCall.setResult(JSONUtil.toJsonStr(result.getData()));
            toolCall.setExecutionTime(endTime - startTime);
            toolCall.setEndTime(endTime);
            if (!result.isSuccess()) {
                toolCall.setErrorMessage(result.getMessage());
            }

            // 设置调用ID到结果中
            result.setCallId(callId);
            result.setExecutionTime(endTime - startTime);

            // 保存调用记录
            saveToolCall(toolCall);

            // 更新工具统计
            updateToolStats(toolId, endTime - startTime, result.isSuccess());

            return result;

        } catch (Exception e) {
            log.error("工具调用失败: toolId={}, callId={}", toolId, callId, e);
            return createFailureResult(toolCall, startTime, "工具调用失败: " + e.getMessage());
        }
    }


    // 由于接口方法签名不同，这些方法需要重新实现以匹配接口定义
    // 暂时注释掉不匹配的方法实现

    // TODO: 实现接口中定义的其他方法
    @Override
    public String executeToolAsync(String toolId, Map<String, Object> parameters, ToolCallContext context, Long userId) {
        try {

            // 参数验证
            if (StrUtil.isBlank(toolId)) {
                return null;
            }

            // 快速权限检查
            if (!checkToolPermission(toolId, userId)) {
                log.error("无权限调用该工具: toolId={}, userId={}", toolId, userId);
                return null;
            }

            String callId = generateCallId();

            // 创建调用记录
            ToolCall toolCall = createToolCall(callId, toolId, parameters, context, userId, System.currentTimeMillis());

            try {
                toolCallMapper.insert(toolCall);
            } catch (Exception e) {
                log.error("保存异步调用记录失败: callId={}", callId, e);
                return null;
            }

            // 异步执行
            CompletableFuture.runAsync(() -> {
                try {
                    log.debug("开始异步执行工具: callId={}", callId);

                    // 获取工具信息
                    AiTool tool = getToolDetail(toolId, userId);
                    if (tool == null) {
                        updateAsyncCallFailure(callId, toolCall.getStartTime(), "工具不存在或已禁用");
                        return;
                    }

                    // 验证参数
                    ParameterValidationResult validation = validateParameters(toolId, parameters);
                    if (!validation.isValid()) {
                        String errorMsg = "参数验证失败: " + String.join(", ", validation.getErrors());
                        updateAsyncCallFailure(callId, toolCall.getStartTime(), errorMsg);
                        return;
                    }

                    // 获取工具执行器
                    ToolExecutor executor = toolRegistry.getExecutor(toolId);
                    if (executor == null) {
                        updateAsyncCallFailure(callId, toolCall.getStartTime(), "工具执行器不存在");
                        return;
                    }

                    // 检查执行器是否可用
                    if (!executor.isAvailable()) {
                        updateAsyncCallFailure(callId, toolCall.getStartTime(), "工具执行器当前不可用");
                        return;
                    }

                    // 执行工具
                    ToolCallResult result = executeWithConfig(executor, parameters, context, tool);

                    // 更新调用记录
                    updateAsyncCallResult(callId, toolCall.getStartTime(), result, toolId);

                    log.debug("异步工具执行完成: callId={}, success={}", callId, result.isSuccess());

                } catch (Exception e) {
                    log.error("异步执行工具失败: callId={}", callId, e);
                    updateAsyncCallFailure(callId, toolCall.getStartTime(), "异步执行失败: " + e.getMessage());
                }
            }, getAsyncExecutor());

            return callId;

        } catch (Exception e) {
            log.error("启动异步工具执行失败: toolId={}", toolId, e);
            return null;
        }
    }

    @Override
    public List<ToolCallResult> executeBatchTools(List<BatchToolCall> toolCalls, Long userId) {

        List<ToolCallResult> results = new ArrayList<>();

        if (toolCalls == null || toolCalls.isEmpty()) {
            log.warn("批量工具调用列表为空");
            return results;
        }

        // 限制批量调用数量，防止系统过载
        int maxBatchSize = 50; // 可配置
        if (toolCalls.size() > maxBatchSize) {
            log.warn("批量调用数量超过限制: {} > {}", toolCalls.size(), maxBatchSize);
            ToolCallResult errorResult = new ToolCallResult(false, null,
                "批量调用数量超过限制: " + toolCalls.size() + " > " + maxBatchSize);
            results.add(errorResult);
            return results;
        }

        // 并行执行以提高性能
        boolean useParallel = toolCalls.size() > 5; // 超过5个时使用并行

        if (useParallel) {
            log.debug("使用并行执行批量工具调用");
            results = toolCalls.parallelStream()
                .map(batchCall -> executeSingleBatchCall(batchCall, userId))
                .collect(java.util.stream.Collectors.toList());
        } else {
            log.debug("使用串行执行批量工具调用");
            for (BatchToolCall batchCall : toolCalls) {
                ToolCallResult result = executeSingleBatchCall(batchCall, userId);
                results.add(result);
            }
        }

        // 统计执行结果
        long successCount = results.stream().mapToLong(r -> r.isSuccess() ? 1 : 0).sum();
        long failureCount = results.size() - successCount;

        return results;
    }

    /**
     * 执行单个批量调用
     */
    private ToolCallResult executeSingleBatchCall(BatchToolCall batchCall, Long userId) {
        try {
            if (batchCall == null) {
                return new ToolCallResult(false, null, "批量调用对象为空");
            }

            String toolId = batchCall.getToolId();
            if (StrUtil.isBlank(toolId)) {
                return new ToolCallResult(false, null, "工具ID不能为空");
            }

            Map<String, Object> parameters = batchCall.getParameters();
            if (parameters == null) {
                parameters = new HashMap<>();
            }

            ToolCallContext context = batchCall.getContext();

            return executeTool(toolId, parameters, context, userId);

        } catch (Exception e) {
            String toolId = batchCall != null ? batchCall.getToolId() : "unknown";
            log.error("批量执行单个工具失败: toolId={}", toolId, e);
            return new ToolCallResult(false, null, "批量执行失败: " + e.getMessage());
        }
    }

    @Override
    public List<ParsedToolCall> parseToolCalls(String aiMessage) {

        List<ParsedToolCall> parsedCalls = new ArrayList<>();

        if (StrUtil.isBlank(aiMessage)) {
            return parsedCalls;
        }

        try {
            // 支持多种格式的工具调用解析
            parsedCalls.addAll(parseJsonArrayFormat(aiMessage));
            parsedCalls.addAll(parseXmlFormat(aiMessage));
            parsedCalls.addAll(parseMarkdownFormat(aiMessage));
            parsedCalls.addAll(parseFunctionCallFormat(aiMessage));

            // 去重和验证
            parsedCalls = validateAndDeduplicateParsedCalls(parsedCalls);


        } catch (Exception e) {
            log.error("解析工具调用失败", e);
        }

        return parsedCalls;
    }

    /**
     * 解析JSON数组格式的工具调用
     * 格式: [{"tool": "calculator", "parameters": {"expression": "2+3"}, "reasoning": "计算表达式"}]
     */
    private List<ParsedToolCall> parseJsonArrayFormat(String aiMessage) {
        List<ParsedToolCall> calls = new ArrayList<>();

        try {
            if (aiMessage.contains("[") && aiMessage.contains("]")) {
                String jsonPart = aiMessage.substring(aiMessage.indexOf("["), aiMessage.lastIndexOf("]") + 1);

                List<?> rawList = JSONUtil.toList(jsonPart, Map.class);
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> toolCallsJson = (List<Map<String, Object>>) rawList;

                for (Map<String, Object> callJson : toolCallsJson) {
                    ParsedToolCall parsedCall = new ParsedToolCall();
                    parsedCall.setToolName((String) callJson.get("tool"));

                    @SuppressWarnings("unchecked")
                    Map<String, Object> params = (Map<String, Object>) callJson.get("parameters");
                    parsedCall.setParameters(params != null ? params : new HashMap<>());

                    parsedCall.setReasoning((String) callJson.get("reasoning"));
                    calls.add(parsedCall);
                }
            }
        } catch (Exception e) {
            log.debug("JSON数组格式解析失败", e);
        }

        return calls;
    }

    /**
     * 解析XML格式的工具调用
     * 格式: <tool_call><tool>calculator</tool><parameters>{"expression": "2+3"}</parameters></tool_call>
     */
    private List<ParsedToolCall> parseXmlFormat(String aiMessage) {
        List<ParsedToolCall> calls = new ArrayList<>();

        try {
            // 使用正则表达式匹配XML格式
            java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(
                "<tool_call>\\s*<tool>([^<]+)</tool>\\s*<parameters>([^<]*)</parameters>(?:\\s*<reasoning>([^<]*)</reasoning>)?\\s*</tool_call>",
                java.util.regex.Pattern.DOTALL
            );

            java.util.regex.Matcher matcher = pattern.matcher(aiMessage);

            while (matcher.find()) {
                ParsedToolCall parsedCall = new ParsedToolCall();
                parsedCall.setToolName(matcher.group(1).trim());

                String parametersStr = matcher.group(2).trim();
                if (StrUtil.isNotBlank(parametersStr)) {
                    try {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> params = JSONUtil.toBean(parametersStr, Map.class);
                        parsedCall.setParameters(params);
                    } catch (Exception e) {
                        log.debug("解析XML参数失败: {}", parametersStr, e);
                        parsedCall.setParameters(new HashMap<>());
                    }
                } else {
                    parsedCall.setParameters(new HashMap<>());
                }

                parsedCall.setReasoning(matcher.group(3) != null ? matcher.group(3).trim() : null);
                calls.add(parsedCall);
            }
        } catch (Exception e) {
            log.debug("XML格式解析失败", e);
        }

        return calls;
    }

    /**
     * 解析Markdown格式的工具调用
     * 格式: ```tool:calculator\n{"expression": "2+3"}\n```
     */
    private List<ParsedToolCall> parseMarkdownFormat(String aiMessage) {
        List<ParsedToolCall> calls = new ArrayList<>();

        try {
            java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(
                "```tool:([^\\n]+)\\n([^`]*)```",
                java.util.regex.Pattern.DOTALL
            );

            java.util.regex.Matcher matcher = pattern.matcher(aiMessage);

            while (matcher.find()) {
                ParsedToolCall parsedCall = new ParsedToolCall();
                parsedCall.setToolName(matcher.group(1).trim());

                String parametersStr = matcher.group(2).trim();
                if (StrUtil.isNotBlank(parametersStr)) {
                    try {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> params = JSONUtil.toBean(parametersStr, Map.class);
                        parsedCall.setParameters(params);
                    } catch (Exception e) {
                        log.debug("解析Markdown参数失败: {}", parametersStr, e);
                        parsedCall.setParameters(new HashMap<>());
                    }
                } else {
                    parsedCall.setParameters(new HashMap<>());
                }

                calls.add(parsedCall);
            }
        } catch (Exception e) {
            log.debug("Markdown格式解析失败", e);
        }

        return calls;
    }

    /**
     * 解析函数调用格式
     * 格式: calculator(expression="2+3")
     */
    private List<ParsedToolCall> parseFunctionCallFormat(String aiMessage) {
        List<ParsedToolCall> calls = new ArrayList<>();

        try {
            java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(
                "([a-zA-Z_][a-zA-Z0-9_]*)\\s*\\(([^)]*)\\)",
                java.util.regex.Pattern.DOTALL
            );

            java.util.regex.Matcher matcher = pattern.matcher(aiMessage);

            while (matcher.find()) {
                String toolName = matcher.group(1).trim();
                String paramsStr = matcher.group(2).trim();

                // 验证是否是有效的工具名
                if (isValidToolName(toolName)) {
                    ParsedToolCall parsedCall = new ParsedToolCall();
                    parsedCall.setToolName(toolName);
                    parsedCall.setParameters(parseFunctionParameters(paramsStr));
                    calls.add(parsedCall);
                }
            }
        } catch (Exception e) {
            log.debug("函数调用格式解析失败", e);
        }

        return calls;
    }

    @Override
    public ParameterValidationResult validateParameters(String toolId, Map<String, Object> parameters) {
        log.debug("开始验证工具参数: toolId={}", toolId);

        try {
            // 基础验证
            if (StrUtil.isBlank(toolId)) {
                ParameterValidationResult result = new ParameterValidationResult(false);
                result.setErrors(List.of("工具ID不能为空"));
                return result;
            }

            if (parameters == null) {
                parameters = new HashMap<>();
            }

            // 获取工具信息
            AiTool tool = aiToolMapper.selectById(toolId);
            if (tool == null) {
                ParameterValidationResult result = new ParameterValidationResult(false);
                result.setErrors(List.of("工具不存在: " + toolId));
                return result;
            }

            // 检查工具是否启用
            if (!Boolean.TRUE.equals(tool.getEnabled())) {
                ParameterValidationResult result = new ParameterValidationResult(false);
                result.setErrors(List.of("工具已禁用: " + toolId));
                return result;
            }

            // 处理工具数据
            processToolData(tool);

            // 详细参数验证
            List<String> errors = new ArrayList<>();
            Map<String, Object> normalizedParams = new HashMap<>(parameters);

            // 验证参数Schema
            if (tool.getParameterSchemaObject() != null) {
                validateParametersAgainstSchema(tool.getParameterSchemaObject(), normalizedParams, errors);
            }

            // 参数类型转换和标准化
            if (errors.isEmpty()) {
                normalizedParams = normalizeParameters(tool, normalizedParams);

                // 再次验证标准化后的参数
                validateNormalizedParameters(tool.getParameterSchemaObject(), normalizedParams, errors);
            }

            // 返回结果
            if (errors.isEmpty()) {
                ParameterValidationResult result = new ParameterValidationResult(true);
                result.setNormalizedParameters(normalizedParams);
                log.debug("参数验证成功: toolId={}", toolId);
                return result;
            } else {
                ParameterValidationResult result = new ParameterValidationResult(false);
                result.setErrors(errors);
                log.debug("参数验证失败: toolId={}, errors={}", toolId, errors);
                return result;
            }

        } catch (Exception e) {
            log.error("参数验证异常: toolId={}", toolId, e);
            ParameterValidationResult result = new ParameterValidationResult(false);
            result.setErrors(List.of("参数验证异常: " + e.getMessage()));
            return result;
        }
    }

    /**
     * 根据Schema验证参数
     */
    private void validateParametersAgainstSchema(AiTool.ParameterSchema schema,
                                                 Map<String, Object> parameters,
                                                 List<String> errors) {
        try {
            if (schema == null) {
                return;
            }

            // 检查必需参数
            List<String> required = schema.getRequired();
            if (required != null) {
                for (String requiredParam : required) {
                    if (!parameters.containsKey(requiredParam)) {
                        errors.add("缺少必需参数: " + requiredParam);
                    } else {
                        Object value = parameters.get(requiredParam);
                        if (value == null || (value instanceof String && StrUtil.isBlank((String) value))) {
                            errors.add("必需参数不能为空: " + requiredParam);
                        }
                    }
                }
            }

            // 检查参数属性
            Map<String, AiTool.ParameterSchema.ParameterProperty> properties = schema.getProperties();
            if (properties != null) {
                for (Map.Entry<String, Object> entry : parameters.entrySet()) {
                    String paramName = entry.getKey();
                    Object paramValue = entry.getValue();

                    AiTool.ParameterSchema.ParameterProperty property = properties.get(paramName);
                    if (property != null) {
                        validateParameterProperty(paramName, paramValue, property, errors);
                    }
                }
            }

        } catch (Exception e) {
            log.error("Schema验证失败", e);
            errors.add("Schema验证异常: " + e.getMessage());
        }
    }

    /**
     * 验证单个参数属性
     */
    private void validateParameterProperty(String paramName, Object paramValue,
                                           AiTool.ParameterSchema.ParameterProperty property,
                                           List<String> errors) {
        try {
            if (paramValue == null) {
                return; // null值在必需参数检查中已处理
            }

            String type = property.getType();
            if (StrUtil.isNotBlank(type)) {
                if (!isValidParameterType(paramValue, type)) {
                    errors.add("参数类型错误: " + paramName + " 应为 " + type + " 类型");
                    return;
                }
            }

            // 字符串长度验证
            if (paramValue instanceof String) {
                String strValue = (String) paramValue;
                if (property.getMinLength() != null && strValue.length() < property.getMinLength()) {
                    errors.add("参数长度过短: " + paramName + " 最小长度为 " + property.getMinLength());
                }
                if (property.getMaxLength() != null && strValue.length() > property.getMaxLength()) {
                    errors.add("参数长度过长: " + paramName + " 最大长度为 " + property.getMaxLength());
                }

                // 正则表达式验证
                if (StrUtil.isNotBlank(property.getPattern())) {
                    if (!strValue.matches(property.getPattern())) {
                        errors.add("参数格式错误: " + paramName + " 不匹配模式 " + property.getPattern());
                    }
                }
            }

            // 数值范围验证
            if (paramValue instanceof Number) {
                Number numValue = (Number) paramValue;
                if (property.getMinimum() != null && numValue.doubleValue() < property.getMinimum().doubleValue()) {
                    errors.add("参数值过小: " + paramName + " 最小值为 " + property.getMinimum());
                }
                if (property.getMaximum() != null && numValue.doubleValue() > property.getMaximum().doubleValue()) {
                    errors.add("参数值过大: " + paramName + " 最大值为 " + property.getMaximum());
                }
            }

            // 枚举值验证
            List<Object> enumValues = property.getEnumValues();
            if (enumValues != null && !enumValues.isEmpty()) {
                if (!enumValues.contains(paramValue)) {
                    errors.add("参数值不在允许范围内: " + paramName + " 允许值为 " + enumValues);
                }
            }

        } catch (Exception e) {
            log.error("参数属性验证失败: {}", paramName, e);
            errors.add("参数验证异常: " + paramName + " - " + e.getMessage());
        }
    }

    @Override
    public Page<ToolCall> getToolCallHistory(Long userId, Integer pageNum, Integer pageSize) {
        log.debug("获取工具调用历史: userId={}, pageNum={}, pageSize={}", userId, pageNum, pageSize);

        try {
            // 参数验证
            if (userId == null) {
                log.warn("用户ID不能为空");
                return new Page<>();
            }

            if (pageNum == null || pageNum < 1) {
                pageNum = 1;
            }

            if (pageSize == null || pageSize < 1) {
                pageSize = 20;
            }

            // 限制每页大小，防止查询过多数据
            if (pageSize > 100) {
                pageSize = 100;
                log.warn("每页大小超过限制，调整为100: userId={}", userId);
            }

            Page<ToolCall> page = new Page<>(pageNum, pageSize);
            Page<ToolCall> result = toolCallMapper.selectCallHistory(page, null, userId);

            // 处理结果数据
            if (result != null && result.getRecords() != null) {
                for (ToolCall toolCall : result.getRecords()) {
                    processToolCallData(toolCall);
                }
            }

            log.debug("获取工具调用历史成功: userId={}, total={}, records={}",
                userId, result != null ? result.getTotal() : 0,
                result != null && result.getRecords() != null ? result.getRecords().size() : 0);

            return result != null ? result : new Page<>();

        } catch (Exception e) {
            log.error("获取工具调用历史失败: userId={}", userId, e);
            return new Page<>();
        }
    }

    @Override
    public List<ToolCall> getSessionToolCalls(String sessionId, Long userId) {
        log.debug("获取会话工具调用: sessionId={}, userId={}", sessionId, userId);

        try {
            // 参数验证
            if (StrUtil.isBlank(sessionId)) {
                log.warn("会话ID不能为空");
                return Collections.emptyList();
            }

            if (userId == null) {
                log.warn("用户ID不能为空");
                return Collections.emptyList();
            }

            List<ToolCall> toolCalls = toolCallMapper.getSessionToolCalls(sessionId, userId);

            if (toolCalls == null) {
                toolCalls = Collections.emptyList();
            }

            // 处理结果数据
            for (ToolCall toolCall : toolCalls) {
                processToolCallData(toolCall);
            }

            // 按时间排序
            toolCalls.sort((a, b) -> {
                Long timeA = a.getCreateTime() != null ? a.getCreateTime().getTime() : 0L;
                Long timeB = b.getCreateTime() != null ? b.getCreateTime().getTime() : 0L;
                return timeA.compareTo(timeB);
            });

            log.debug("获取会话工具调用成功: sessionId={}, count={}", sessionId, toolCalls.size());
            return toolCalls;

        } catch (Exception e) {
            log.error("获取会话工具调用失败: sessionId={}, userId={}", sessionId, userId, e);
            return Collections.emptyList();
        }
    }

    @Override
    public ToolCall getToolCallDetail(String callId, Long userId) {
        log.debug("获取工具调用详情: callId={}, userId={}", callId, userId);

        try {
            // 参数验证
            if (StrUtil.isBlank(callId)) {
                log.warn("调用记录ID不能为空");
                return null;
            }

            if (userId == null) {
                log.warn("用户ID不能为空");
                return null;
            }

            // 查询调用记录
            ToolCall toolCall = toolCallMapper.selectById(callId);
            if (toolCall == null) {
                log.debug("调用记录不存在: callId={}", callId);
                return null;
            }

            // 权限检查
            if (!toolCall.getUserId().equals(userId)) {
                log.warn("用户无权限访问调用记录: callId={}, userId={}, recordUserId={}",
                    callId, userId, toolCall.getUserId());
                return null;
            }

            // 处理调用记录数据
            processToolCallData(toolCall);

            log.debug("获取工具调用详情成功: callId={}", callId);
            return toolCall;

        } catch (Exception e) {
            log.error("获取工具调用详情失败: callId={}, userId={}", callId, userId, e);
            return null;
        }
    }

    @Override
    public ToolCallResult retryToolCall(String callId, Long userId) {

        try {
            // 参数验证
            if (StrUtil.isBlank(callId)) {
                return new ToolCallResult(false, null, "调用记录ID不能为空");
            }

            if (userId == null) {
                return new ToolCallResult(false, null, "用户ID不能为空");
            }

            // 获取原始调用记录
            ToolCall originalCall = toolCallMapper.selectById(callId);
            if (originalCall == null) {
                return new ToolCallResult(false, null, "调用记录不存在: " + callId);
            }

            // 权限检查
            if (!originalCall.getUserId().equals(userId)) {
                log.warn("用户无权限重试调用: callId={}, userId={}, originalUserId={}",
                    callId, userId, originalCall.getUserId());
                return new ToolCallResult(false, null, "无权限重试该调用");
            }

            // 检查重试次数限制
            int maxRetries = 5; // 可配置
            if (originalCall.getRetryCount() != null && originalCall.getRetryCount() >= maxRetries) {
                return new ToolCallResult(false, null, "重试次数已达上限: " + maxRetries);
            }

            // 检查工具是否仍然可用
            if (!checkToolPermission(originalCall.getToolId(), userId)) {
                return new ToolCallResult(false, null, "工具不可用或无权限");
            }

            // 解析原始参数
            @SuppressWarnings("unchecked")
            Map<String, Object> parameters = JSONUtil.toBean(originalCall.getParameters(), Map.class);
            if (parameters == null) {
                parameters = new HashMap<>();
            }

            // 解析原始上下文
            ToolCallContext context = null;
            if (StrUtil.isNotBlank(originalCall.getContext())) {
                try {
                    context = JSONUtil.toBean(originalCall.getContext(), ToolCallContext.class);
                } catch (Exception e) {
                    log.warn("解析调用上下文失败: callId={}", callId, e);
                    // 继续执行，使用空上下文
                }
            }

            // 重新执行工具
            ToolCallResult result = executeTool(originalCall.getToolId(), parameters, context, userId);

            // 更新重试次数
            try {
                ToolCall updateCall = new ToolCall();
                updateCall.setId(callId);
                updateCall.setRetryCount((originalCall.getRetryCount() != null ? originalCall.getRetryCount() : 0) + 1);
                updateCall.setUpdateTime(DateTime.now());

                toolCallMapper.updateById(updateCall);

            } catch (Exception e) {
                log.error("更新重试次数失败: callId={}", callId, e);
                // 不影响重试结果
            }

            return result;

        } catch (Exception e) {
            log.error("重试工具调用失败: callId={}", callId, e);
            return new ToolCallResult(false, null, "重试失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getToolUsageStats(Long userId) {
        log.debug("获取工具使用统计: userId={}", userId);

        try {
            if (userId == null) {
                log.warn("用户ID不能为空");
                return Collections.emptyMap();
            }

            Map<String, Object> stats = toolCallMapper.getUserToolStats(userId);
            if (stats == null) {
                stats = new HashMap<>();
            }

            // 添加额外的统计信息
            enhanceUserStats(stats, userId);

            log.debug("获取工具使用统计成功: userId={}, stats={}", userId, stats.keySet());
            return stats;

        } catch (Exception e) {
            log.error("获取工具使用统计失败: userId={}", userId, e);
            return Collections.emptyMap();
        }
    }

    @Override
    public Map<String, Object> getToolPerformanceStats(String toolId) {
        log.debug("获取工具性能统计: toolId={}", toolId);

        try {
            if (StrUtil.isBlank(toolId)) {
                log.warn("工具ID不能为空");
                return Collections.emptyMap();
            }

            Map<String, Object> stats = toolCallMapper.getToolUsageStats(toolId);
            if (stats == null) {
                stats = new HashMap<>();
            }

            // 添加额外的性能统计信息
            enhanceToolStats(stats, toolId);

            log.debug("获取工具性能统计成功: toolId={}, stats={}", toolId, stats.keySet());
            return stats;

        } catch (Exception e) {
            log.error("获取工具性能统计失败: toolId={}", toolId, e);
            return Collections.emptyMap();
        }
    }

    /**
     * 增强用户统计信息
     */
    private void enhanceUserStats(Map<String, Object> stats, Long userId) {
        try {
            // 获取最近使用的工具
            List<ToolCall> recentCalls = toolCallMapper.selectList(
                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<ToolCall>()
                    .eq(ToolCall::getUserId, userId)
                    .orderByDesc(ToolCall::getCreateTime)
                    .last("LIMIT 10")
            );

            if (recentCalls != null && !recentCalls.isEmpty()) {
                List<String> recentTools = recentCalls.stream()
                    .map(ToolCall::getToolId)
                    .distinct()
                    .collect(java.util.stream.Collectors.toList());
                stats.put("recentTools", recentTools);
                stats.put("lastCallTime", recentCalls.get(0).getCreateTime());
            }

            // 计算今日调用次数
            long todayCalls = toolCallMapper.selectCount(
                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<ToolCall>()
                    .eq(ToolCall::getUserId, userId)
                    .ge(ToolCall::getCreateTime, cn.hutool.core.date.DateUtil.beginOfDay(DateTime.now()))
            );
            stats.put("todayCalls", todayCalls);

        } catch (Exception e) {
            log.error("增强用户统计信息失败: userId={}", userId, e);
        }
    }

    /**
     * 增强工具统计信息
     */
    private void enhanceToolStats(Map<String, Object> stats, String toolId) {
        try {
            // 获取工具基本信息
            AiTool tool = aiToolMapper.selectById(toolId);
            if (tool != null) {
                stats.put("toolName", tool.getDisplayName());
                stats.put("toolCategory", tool.getCategory());
                stats.put("isSystemTool", tool.getIsSystem());
            }

            // 获取最近的调用记录
            List<ToolCall> recentCalls = toolCallMapper.selectList(
                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<ToolCall>()
                    .eq(ToolCall::getToolId, toolId)
                    .orderByDesc(ToolCall::getCreateTime)
                    .last("LIMIT 5")
            );

            if (recentCalls != null && !recentCalls.isEmpty()) {
                stats.put("lastCallTime", recentCalls.get(0).getCreateTime());

                // 计算最近调用的平均执行时间
                double avgTime = recentCalls.stream()
                    .filter(call -> call.getExecutionTime() != null)
                    .mapToLong(ToolCall::getExecutionTime)
                    .average()
                    .orElse(0.0);
                stats.put("recentAvgExecutionTime", avgTime);
            }

            // 计算今日调用次数
            long todayCalls = toolCallMapper.selectCount(
                new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<ToolCall>()
                    .eq(ToolCall::getToolId, toolId)
                    .ge(ToolCall::getCreateTime, cn.hutool.core.date.DateUtil.beginOfDay(DateTime.now()))
            );
            stats.put("todayCalls", todayCalls);

        } catch (Exception e) {
            log.error("增强工具统计信息失败: toolId={}", toolId, e);
        }
    }

    @Override
    public void initSystemTools() {

        // 初始化默认工具
        initDefaultTools();

        // 注册默认工具执行器
        registerDefaultToolExecutors();

    }

    // =================  私有方法  =================

    /**
     * 验证工具配置完整性
     */
    private boolean validateToolConfiguration(AiTool tool) {
        try {
            if (tool == null) {
                return false;
            }

            // 检查必需字段
            if (StrUtil.isBlank(tool.getId()) || StrUtil.isBlank(tool.getName())) {
                return false;
            }

            // 验证函数定义
            if (StrUtil.isNotBlank(tool.getFunctionDefinition())) {
                try {
                    JSONUtil.parseObj(tool.getFunctionDefinition());
                } catch (Exception e) {
                    log.error("函数定义JSON格式错误: toolId={}", tool.getId(), e);
                    return false;
                }
            }

            // 验证参数Schema
            if (StrUtil.isNotBlank(tool.getParameterSchema())) {
                try {
                    JSONUtil.parseObj(tool.getParameterSchema());
                } catch (Exception e) {
                    log.error("参数Schema JSON格式错误: toolId={}", tool.getId(), e);
                    return false;
                }
            }

            // 验证工具配置
            if (StrUtil.isNotBlank(tool.getToolConfig())) {
                try {
                    JSONUtil.parseObj(tool.getToolConfig());
                } catch (Exception e) {
                    log.error("工具配置JSON格式错误: toolId={}", tool.getId(), e);
                    return false;
                }
            }

            return true;
        } catch (Exception e) {
            log.error("验证工具配置失败: toolId={}", tool != null ? tool.getId() : "null", e);
            return false;
        }
    }

    /**
     * 检查特定权限
     */
    private boolean checkSpecificPermission(AiTool tool, Long userId) {
        try {
            String requiredPermissions = tool.getRequiredPermissions();
            if (StrUtil.isBlank(requiredPermissions)) {
                return true; // 没有特定权限要求
            }

            // TODO: 实现具体的权限检查逻辑
            // 这里可以集成具体的权限系统，比如检查用户角色、权限等
            // 目前简单实现：只要是登录用户就有权限
            return userId != null;
        } catch (Exception e) {
            log.error("检查特定权限失败: toolId={}, userId={}", tool.getId(), userId, e);
            return false;
        }
    }

    /**
     * 清除工具相关缓存
     */
    private void clearToolCache(String toolId) {
        try {
            // TODO: 实现具体的缓存清除逻辑
            // 这里可以清除Caffeine缓存、Redis缓存等
            log.debug("清除工具缓存: toolId={}", toolId);
        } catch (Exception e) {
            log.error("清除工具缓存失败: toolId={}", toolId, e);
        }
    }

    /**
     * 创建失败结果并更新调用记录
     */
    private ToolCallResult createFailureResult(ToolCall toolCall, long startTime, String errorMessage) {
        try {
            long endTime = System.currentTimeMillis();
            toolCall.setStatus(2); // 失败状态
            toolCall.setErrorMessage(errorMessage);
            toolCall.setExecutionTime(endTime - startTime);
            toolCall.setEndTime(endTime);

            // 保存调用记录
            saveToolCall(toolCall);

            return new ToolCallResult(false, null, errorMessage);
        } catch (Exception e) {
            log.error("创建失败结果时出错", e);
            return new ToolCallResult(false, null, errorMessage);
        }
    }

    /**
     * 使用工具配置执行工具
     */
    private ToolCallResult executeWithConfig(ToolExecutor executor, Map<String, Object> parameters,
                                             ToolCallContext context, AiTool tool) {
        try {
            // 获取工具配置
            AiTool.ToolConfig config = tool.getToolConfigObject();

            if (config != null && config.getTimeout() != null && config.getTimeout() > 0) {
                // 使用超时执行
                return executeWithTimeout(executor, parameters, context, config.getTimeout());
            } else {
                // 直接执行 - 需要转换上下文类型
                return executor.execute(parameters, context);
            }
        } catch (Exception e) {
            log.error("执行工具时出错: toolId={}", tool.getId(), e);
            return new ToolCallResult(false, null, "执行工具时出错: " + e.getMessage());
        }
    }

    /**
     * 带超时的工具执行
     */
    private ToolCallResult executeWithTimeout(ToolExecutor executor, Map<String, Object> parameters,
                                              ToolCallContext context, int timeoutSeconds) {
        try {
            CompletableFuture<ToolCallResult> future = CompletableFuture.supplyAsync(() -> {
                // 直接执行，需要转换上下文类型
                return executor.execute(parameters, context);
            }, getAsyncExecutor());

            return future.get(timeoutSeconds, java.util.concurrent.TimeUnit.SECONDS);
        } catch (java.util.concurrent.TimeoutException e) {
            log.error("工具执行超时: timeout={}s", timeoutSeconds);
            return new ToolCallResult(false, null, "工具执行超时");
        } catch (Exception e) {
            log.error("带超时的工具执行失败", e);
            return new ToolCallResult(false, null, "工具执行失败: " + e.getMessage());
        }
    }

    /**
     * 获取异步执行器
     */
    private java.util.concurrent.Executor getAsyncExecutor() {
        // 使用静态的线程池，避免重复创建
        return ASYNC_EXECUTOR;
    }

    /**
     * 验证和去重解析的工具调用
     */
    private List<ParsedToolCall> validateAndDeduplicateParsedCalls(List<ParsedToolCall> parsedCalls) {
        if (parsedCalls == null || parsedCalls.isEmpty()) {
            return new ArrayList<>();
        }

        // 去重（基于工具名和参数）
        Map<String, ParsedToolCall> uniqueCalls = new LinkedHashMap<>();

        for (ParsedToolCall call : parsedCalls) {
            if (call == null || StrUtil.isBlank(call.getToolName())) {
                continue;
            }

            // 创建唯一键
            String key = call.getToolName() + ":" + JSONUtil.toJsonStr(call.getParameters());

            // 如果已存在，保留第一个
            if (!uniqueCalls.containsKey(key)) {
                uniqueCalls.put(key, call);
            }
        }

        return new ArrayList<>(uniqueCalls.values());
    }

    /**
     * 验证工具名是否有效
     */
    private boolean isValidToolName(String toolName) {
        if (StrUtil.isBlank(toolName)) {
            return false;
        }

        // 检查是否是已注册的工具
        return toolRegistry.isRegistered(toolName) || aiToolMapper.selectById(toolName) != null;
    }

    /**
     * 解析函数参数
     */
    private Map<String, Object> parseFunctionParameters(String paramsStr) {
        Map<String, Object> params = new HashMap<>();

        if (StrUtil.isBlank(paramsStr)) {
            return params;
        }

        try {
            // 尝试解析为JSON格式
            if (paramsStr.trim().startsWith("{")) {
                @SuppressWarnings("unchecked")
                Map<String, Object> result = JSONUtil.toBean(paramsStr, Map.class);
                return result;
            }

            // 解析key=value格式
            String[] pairs = paramsStr.split(",");
            for (String pair : pairs) {
                String[] kv = pair.split("=", 2);
                if (kv.length == 2) {
                    String key = kv[0].trim();
                    String value = kv[1].trim();

                    // 移除引号
                    if ((value.startsWith("\"") && value.endsWith("\"")) ||
                        (value.startsWith("'") && value.endsWith("'"))) {
                        value = value.substring(1, value.length() - 1);
                    }

                    params.put(key, value);
                }
            }
        } catch (Exception e) {
            log.debug("解析函数参数失败: {}", paramsStr, e);
        }

        return params;
    }

    /**
     * 验证标准化后的参数
     */
    private void validateNormalizedParameters(AiTool.ParameterSchema schema,
                                              Map<String, Object> parameters,
                                              List<String> errors) {
        // 这里可以添加标准化后的额外验证逻辑
        // 目前保持简单实现
    }

    /**
     * 验证参数类型
     */
    private boolean isValidParameterType(Object value, String expectedType) {
        if (value == null) {
            return true; // null值在其他地方处理
        }

        switch (expectedType.toLowerCase()) {
            case "string":
                return value instanceof String;
            case "integer":
            case "int":
                return value instanceof Integer ||
                    (value instanceof String && isInteger((String) value));
            case "number":
            case "double":
            case "float":
                return value instanceof Number ||
                    (value instanceof String && isNumber((String) value));
            case "boolean":
                return value instanceof Boolean ||
                    (value instanceof String && isBooleanString((String) value));
            case "array":
                return value instanceof java.util.Collection || value.getClass().isArray();
            case "object":
                return value instanceof Map;
            default:
                return true; // 未知类型，允许通过
        }
    }

    /**
     * 检查字符串是否为整数
     */
    private boolean isInteger(String str) {
        try {
            Integer.parseInt(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 检查字符串是否为数字
     */
    private boolean isNumber(String str) {
        try {
            Double.parseDouble(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 检查字符串是否为布尔值
     */
    private boolean isBooleanString(String str) {
        return "true".equalsIgnoreCase(str) || "false".equalsIgnoreCase(str);
    }

    /**
     * 处理工具调用记录数据
     */
    private void processToolCallData(ToolCall toolCall) {
        try {
            if (toolCall == null) {
                return;
            }

            // 解析参数JSON
            if (StrUtil.isNotBlank(toolCall.getParameters())) {
                try {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> parametersObject = JSONUtil.toBean(toolCall.getParameters(), Map.class);
                    toolCall.setParametersObject(parametersObject);
                } catch (Exception e) {
                    log.debug("解析调用参数失败: callId={}", toolCall.getId(), e);
                }
            }

            // 解析结果JSON
            if (StrUtil.isNotBlank(toolCall.getResult())) {
                try {
                    Object resultData = JSONUtil.parse(toolCall.getResult());
                    ToolCall.ToolCallResult resultObject = new ToolCall.ToolCallResult();

                    if (resultData instanceof Map) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> resultMap = (Map<String, Object>) resultData;
                        resultObject.setSuccess((Boolean) resultMap.get("success"));
                        resultObject.setData(resultMap.get("data"));
                        resultObject.setMessage((String) resultMap.get("message"));
                        resultObject.setType((String) resultMap.get("type"));

                        @SuppressWarnings("unchecked")
                        Map<String, Object> metadata = (Map<String, Object>) resultMap.get("metadata");
                        resultObject.setMetadata(metadata);
                    }

                    toolCall.setResultObject(resultObject);
                } catch (Exception e) {
                    log.debug("解析调用结果失败: callId={}", toolCall.getId(), e);
                }
            }

            // 解析上下文JSON
            if (StrUtil.isNotBlank(toolCall.getContext())) {
                try {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> contextMap = JSONUtil.toBean(toolCall.getContext(), Map.class);

                    ToolCall.CallContext contextObject = new ToolCall.CallContext();
                    contextObject.setUserQuery((String) contextMap.get("userQuery"));
                    contextObject.setAiReasoning((String) contextMap.get("aiReasoning"));
                    contextObject.setTriggerReason((String) contextMap.get("triggerReason"));
                    contextObject.setPreviousSteps((String) contextMap.get("previousSteps"));

                    @SuppressWarnings("unchecked")
                    Map<String, Object> environment = (Map<String, Object>) contextMap.get("environment");
                    contextObject.setEnvironment(environment);

                    toolCall.setContextObject(contextObject);
                } catch (Exception e) {
                    log.debug("解析调用上下文失败: callId={}", toolCall.getId(), e);
                }
            }

            // 设置工具名称（如果没有的话）
            if (StrUtil.isBlank(toolCall.getToolName()) && StrUtil.isNotBlank(toolCall.getToolId())) {
                try {
                    AiTool tool = aiToolMapper.selectById(toolCall.getToolId());
                    if (tool != null) {
                        toolCall.setToolName(tool.getDisplayName());
                    }
                } catch (Exception e) {
                    log.debug("获取工具名称失败: toolId={}", toolCall.getToolId(), e);
                }
            }

        } catch (Exception e) {
            log.error("处理工具调用记录数据失败: callId={}", toolCall != null ? toolCall.getId() : "null", e);
        }
    }

    private void processToolData(AiTool tool) {
        try {
            // 解析函数定义
            if (StrUtil.isNotBlank(tool.getFunctionDefinition())) {
                AiTool.FunctionDefinition functionDef = JSONUtil.toBean(
                    tool.getFunctionDefinition(), AiTool.FunctionDefinition.class);
                tool.setFunctionDefinitionObject(functionDef);
            }

            // 解析参数Schema
            if (StrUtil.isNotBlank(tool.getParameterSchema())) {
                AiTool.ParameterSchema parameterSchema = JSONUtil.toBean(
                    tool.getParameterSchema(), AiTool.ParameterSchema.class);
                tool.setParameterSchemaObject(parameterSchema);
            }

            // 解析工具配置
            if (StrUtil.isNotBlank(tool.getToolConfig())) {
                AiTool.ToolConfig toolConfig = JSONUtil.toBean(
                    tool.getToolConfig(), AiTool.ToolConfig.class);
                tool.setToolConfigObject(toolConfig);
            }

            // 解析标签
            if (StrUtil.isNotBlank(tool.getTags())) {
                List<String> tags = Arrays.asList(tool.getTags().split(","));
                tool.setTagList(tags);
            }
        } catch (Exception e) {
            log.error("处理工具数据失败: {}", tool.getId(), e);
        }
    }


    private ToolCall createToolCall(String callId, String toolId,
                                    Map<String, Object> parameters,
                                    ToolCallContext context, Long userId, long startTime) {
        ToolCall toolCall = new ToolCall();
        toolCall.setId(callId);
        toolCall.setToolId(toolId);
        toolCall.setUserId(userId);
        toolCall.setParameters(JSONUtil.toJsonStr(parameters));
        toolCall.setStatus(0); // 调用中
        toolCall.setStartTime(startTime);
        toolCall.setRetryCount(0);
        toolCall.setSource("user");
        toolCall.setCreateTime(DateTime.now());

        if (context != null) {
            toolCall.setSessionId(context.getSessionId());
            toolCall.setMessageId(context.getMessageId());
            toolCall.setContext(JSONUtil.toJsonStr(context));
        }

        return toolCall;
    }

    private void saveToolCall(ToolCall toolCall) {
        try {
            // 保存到数据库
            toolCallMapper.insert(toolCall);
            log.debug("保存工具调用记录: {}", toolCall.getId());
        } catch (Exception e) {
            log.error("保存工具调用记录失败", e);
        }
    }

    private void updateToolStats(String toolId, long executionTime, boolean success) {
        try {
            // 更新数据库统计
            AiTool tool = aiToolMapper.selectById(toolId);
            if (tool != null) {
                tool.setUsageCount(tool.getUsageCount() + 1);
                tool.setLastUsed(System.currentTimeMillis());

                // 计算平均执行时间
                long avgTime = tool.getAvgExecutionTime();
                long count = tool.getUsageCount();
                long newAvgTime = (avgTime * (count - 1) + executionTime) / count;
                tool.setAvgExecutionTime(newAvgTime);

                // 计算成功率
                double successRate = tool.getSuccessRate();
                if (success) {
                    tool.setSuccessRate((successRate * (count - 1) + 1.0) / count);
                } else {
                    tool.setSuccessRate((successRate * (count - 1)) / count);
                }

                aiToolMapper.updateById(tool);
            }
        } catch (Exception e) {
            log.error("更新工具统计失败", e);
        }
    }

    private String generateCallId() {
        return "call_" + System.currentTimeMillis() + "_" +
            Integer.toHexString((int) (Math.random() * 0x10000));
    }

    /**
     * 更新异步调用失败状态
     */
    private void updateAsyncCallFailure(String callId, Long startTime, String errorMessage) {
        try {
            ToolCall updateCall = new ToolCall();
            updateCall.setId(callId);
            updateCall.setStatus(2); // 失败状态
            updateCall.setErrorMessage(errorMessage);
            updateCall.setEndTime(System.currentTimeMillis());
            updateCall.setExecutionTime(updateCall.getEndTime() - startTime);

            toolCallMapper.updateById(updateCall);
        } catch (Exception e) {
            log.error("更新异步调用失败状态失败: callId={}", callId, e);
        }
    }

    /**
     * 更新异步调用结果
     */
    private void updateAsyncCallResult(String callId, Long startTime, ToolCallResult result, String toolId) {
        try {
            ToolCall updateCall = new ToolCall();
            updateCall.setId(callId);
            updateCall.setStatus(result.isSuccess() ? 1 : 2);
            updateCall.setResult(JSONUtil.toJsonStr(result.getData()));
            updateCall.setEndTime(System.currentTimeMillis());
            updateCall.setExecutionTime(updateCall.getEndTime() - startTime);

            if (!result.isSuccess()) {
                updateCall.setErrorMessage(result.getMessage());
            }

            toolCallMapper.updateById(updateCall);

            // 更新工具统计
            updateToolStats(toolId, updateCall.getExecutionTime(), result.isSuccess());
        } catch (Exception e) {
            log.error("更新异步调用结果失败: callId={}", callId, e);
        }
    }


    private void initDefaultTools() {
        // 先查看是否存在默认AI工具
        List<AiTool> existingTools = aiToolMapper.selectList(null);
        if (!existingTools.isEmpty()) {
            return;
        }

        // 创建计算器工具
        createCalculatorTool();

        // 创建文本处理工具
        createTextProcessingTool();

        // 创建时间工具
        createDateTimeTool();

        // 创建文件操作工具
        createFileOperationTool();
    }

    private void createCalculatorTool() {
        AiTool calculator = new AiTool();
        calculator.setId("calculator");
        calculator.setName("calculator");
        calculator.setDisplayName("计算器");
        calculator.setDescription("执行数学计算");
        calculator.setCategory("calculation");
        calculator.setIcon("calculator");
        calculator.setColor("#4CAF50");
        calculator.setEnabled(true);
        calculator.setIsSystem(true);
        calculator.setPermissionLevel(0);
        calculator.setUsageCount(0L);
        calculator.setAvgExecutionTime(0L);
        calculator.setSuccessRate(1.0);
        calculator.setSortOrder(1);
        calculator.setVersion("1.0.0");
        calculator.setAuthor("system");
        calculator.setImplementationClass("org.dromara.app.tool.impl.CalculatorToolExecutor");

        // 设置函数定义
        AiTool.FunctionDefinition functionDef = new AiTool.FunctionDefinition();
        functionDef.setName("calculate");
        functionDef.setDescription("执行数学计算");

        Map<String, Object> parameters = new HashMap<>();
        parameters.put("type", "object");

        Map<String, Object> properties = new HashMap<>();
        Map<String, Object> expressionProp = new HashMap<>();
        expressionProp.put("type", "string");
        expressionProp.put("description", "要计算的数学表达式");
        properties.put("expression", expressionProp);

        parameters.put("properties", properties);
        parameters.put("required", Arrays.asList("expression"));

        functionDef.setParameters(parameters);
        calculator.setFunctionDefinition(JSONUtil.toJsonStr(functionDef));

        // 设置参数Schema
        AiTool.ParameterSchema paramSchema = new AiTool.ParameterSchema();
        paramSchema.setType("object");
        paramSchema.setRequired(Arrays.asList("expression"));

        Map<String, AiTool.ParameterSchema.ParameterProperty> schemaProperties = new HashMap<>();
        AiTool.ParameterSchema.ParameterProperty exprProperty = new AiTool.ParameterSchema.ParameterProperty();
        exprProperty.setType("string");
        exprProperty.setDescription("要计算的数学表达式，支持+、-、*、/、()等运算符");
        schemaProperties.put("expression", exprProperty);

        paramSchema.setProperties(schemaProperties);
        calculator.setParameterSchema(JSONUtil.toJsonStr(paramSchema));

        // 设置工具配置
        AiTool.ToolConfig toolConfig = new AiTool.ToolConfig();
        toolConfig.setTimeout(5);
        toolConfig.setMaxRetries(3);
        toolConfig.setEnableCache(true);
        toolConfig.setCacheTimeout(300);
        toolConfig.setEnableLogging(true);
        toolConfig.setLogLevel("INFO");
        calculator.setToolConfig(JSONUtil.toJsonStr(toolConfig));

        calculator.setTags("数学,计算,算术");
        calculator.setCreateTime(DateTime.now());
        calculator.setUpdateTime(DateTime.now());

        try {
            aiToolMapper.insert(calculator);
        } catch (Exception e) {
            log.error("创建计算器工具失败", e);
        }
    }

    private void createTextProcessingTool() {
        AiTool textTool = new AiTool();
        textTool.setId("text_processor");
        textTool.setName("text_processor");
        textTool.setDisplayName("文本处理");
        textTool.setDescription("处理和分析文本内容，支持统计、格式化、提取等操作");
        textTool.setCategory("text");
        textTool.setIcon("text-format");
        textTool.setColor("#2196F3");
        textTool.setEnabled(true);
        textTool.setIsSystem(true);
        textTool.setPermissionLevel(0);
        textTool.setUsageCount(0L);
        textTool.setAvgExecutionTime(0L);
        textTool.setSuccessRate(1.0);
        textTool.setSortOrder(2);
        textTool.setVersion("1.0.0");
        textTool.setAuthor("system");
        textTool.setImplementationClass("org.dromara.app.tool.impl.TextProcessorToolExecutor");

        // 设置函数定义
        AiTool.FunctionDefinition functionDef = new AiTool.FunctionDefinition();
        functionDef.setName("process_text");
        functionDef.setDescription("处理和分析文本内容");

        Map<String, Object> parameters = new HashMap<>();
        parameters.put("type", "object");

        Map<String, Object> properties = new HashMap<>();

        // text参数
        Map<String, Object> textProp = new HashMap<>();
        textProp.put("type", "string");
        textProp.put("description", "要处理的文本内容");
        properties.put("text", textProp);

        // operation参数
        Map<String, Object> operationProp = new HashMap<>();
        operationProp.put("type", "string");
        operationProp.put("description", "处理操作类型");
        operationProp.put("enum", Arrays.asList("count", "format", "extract", "replace", "analyze"));
        properties.put("operation", operationProp);

        parameters.put("properties", properties);
        parameters.put("required", Arrays.asList("text", "operation"));

        functionDef.setParameters(parameters);
        textTool.setFunctionDefinition(JSONUtil.toJsonStr(functionDef));

        // 设置参数Schema
        AiTool.ParameterSchema paramSchema = new AiTool.ParameterSchema();
        paramSchema.setType("object");
        paramSchema.setRequired(Arrays.asList("text", "operation"));

        Map<String, AiTool.ParameterSchema.ParameterProperty> schemaProperties = new HashMap<>();

        AiTool.ParameterSchema.ParameterProperty textProperty = new AiTool.ParameterSchema.ParameterProperty();
        textProperty.setType("string");
        textProperty.setDescription("要处理的文本内容");
        textProperty.setMinLength(1);
        textProperty.setMaxLength(10000);
        schemaProperties.put("text", textProperty);

        AiTool.ParameterSchema.ParameterProperty operationProperty = new AiTool.ParameterSchema.ParameterProperty();
        operationProperty.setType("string");
        operationProperty.setDescription("处理操作类型：count-统计，format-格式化，extract-提取，replace-替换，analyze-分析");
        operationProperty.setEnumValues(Arrays.asList("count", "format", "extract", "replace", "analyze"));
        schemaProperties.put("operation", operationProperty);

        paramSchema.setProperties(schemaProperties);
        textTool.setParameterSchema(JSONUtil.toJsonStr(paramSchema));

        // 设置工具配置
        AiTool.ToolConfig toolConfig = new AiTool.ToolConfig();
        toolConfig.setTimeout(10);
        toolConfig.setMaxRetries(3);
        toolConfig.setEnableCache(true);
        toolConfig.setCacheTimeout(600);
        toolConfig.setEnableLogging(true);
        toolConfig.setLogLevel("INFO");
        textTool.setToolConfig(JSONUtil.toJsonStr(toolConfig));

        textTool.setTags("文本,处理,分析,统计");
        textTool.setCreateTime(DateTime.now());
        textTool.setUpdateTime(DateTime.now());

        try {
            aiToolMapper.insert(textTool);
        } catch (Exception e) {
            log.error("创建文本处理工具失败", e);
        }
    }

    private void createDateTimeTool() {
        AiTool dateTool = new AiTool();
        dateTool.setId("datetime_helper");
        dateTool.setName("datetime_helper");
        dateTool.setDisplayName("时间助手");
        dateTool.setDescription("处理时间和日期相关操作，支持格式化、计算、转换等");
        dateTool.setCategory("utility");
        dateTool.setIcon("clock");
        dateTool.setColor("#FF9800");
        dateTool.setEnabled(true);
        dateTool.setIsSystem(true);
        dateTool.setPermissionLevel(0);
        dateTool.setUsageCount(0L);
        dateTool.setAvgExecutionTime(0L);
        dateTool.setSuccessRate(1.0);
        dateTool.setSortOrder(3);
        dateTool.setVersion("1.0.0");
        dateTool.setAuthor("system");
        dateTool.setImplementationClass("org.dromara.app.tool.impl.DateTimeToolExecutor");

        // 设置函数定义
        AiTool.FunctionDefinition functionDef = new AiTool.FunctionDefinition();
        functionDef.setName("datetime_operation");
        functionDef.setDescription("执行时间和日期相关操作");

        Map<String, Object> parameters = new HashMap<>();
        parameters.put("type", "object");

        Map<String, Object> properties = new HashMap<>();

        // operation参数
        Map<String, Object> operationProp = new HashMap<>();
        operationProp.put("type", "string");
        operationProp.put("description", "时间操作类型");
        operationProp.put("enum", Arrays.asList("now", "format", "parse", "add", "subtract", "compare", "timezone"));
        properties.put("operation", operationProp);

        // datetime参数（可选）
        Map<String, Object> datetimeProp = new HashMap<>();
        datetimeProp.put("type", "string");
        datetimeProp.put("description", "时间字符串（可选）");
        properties.put("datetime", datetimeProp);

        // format参数（可选）
        Map<String, Object> formatProp = new HashMap<>();
        formatProp.put("type", "string");
        formatProp.put("description", "时间格式（可选）");
        properties.put("format", formatProp);

        parameters.put("properties", properties);
        parameters.put("required", Arrays.asList("operation"));

        functionDef.setParameters(parameters);
        dateTool.setFunctionDefinition(JSONUtil.toJsonStr(functionDef));

        // 设置参数Schema
        AiTool.ParameterSchema paramSchema = new AiTool.ParameterSchema();
        paramSchema.setType("object");
        paramSchema.setRequired(Arrays.asList("operation"));

        Map<String, AiTool.ParameterSchema.ParameterProperty> schemaProperties = new HashMap<>();

        AiTool.ParameterSchema.ParameterProperty operationProperty = new AiTool.ParameterSchema.ParameterProperty();
        operationProperty.setType("string");
        operationProperty.setDescription("时间操作类型：now-当前时间，format-格式化，parse-解析，add-增加，subtract-减少，compare-比较，timezone-时区转换");
        operationProperty.setEnumValues(Arrays.asList("now", "format", "parse", "add", "subtract", "compare", "timezone"));
        schemaProperties.put("operation", operationProperty);

        AiTool.ParameterSchema.ParameterProperty datetimeProperty = new AiTool.ParameterSchema.ParameterProperty();
        datetimeProperty.setType("string");
        datetimeProperty.setDescription("时间字符串，支持多种格式");
        schemaProperties.put("datetime", datetimeProperty);

        AiTool.ParameterSchema.ParameterProperty formatProperty = new AiTool.ParameterSchema.ParameterProperty();
        formatProperty.setType("string");
        formatProperty.setDescription("时间格式字符串，如：yyyy-MM-dd HH:mm:ss");
        schemaProperties.put("format", formatProperty);

        paramSchema.setProperties(schemaProperties);
        dateTool.setParameterSchema(JSONUtil.toJsonStr(paramSchema));

        // 设置工具配置
        AiTool.ToolConfig toolConfig = new AiTool.ToolConfig();
        toolConfig.setTimeout(5);
        toolConfig.setMaxRetries(3);
        toolConfig.setEnableCache(true);
        toolConfig.setCacheTimeout(300);
        toolConfig.setEnableLogging(true);
        toolConfig.setLogLevel("INFO");
        dateTool.setToolConfig(JSONUtil.toJsonStr(toolConfig));

        dateTool.setTags("时间,日期,格式化,计算");
        dateTool.setCreateTime(DateTime.now());
        dateTool.setUpdateTime(DateTime.now());

        try {
            aiToolMapper.insert(dateTool);
        } catch (Exception e) {
            log.error("创建时间助手工具失败", e);
        }
    }

    private void createFileOperationTool() {
        AiTool fileTool = new AiTool();
        fileTool.setId("file_operation");
        fileTool.setName("file_operation");
        fileTool.setDisplayName("文件操作");
        fileTool.setDescription("执行文件和目录相关操作，支持读取、写入、列表等");
        fileTool.setCategory("file");
        fileTool.setIcon("folder");
        fileTool.setColor("#4CAF50");
        fileTool.setEnabled(true);
        fileTool.setIsSystem(true);
        fileTool.setPermissionLevel(1); // 需要登录用户权限
        fileTool.setUsageCount(0L);
        fileTool.setAvgExecutionTime(0L);
        fileTool.setSuccessRate(1.0);
        fileTool.setSortOrder(4);
        fileTool.setVersion("1.0.0");
        fileTool.setAuthor("system");
        fileTool.setImplementationClass("org.dromara.app.tool.impl.FileOperationToolExecutor");

        // 设置函数定义
        AiTool.FunctionDefinition functionDef = new AiTool.FunctionDefinition();
        functionDef.setName("file_operation");
        functionDef.setDescription("执行文件和目录相关操作");

        Map<String, Object> parameters = new HashMap<>();
        parameters.put("type", "object");

        Map<String, Object> properties = new HashMap<>();

        // operation参数
        Map<String, Object> operationProp = new HashMap<>();
        operationProp.put("type", "string");
        operationProp.put("description", "文件操作类型");
        operationProp.put("enum", Arrays.asList("read", "write", "list", "exists", "delete", "copy", "move", "info"));
        properties.put("operation", operationProp);

        // path参数
        Map<String, Object> pathProp = new HashMap<>();
        pathProp.put("type", "string");
        pathProp.put("description", "文件或目录路径");
        properties.put("path", pathProp);

        // content参数（可选）
        Map<String, Object> contentProp = new HashMap<>();
        contentProp.put("type", "string");
        contentProp.put("description", "文件内容（写入操作时使用）");
        properties.put("content", contentProp);

        parameters.put("properties", properties);
        parameters.put("required", Arrays.asList("operation", "path"));

        functionDef.setParameters(parameters);
        fileTool.setFunctionDefinition(JSONUtil.toJsonStr(functionDef));

        // 设置参数Schema
        AiTool.ParameterSchema paramSchema = new AiTool.ParameterSchema();
        paramSchema.setType("object");
        paramSchema.setRequired(Arrays.asList("operation", "path"));

        Map<String, AiTool.ParameterSchema.ParameterProperty> schemaProperties = new HashMap<>();

        AiTool.ParameterSchema.ParameterProperty operationProperty = new AiTool.ParameterSchema.ParameterProperty();
        operationProperty.setType("string");
        operationProperty.setDescription("文件操作类型：read-读取，write-写入，list-列表，exists-存在检查，delete-删除，copy-复制，move-移动，info-信息");
        operationProperty.setEnumValues(Arrays.asList("read", "write", "list", "exists", "delete", "copy", "move", "info"));
        schemaProperties.put("operation", operationProperty);

        AiTool.ParameterSchema.ParameterProperty pathProperty = new AiTool.ParameterSchema.ParameterProperty();
        pathProperty.setType("string");
        pathProperty.setDescription("文件或目录的完整路径");
        pathProperty.setMinLength(1);
        pathProperty.setMaxLength(500);
        schemaProperties.put("path", pathProperty);

        AiTool.ParameterSchema.ParameterProperty contentProperty = new AiTool.ParameterSchema.ParameterProperty();
        contentProperty.setType("string");
        contentProperty.setDescription("文件内容，用于写入操作");
        contentProperty.setMaxLength(100000); // 限制文件大小
        schemaProperties.put("content", contentProperty);

        paramSchema.setProperties(schemaProperties);
        fileTool.setParameterSchema(JSONUtil.toJsonStr(paramSchema));

        // 设置工具配置
        AiTool.ToolConfig toolConfig = new AiTool.ToolConfig();
        toolConfig.setTimeout(15);
        toolConfig.setMaxRetries(2);
        toolConfig.setEnableCache(false); // 文件操作不缓存
        toolConfig.setEnableLogging(true);
        toolConfig.setLogLevel("INFO");

        // 自定义配置
        Map<String, Object> customConfig = new HashMap<>();
        customConfig.put("allowedExtensions", Arrays.asList(".txt", ".json", ".xml", ".csv", ".log"));
        customConfig.put("maxFileSize", 10 * 1024 * 1024); // 10MB
        customConfig.put("sandboxPath", "/tmp/file_operations"); // 沙盒路径
        toolConfig.setCustomConfig(customConfig);

        fileTool.setToolConfig(JSONUtil.toJsonStr(toolConfig));

        fileTool.setTags("文件,目录,读取,写入,操作");
        fileTool.setCreateTime(DateTime.now());
        fileTool.setUpdateTime(DateTime.now());

        try {
            aiToolMapper.insert(fileTool);
        } catch (Exception e) {
            log.error("创建文件操作工具失败", e);
        }
    }

    private void registerDefaultToolExecutors() {

        try {
            // 手动注册默认的执行器
            toolRegistry.registerExecutor(new org.dromara.app.tool.impl.TextProcessorToolExecutor());
            toolRegistry.registerExecutor(new org.dromara.app.tool.impl.DateTimeToolExecutor());

        } catch (Exception e) {
            log.error("注册默认工具执行器失败", e);
        }
    }

    private Map<String, Object> normalizeParameters(AiTool tool, Map<String, Object> parameters) {
        Map<String, Object> normalized = new HashMap<>(parameters);

        try {
            AiTool.ParameterSchema schema = tool.getParameterSchemaObject();
            if (schema != null && schema.getProperties() != null) {
                for (Map.Entry<String, AiTool.ParameterSchema.ParameterProperty> entry : schema.getProperties().entrySet()) {
                    String paramName = entry.getKey();
                    AiTool.ParameterSchema.ParameterProperty property = entry.getValue();

                    Object value = normalized.get(paramName);
                    if (value != null) {
                        // 类型转换
                        Object normalizedValue = normalizeParameterValue(value, property);
                        normalized.put(paramName, normalizedValue);
                    } else if (property.getDefaultValue() != null) {
                        // 设置默认值
                        normalized.put(paramName, property.getDefaultValue());
                    }
                }
            }
        } catch (Exception e) {
            log.error("参数标准化失败", e);
        }

        return normalized;
    }

    private Object normalizeParameterValue(Object value, AiTool.ParameterSchema.ParameterProperty property) {
        if (value == null) {
            return null;
        }

        String type = property.getType();
        if (type == null) {
            return value;
        }

        try {
            switch (type.toLowerCase()) {
                case "string":
                    return value.toString();
                case "integer":
                case "int":
                    return Integer.valueOf(value.toString());
                case "number":
                case "double":
                case "float":
                    return Double.valueOf(value.toString());
                case "boolean":
                    return Boolean.valueOf(value.toString());
                case "array":
                    if (value instanceof List) {
                        return value;
                    } else if (value instanceof String) {
                        return Arrays.asList(value.toString().split(","));
                    }
                    return value;
                default:
                    return value;
            }
        } catch (Exception e) {
            log.warn("参数类型转换失败: {} -> {}", value, type);
            return value;
        }
    }

}
