{"doc": "\n SSE工具类\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "getUserIdIfLogin", "paramTypes": ["java.lang.String"], "doc": "\n 获取当前登录用户的ID\r\n\r\n @param satoken token令牌\r\n @return 用户ID\r\n"}, {"name": "sendMessage", "paramTypes": ["java.lang.Long", "java.lang.String"], "doc": "\n 向指定的SSE会话发送消息\r\n\r\n @param userId  要发送消息的用户id\r\n @param message 要发送的消息内容\r\n"}, {"name": "sendMessage", "paramTypes": ["java.lang.String"], "doc": "\n 本机全用户会话发送消息\r\n\r\n @param message 要发送的消息内容\r\n"}, {"name": "publishMessage", "paramTypes": ["org.dromara.common.sse.dto.SseMessageDto"], "doc": "\n 发布SSE订阅消息\r\n\r\n @param sseMessageDto 要发布的SSE消息对象\r\n"}, {"name": "publishAll", "paramTypes": ["java.lang.String"], "doc": "\n 向所有的用户发布订阅的消息(群发)\r\n\r\n @param message 要发布的消息内容\r\n"}, {"name": "isEnable", "paramTypes": [], "doc": "\n 是否开启\r\n"}], "constructors": []}