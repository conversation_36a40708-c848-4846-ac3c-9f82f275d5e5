package org.dromara.app.domain;

import com.pgvector.PGvector;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 向量嵌入对象，用于知识库检索
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class VectorEmbedding {

    /**
     * 唯一标识
     */
    private String id;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 知识库ID
     */
    private Long knowledgeBaseId;

    /**
     * 文档ID
     */
    private Long documentId;

    /**
     * 文档内容
     */
    private String content;

    /**
     * 嵌入向量（PGvector格式）
     */
    private PGvector embedding;

    /**
     * 文档标题
     */
    private String title;

    /**
     * 摘要
     */
    private String summary;

    /**
     * 位置
     */
    private Integer position;

    /**
     * 内容长度
     */
    private Integer contentLength;

    /**
     * 分块类型
     */
    private String chunkType;

    /**
     * 模型名称
     */
    private String modelName;

    /**
     * 向量维度
     */
    private Integer dimension;

    /**
     * 元数据
     */
    private String metadata;

    /**
     * 标签
     */
    private String tags;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建部门
     */
    private Long createDept;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标志
     */
    private String delFlag;

    /**
     * 版本号
     */
    private Long version;

    /**
     * 相似度得分（查询时使用）
     */
    private Float similarity;

    /**
     * 来源
     */
    private String source;

    /**
     * 文档类型
     */
    private String documentType;
}
