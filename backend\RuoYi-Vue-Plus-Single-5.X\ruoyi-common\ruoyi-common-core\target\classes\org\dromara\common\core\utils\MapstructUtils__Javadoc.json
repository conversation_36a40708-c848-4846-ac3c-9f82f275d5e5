{"doc": "\n Mapstruct 工具类\r\n <p>参考文档：<a href=\"https://mapstruct.plus/introduction/quick-start.html\">mapstruct-plus</a></p>\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "convert", "paramTypes": ["java.lang.Object", "java.lang.Class"], "doc": "\n 将 T 类型对象，转换为 desc 类型的对象并返回\r\n\r\n @param source 数据来源实体\r\n @param desc   描述对象 转换后的对象\r\n @return desc\r\n"}, {"name": "convert", "paramTypes": ["java.lang.Object", "java.lang.Object"], "doc": "\n 将 T 类型对象，按照配置的映射字段规则，给 desc 类型的对象赋值并返回 desc 对象\r\n\r\n @param source 数据来源实体\r\n @param desc   转换后的对象\r\n @return desc\r\n"}, {"name": "convert", "paramTypes": ["java.util.List", "java.lang.Class"], "doc": "\n 将 T 类型的集合，转换为 desc 类型的集合并返回\r\n\r\n @param sourceList 数据来源实体列表\r\n @param desc       描述对象 转换后的对象\r\n @return desc\r\n"}, {"name": "convert", "paramTypes": ["java.util.Map", "java.lang.Class"], "doc": "\n 将 Map 转换为 beanClass 类型的集合并返回\r\n\r\n @param map       数据来源\r\n @param beanClass bean类\r\n @return bean对象\r\n"}], "constructors": []}