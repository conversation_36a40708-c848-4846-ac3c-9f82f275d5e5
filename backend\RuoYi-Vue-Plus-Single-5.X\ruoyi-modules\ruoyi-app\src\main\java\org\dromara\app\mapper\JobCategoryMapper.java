package org.dromara.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.dromara.app.domain.JobCategory;
import org.dromara.app.domain.vo.JobCategoryVo;

import java.util.List;

/**
 * 岗位分类Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Mapper
public interface JobCategoryMapper extends BaseMapper<JobCategory> {

    /**
     * 查询岗位分类列表（包含岗位数量）
     *
     * @param includeJobCount 是否包含岗位数量
     * @return 分类列表
     */
    @Select({
        "<script>",
        "SELECT c.*, ",
        "<if test='includeJobCount'>",
        "(SELECT COUNT(*) FROM app_job j WHERE j.category_id = c.id AND j.del_flag = '0' AND j.status = '0') as job_count",
        "</if>",
        "<if test='!includeJobCount'>",
        "0 as job_count",
        "</if>",
        "FROM app_job_category c ",
        "WHERE c.del_flag = '0' AND c.status = '0' ",
        "ORDER BY c.sort_order ASC, c.create_time ASC",
        "</script>"
    })
    List<JobCategoryVo> selectCategoriesWithJobCount(@Param("includeJobCount") Boolean includeJobCount);

}
