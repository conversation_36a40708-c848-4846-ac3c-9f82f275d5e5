{"doc": "\n 通用 字典服务\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "SEPARATOR", "doc": "\n 分隔符\r\n"}], "enumConstants": [], "methods": [{"name": "getDictLabel", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 根据字典类型和字典值获取字典标签\r\n\r\n @param dictType  字典类型\r\n @param dictValue 字典值\r\n @return 字典标签\r\n"}, {"name": "getDictValue", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 根据字典类型和字典标签获取字典值\r\n\r\n @param dictType  字典类型\r\n @param dictLabel 字典标签\r\n @return 字典值\r\n"}, {"name": "getDictLabel", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 根据字典类型和字典值获取字典标签\r\n\r\n @param dictType  字典类型\r\n @param dictValue 字典值\r\n @param separator 分隔符\r\n @return 字典标签\r\n"}, {"name": "getDictValue", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 根据字典类型和字典标签获取字典值\r\n\r\n @param dictType  字典类型\r\n @param dictLabel 字典标签\r\n @param separator 分隔符\r\n @return 字典值\r\n"}, {"name": "getAllDictByDictType", "paramTypes": ["java.lang.String"], "doc": "\n 获取字典下所有的字典值与标签\r\n\r\n @param dictType 字典类型\r\n @return dictValue为key，dictLabel为值组成的Map\r\n"}, {"name": "getDictType", "paramTypes": ["java.lang.String"], "doc": "\n 根据字典类型查询详细信息\r\n\r\n @param dictType 字典类型\r\n @return 字典类型详细信息\r\n"}, {"name": "getDictData", "paramTypes": ["java.lang.String"], "doc": "\n 根据字典类型查询字典数据列表\r\n\r\n @param dictType 字典类型\r\n @return 字典数据列表\r\n"}], "constructors": []}