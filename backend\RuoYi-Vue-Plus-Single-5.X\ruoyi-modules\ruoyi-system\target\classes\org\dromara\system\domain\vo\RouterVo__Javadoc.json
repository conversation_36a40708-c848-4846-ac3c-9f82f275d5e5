{"doc": "\n 路由配置信息\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "name", "doc": "\n 路由名字\r\n"}, {"name": "path", "doc": "\n 路由地址\r\n"}, {"name": "hidden", "doc": "\n 是否隐藏路由，当设置 true 的时候该路由不会再侧边栏出现\r\n"}, {"name": "redirect", "doc": "\n 重定向地址，当设置 noRedirect 的时候该路由在面包屑导航中不可被点击\r\n"}, {"name": "component", "doc": "\n 组件地址\r\n"}, {"name": "query", "doc": "\n 路由参数：如 {\"id\": 1, \"name\": \"ry\"}\r\n"}, {"name": "alwaysShow", "doc": "\n 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面\r\n"}, {"name": "meta", "doc": "\n 其他元素\r\n"}, {"name": "children", "doc": "\n 子路由\r\n"}], "enumConstants": [], "methods": [], "constructors": []}