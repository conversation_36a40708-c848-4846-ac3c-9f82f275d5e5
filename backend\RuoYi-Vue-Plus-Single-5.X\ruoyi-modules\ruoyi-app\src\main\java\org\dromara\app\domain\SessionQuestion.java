package org.dromara.app.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;
import java.util.List;

/**
 * 面试会话问题对象 app_session_question
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_session_question")
public class SessionQuestion extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 记录ID
     */
    @TableId(value = "id")
    private String id;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 问题ID（唯一标识）
     */
    private String questionId;

    /**
     * 问题内容
     */
    private String content;

    /**
     * 问题类型（technical, behavioral, project, case）
     */
    private String type;

    /**
     * 难度等级（easy, medium, hard）
     */
    private String difficulty;

    /**
     * 问题分类
     */
    private String category;

    /**
     * 子分类
     */
    private String subcategory;

    /**
     * 时间限制（秒）
     */
    private Integer timeLimit;

    /**
     * 问题顺序
     */
    private Integer questionOrder;

    /**
     * 标签（JSON数组）
     */
    private List<String> tags;

    /**
     * 问题状态（pending, current, answered, skipped）
     */
    private String status;

    /**
     * 参考答案要点（JSON数组）
     */
    private List<String> answerPoints;

    /**
     * 提示信息（JSON数组）
     */
    private List<String> hints;

    /**
     * 评分标准（JSON格式）
     */
    private String scoringCriteria;

    /**
     * 是否为自定义问题
     */
    private Boolean isCustom;

    /**
     * 问题来源（system, custom, ai_generated）
     */
    private String source;

    /**
     * 元数据（JSON格式）
     */
    private String metadata;
}
