package org.dromara.app.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 视频播放进度对象 app_video_progress
 *
 * <AUTHOR>
 */
@Data
@TableName("app_video_progress")
public class VideoProgress {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 记录ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 视频ID
     */
    private Long videoId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 当前播放时间(秒)
     */
    private Integer currentTime;

    /**
     * 视频总时长(秒)
     */
    private Integer duration;

    /**
     * 完成率
     */
    private BigDecimal completionRate;

    /**
     * 是否完成(0-否 1-是)
     */
    private Integer isCompleted;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
