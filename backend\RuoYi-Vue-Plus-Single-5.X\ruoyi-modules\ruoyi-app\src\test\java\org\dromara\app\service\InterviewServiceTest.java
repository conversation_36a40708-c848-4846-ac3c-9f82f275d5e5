package org.dromara.app.service;

import org.dromara.app.domain.Job;
import org.dromara.app.domain.InterviewQuestion;
import org.dromara.app.domain.vo.InterviewResponseVo;
import org.dromara.app.mapper.JobMapper;
import org.dromara.app.mapper.InterviewQuestionMapper;
import org.dromara.app.service.impl.InterviewServiceImpl;
import org.dromara.common.core.exception.ServiceException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 面试服务测试类
 * 
 * <AUTHOR> Assistant
 * @date 2025-07-26
 */
@ExtendWith(MockitoExtension.class)
class InterviewServiceTest {

    @Mock
    private JobMapper jobMapper;

    @Mock
    private InterviewQuestionMapper interviewQuestionMapper;

    @InjectMocks
    private InterviewServiceImpl interviewService;

    private Job testJob;
    private List<InterviewQuestion> testQuestions;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        testJob = new Job();
        testJob.setId(101L);
        testJob.setName("前端开发工程师");
        testJob.setCompany("腾讯科技");

        // 准备测试问题
        testQuestions = new ArrayList<>();
        
        InterviewQuestion question1 = new InterviewQuestion();
        question1.setId(1L);
        question1.setJobId(101L);
        question1.setQuestion("请解释Vue.js的响应式原理");
        question1.setQuestionType("open");
        question1.setDifficulty(3);
        question1.setCategory("技术基础");
        question1.setTags(Arrays.asList("Vue.js", "响应式", "原理"));
        question1.setTimeLimit(600);
        testQuestions.add(question1);

        InterviewQuestion question2 = new InterviewQuestion();
        question2.setId(2L);
        question2.setJobId(null); // 通用问题
        question2.setQuestion("请简单介绍一下你自己");
        question2.setQuestionType("open");
        question2.setDifficulty(1);
        question2.setCategory("自我介绍");
        question2.setTags(Arrays.asList("自我介绍", "背景", "经历"));
        question2.setTimeLimit(300);
        testQuestions.add(question2);
    }

    @Test
    void testGetSampleQuestions_Success() {
        // Given
        Long jobId = 101L;
        Integer count = 5;
        
        when(jobMapper.selectById(jobId)).thenReturn(testJob);
        when(interviewQuestionMapper.selectByJobId(eq(jobId), isNull(), eq(count)))
            .thenReturn(testQuestions);

        // When
        InterviewResponseVo.SampleQuestionsResponse response = interviewService.getSampleQuestions(jobId, count);

        // Then
        assertNotNull(response);
        assertEquals(jobId, response.getJobId());
        assertEquals(2, response.getTotalCount());
        assertEquals(2, response.getQuestions().size());

        // 验证第一个问题
        InterviewResponseVo.QuestionInfo firstQuestion = response.getQuestions().get(0);
        assertEquals("1", firstQuestion.getQuestionId());
        assertEquals("请解释Vue.js的响应式原理", firstQuestion.getContent());
        assertEquals("技术基础", firstQuestion.getCategoryName());
        assertEquals("3", firstQuestion.getDifficulty());
        assertEquals("open", firstQuestion.getType());
        assertEquals(600, firstQuestion.getTimeLimit());
        assertEquals(3, firstQuestion.getTags().size());
        assertTrue(firstQuestion.getTags().contains("Vue.js"));

        // 验证第二个问题（通用问题）
        InterviewResponseVo.QuestionInfo secondQuestion = response.getQuestions().get(1);
        assertEquals("2", secondQuestion.getQuestionId());
        assertEquals("请简单介绍一下你自己", secondQuestion.getContent());
        assertEquals("自我介绍", secondQuestion.getCategoryName());
        assertEquals("1", secondQuestion.getDifficulty());

        // 验证方法调用
        verify(jobMapper, times(1)).selectById(jobId);
        verify(interviewQuestionMapper, times(1)).selectByJobId(eq(jobId), isNull(), eq(count));
    }

    @Test
    void testGetSampleQuestions_DefaultCount() {
        // Given
        Long jobId = 101L;
        Integer count = null; // 测试默认值
        
        when(jobMapper.selectById(jobId)).thenReturn(testJob);
        when(interviewQuestionMapper.selectByJobId(eq(jobId), isNull(), eq(5)))
            .thenReturn(testQuestions);

        // When
        InterviewResponseVo.SampleQuestionsResponse response = interviewService.getSampleQuestions(jobId, count);

        // Then
        assertNotNull(response);
        assertEquals(2, response.getTotalCount());
        
        // 验证使用了默认值 5
        verify(interviewQuestionMapper, times(1)).selectByJobId(eq(jobId), isNull(), eq(5));
    }

    @Test
    void testGetSampleQuestions_JobNotFound() {
        // Given
        Long jobId = 999L;
        Integer count = 5;
        
        when(jobMapper.selectById(jobId)).thenReturn(null);

        // When & Then
        ServiceException exception = assertThrows(ServiceException.class, () -> {
            interviewService.getSampleQuestions(jobId, count);
        });
        
        assertEquals("岗位不存在", exception.getMessage());
        
        // 验证没有调用问题查询
        verify(interviewQuestionMapper, never()).selectByJobId(any(), any(), any());
    }

    @Test
    void testGetSampleQuestions_EmptyQuestions() {
        // Given
        Long jobId = 101L;
        Integer count = 5;
        
        when(jobMapper.selectById(jobId)).thenReturn(testJob);
        when(interviewQuestionMapper.selectByJobId(eq(jobId), isNull(), eq(count)))
            .thenReturn(new ArrayList<>());

        // When
        InterviewResponseVo.SampleQuestionsResponse response = interviewService.getSampleQuestions(jobId, count);

        // Then
        assertNotNull(response);
        assertEquals(jobId, response.getJobId());
        assertEquals(0, response.getTotalCount());
        assertTrue(response.getQuestions().isEmpty());
    }

    @Test
    void testGetSampleQuestions_NullTags() {
        // Given
        Long jobId = 101L;
        Integer count = 5;
        
        InterviewQuestion questionWithNullTags = new InterviewQuestion();
        questionWithNullTags.setId(3L);
        questionWithNullTags.setJobId(101L);
        questionWithNullTags.setQuestion("测试问题");
        questionWithNullTags.setQuestionType("open");
        questionWithNullTags.setDifficulty(2);
        questionWithNullTags.setCategory("测试分类");
        questionWithNullTags.setTags(null); // 测试空标签
        questionWithNullTags.setTimeLimit(300);
        
        when(jobMapper.selectById(jobId)).thenReturn(testJob);
        when(interviewQuestionMapper.selectByJobId(eq(jobId), isNull(), eq(count)))
            .thenReturn(Arrays.asList(questionWithNullTags));

        // When
        InterviewResponseVo.SampleQuestionsResponse response = interviewService.getSampleQuestions(jobId, count);

        // Then
        assertNotNull(response);
        assertEquals(1, response.getQuestions().size());
        
        InterviewResponseVo.QuestionInfo question = response.getQuestions().get(0);
        assertNotNull(question.getTags());
        assertTrue(question.getTags().isEmpty());
    }

    @Test
    void testGetSampleQuestions_DatabaseException() {
        // Given
        Long jobId = 101L;
        Integer count = 5;
        
        when(jobMapper.selectById(jobId)).thenReturn(testJob);
        when(interviewQuestionMapper.selectByJobId(eq(jobId), isNull(), eq(count)))
            .thenThrow(new RuntimeException("数据库连接异常"));

        // When & Then
        ServiceException exception = assertThrows(ServiceException.class, () -> {
            interviewService.getSampleQuestions(jobId, count);
        });
        
        assertTrue(exception.getMessage().contains("获取示例问题失败"));
        assertTrue(exception.getMessage().contains("数据库连接异常"));
    }
}
