{"doc": "\n 对象工具类\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "notNullGetter", "paramTypes": ["java.lang.Object", "java.util.function.Function"], "doc": "\n 如果对象不为空，则获取对象中的某个字段 ObjectUtils.notNullGetter(user, User::getName);\r\n\r\n @param obj  对象\r\n @param func 获取方法\r\n @return 对象字段\r\n"}, {"name": "notNullGetter", "paramTypes": ["java.lang.Object", "java.util.function.Function", "java.lang.Object"], "doc": "\n 如果对象不为空，则获取对象中的某个字段，否则返回默认值\r\n\r\n @param obj          对象\r\n @param func         获取方法\r\n @param defaultValue 默认值\r\n @return 对象字段\r\n"}, {"name": "notNull", "paramTypes": ["java.lang.Object", "java.lang.Object"], "doc": "\n 如果值不为空，则返回值，否则返回默认值\r\n\r\n @param obj          对象\r\n @param defaultValue 默认值\r\n @return 对象字段\r\n"}], "constructors": []}