package org.dromara.app.exception;

import org.dromara.app.domain.enums.ErrorCode;

/**
 * 聊天相关异常
 *
 * <AUTHOR>
 */
public class ChatException extends BaseBusinessException {

    private final String sessionId;

    public ChatException(ErrorCode errorCode) {
        super(errorCode);
        this.sessionId = null;
    }

    public ChatException(ErrorCode errorCode, String sessionId) {
        super(errorCode);
        this.sessionId = sessionId;
    }

    public ChatException(ErrorCode errorCode, String sessionId, String message) {
        super(errorCode, message);
        this.sessionId = sessionId;
    }

    public ChatException(ErrorCode errorCode, String sessionId, Throwable cause) {
        super(errorCode, cause);
        this.sessionId = sessionId;
    }

    public static ChatException sessionNotFound(String sessionId) {
        return new ChatException(ErrorCode.CHAT_SESSION_NOT_FOUND, sessionId);
    }

    public static ChatException sessionExpired(String sessionId) {
        return new ChatException(ErrorCode.CHAT_SESSION_EXPIRED, sessionId);
    }

    public static ChatException messageEmpty() {
        return new ChatException(ErrorCode.CHAT_MESSAGE_EMPTY);
    }

    public static ChatException messageTooLong() {
        return new ChatException(ErrorCode.CHAT_MESSAGE_TOO_LONG);
    }

    public static ChatException serviceError(Throwable cause) {
        return new ChatException(ErrorCode.CHAT_SERVICE_ERROR, null, cause);
    }

    public static ChatException rateLimitExceeded() {
        return new ChatException(ErrorCode.CHAT_RATE_LIMIT_EXCEEDED);
    }

    public String getSessionId() {
        return sessionId;
    }
}
