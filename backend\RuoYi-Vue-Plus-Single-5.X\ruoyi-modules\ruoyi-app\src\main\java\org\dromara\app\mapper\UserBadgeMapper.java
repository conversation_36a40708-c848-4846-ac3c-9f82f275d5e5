package org.dromara.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.dromara.app.domain.UserBadge;
import org.dromara.app.domain.vo.BadgeVo;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户徽章数据层
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
public interface UserBadgeMapper extends BaseMapper<UserBadge> {

    /**
     * 获取用户所有徽章
     *
     * @param userId 用户ID
     * @return 徽章列表
     */
    List<BadgeVo> selectUserBadges(@Param("userId") String userId);

    /**
     * 获取用户徽章详情
     *
     * @param userId  用户ID
     * @param badgeId 徽章ID
     * @return 徽章详情
     */
    BadgeVo selectUserBadgeById(@Param("userId") String userId, @Param("badgeId") String badgeId);

    /**
     * 获取用户置顶徽章数量
     *
     * @param userId 用户ID
     * @return 置顶徽章数量
     */
    long countPinnedBadges(@Param("userId") String userId);

    /**
     * 更新徽章置顶状态
     *
     * @param userId   用户ID
     * @param badgeId  徽章ID
     * @param isPinned 是否置顶
     * @param pinnedAt 置顶时间
     * @return 更新行数
     */
    int updatePinStatus(@Param("userId") String userId, @Param("badgeId") String badgeId,
                        @Param("isPinned") Boolean isPinned, @Param("pinnedAt") LocalDateTime pinnedAt);

    /**
     * 获取用户置顶徽章
     *
     * @param userId 用户ID
     * @return 置顶徽章列表
     */
    List<BadgeVo> selectPinnedBadges(@Param("userId") String userId);

    /**
     * 获取总徽章数
     *
     * @return 总徽章数
     */
    int countTotalBadges();

    /**
     * 获取用户已解锁徽章数
     *
     * @param userId 用户ID
     * @return 已解锁徽章数
     */
    int countUnlockedBadges(@Param("userId") String userId);

    /**
     * 根据用户ID和徽章ID获取用户徽章
     *
     * @param userId  用户ID
     * @param badgeId 徽章ID
     * @return 用户徽章
     */
    UserBadge selectByUserIdAndBadgeId(@Param("userId") String userId, @Param("badgeId") String badgeId);

    /**
     * 获取用户最近解锁的徽章
     *
     * @param userId 用户ID
     * @param limit  数量限制
     * @return 最近解锁的徽章
     */
    List<BadgeVo> selectRecentUnlockedBadges(@Param("userId") String userId, @Param("limit") int limit);

    /**
     * 更新徽章通知状态
     *
     * @param userId 用户ID
     * @param status 状态（0=未通知，1=已通知）
     * @return 更新行数
     */
    int updateNotificationStatus(@Param("userId") String userId, @Param("status") int status);

    /**
     * 标记徽章为已查看
     *
     * @param userId  用户ID
     * @param badgeId 徽章ID
     * @return 更新行数
     */
    int markAsViewed(@Param("userId") String userId, @Param("badgeId") String badgeId);

    /**
     * 获取用户某类别已解锁徽章数
     *
     * @param userId   用户ID
     * @param category 类别
     * @return 已解锁徽章数
     */
    int countUnlockedBadgesByCategory(@Param("userId") String userId, @Param("category") String category);

    /**
     * 获取用户某稀有度已解锁徽章数
     *
     * @param userId 用户ID
     * @param rarity 稀有度
     * @return 已解锁徽章数
     */
    int countUnlockedBadgesByRarity(@Param("userId") String userId, @Param("rarity") String rarity);
}
