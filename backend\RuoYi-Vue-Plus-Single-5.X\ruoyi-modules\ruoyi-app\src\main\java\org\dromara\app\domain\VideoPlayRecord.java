package org.dromara.app.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.time.LocalDateTime;

/**
 * 视频播放记录对象 video_play_record
 *
 * <AUTHOR>
 */
@Data
@TableName("video_play_record")
public class VideoPlayRecord {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 播放记录ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 视频ID
     */
    private Long videoId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 最后播放时间
     */
    private LocalDateTime lastPlayTime;

    /**
     * 播放总次数
     */
    private Integer playCount;

    /**
     * 播放总时长（秒）
     */
    private Integer totalPlayDuration;
}
