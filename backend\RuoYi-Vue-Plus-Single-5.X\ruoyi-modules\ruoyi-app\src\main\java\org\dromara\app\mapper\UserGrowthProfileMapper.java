package org.dromara.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.dromara.app.domain.UserGrowthProfile;

import java.util.List;

/**
 * 用户成长档案Mapper接口
 *
 * <AUTHOR>
 */
public interface UserGrowthProfileMapper extends BaseMapper<UserGrowthProfile> {

    /**
     * 根据用户ID查询成长档案
     *
     * @param userId 用户ID
     * @return 成长档案
     */
    UserGrowthProfile selectProfileByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID更新成长档案
     *
     * @param profile 成长档案
     * @return 更新数量
     */
    int updateProfileByUserId(UserGrowthProfile profile);

    /**
     * 根据用户ID删除成长档案
     *
     * @param userId 用户ID
     * @return 删除数量
     */
    int deleteByUserId(@Param("userId") Long userId);

    /**
     * 根据当前阶段查询用户列表
     *
     * @param currentStage 当前阶段
     * @return 用户成长档案列表
     */
    List<UserGrowthProfile> selectProfilesByCurrentStage(@Param("currentStage") String currentStage);

    /**
     * 查询所有用户的成长档案统计信息
     *
     * @return 成长档案统计信息
     */
    List<UserGrowthProfile> selectProfilesStatistics();

    /**
     * 根据用户ID更新最后活跃时间
     *
     * @param userId 用户ID
     * @return 更新数量
     */
    int updateLastActiveTimeByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID增加面试次数
     *
     * @param userId    用户ID
     * @param increment 增加数量
     * @return 更新数量
     */
    int incrementInterviewCountByUserId(@Param("userId") Long userId, @Param("increment") Integer increment);

    /**
     * 根据用户ID更新连续学习天数
     *
     * @param userId 用户ID
     * @param days   连续学习天数
     * @return 更新数量
     */
    int updateContinuousLearningDaysByUserId(@Param("userId") Long userId, @Param("days") Integer days);

    /**
     * 根据用户ID增加完成课程数
     *
     * @param userId    用户ID
     * @param increment 增加数量
     * @return 更新数量
     */
    int incrementCompletedCoursesByUserId(@Param("userId") Long userId, @Param("increment") Integer increment);
}
