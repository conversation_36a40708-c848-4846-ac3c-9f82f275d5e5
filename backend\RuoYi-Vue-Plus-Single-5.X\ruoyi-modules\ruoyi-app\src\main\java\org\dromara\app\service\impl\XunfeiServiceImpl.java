package org.dromara.app.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.config.XunfeiConfig;
import org.dromara.app.service.IXunfeiService;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import okhttp3.*;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 讯飞AI服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class XunfeiServiceImpl implements IXunfeiService {

    private final XunfeiConfig xunfeiConfig;

    /**
     * 智能体上下文
     */
    public static class AgentContext {
        private String systemPrompt;
        private List<Map<String, String>> history;
        private String userId;

        public String getSystemPrompt() {
            return systemPrompt;
        }

        public void setSystemPrompt(String systemPrompt) {
            this.systemPrompt = systemPrompt;
        }

        public List<Map<String, String>> getHistory() {
            return history;
        }

        public void setHistory(List<Map<String, String>> history) {
            this.history = history;
        }

        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }
    }

    /**
     * 流式聊天响应处理器
     */
    public interface StreamingChatResponseHandler {
        /**
         * 收到响应片段
         *
         * @param token 文本片段
         */
        void onResponse(String token);

        /**
         * 完成回调
         *
         * @param fullResponse 完整响应
         */
        void onComplete(String fullResponse);

        /**
         * 错误回调
         *
         * @param throwable 异常信息
         */
        void onError(Throwable throwable);
    }

    /**
     * 智能体spark
     */

    @Override
    public String sparkChat(String message, Map<String, Object> context) {
        try {
            log.info("调用讯飞星火大模型，消息长度: {}", message.length());

            // 构建请求参数
            JSONObject requestBody = buildSparkChatRequest(message, context);

            // 生成认证头
            Map<String, String> headers = generateSparkAuthHeaders();

            // 发送HTTP请求
            HttpResponse response = HttpRequest.post(xunfeiConfig.getSpark().getBaseUrl())
                .headerMap(headers, true)
                .body(requestBody.toString())
                .timeout(xunfeiConfig.getSpark().getReadTimeout() * 1000)
                .execute();

            if (response.isOk()) {
                JSONObject responseJson = JSONUtil.parseObj(response.body());
                return extractSparkResponse(responseJson);
            } else {
                log.error("讯飞星火大模型调用失败，状态码: {}, 响应: {}", response.getStatus(), response.body());
                return "抱歉，AI服务暂时不可用，请稍后再试。";
            }

        } catch (Exception e) {
            log.error("调用讯飞星火大模型异常", e);
            return "抱歉，处理您的请求时发生了错误。";
        }
    }

    @Override
    public void sparkChatStream(String message, Map<String, Object> context, StreamCallback callback) {
        CompletableFuture.runAsync(() -> {
            try {
                log.info("调用讯飞星火大模型流式接口，消息长度: {}", message.length());

                // 构建请求参数
                JSONObject requestBody = buildSparkChatRequest(message, context);
                requestBody.put("stream", true);

                // 生成认证头
                Map<String, String> headers = generateSparkAuthHeaders();

                // 使用OkHttp进行真正的流式请求
                performRealStreamRequest(requestBody.toString(), headers, callback);

            } catch (Exception e) {
                log.error("讯飞星火大模型流式调用异常", e);
                callback.onError(e);
            }
        });
    }

    @Override
    public void sparkChatAgent(String message, String agentType, Map<String, Object> context, StreamCallback callback) {
        CompletableFuture.runAsync(() -> {
            try {
                log.info("调用讯飞星火智能体流式服务，角色类型: {}, 消息长度: {}", agentType, message.length());

                // 构建请求参数
                JSONObject requestBody = buildSparkChatAgentRequest(message, agentType, context);
                requestBody.put("stream", true);

                // 生成认证头
                Map<String, String> headers = generateSparkAuthHeaders();

                // 使用OkHttp进行真正的流式请求
                performRealStreamRequest(requestBody.toString(), headers, callback);

            } catch (Exception e) {
                log.error("讯飞星火智能体流式调用异常", e);
                callback.onError(e);
            }
        });
    }

    /**
     * 调用智能体服务（非流式）
     */
    @Override
    public String sparkChatAgentSync(String message, String agentType, Map<String, Object> context) {
        try {
            log.info("调用讯飞星火智能体服务，角色类型: {}, 消息长度: {}", agentType, message.length());

            // 构建请求参数
            JSONObject requestBody = buildSparkChatAgentRequest(message, agentType, context);

            // 生成认证头
            Map<String, String> headers = generateSparkAuthHeaders();

            // 发送HTTP请求
            HttpResponse response = HttpRequest.post(xunfeiConfig.getSpark().getBaseUrl())
                .headerMap(headers, true)
                .body(requestBody.toString())
                .timeout(xunfeiConfig.getSpark().getReadTimeout() * 1000)
                .execute();

            if (response.isOk()) {
                JSONObject responseJson = JSONUtil.parseObj(response.body());
                return extractSparkResponse(responseJson);
            } else {
                log.error("讯飞星火智能体调用失败，状态码: {}, 响应: {}", response.getStatus(), response.body());
                return "抱歉，AI智能体服务暂时不可用，请稍后再试。";
            }

        } catch (Exception e) {
            log.error("调用讯飞星火智能体异常", e);
            return "抱歉，处理您的请求时发生了错误。";
        }
    }

    @Override
    public SpeechRecognitionResult speechRecognition(MultipartFile audioFile) {
        try {
            log.info("调用讯飞语音识别，文件大小: {} bytes", audioFile.getSize());

            if (audioFile.isEmpty()) {
                return SpeechRecognitionResult.error("音频文件为空");
            }

            // 验证文件格式
            String contentType = audioFile.getContentType();
            if (!isValidAudioFormat(contentType)) {
                return SpeechRecognitionResult.error("不支持的音频格式: " + contentType);
            }

            // 构建请求参数
            JSONObject requestBody = buildSpeechRecognitionRequest(audioFile);

            // 生成认证头
            Map<String, String> headers = generateAuthHeaders(
                xunfeiConfig.getSpeech().getAppId(),
                xunfeiConfig.getSpeech().getApiKey(),
                xunfeiConfig.getSpeech().getApiSecret()
            );

            // 发送HTTP请求
            HttpResponse response = HttpRequest.post(xunfeiConfig.getSpeech().getBaseUrl())
                .headerMap(headers, true)
                .body(requestBody.toString())
                .timeout(30000)
                .execute();

            if (response.isOk()) {
                JSONObject responseJson = JSONUtil.parseObj(response.body());
                return parseSpeechRecognitionResponse(responseJson);
            } else {
                log.error("讯飞语音识别调用失败，状态码: {}, 响应: {}", response.getStatus(), response.body());
                return SpeechRecognitionResult.error("语音识别服务调用失败");
            }

        } catch (Exception e) {
            log.error("讯飞语音识别异常", e);
            return SpeechRecognitionResult.error("语音识别处理异常: " + e.getMessage());
        }
    }

    @Override
    public SpeechRecognitionResult speechRecognition(String audioUrl) {
        try {
            log.info("通过URL调用讯飞语音识别: {}", audioUrl);

            // 下载音频文件
            byte[] audioData = downloadAudioFromUrl(audioUrl);
            if (audioData == null || audioData.length == 0) {
                return SpeechRecognitionResult.error("无法下载音频文件");
            }

            // 构建请求参数
            JSONObject requestBody = buildSpeechRecognitionRequest(audioData);

            // 生成认证头
            Map<String, String> headers = generateAuthHeaders(
                xunfeiConfig.getSpeech().getAppId(),
                xunfeiConfig.getSpeech().getApiKey(),
                xunfeiConfig.getSpeech().getApiSecret()
            );

            // 发送HTTP请求
            HttpResponse response = HttpRequest.post(xunfeiConfig.getSpeech().getBaseUrl())
                .headerMap(headers, true)
                .body(requestBody.toString())
                .timeout(30000)
                .execute();

            if (response.isOk()) {
                JSONObject responseJson = JSONUtil.parseObj(response.body());
                return parseSpeechRecognitionResponse(responseJson);
            } else {
                log.error("讯飞语音识别调用失败，状态码: {}, 响应: {}", response.getStatus(), response.body());
                return SpeechRecognitionResult.error("语音识别服务调用失败");
            }

        } catch (Exception e) {
            log.error("通过URL进行语音识别异常", e);
            return SpeechRecognitionResult.error("语音识别处理异常: " + e.getMessage());
        }
    }

    @Override
    public EmotionAnalysisResult emotionAnalysis(String text) {
        try {
            log.info("调用讯飞情感分析，文本长度: {}", text.length());

            if (StrUtil.isBlank(text)) {
                return EmotionAnalysisResult.error("待分析文本为空");
            }

            // 构建请求参数
            JSONObject requestBody = buildEmotionAnalysisRequest(text);

            // 生成认证头
            Map<String, String> headers = generateAuthHeaders(
                xunfeiConfig.getEmotion().getAppId(),
                xunfeiConfig.getEmotion().getApiKey(),
                xunfeiConfig.getEmotion().getApiSecret()
            );

            // 发送HTTP请求
            HttpResponse response = HttpRequest.post(xunfeiConfig.getEmotion().getBaseUrl())
                .headerMap(headers, true)
                .body(requestBody.toString())
                .timeout(15000)
                .execute();

            if (response.isOk()) {
                JSONObject responseJson = JSONUtil.parseObj(response.body());
                return parseEmotionAnalysisResponse(responseJson);
            } else {
                log.error("讯飞情感分析调用失败，状态码: {}, 响应: {}", response.getStatus(), response.body());
                return EmotionAnalysisResult.error("情感分析服务调用失败");
            }

        } catch (Exception e) {
            log.error("讯飞情感分析异常", e);
            return EmotionAnalysisResult.error("情感分析处理异常: " + e.getMessage());
        }
    }

    @Override
    public VoiceEmotionResult voiceEmotionAnalysis(MultipartFile audioFile) {
        try {
            log.info("调用讯飞语音情感分析，文件大小: {} bytes", audioFile.getSize());

            // 先进行语音识别
            SpeechRecognitionResult speechResult = speechRecognition(audioFile);
            if (!speechResult.isSuccess()) {
                return VoiceEmotionResult.error("语音识别失败: " + speechResult.getErrorMessage());
            }

            // 对识别出的文本进行情感分析
            EmotionAnalysisResult textEmotionResult = emotionAnalysis(speechResult.getText());
            if (!textEmotionResult.isSuccess()) {
                return VoiceEmotionResult.error("文本情感分析失败: " + textEmotionResult.getErrorMessage());
            }

            // 分析语音特征（简化实现）
            Map<String, Double> voiceFeatures = analyzeVoiceFeatures(audioFile);

            // 综合分析结果
            String overallEmotion = combineEmotionResults(textEmotionResult, voiceFeatures);
            Double confidence = calculateOverallConfidence(textEmotionResult.getConfidence(), voiceFeatures);

            return VoiceEmotionResult.success(
                speechResult.getText(),
                textEmotionResult,
                voiceFeatures,
                overallEmotion,
                confidence
            );

        } catch (Exception e) {
            log.error("讯飞语音情感分析异常", e);
            return VoiceEmotionResult.error("语音情感分析处理异常: " + e.getMessage());
        }
    }

    @Override
    public byte[] textToSpeech(String text, VoiceConfig voiceConfig) {
        try {
            log.info("调用讯飞语音合成，文本长度: {}", text.length());

            if (StrUtil.isBlank(text)) {
                throw new IllegalArgumentException("待合成文本为空");
            }

            // 构建请求参数
            JSONObject requestBody = buildTextToSpeechRequest(text, voiceConfig);

            // 生成认证头
            Map<String, String> headers = generateAuthHeaders(
                xunfeiConfig.getTts().getAppId(),
                xunfeiConfig.getTts().getApiKey(),
                xunfeiConfig.getTts().getApiSecret()
            );

            // 发送HTTP请求
            HttpResponse response = HttpRequest.post(xunfeiConfig.getTts().getBaseUrl())
                .headerMap(headers, true)
                .body(requestBody.toString())
                .timeout(30000)
                .execute();

            if (response.isOk()) {
                // 返回音频数据
                return response.bodyBytes();
            } else {
                log.error("讯飞语音合成调用失败，状态码: {}, 响应: {}", response.getStatus(), response.body());
                throw new RuntimeException("语音合成服务调用失败");
            }

        } catch (Exception e) {
            log.error("讯飞语音合成异常", e);
            throw new RuntimeException("语音合成处理异常: " + e.getMessage());
        }
    }

    // ========== 私有辅助方法 ==========

    /**
     * 使用OkHttp执行真正的流式请求
     */
    private void performRealStreamRequest(String requestBody, Map<String, String> headers, StreamCallback callback) {
        try {
            // 创建OkHttp客户端
            OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(300, TimeUnit.SECONDS) // 5分钟读取超时
                .writeTimeout(30, TimeUnit.SECONDS)
                .build();

            // 构建请求体
            RequestBody body = RequestBody.create(
                requestBody,
                MediaType.parse("application/json; charset=utf-8")
            );

            // 构建请求
            okhttp3.Request.Builder requestBuilder = new okhttp3.Request.Builder()
                .url(xunfeiConfig.getSpark().getBaseUrl())
                .post(body);

            // 添加请求头
            for (Map.Entry<String, String> header : headers.entrySet()) {
                requestBuilder.addHeader(header.getKey(), header.getValue());
            }

            okhttp3.Request request = requestBuilder.build();

            // 执行流式请求
            client.newCall(request).enqueue(new okhttp3.Callback() {
                @Override
                public void onFailure(okhttp3.Call call, IOException e) {
                    log.error("流式请求失败", e);
                    callback.onError(e);
                }

                @Override
                public void onResponse(okhttp3.Call call, okhttp3.Response response) throws IOException {
                    if (!response.isSuccessful()) {
                        String errorBody = response.body() != null ? response.body().string() : "Unknown error";
                        log.error("流式请求失败，状态码: {}, 响应: {}", response.code(), errorBody);
                        callback.onError(new RuntimeException("HTTP " + response.code() + ": " + errorBody));
                        return;
                    }

                    try (BufferedReader reader = new BufferedReader(
                            new InputStreamReader(response.body().byteStream(), StandardCharsets.UTF_8))) {

                        StringBuilder fullResponse = new StringBuilder();
                        String line;

                        while ((line = reader.readLine()) != null) {
                            if (line.startsWith("data: ")) {
                                String data = line.substring(6).trim();

                                // 检查是否为结束标记
                                if ("[DONE]".equals(data)) {
                                    callback.onComplete(fullResponse.toString());
                                    return;
                                }

                                try {
                                    JSONObject jsonData = JSONUtil.parseObj(data);

                                    // 检查错误
                                    if (jsonData.containsKey("code") && jsonData.getInt("code") != 0) {
                                        String errorMessage = jsonData.getStr("message", "未知错误");
                                        callback.onError(new RuntimeException("流式响应错误: " + errorMessage));
                                        return;
                                    }

                                    // 解析delta内容
                                    String token = extractTokenFromResponse(jsonData);
                                    if (StrUtil.isNotBlank(token)) {
                                        fullResponse.append(token);
                                        callback.onToken(token);
                                    }

                                } catch (Exception e) {
                                    log.warn("解析流式响应数据失败: {}", data, e);
                                }
                            }
                        }

                        // 如果没有收到[DONE]标记，也要调用完成回调
                        callback.onComplete(fullResponse.toString());

                    } catch (Exception e) {
                        log.error("处理流式响应失败", e);
                        callback.onError(e);
                    }
                }
            });

        } catch (Exception e) {
            log.error("创建流式请求失败", e);
            callback.onError(e);
        }
    }

    /**
     * 从响应JSON中提取token
     */
    private String extractTokenFromResponse(JSONObject jsonData) {
        try {
            if (jsonData.containsKey("choices")) {
                Object choicesObj = jsonData.get("choices");
                if (choicesObj instanceof List) {
                    @SuppressWarnings("unchecked")
                    List<Object> choices = (List<Object>) choicesObj;
                    if (!choices.isEmpty() && choices.get(0) instanceof Map) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> choice = (Map<String, Object>) choices.get(0);
                        if (choice.containsKey("delta")) {
                            @SuppressWarnings("unchecked")
                            Map<String, Object> delta = (Map<String, Object>) choice.get("delta");
                            if (delta.containsKey("content")) {
                                return delta.get("content").toString();
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.warn("提取token失败", e);
        }
        return null;
    }

    /**
     * 构建星火大模型智能体聊天请求（角色化）
     */
    private JSONObject buildSparkChatAgentRequest(String message, String agentType, Map<String, Object> context) {
        JSONObject request = new JSONObject();

        // 设置模型
        request.put("model", xunfeiConfig.getSpark().getModel());

        // 设置消息列表
        List<Map<String, String>> messages = new ArrayList<>();

        // 添加系统消息（定义智能体角色）
        Map<String, String> systemMsg = new HashMap<>();
        systemMsg.put("role", "system");

        // 根据不同的智能体类型设置不同的角色指令
        String roleInstruction;
        switch (agentType.toLowerCase()) {
            case "assistant":
                roleInstruction = "你是一个专业的助手，提供有用、准确、简洁的回答。";
                break;
            case "teacher":
                roleInstruction = "你是一位耐心的教师，擅长解释复杂概念并引导学习过程。请用教学的方式回答问题，提供例子和解释。";
                break;
            case "expert":
                roleInstruction = "你是该领域的专家，提供深入、专业的分析和见解。使用专业术语并展示你的专业知识。";
                break;
            case "creative":
                roleInstruction = "你是一个富有创造力的助手，提供创新、独特的想法和解决方案。思考要有跳跃性并提出新颖的观点。";
                break;
            case "interviewer":
                roleInstruction = "你是一名面试官，你的任务是评估候选人的能力和适合性。提出相关问题并深入探讨回答。";
                break;
            default:
                roleInstruction = "你是一个有帮助的助手，请提供清晰、准确的回答。";
        }

        // 添加自定义系统提示（如果有）
        if (context != null && context.containsKey("systemPrompt")) {
            roleInstruction += "\n" + context.get("systemPrompt").toString();
        }

        systemMsg.put("content", roleInstruction);
        messages.add(systemMsg);

        // 添加历史对话（如果有上下文）
        if (context != null && context.containsKey("history")) {
            List<Map<String, String>> history = (List<Map<String, String>>) context.get("history");
            if (history != null) {
                messages.addAll(history);
            }
        }

        // 添加用户消息
        Map<String, String> userMsg = new HashMap<>();
        userMsg.put("role", "user");
        userMsg.put("content", message);
        messages.add(userMsg);

        request.put("messages", messages);

        // 根据角色类型调整参数
        double temperature;
        int maxTokens;

        switch (agentType.toLowerCase()) {
            case "creative":
                temperature = Math.min(0.9, xunfeiConfig.getSpark().getTemperature() + 0.2);
                maxTokens = xunfeiConfig.getSpark().getMaxTokens();
                break;
            case "expert":
                temperature = Math.max(0.1, xunfeiConfig.getSpark().getTemperature() - 0.1);
                maxTokens = (int) (xunfeiConfig.getSpark().getMaxTokens() * 1.2);
                break;
            default:
                temperature = xunfeiConfig.getSpark().getTemperature();
                maxTokens = xunfeiConfig.getSpark().getMaxTokens();
        }

        request.put("temperature", temperature);
        request.put("max_tokens", maxTokens);

        // 添加用户ID（如果有）
        if (context != null && context.containsKey("userId")) {
            request.put("user", context.get("userId").toString());
        }

        return request;
    }

    /**
     * 调用流式Agent服务
     */
    private void invokeStreamingAgentService(AgentContext context, String enhancedMessage, String agentType,
                                             String provider, StreamingChatResponseHandler handler) {
        // 将StreamingChatResponseHandler适配为StreamCallback
        StreamCallback callback = new StreamCallback() {
            @Override
            public void onToken(String token) {
                handler.onResponse(token);
            }

            @Override
            public void onComplete(String fullResponse) {
                handler.onComplete(fullResponse);
            }

            @Override
            public void onError(Throwable throwable) {
                handler.onError(throwable);
            }
        };

        // 构建上下文参数
        Map<String, Object> contextMap = new HashMap<>();
        if (context != null) {
            // 添加系统提示
            if (context.getSystemPrompt() != null) {
                contextMap.put("systemPrompt", context.getSystemPrompt());
            }

            // 添加对话历史
            if (context.getHistory() != null) {
                contextMap.put("history", context.getHistory());
            }

            // 添加用户ID
            if (context.getUserId() != null) {
                contextMap.put("userId", context.getUserId());
            }
        }

        // 调用星火智能体服务
        sparkChatAgent(enhancedMessage, agentType, contextMap, callback);
    }

    /**
     * 构建星火大模型聊天请求（OpenAI兼容格式）
     */
    private JSONObject buildSparkChatRequest(String message, Map<String, Object> context) {
        JSONObject request = new JSONObject();

        // 设置模型
        request.put("model", xunfeiConfig.getSpark().getModel());

        // 设置消息列表
        List<Map<String, String>> messages = new ArrayList<>();

        // 添加系统消息（如果有上下文）
        if (context != null && context.containsKey("systemPrompt")) {
            Map<String, String> systemMsg = new HashMap<>();
            systemMsg.put("role", "system");
            systemMsg.put("content", context.get("systemPrompt").toString());
            messages.add(systemMsg);
        }

        // 添加历史对话（如果有上下文）
        if (context != null && context.containsKey("history")) {
            List<Map<String, String>> history = (List<Map<String, String>>) context.get("history");
            if (history != null) {
                messages.addAll(history);
            }
        }

        // 添加用户消息
        Map<String, String> userMsg = new HashMap<>();
        userMsg.put("role", "user");
        userMsg.put("content", message);
        messages.add(userMsg);

        request.put("messages", messages);

        // 设置可选参数
        request.put("temperature", xunfeiConfig.getSpark().getTemperature());
        request.put("max_tokens", xunfeiConfig.getSpark().getMaxTokens());

        // 添加用户ID（如果有）
        if (context != null && context.containsKey("userId")) {
            request.put("user", context.get("userId").toString());
        }

        return request;
    }

    /**
     * 构建语音识别请求
     */
    private JSONObject buildSpeechRecognitionRequest(MultipartFile audioFile) throws IOException {
        JSONObject request = new JSONObject();

        // 音频数据Base64编码
        byte[] audioData = audioFile.getBytes();
        String audioBase64 = Base64.getEncoder().encodeToString(audioData);

        JSONObject data = new JSONObject();
        data.put("audio", audioBase64);
        data.put("format", xunfeiConfig.getSpeech().getAudioFormat());
        data.put("rate", 16000);
        data.put("channel", 1);

        request.put("data", data);

        // 设置参数
        JSONObject parameter = new JSONObject();
        JSONObject result = new JSONObject();
        result.put("language", xunfeiConfig.getSpeech().getLanguage());
        result.put("domain", "iat");
        parameter.put("result", result);
        request.put("parameter", parameter);

        return request;
    }

    /**
     * 构建语音识别请求（字节数组）
     */
    private JSONObject buildSpeechRecognitionRequest(byte[] audioData) {
        JSONObject request = new JSONObject();

        // 音频数据Base64编码
        String audioBase64 = Base64.getEncoder().encodeToString(audioData);

        JSONObject data = new JSONObject();
        data.put("audio", audioBase64);
        data.put("format", xunfeiConfig.getSpeech().getAudioFormat());
        data.put("rate", 16000);
        data.put("channel", 1);

        request.put("data", data);

        // 设置参数
        JSONObject parameter = new JSONObject();
        JSONObject result = new JSONObject();
        result.put("language", xunfeiConfig.getSpeech().getLanguage());
        result.put("domain", "iat");
        parameter.put("result", result);
        request.put("parameter", parameter);

        return request;
    }

    /**
     * 构建情感分析请求
     */
    private JSONObject buildEmotionAnalysisRequest(String text) {
        JSONObject request = new JSONObject();

        JSONObject data = new JSONObject();
        data.put("text", text);
        request.put("data", data);

        JSONObject parameter = new JSONObject();
        JSONObject result = new JSONObject();
        result.put("type", xunfeiConfig.getEmotion().getAnalysisType());
        result.put("language", xunfeiConfig.getEmotion().getLanguage());
        parameter.put("result", result);
        request.put("parameter", parameter);

        return request;
    }

    /**
     * 构建语音合成请求
     */
    private JSONObject buildTextToSpeechRequest(String text, VoiceConfig voiceConfig) {
        JSONObject request = new JSONObject();

        JSONObject data = new JSONObject();
        data.put("text", Base64.getEncoder().encodeToString(text.getBytes(StandardCharsets.UTF_8)));
        request.put("data", data);

        JSONObject parameter = new JSONObject();
        JSONObject result = new JSONObject();
        result.put("voice_name", voiceConfig != null ? voiceConfig.getVoiceName() : xunfeiConfig.getTts().getVoiceName());
        result.put("speed", voiceConfig != null ? voiceConfig.getSpeed() : xunfeiConfig.getTts().getSpeed());
        result.put("volume", voiceConfig != null ? voiceConfig.getVolume() : xunfeiConfig.getTts().getVolume());
        result.put("pitch", voiceConfig != null ? voiceConfig.getPitch() : xunfeiConfig.getTts().getPitch());
        result.put("audio_format", voiceConfig != null ? voiceConfig.getAudioFormat() : xunfeiConfig.getTts().getAudioFormat());
        parameter.put("result", result);
        request.put("parameter", parameter);

        return request;
    }

    /**
     * 生成星火大模型认证头（HTTP Bearer Token）
     */
    private Map<String, String> generateSparkAuthHeaders() {
        Map<String, String> headers = new HashMap<>();

        headers.put("Content-Type", "application/json");
        headers.put("Authorization", "Bearer " + xunfeiConfig.getSpark().getApiPassword());

        return headers;
    }

    /**
     * 生成认证头（其他API使用的签名认证）
     */
    private Map<String, String> generateAuthHeaders(String appId, String apiKey, String apiSecret) {
        Map<String, String> headers = new HashMap<>();

        try {
            // 生成时间戳
            String timestamp = String.valueOf(System.currentTimeMillis() / 1000);

            // 生成签名
            String signature = generateSignature(apiKey, apiSecret, timestamp);

            headers.put("Content-Type", "application/json");
            headers.put("X-Appid", appId);
            headers.put("X-CurTime", timestamp);
            headers.put("X-Param", Base64.getEncoder().encodeToString("{}".getBytes(StandardCharsets.UTF_8)));
            headers.put("X-CheckSum", signature);

        } catch (Exception e) {
            log.error("生成认证头失败", e);
        }

        return headers;
    }

    /**
     * 生成签名
     */
    private String generateSignature(String apiKey, String apiSecret, String timestamp) throws Exception {
        String signStr = apiKey + timestamp;
        Mac mac = Mac.getInstance("HmacSHA1");
        SecretKeySpec spec = new SecretKeySpec(apiSecret.getBytes(StandardCharsets.UTF_8), "HmacSHA1");
        mac.init(spec);
        byte[] hexDigits = mac.doFinal(signStr.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(hexDigits);
    }

    /**
     * 提取星火大模型响应（OpenAI兼容格式）
     */
    private String extractSparkResponse(JSONObject responseJson) {
        try {
            // 检查错误码
            if (responseJson.containsKey("code") && responseJson.getInt("code") != 0) {
                String errorMessage = responseJson.getStr("message", "未知错误");
                log.error("星火大模型返回错误: {}", errorMessage);
                return "抱歉，AI服务返回错误: " + errorMessage;
            }

            // 解析choices数组
            if (responseJson.containsKey("choices")) {
                Object choicesObj = responseJson.get("choices");
                if (choicesObj instanceof List) {
                    @SuppressWarnings("unchecked")
                    List<Object> choices = (List<Object>) choicesObj;
                    if (!choices.isEmpty() && choices.get(0) instanceof Map) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> firstChoice = (Map<String, Object>) choices.get(0);
                        if (firstChoice.containsKey("message")) {
                            @SuppressWarnings("unchecked")
                            Map<String, Object> message = (Map<String, Object>) firstChoice.get("message");
                            if (message.containsKey("content")) {
                                return message.get("content").toString();
                            }
                        }
                    }
                }
            }

            // 如果无法解析，返回默认响应
            log.warn("无法解析星火大模型响应: {}", responseJson.toString());
            return "抱歉，我无法理解您的问题，请重新表述。";

        } catch (Exception e) {
            log.error("解析星火大模型响应失败", e);
            return "抱歉，处理响应时发生了错误。";
        }
    }

    /**
     * 解析语音识别响应
     */
    private SpeechRecognitionResult parseSpeechRecognitionResponse(JSONObject responseJson) {
        try {
            if (responseJson.getInt("code") == 0) {
                JSONObject data = responseJson.getJSONObject("data");
                String text = data.getStr("result");
                String language = data.getStr("language", "zh-cn");
                Double confidence = data.getDouble("confidence", 0.9);
                Long duration = data.getLong("duration", 0L);

                return SpeechRecognitionResult.success(text, language, confidence, duration);
            } else {
                String errorMessage = responseJson.getStr("message", "语音识别失败");
                return SpeechRecognitionResult.error(errorMessage);
            }
        } catch (Exception e) {
            log.error("解析语音识别响应失败", e);
            return SpeechRecognitionResult.error("解析响应失败");
        }
    }

    /**
     * 解析情感分析响应
     */
    private EmotionAnalysisResult parseEmotionAnalysisResponse(JSONObject responseJson) {
        try {
            if (responseJson.getInt("code") == 0) {
                JSONObject data = responseJson.getJSONObject("data");
                String emotion = data.getStr("emotion");
                Double confidence = data.getDouble("confidence", 0.8);

                // 构建情感分数映射
                Map<String, Double> emotionScores = new HashMap<>();
                if (data.containsKey("emotions")) {
                    JSONObject emotions = data.getJSONObject("emotions");
                    emotions.forEach((key, value) -> emotionScores.put(key, Double.valueOf(value.toString())));
                }

                String sentiment = data.getStr("sentiment", "neutral");
                Double sentimentScore = data.getDouble("sentiment_score", 0.5);

                return EmotionAnalysisResult.success(emotion, confidence, emotionScores, sentiment, sentimentScore);
            } else {
                String errorMessage = responseJson.getStr("message", "情感分析失败");
                return EmotionAnalysisResult.error(errorMessage);
            }
        } catch (Exception e) {
            log.error("解析情感分析响应失败", e);
            return EmotionAnalysisResult.error("解析响应失败");
        }
    }

    /**
     * 验证音频格式
     */
    private boolean isValidAudioFormat(String contentType) {
        if (StrUtil.isBlank(contentType)) {
            return false;
        }

        List<String> validFormats = Arrays.asList(
            "audio/wav", "audio/wave", "audio/x-wav",
            "audio/mpeg", "audio/mp3",
            "audio/ogg", "audio/webm",
            "audio/m4a", "audio/aac"
        );

        return validFormats.stream().anyMatch(format -> contentType.toLowerCase().contains(format));
    }

    /**
     * 从URL下载音频文件
     */
    private byte[] downloadAudioFromUrl(String audioUrl) {
        try {
            HttpResponse response = HttpRequest.get(audioUrl)
                .timeout(30000)
                .execute();

            if (response.isOk()) {
                return response.bodyBytes();
            } else {
                log.error("下载音频文件失败，状态码: {}", response.getStatus());
                return null;
            }
        } catch (Exception e) {
            log.error("下载音频文件异常: {}", audioUrl, e);
            return null;
        }
    }

    /**
     * 分析语音特征（简化实现）
     */
    private Map<String, Double> analyzeVoiceFeatures(MultipartFile audioFile) {
        Map<String, Double> features = new HashMap<>();

        // 这里应该实现真正的语音特征分析
        // 暂时返回模拟数据
        features.put("pitch", 0.6);
        features.put("energy", 0.7);
        features.put("tempo", 0.5);
        features.put("tone", 0.8);

        return features;
    }

    /**
     * 综合情感分析结果
     */
    private String combineEmotionResults(EmotionAnalysisResult textEmotion, Map<String, Double> voiceFeatures) {
        // 简化的综合分析逻辑
        String textEmotionResult = textEmotion.getEmotion();

        // 根据语音特征调整情感判断
        double pitch = voiceFeatures.getOrDefault("pitch", 0.5);
        double energy = voiceFeatures.getOrDefault("energy", 0.5);

        if (pitch > 0.7 && energy > 0.7) {
            return "excited";
        } else if (pitch < 0.3 && energy < 0.3) {
            return "calm";
        } else {
            return textEmotionResult;
        }
    }

    /**
     * 计算综合置信度
     */
    private Double calculateOverallConfidence(Double textConfidence, Map<String, Double> voiceFeatures) {
        if (textConfidence == null) {
            textConfidence = 0.5;
        }

        // 简化的置信度计算
        double voiceConfidence = voiceFeatures.values().stream()
            .mapToDouble(Double::doubleValue)
            .average()
            .orElse(0.5);

        return (textConfidence * 0.6 + voiceConfidence * 0.4);
    }

    /**
     * 处理流式响应（SSE格式）
     */
    private void processStreamResponse(String responseBody, StreamCallback callback) {
        try {
            StringBuilder fullResponse = new StringBuilder();
            String[] lines = responseBody.split("\n");

            for (String line : lines) {
                if (line.startsWith("data:")) {
                    String data = line.substring(5).trim();

                    // 检查是否为结束标记
                    if ("[DONE]".equals(data)) {
                        callback.onComplete(fullResponse.toString());
                        return;
                    }

                    try {
                        JSONObject jsonData = JSONUtil.parseObj(data);

                        // 检查错误
                        if (jsonData.containsKey("code") && jsonData.getInt("code") != 0) {
                            String errorMessage = jsonData.getStr("message", "未知错误");
                            callback.onError(new RuntimeException("流式响应错误: " + errorMessage));
                            return;
                        }

                        // 解析delta内容
                        if (jsonData.containsKey("choices")) {
                            Object choicesObj = jsonData.get("choices");
                            if (choicesObj instanceof List) {
                                @SuppressWarnings("unchecked")
                                List<Object> choices = (List<Object>) choicesObj;
                                if (!choices.isEmpty() && choices.get(0) instanceof Map) {
                                    @SuppressWarnings("unchecked")
                                    Map<String, Object> firstChoice = (Map<String, Object>) choices.get(0);
                                    if (firstChoice.containsKey("delta")) {
                                        @SuppressWarnings("unchecked")
                                        Map<String, Object> delta = (Map<String, Object>) firstChoice.get("delta");
                                        if (delta.containsKey("content")) {
                                            String content = delta.get("content").toString();
                                            if (StrUtil.isNotBlank(content)) {
                                                fullResponse.append(content);
                                                callback.onToken(content);
                                            }
                                        }
                                    }
                                }
                            }
                        }

                    } catch (Exception e) {
                        log.warn("解析流式数据失败: {}", data, e);
                    }
                }
            }

            // 如果没有收到[DONE]标记，也要调用完成回调
            callback.onComplete(fullResponse.toString());

        } catch (Exception e) {
            log.error("处理流式响应失败", e);
            callback.onError(e);
        }
    }

    @Override
    public EmotionAnalysisResult imageEmotionAnalysis(String imageData) {
        try {
            log.info("调用讯飞图像情感分析，图像数据长度: {}", imageData.length());

            if (StrUtil.isBlank(imageData)) {
                return EmotionAnalysisResult.error("图像数据为空");
            }

            // 方案1: 如果讯飞有专门的图像情感分析API，直接调用
            // 方案2: 使用星火大模型的多模态能力分析图像情感
            // 这里先使用星火大模型的方式

            String prompt = "请分析这张图片中人物的情感状态，返回各种情绪的百分比，包括：开心(happy)、平静(neutral)、悲伤(sad)、生气(angry)、惊讶(surprised)、恐惧(fear)、厌恶(disgusted)。请以JSON格式返回结果。";

            Map<String, Object> context = new HashMap<>();
            context.put("image_data", imageData);

            String result = sparkChat(prompt, context);

            // 解析结果并转换为EmotionAnalysisResult格式
            return parseImageEmotionResult(result);

        } catch (Exception e) {
            log.error("讯飞图像情感分析异常", e);
            return EmotionAnalysisResult.error("图像情感分析处理异常: " + e.getMessage());
        }
    }

    @Override
    public String generateInterviewSuggestion(Map<String, Object> context, Map<String, Object> analysisData) {
        try {
            log.info("生成面试智能建议");

            // 构建面试建议的prompt
            String prompt = buildInterviewSuggestionPrompt(context, analysisData);

            // 调用星火大模型生成建议
            String suggestion = sparkChat(prompt, context);

            log.info("面试建议生成完成");
            return suggestion;

        } catch (Exception e) {
            log.error("生成面试建议异常", e);
            return "抱歉，智能建议生成失败，请稍后再试。";
        }
    }

    /**
     * 解析图像情感分析结果
     */
    private EmotionAnalysisResult parseImageEmotionResult(String result) {
        try {
            // 尝试解析JSON结果
            JSONObject jsonResult = JSONUtil.parseObj(result);

            // 提取情感数据
            String emotion = "neutral"; // 默认情感
            Double confidence = 0.5; // 默认置信度
            Map<String, Double> emotionScores = new HashMap<>();
            String sentiment = "neutral";
            Double sentimentScore = 0.5;

            // 如果结果包含情感信息，提取主要情感
            if (jsonResult.containsKey("happy") || jsonResult.containsKey("emotions")) {
                // 找出最高分的情感
                emotion = findDominantEmotion(jsonResult);
                confidence = 0.8; // 设置较高的置信度

                // 构建情感分数映射
                emotionScores = extractEmotionScores(jsonResult);

                // 设置情感倾向
                sentiment = emotion;
                sentimentScore = confidence;
            }

            return EmotionAnalysisResult.success(emotion, confidence, emotionScores, sentiment, sentimentScore);

        } catch (Exception e) {
            log.warn("解析图像情感结果失败，返回默认结果: {}", e.getMessage());
            // 返回默认的情感分析结果
            Map<String, Double> defaultScores = new HashMap<>();
            defaultScores.put("neutral", 100.0);
            return EmotionAnalysisResult.success("neutral", 0.5, defaultScores, "neutral", 0.5);
        }
    }

    /**
     * 找出主导情感
     */
    private String findDominantEmotion(JSONObject emotionData) {
        String dominantEmotion = "neutral";
        double maxScore = 0.0;

        String[] emotions = {"happy", "neutral", "sad", "angry", "surprised", "fear", "disgusted"};

        for (String emotion : emotions) {
            if (emotionData.containsKey(emotion)) {
                double score = emotionData.getDouble(emotion, 0.0);
                if (score > maxScore) {
                    maxScore = score;
                    dominantEmotion = emotion;
                }
            }
        }

        return dominantEmotion;
    }

    /**
     * 提取情感分数映射
     */
    private Map<String, Double> extractEmotionScores(JSONObject emotionData) {
        Map<String, Double> emotionScores = new HashMap<>();

        String[] emotions = {"happy", "neutral", "sad", "angry", "surprised", "fear", "disgusted"};

        // 如果有emotions字段，直接提取
        if (emotionData.containsKey("emotions")) {
            JSONObject emotions_obj = emotionData.getJSONObject("emotions");
            for (String emotion : emotions) {
                if (emotions_obj.containsKey(emotion)) {
                    emotionScores.put(emotion, emotions_obj.getDouble(emotion, 0.0));
                }
            }
        } else {
            // 否则从根级别提取
            for (String emotion : emotions) {
                if (emotionData.containsKey(emotion)) {
                    emotionScores.put(emotion, emotionData.getDouble(emotion, 0.0));
                }
            }
        }

        // 如果没有提取到任何情感数据，设置默认值
        if (emotionScores.isEmpty()) {
            emotionScores.put("neutral", 100.0);
        }

        return emotionScores;
    }

    /**
     * 构建面试建议的prompt
     */
    private String buildInterviewSuggestionPrompt(Map<String, Object> context, Map<String, Object> analysisData) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("作为专业的面试指导专家，请基于以下信息为面试者提供实时建议：\n\n");

        // 添加上下文信息
        if (context.containsKey("question_id")) {
            prompt.append("当前问题编号：").append(context.get("question_id")).append("\n");
        }

        if (context.containsKey("question_text")) {
            prompt.append("当前问题：").append(context.get("question_text")).append("\n");
        }

        // 添加分析数据
        if (analysisData.containsKey("emotion")) {
            prompt.append("检测到的情绪：").append(analysisData.get("emotion")).append("\n");
        }

        if (analysisData.containsKey("speech_rate")) {
            prompt.append("语速：").append(analysisData.get("speech_rate")).append("\n");
        }

        if (analysisData.containsKey("speech_text")) {
            prompt.append("回答内容：").append(analysisData.get("speech_text")).append("\n");
        }

        prompt.append("\n请提供具体的改进建议，包括：\n");
        prompt.append("1. 情绪调节建议\n");
        prompt.append("2. 语速和表达建议\n");
        prompt.append("3. 内容完善建议\n");
        prompt.append("4. 肢体语言建议\n\n");
        prompt.append("请以JSON格式返回，包含type、level、title、message、action字段。");

        return prompt.toString();
    }
}
