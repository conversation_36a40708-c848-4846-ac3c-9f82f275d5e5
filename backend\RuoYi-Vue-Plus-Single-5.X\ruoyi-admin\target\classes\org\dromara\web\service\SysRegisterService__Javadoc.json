{"doc": "\n 注册校验方法\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "register", "paramTypes": ["org.dromara.common.core.domain.model.RegisterBody"], "doc": "\n 注册\r\n"}, {"name": "validate<PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 校验验证码\r\n\r\n @param username 用户名\r\n @param code     验证码\r\n @param uuid     唯一标识\r\n"}, {"name": "recordLogininfor", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 记录登录信息\r\n\r\n @param username 用户名\r\n @param status   状态\r\n @param message  消息内容\r\n"}], "constructors": []}