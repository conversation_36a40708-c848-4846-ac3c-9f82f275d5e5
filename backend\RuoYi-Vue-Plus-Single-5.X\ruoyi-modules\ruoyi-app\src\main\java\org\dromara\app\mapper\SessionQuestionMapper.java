package org.dromara.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.dromara.app.domain.SessionQuestion;

import java.util.List;

/**
 * 面试会话问题Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Mapper
public interface SessionQuestionMapper extends BaseMapper<SessionQuestion> {

    /**
     * 根据会话ID查询所有问题（按顺序）
     *
     * @param sessionId 会话ID
     * @return 问题列表
     */
    List<SessionQuestion> selectBySessionIdOrderByOrder(@Param("sessionId") String sessionId);

    /**
     * 根据会话ID和问题ID查询问题
     *
     * @param sessionId  会话ID
     * @param questionId 问题ID
     * @return 问题
     */
    SessionQuestion selectBySessionIdAndQuestionId(@Param("sessionId") String sessionId, 
                                                   @Param("questionId") String questionId);

    /**
     * 根据会话ID和顺序查询问题
     *
     * @param sessionId     会话ID
     * @param questionOrder 问题顺序
     * @return 问题
     */
    SessionQuestion selectBySessionIdAndOrder(@Param("sessionId") String sessionId, 
                                              @Param("questionOrder") Integer questionOrder);

    /**
     * 查询会话的下一个问题
     *
     * @param sessionId      会话ID
     * @param currentOrder   当前问题顺序
     * @return 下一个问题
     */
    SessionQuestion selectNextQuestion(@Param("sessionId") String sessionId, 
                                       @Param("currentOrder") Integer currentOrder);

    /**
     * 查询会话的当前问题
     *
     * @param sessionId 会话ID
     * @return 当前问题
     */
    SessionQuestion selectCurrentQuestion(@Param("sessionId") String sessionId);

    /**
     * 统计会话问题总数
     *
     * @param sessionId 会话ID
     * @return 问题总数
     */
    Integer countBySessionId(@Param("sessionId") String sessionId);

    /**
     * 根据状态统计问题数量
     *
     * @param sessionId 会话ID
     * @param status    状态
     * @return 问题数量
     */
    Integer countBySessionIdAndStatus(@Param("sessionId") String sessionId, 
                                      @Param("status") String status);

    /**
     * 批量插入问题
     *
     * @param questions 问题列表
     * @return 插入数量
     */
    Integer batchInsert(@Param("questions") List<SessionQuestion> questions);

    /**
     * 更新问题状态
     *
     * @param sessionId  会话ID
     * @param questionId 问题ID
     * @param status     新状态
     * @return 更新数量
     */
    Integer updateStatusBySessionIdAndQuestionId(@Param("sessionId") String sessionId,
                                                 @Param("questionId") String questionId,
                                                 @Param("status") String status);
}
