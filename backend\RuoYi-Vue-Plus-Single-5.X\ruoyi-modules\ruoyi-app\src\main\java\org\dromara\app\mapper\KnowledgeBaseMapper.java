package org.dromara.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.dromara.app.domain.KnowledgeBase;

import java.util.List;

/**
 * 知识库Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface KnowledgeBaseMapper extends BaseMapper<KnowledgeBase> {

    /**
     * 查询启用的知识库列表
     *
     * @return 知识库列表
     */
    @Select("SELECT * FROM app_knowledge_base WHERE status = 1 AND del_flag = 0 ORDER BY sort_order ASC, create_time DESC")
    List<KnowledgeBase> selectEnabledKnowledgeBases();

    /**
     * 根据类型查询知识库
     *
     * @param type 知识库类型
     * @return 知识库列表
     */
    @Select("SELECT * FROM app_knowledge_base WHERE type = #{type} AND status = 1 AND del_flag = 0 ORDER BY sort_order ASC")
    List<KnowledgeBase> selectByType(@Param("type") String type);

    /**
     * 更新文档数量
     *
     * @param knowledgeBaseId 知识库ID
     * @param documentCount   文档数量
     * @return 更新结果
     */
    @Update("UPDATE app_knowledge_base SET document_count = #{documentCount} WHERE id = #{knowledgeBaseId}")
    int updateDocumentCount(@Param("knowledgeBaseId") Long knowledgeBaseId, @Param("documentCount") Long documentCount);

    /**
     * 更新向量数量
     *
     * @param knowledgeBaseId 知识库ID
     * @param vectorCount     向量数量
     * @return 更新结果
     */
    @Update("UPDATE app_knowledge_base SET vector_count = #{vectorCount} WHERE id = #{knowledgeBaseId}")
    int updateVectorCount(@Param("knowledgeBaseId") Long knowledgeBaseId, @Param("vectorCount") Long vectorCount);

    /**
     * 根据用户ID查询知识库
     *
     * @param userId 用户ID
     * @return 知识库列表
     */
    @Select("SELECT * FROM app_knowledge_base WHERE create_by = #{userId} AND del_flag = 0 ORDER BY sort_order ASC, create_time DESC")
    List<KnowledgeBase> selectByUserId(@Param("userId") Long userId);

    /**
     * 批量更新知识库状态
     *
     * @param ids    知识库ID列表
     * @param status 状态
     * @return 更新结果
     */
    int updateStatusByIds(@Param("ids") List<Long> ids, @Param("status") Integer status);
}
