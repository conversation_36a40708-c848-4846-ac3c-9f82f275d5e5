package org.dromara.app.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 徽章数据视图对象
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Data
@Schema(description = "徽章数据视图对象")
@ExcelIgnoreUnannotated
public class BadgeVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 徽章ID
     */
    @Schema(description = "徽章ID")
    @ExcelProperty(value = "徽章ID")
    private String id;

    /**
     * 徽章图标
     */
    @Schema(description = "徽章图标")
    @ExcelProperty(value = "徽章图标")
    private String icon;

    /**
     * 徽章颜色
     */
    @Schema(description = "徽章颜色")
    @ExcelProperty(value = "徽章颜色")
    private String color;

    /**
     * 徽章标题
     */
    @Schema(description = "徽章标题")
    @ExcelProperty(value = "徽章标题")
    private String title;

    /**
     * 徽章描述
     */
    @Schema(description = "徽章描述")
    @ExcelProperty(value = "徽章描述")
    private String desc;

    /**
     * 是否解锁
     */
    @Schema(description = "是否解锁")
    @ExcelProperty(value = "是否解锁")
    private Boolean unlocked;

    /**
     * 解锁时间
     */
    @Schema(description = "解锁时间")
    @ExcelProperty(value = "解锁时间")
    private LocalDateTime unlockedAt;

    /**
     * 是否置顶
     */
    @Schema(description = "是否置顶")
    @ExcelProperty(value = "是否置顶")
    private Boolean isPinned;

    /**
     * 置顶时间
     */
    @Schema(description = "置顶时间")
    @ExcelProperty(value = "置顶时间")
    private LocalDateTime pinnedAt;

    /**
     * 徽章类别
     */
    @Schema(description = "徽章类别")
    @ExcelProperty(value = "徽章类别")
    private String category;

    /**
     * 稀有度
     */
    @Schema(description = "稀有度")
    @ExcelProperty(value = "稀有度")
    private String rarity;

    /**
     * 关联成就ID
     */
    @Schema(description = "关联成就ID")
    @ExcelProperty(value = "关联成就ID")
    private String achievementId;

    /**
     * 排序
     */
    @Schema(description = "排序")
    @ExcelProperty(value = "排序")
    private Integer sort;
}
