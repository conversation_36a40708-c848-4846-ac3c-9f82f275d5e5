package org.dromara.app.service.impl;

import cn.hutool.core.date.DateUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.service.IMultimodalAnalysisService;
import org.dromara.app.service.IReportGenerationService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 报告生成服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public  class ReportGenerationServiceImpl implements IReportGenerationService {

    private final IMultimodalAnalysisService multimodalAnalysisService;

    @Value("${app.file.upload.path:/data/reports}")
    private String reportPath;

    @Override
    public InterviewReportData generateInterviewReport(String sessionId) {
        log.info("开始生成面试报告，会话ID: {}", sessionId);

        try {
            InterviewReportData reportData = new InterviewReportData();
            reportData.setSessionId(sessionId);
            reportData.setGenerateTime(System.currentTimeMillis());

            // 获取多模态分析结果（这里使用模拟数据，实际应从数据库获取）
            IMultimodalAnalysisService.MultimodalAnalysisResult analysisResult =
                createMockAnalysisResult(sessionId);

            if (analysisResult != null && analysisResult.getOverallAssessment() != null) {
                IMultimodalAnalysisService.OverallAssessment assessment = analysisResult.getOverallAssessment();

                // 设置基本信息
                BasicInfo basicInfo = new BasicInfo();
                basicInfo.setCandidateName("面试者");
                basicInfo.setJobPosition("Java开发工程师");
                basicInfo.setCompany("科技公司");
                basicInfo.setInterviewDate(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                basicInfo.setDuration("45分钟");
                basicInfo.setTotalQuestions(10);
                basicInfo.setAnsweredQuestions(10);
                reportData.setBasicInfo(basicInfo);

                // 设置评分信息
                reportData.setOverallScore(assessment.getTotalScore());
                reportData.setRank(assessment.getLevel());
                reportData.setPercentile(assessment.getPercentile());
                reportData.setOverallFeedback(assessment.getOverallFeedback());

                // 设置优势和劣势
                reportData.setStrengths(assessment.getTopStrengths());
                reportData.setWeaknesses(assessment.getTopWeaknesses());

                // 转换维度评分
                if (assessment.getDimensionScores() != null) {
                    List<DimensionScore> dimensionScores = convertDimensionScores(assessment.getDimensionScores());
                    reportData.setDimensionScores(dimensionScores);

                    // 生成雷达图数据
                    RadarChartData radarChartData = generateRadarChartData(assessment.getDimensionScores());
                    reportData.setRadarChartData(radarChartData);
                }

                // 生成改进建议
                List<ImprovementSuggestion> suggestions = generateImprovementSuggestions(
                    assessment.getTopWeaknesses(), assessment.getDimensionScores());
                reportData.setImprovementSuggestions(suggestions);

                // 生成学习路径推荐
                List<LearningPathRecommendation> learningPaths = generateLearningPathRecommendations(
                    sessionId, reportData);
                reportData.setLearningPaths(learningPaths);

                // 设置对比数据
                Map<String, Object> comparisonData = new HashMap<>();
                comparisonData.put("industryAverage", 72);
                comparisonData.put("userRanking", "前25%");
                comparisonData.put("improvementRate", "+15%");
                reportData.setComparisonData(comparisonData);
            }

            log.info("面试报告生成成功，会话ID: {}", sessionId);
            return reportData;

        } catch (Exception e) {
            log.error("生成面试报告失败", e);
            throw new RuntimeException("生成面试报告失败: " + e.getMessage());
        }
    }

    @Override
    public String generatePdfReport(String sessionId) {
        log.info("开始生成PDF报告，会话ID: {}", sessionId);

        try {
            // 获取报告数据
            InterviewReportData reportData = generateInterviewReport(sessionId);

            // 生成PDF文件名
            String fileName = String.format("interview_report_%s_%s.pdf",
                sessionId, DateUtil.format(new Date(), "yyyyMMdd_HHmmss"));
            String filePath = reportPath + "/" + fileName;

            // 生成PDF内容
            String pdfContent = generatePdfContent(reportData);

            // 确保目录存在
            java.nio.file.Files.createDirectories(java.nio.file.Paths.get(reportPath));

            // 写入文件（这里简化处理，实际应使用PDF生成库）
            java.nio.file.Files.write(java.nio.file.Paths.get(filePath), pdfContent.getBytes("UTF-8"));

            log.info("PDF报告生成完成，文件路径: {}", filePath);
            return filePath;

        } catch (Exception e) {
            log.error("生成PDF报告失败", e);
            throw new RuntimeException("生成PDF报告失败: " + e.getMessage());
        }
    }

    @Override
    public RadarChartData generateRadarChartData(List<IMultimodalAnalysisService.DimensionScore> dimensionScores) {
        RadarChartData radarData = new RadarChartData();

        if (dimensionScores == null || dimensionScores.isEmpty()) {
            return radarData;
        }

        // 提取维度名称
        List<String> dimensions = dimensionScores.stream()
            .map(IMultimodalAnalysisService.DimensionScore::getDimension)
            .collect(Collectors.toList());
        radarData.setDimensions(dimensions);

        // 提取分数
        List<Integer> scores = dimensionScores.stream()
            .map(IMultimodalAnalysisService.DimensionScore::getScore)
            .collect(Collectors.toList());
        radarData.setScores(scores);

        // 提取最大分数
        List<Integer> maxScores = dimensionScores.stream()
            .map(IMultimodalAnalysisService.DimensionScore::getMaxScore)
            .collect(Collectors.toList());
        radarData.setMaxScores(maxScores);

        // 生成行业平均分（模拟数据）
        List<Integer> industryAverages = dimensionScores.stream()
            .map(score -> (int) (score.getMaxScore() * 0.72)) // 假设行业平均为72%
            .collect(Collectors.toList());
        radarData.setIndustryAverages(industryAverages);

        // 设置图表配置
        Map<String, Object> chartConfig = new HashMap<>();
        chartConfig.put("title", "能力雷达图");
        chartConfig.put("maxValue", 100);
        chartConfig.put("levels", 5);
        chartConfig.put("showLegend", true);
        chartConfig.put("colors", Arrays.asList("#4CAF50", "#2196F3", "#FF9800"));
        radarData.setChartConfig(chartConfig);

        return radarData;
    }


    @Override
    public List<ImprovementSuggestion> generateImprovementSuggestions(List<String> weaknesses,
                                                                     List<IMultimodalAnalysisService.DimensionScore> dimensionScores) {
        List<ImprovementSuggestion> suggestions = new ArrayList<>();

        if (weaknesses == null || weaknesses.isEmpty()) {
            return suggestions;
        }

        for (String weakness : weaknesses) {
            ImprovementSuggestion suggestion = createImprovementSuggestion(weakness);
            if (suggestion != null) {
                suggestions.add(suggestion);
            }
        }

        // 基于维度评分生成额外建议
        if (dimensionScores != null) {
            for (IMultimodalAnalysisService.DimensionScore score : dimensionScores) {
                if (score.getScore() < 60) {
                    ImprovementSuggestion suggestion = createDimensionImprovementSuggestion(score);
                    if (suggestion != null) {
                        suggestions.add(suggestion);
                    }
                }
            }
        }

        // 去重并按优先级排序
        return suggestions.stream()
            .distinct()
            .sorted((s1, s2) -> getPriorityValue(s2.getPriority()) - getPriorityValue(s1.getPriority()))
            .limit(5) // 最多返回5个建议
            .collect(Collectors.toList());
    }

    @Override
    public List<LearningPathRecommendation> generateLearningPathRecommendations(String sessionId,
                                                                               InterviewReportData reportData) {
        List<LearningPathRecommendation> recommendations = new ArrayList<>();

        // 基于弱项生成推荐
        if (reportData.getWeaknesses() != null) {
            for (String weakness : reportData.getWeaknesses()) {
                if (weakness.contains("专业知识")) {
                    recommendations.add(createTechnicalLearningPath());
                } else if (weakness.contains("表达") || weakness.contains("语速") || weakness.contains("流利")) {
                    recommendations.add(createCommunicationLearningPath());
                } else if (weakness.contains("逻辑")) {
                    recommendations.add(createLogicalThinkingLearningPath());
                }
            }
        }

        // 如果没有基于弱项的推荐，添加通用推荐
        if (recommendations.isEmpty()) {
            recommendations.add(createInterviewSkillsLearningPath());
        }

        // 确保不超过3个推荐
        return recommendations.stream().limit(3).collect(Collectors.toList());
    }

    // ========== 私有辅助方法 ==========

    /**
     * 创建模拟分析结果
     */
    private IMultimodalAnalysisService.MultimodalAnalysisResult createMockAnalysisResult(String sessionId) {
        IMultimodalAnalysisService.MultimodalAnalysisResult result =
            new IMultimodalAnalysisService.MultimodalAnalysisResult();
        result.setSessionId(sessionId);
        result.setStatus("completed");

        // 创建模拟的综合评估
        IMultimodalAnalysisService.OverallAssessment assessment =
            new IMultimodalAnalysisService.OverallAssessment();
        assessment.setTotalScore(75);
        assessment.setLevel("good");
        assessment.setPercentile(68);
        assessment.setTopStrengths(Arrays.asList("表达清晰", "逻辑性强", "专业知识扎实"));
        assessment.setTopWeaknesses(Arrays.asList("眼神交流不足", "手势表达较少", "创新思维有待提升"));
        assessment.setOverallFeedback("整体表现良好，在某些方面还有提升空间。");

        // 创建维度评分
        List<IMultimodalAnalysisService.DimensionScore> dimensionScores = new ArrayList<>();
        dimensionScores.add(createMockDimensionScore("专业知识水平", 80, "技术基础扎实"));
        dimensionScores.add(createMockDimensionScore("语言表达能力", 75, "表达清晰流利"));
        dimensionScores.add(createMockDimensionScore("逻辑思维能力", 78, "思路清晰有条理"));
        dimensionScores.add(createMockDimensionScore("肢体语言表现", 65, "需要改善眼神交流"));
        dimensionScores.add(createMockDimensionScore("创新能力", 60, "创新思维有待加强"));
        assessment.setDimensionScores(dimensionScores);

        result.setOverallAssessment(assessment);
        return result;
    }

    /**
     * 创建模拟维度评分
     */
    private IMultimodalAnalysisService.DimensionScore createMockDimensionScore(String dimension, int score, String description) {
        IMultimodalAnalysisService.DimensionScore dimensionScore =
            new IMultimodalAnalysisService.DimensionScore();
        dimensionScore.setDimension(dimension);
        dimensionScore.setScore(score);
        dimensionScore.setMaxScore(100);
        dimensionScore.setPercentile((int) (score * 0.8)); // 简化的百分位计算
        dimensionScore.setDescription(description);
        return dimensionScore;
    }

    /**
     * 转换维度评分格式
     */
    private List<DimensionScore> convertDimensionScores(List<IMultimodalAnalysisService.DimensionScore> sourceDimensionScores) {
        return sourceDimensionScores.stream().map(source -> {
            DimensionScore target = new DimensionScore();
            target.setDimension(source.getDimension());
            target.setScore(source.getScore());
            target.setMaxScore(source.getMaxScore());
            target.setPercentile(source.getPercentile());
            target.setDescription(source.getDescription());
            return target;
        }).collect(Collectors.toList());
    }

    /**
     * 生成PDF内容
     */
    private String generatePdfContent(InterviewReportData reportData) {
        StringBuilder content = new StringBuilder();

        content.append("=== 面试评估报告 ===\n\n");

        // 基本信息
        if (reportData.getBasicInfo() != null) {
            BasicInfo basicInfo = reportData.getBasicInfo();
            content.append("候选人：").append(basicInfo.getCandidateName()).append("\n");
            content.append("应聘岗位：").append(basicInfo.getJobPosition()).append("\n");
            content.append("面试时间：").append(basicInfo.getInterviewDate()).append("\n");
            content.append("面试时长：").append(basicInfo.getDuration()).append("\n\n");
        }

        // 总体评分
        content.append("=== 总体评分 ===\n");
        content.append("综合得分：").append(reportData.getOverallScore()).append("分\n");
        content.append("评级：").append(reportData.getRank()).append("\n");
        content.append("百分位：").append(reportData.getPercentile()).append("%\n\n");

        // 维度评分
        content.append("=== 维度评分 ===\n");
        if (reportData.getDimensionScores() != null) {
            for (DimensionScore score : reportData.getDimensionScores()) {
                content.append(score.getDimension()).append("：").append(score.getScore()).append("分\n");
                if (score.getDescription() != null && !score.getDescription().isEmpty()) {
                    content.append("  ").append(score.getDescription()).append("\n");
                }
            }
        }
        content.append("\n");

        // 优势和劣势
        content.append("=== 主要优势 ===\n");
        if (reportData.getStrengths() != null) {
            for (String strength : reportData.getStrengths()) {
                content.append("• ").append(strength).append("\n");
            }
        }
        content.append("\n");

        content.append("=== 需要改进 ===\n");
        if (reportData.getWeaknesses() != null) {
            for (String weakness : reportData.getWeaknesses()) {
                content.append("• ").append(weakness).append("\n");
            }
        }
        content.append("\n");

        // 改进建议
        content.append("=== 改进建议 ===\n");
        if (reportData.getImprovementSuggestions() != null) {
            for (ImprovementSuggestion suggestion : reportData.getImprovementSuggestions()) {
                content.append(suggestion.getTitle()).append("\n");
                content.append("  ").append(suggestion.getDescription()).append("\n");
                if (suggestion.getActionItems() != null) {
                    for (String action : suggestion.getActionItems()) {
                        content.append("  - ").append(action).append("\n");
                    }
                }
                content.append("\n");
            }
        }

        // 总体反馈
        content.append("=== 总体反馈 ===\n");
        content.append(reportData.getOverallFeedback()).append("\n\n");

        content.append("报告生成时间：").append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        return content.toString();
    }

    /**
     * 创建改进建议
     */
    private ImprovementSuggestion createImprovementSuggestion(String weakness) {
        ImprovementSuggestion suggestion = new ImprovementSuggestion();

        if (weakness.contains("眼神交流")) {
            suggestion.setTitle("改善眼神交流");
            suggestion.setDescription("练习与摄像头保持自然的眼神接触，增强面试中的互动感");
            suggestion.setActionItems(Arrays.asList(
                "每天对着镜子练习3-5分钟自我介绍，保持眼神接触",
                "录制视频练习，观察自己的眼神表现",
                "与朋友进行模拟面试练习，请他们反馈你的眼神交流情况"
            ));
            suggestion.setPriority("高");
            suggestion.setEstimatedTime(7);
            suggestion.setDifficulty("简单");

        } else if (weakness.contains("手势") || weakness.contains("表达")) {
            suggestion.setTitle("提升肢体语言表达");
            suggestion.setDescription("通过适当的手势和肢体语言增强表达效果");
            suggestion.setActionItems(Arrays.asList(
                "学习基本的手势表达技巧",
                "练习在说话时配合适当的手势",
                "观看优秀演讲者的视频，学习肢体语言运用",
                "在日常交流中有意识地练习肢体语言"
            ));
            suggestion.setPriority("中");
            suggestion.setEstimatedTime(10);
            suggestion.setDifficulty("中等");

        } else if (weakness.contains("创新") || weakness.contains("思维")) {
            suggestion.setTitle("培养创新思维");
            suggestion.setDescription("通过多种方法培养和提升创新思维能力");
            suggestion.setActionItems(Arrays.asList(
                "多角度思考问题，尝试不同的解决方案",
                "关注行业前沿技术和发展趋势",
                "参与头脑风暴和创新讨论",
                "阅读创新思维相关书籍和案例"
            ));
            suggestion.setPriority("中");
            suggestion.setEstimatedTime(14);
            suggestion.setDifficulty("中等");

        } else {
            // 通用改进建议
            suggestion.setTitle("综合能力提升");
            suggestion.setDescription("全面提升面试表现");
            suggestion.setActionItems(Arrays.asList(
                "进行更多模拟面试练习",
                "向有经验的人请教面试技巧",
                "录制自己的面试视频并分析",
                "阅读面试技巧相关书籍"
            ));
            suggestion.setPriority("中");
            suggestion.setEstimatedTime(10);
            suggestion.setDifficulty("简单");
        }

        return suggestion;
    }

    /**
     * 基于维度评分创建改进建议
     */
    private ImprovementSuggestion createDimensionImprovementSuggestion(IMultimodalAnalysisService.DimensionScore score) {
        ImprovementSuggestion suggestion = new ImprovementSuggestion();

        String dimension = score.getDimension();
        suggestion.setTitle("提升" + dimension);
        suggestion.setDescription("针对" + dimension + "的专项改进");
        suggestion.setActionItems(Arrays.asList(
            "制定专项提升计划",
            "寻找相关学习资源",
            "进行针对性练习",
            "定期评估改进效果"
        ));
        suggestion.setPriority("中");
        suggestion.setEstimatedTime(10);
        suggestion.setDifficulty("中等");

        return suggestion;
    }

    /**
     * 创建技术学习路径
     */
    private LearningPathRecommendation createTechnicalLearningPath() {
        LearningPathRecommendation path = new LearningPathRecommendation();
        path.setTitle("技术能力提升计划");
        path.setDescription("针对技术岗位的核心技能提升");
        path.setEstimatedHours(40);
        path.setDifficulty("中等");
        path.setPriority(90);

        List<LearningResource> resources = new ArrayList<>();
        resources.add(createResource("Java核心技术精讲", "course", "Java核心技术深入学习"));
        resources.add(createResource("Spring Boot实战", "video", "Spring Boot框架实战项目"));
        resources.add(createResource("算法与数据结构", "practice", "编程算法练习"));
        path.setResources(resources);

        path.setMilestones(Arrays.asList(
            "完成基础理论学习 (10小时)",
            "实践核心技能 (15小时)",
            "解决实际问题 (10小时)",
            "模拟面试练习 (5小时)"
        ));

        return path;
    }

    /**
     * 创建沟通表达学习路径
     */
    private LearningPathRecommendation createCommunicationLearningPath() {
        LearningPathRecommendation path = new LearningPathRecommendation();
        path.setTitle("沟通表达能力提升");
        path.setDescription("提升语言表达和沟通技巧");
        path.setEstimatedHours(20);
        path.setDifficulty("简单");
        path.setPriority(85);

        List<LearningResource> resources = new ArrayList<>();
        resources.add(createResource("有效沟通的艺术", "course", "沟通技巧理论与实践"));
        resources.add(createResource("演讲与表达技巧", "video", "公众演讲与表达训练"));
        resources.add(createResource("STAR回答法训练", "practice", "结构化回答方法练习"));
        path.setResources(resources);

        path.setMilestones(Arrays.asList(
            "掌握基本沟通理论 (3小时)",
            "语速与停顿控制练习 (5小时)",
            "结构化表达训练 (7小时)",
            "模拟面试对话练习 (5小时)"
        ));

        return path;
    }

    /**
     * 创建逻辑思维学习路径
     */
    private LearningPathRecommendation createLogicalThinkingLearningPath() {
        LearningPathRecommendation path = new LearningPathRecommendation();
        path.setTitle("逻辑思维能力提升");
        path.setDescription("增强逻辑分析和结构化思考能力");
        path.setEstimatedHours(25);
        path.setDifficulty("中等");
        path.setPriority(80);

        List<LearningResource> resources = new ArrayList<>();
        resources.add(createResource("结构化思考方法", "course", "MECE原则与金字塔原理"));
        resources.add(createResource("逻辑思维训练", "practice", "逻辑推理与分析练习"));
        resources.add(createResource("问题分析与解决", "video", "系统性问题解决方法"));
        path.setResources(resources);

        path.setMilestones(Arrays.asList(
            "掌握基本逻辑框架 (5小时)",
            "结构化表达练习 (8小时)",
            "案例分析训练 (7小时)",
            "逻辑答题实战 (5小时)"
        ));

        return path;
    }

    /**
     * 创建面试技巧学习路径
     */
    private LearningPathRecommendation createInterviewSkillsLearningPath() {
        LearningPathRecommendation path = new LearningPathRecommendation();
        path.setTitle("面试技巧全面提升");
        path.setDescription("综合提升面试各环节的表现能力");
        path.setEstimatedHours(30);
        path.setDifficulty("简单");
        path.setPriority(75);

        List<LearningResource> resources = new ArrayList<>();
        resources.add(createResource("面试全流程指南", "course", "面试准备到结束全流程"));
        resources.add(createResource("常见面试问题解析", "video", "HR和技术面试问题详解"));
        resources.add(createResource("面试肢体语言技巧", "article", "非语言沟通在面试中的应用"));
        path.setResources(resources);

        path.setMilestones(Arrays.asList(
            "面试准备与心态调整 (5小时)",
            "常见问题回答技巧 (10小时)",
            "肢体语言与非语言表达 (5小时)",
            "模拟面试实战 (10小时)"
        ));

        return path;
    }

    /**
     * 创建学习资源
     */
    private LearningResource createResource(String title, String type, String description) {
        LearningResource resource = new LearningResource();
        resource.setTitle(title);
        resource.setType(type);
        resource.setDescription(description);
        resource.setUrl("https://example.com/" + title.toLowerCase().replace(" ", "-"));
        resource.setDuration("2-5小时");
        resource.setDifficulty("中等");
        return resource;
    }

    /**
     * 获取优先级数值
     */
    private int getPriorityValue(String priority) {
        switch (priority) {
            case "高":
                return 3;
            case "中":
                return 2;
            case "低":
                return 1;
            default:
                return 0;
        }
    }
}
