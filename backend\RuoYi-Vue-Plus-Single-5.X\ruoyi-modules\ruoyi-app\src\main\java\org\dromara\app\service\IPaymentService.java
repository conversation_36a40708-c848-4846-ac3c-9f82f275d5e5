package org.dromara.app.service;

import jakarta.servlet.http.HttpServletRequest;
import org.dromara.app.domain.dto.PaymentOrderDto;
import org.dromara.app.domain.vo.PaymentOrderVo;


/**
 * 支付服务接口
 *
 * <AUTHOR>
 */
public interface IPaymentService {

    /**
     * 创建支付订单
     *
     * @param paymentOrderDto 支付订单请求参数
     * @return 支付订单信息
     */
    PaymentOrderVo createPaymentOrder(PaymentOrderDto paymentOrderDto);

    /**
     * 支付宝支付
     *
     * @param orderNo  订单号
     * @param payToken 支付token
     * @return 支付页面表单HTML
     */
    String alipayPay(String orderNo, String payToken);

    /**
     * 支付宝异步通知处理
     *
     * @param request HTTP请求
     * @return 处理结果
     */
    String alipayNotify(HttpServletRequest request);

    /**
     * 支付宝同步回调处理
     *
     * @param request HTTP请求
     * @return 处理结果
     */
    String alipayReturn(HttpServletRequest request);

    /**
     * 查询订单状态
     *
     * @param orderNo 订单号
     * @return 订单信息
     */
    PaymentOrderVo queryOrderStatus(String orderNo);

    /**
     * 取消订单
     *
     * @param orderNo 订单号
     * @return 取消结果
     */
    boolean cancelOrder(String orderNo);

    /**
     * 验证支付token
     *
     * @param orderNo  订单号
     * @param payToken 支付token
     * @return 验证结果
     */
    boolean validatePayToken(String orderNo, String payToken);

    /**
     * 标记支付token为已使用
     *
     * @param orderNo 订单号
     */
    void markPayTokenAsUsed(String orderNo);

    /**
     * 处理支付超时
     *
     * @param orderNo 订单号
     */
    void handlePaymentTimeout(String orderNo);
}
