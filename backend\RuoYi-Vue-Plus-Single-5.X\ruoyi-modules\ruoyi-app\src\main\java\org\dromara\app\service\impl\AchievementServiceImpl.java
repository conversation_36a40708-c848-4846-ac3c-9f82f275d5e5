package org.dromara.app.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.Achievement;
import org.dromara.app.domain.UserAchievement;
import org.dromara.app.domain.dto.TrackEventDto;
import org.dromara.app.domain.vo.AchievementStatsVo;
import org.dromara.app.domain.vo.AchievementVo;
import org.dromara.app.domain.vo.BadgeVo;
import org.dromara.app.domain.vo.UserAchievementVo;
import org.dromara.app.mapper.AchievementMapper;
import org.dromara.app.mapper.UserAchievementMapper;
import org.dromara.app.mapper.UserBehaviorMapper;
import org.dromara.app.service.IAchievementService;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

/**
 * 成就系统服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class AchievementServiceImpl implements IAchievementService {

    private final AchievementMapper achievementMapper;
    private final UserAchievementMapper userAchievementMapper;
    private final UserBehaviorMapper userBehaviorMapper;

    // ==================== 核心成就查询方法 ====================

    @Override
    public TableDataInfo<AchievementVo> queryPageList(PageQuery pageQuery) {
        Page<Achievement> achievementPage = achievementMapper.selectPage(new Page<>(pageQuery.getPageNum(), pageQuery.getPageSize()),
            new LambdaQueryWrapper<>());
        return TableDataInfo.build(MapstructUtils.convert(achievementPage.getRecords(), AchievementVo.class));
    }

    @Override
    public List<AchievementVo> queryActiveAchievements() {
        return achievementMapper.selectActiveAchievements();
    }

    @Override
    public List<AchievementVo> queryByAchievementType(String achievementType) {
        return achievementMapper.selectByAchievementType(achievementType);
    }

    @Override
    public Achievement queryByAchievementCode(String achievementCode) {
        return achievementMapper.selectByAchievementCode(achievementCode);
    }

    // ==================== 核心成就检查逻辑 ====================

    @Override
    @Transactional
    public void checkAchievements(Long userId, TrackEventDto trackEventDto) {
        try {
            log.debug("开始检查用户成就: userId={}, eventType={}", userId, trackEventDto.getEventType());

            // 获取所有激活的成就
            List<AchievementVo> activeAchievements = queryActiveAchievements();

            for (AchievementVo achievement : activeAchievements) {
                checkSingleAchievement(userId, achievement, trackEventDto);
            }

        } catch (Exception e) {
            log.error("检查用户成就失败: userId={}, eventType={}", userId, trackEventDto.getEventType(), e);
        }
    }

    /**
     * 检查单个成就
     */
    private void checkSingleAchievement(Long userId, AchievementVo achievement, TrackEventDto trackEventDto) {
        try {
            // 检查用户是否已有此成就记录
            UserAchievement userAchievement = userAchievementMapper.selectByUserIdAndAchievementId(userId, achievement.getId());

            if (userAchievement == null) {
                // 创建新的用户成就记录
                userAchievement = createUserAchievement(userId, achievement);
                userAchievementMapper.insert(userAchievement);
            }

            // 如果已经完成，跳过
            if ("1".equals(userAchievement.getIsCompleted())) {
                return;
            }

            // 解析成就触发条件
            Map<String, Object> triggerCondition = JSONUtil.toBean(achievement.getTriggerCondition(), Map.class);
            String behaviorType = (String) triggerCondition.get("behaviorType");

            // 检查是否匹配当前事件类型
            if (!trackEventDto.getEventType().equals(behaviorType)) {
                return;
            }

            // 根据不同的成就类型进行检查
            boolean isCompleted = checkAchievementCondition(userId, achievement, triggerCondition, trackEventDto);

            if (isCompleted) {
                // 完成成就
                completeAchievement(userId, achievement.getId());
                log.info("用户完成成就: userId={}, achievementCode={}, achievementName={}",
                    userId, achievement.getAchievementCode(), achievement.getAchievementName());
            } else {
                // 更新进度
                updateAchievementProgressInternal(userId, achievement, triggerCondition, trackEventDto);
            }

        } catch (Exception e) {
            log.error("检查单个成就失败: userId={}, achievementId={}", userId, achievement.getId(), e);
        }
    }

    /**
     * 检查成就条件是否满足
     */
    private boolean checkAchievementCondition(Long userId, AchievementVo achievement,
                                            Map<String, Object> triggerCondition, TrackEventDto trackEventDto) {
        String behaviorType = (String) triggerCondition.get("behaviorType");

        switch (behaviorType) {
            case "LOGIN":
                return checkLoginAchievement(userId, triggerCondition);
            case "VIDEO_WATCH":
                return checkVideoWatchAchievement(userId, triggerCondition);
            case "COMMENT":
                return checkCommentAchievement(userId, triggerCondition);
            case "LIKE":
                return checkLikeAchievement(userId, triggerCondition);
            case "STUDY_TIME":
                return checkStudyTimeAchievement(userId, triggerCondition);
            default:
                return false;
        }
    }

    /**
     * 检查登录类成就
     */
    private boolean checkLoginAchievement(Long userId, Map<String, Object> triggerCondition) {
        if (triggerCondition.containsKey("count")) {
            Integer requiredCount = (Integer) triggerCondition.get("count");
            Long actualCount = userBehaviorMapper.countByUserIdAndBehaviorType(userId, "LOGIN");
            return actualCount >= requiredCount;
        }

        if (triggerCondition.containsKey("consecutiveDays")) {
            Integer requiredDays = (Integer) triggerCondition.get("consecutiveDays");
            Integer actualDays = userBehaviorMapper.selectConsecutiveLoginDays(userId);
            return actualDays >= requiredDays;
        }

        return false;
    }

    /**
     * 检查视频观看类成就
     */
    private boolean checkVideoWatchAchievement(Long userId, Map<String, Object> triggerCondition) {
        Integer requiredCount = (Integer) triggerCondition.get("count");
        Long actualCount = userBehaviorMapper.countByUserIdAndBehaviorType(userId, "VIDEO_WATCH");
        return actualCount >= requiredCount;
    }

    /**
     * 检查评论类成就
     */
    private boolean checkCommentAchievement(Long userId, Map<String, Object> triggerCondition) {
        Integer requiredCount = (Integer) triggerCondition.get("count");
        Long actualCount = userBehaviorMapper.countByUserIdAndBehaviorType(userId, "COMMENT");
        return actualCount >= requiredCount;
    }

    /**
     * 检查点赞类成就
     */
    private boolean checkLikeAchievement(Long userId, Map<String, Object> triggerCondition) {
        Integer requiredCount = (Integer) triggerCondition.get("count");
        Long actualCount = userBehaviorMapper.countByUserIdAndBehaviorType(userId, "LIKE");
        return actualCount >= requiredCount;
    }

    /**
     * 检查学习时长类成就
     */
    private boolean checkStudyTimeAchievement(Long userId, Map<String, Object> triggerCondition) {
        Integer requiredMinutes = (Integer) triggerCondition.get("totalMinutes");
        Long actualMinutes = userBehaviorMapper.selectTotalStudyMinutes(userId);
        return actualMinutes >= requiredMinutes;
    }

    /**
     * 更新成就进度
     */
    private void updateAchievementProgressInternal(Long userId, AchievementVo achievement,
                                         Map<String, Object> triggerCondition, TrackEventDto trackEventDto) {
        // 计算当前进度
        long currentValue = getCurrentValue(userId, triggerCondition);
        long targetValue = getTargetValue(triggerCondition);

        BigDecimal progress = BigDecimal.valueOf((double) currentValue / targetValue * 100);
        if (progress.compareTo(BigDecimal.valueOf(100)) > 0) {
            progress = BigDecimal.valueOf(100);
        }

        // 更新进度
        userAchievementMapper.updateProgress(userId, achievement.getId(), currentValue, progress);
    }

    /**
     * 获取当前数值
     */
    private long getCurrentValue(Long userId, Map<String, Object> triggerCondition) {
        String behaviorType = (String) triggerCondition.get("behaviorType");

        switch (behaviorType) {
            case "LOGIN":
                if (triggerCondition.containsKey("consecutiveDays")) {
                    return userBehaviorMapper.selectConsecutiveLoginDays(userId);
                } else {
                    return userBehaviorMapper.countByUserIdAndBehaviorType(userId, "LOGIN");
                }
            case "STUDY_TIME":
                return userBehaviorMapper.selectTotalStudyMinutes(userId);
            default:
                return userBehaviorMapper.countByUserIdAndBehaviorType(userId, behaviorType);
        }
    }

    /**
     * 获取目标数值
     */
    private long getTargetValue(Map<String, Object> triggerCondition) {
        if (triggerCondition.containsKey("count")) {
            return ((Integer) triggerCondition.get("count")).longValue();
        }
        if (triggerCondition.containsKey("consecutiveDays")) {
            return ((Integer) triggerCondition.get("consecutiveDays")).longValue();
        }
        if (triggerCondition.containsKey("totalMinutes")) {
            return ((Integer) triggerCondition.get("totalMinutes")).longValue();
        }
        return 1L;
    }

    /**
     * 完成成就
     */
    private void completeAchievement(Long userId, Long achievementId) {
        userAchievementMapper.completeAchievement(userId, achievementId);
    }

    /**
     * 创建用户成就记录
     */
    private UserAchievement createUserAchievement(Long userId, AchievementVo achievement) {
        UserAchievement userAchievement = new UserAchievement();
        userAchievement.setUserId(userId);
        userAchievement.setAchievementId(achievement.getId());
        userAchievement.setProgress(BigDecimal.ZERO);
        userAchievement.setCurrentValue(0L);

        // 解析目标数值
        Map<String, Object> triggerCondition = JSONUtil.toBean(achievement.getTriggerCondition(), Map.class);
        userAchievement.setTargetValue(getTargetValue(triggerCondition));

        userAchievement.setIsCompleted("0");
        userAchievement.setIsNotified("0");
        userAchievement.setCreateTime(new Date());

        return userAchievement;
    }

    // ==================== 用户成就相关方法实现 ====================

    @Override
    public List<UserAchievementVo> getUserAchievements(String userId) {
        return userAchievementMapper.selectByUserId(Long.valueOf(userId));
    }

    @Override
    public List<UserAchievementVo> getRecentAchievements(String userId, Integer limit) {
        return userAchievementMapper.selectCompletedByUserId(Long.valueOf(userId));
    }

    @Override
    public List<UserAchievementVo> getInProgressAchievements(String userId) {
        return userAchievementMapper.selectInProgressByUserId(Long.valueOf(userId));
    }

    @Override
    public UserAchievementVo getUserAchievementDetail(String userId, String achievementId) {
        UserAchievement userAchievement = userAchievementMapper.selectByUserIdAndAchievementId(
            Long.valueOf(userId), Long.valueOf(achievementId));

        if (userAchievement == null) {
            return null;
        }

        // 转换为VO并补充成就信息
        UserAchievementVo vo = MapstructUtils.convert(userAchievement, UserAchievementVo.class);
        AchievementVo achievement = achievementMapper.selectVoById(Long.valueOf(achievementId));
        if (achievement != null) {
            vo.setAchievementCode(achievement.getAchievementCode());
            vo.setAchievementName(achievement.getAchievementName());
            vo.setAchievementDesc(achievement.getAchievementDesc());
            vo.setAchievementIcon(achievement.getAchievementIcon());
            vo.setAchievementType(achievement.getAchievementType());
            vo.setRewardPoints(achievement.getRewardPoints());
        }

        return vo;
    }

    @Override
    public List<AchievementVo> checkAndUpdateAchievements(String userId) {
        List<AchievementVo> newlyUnlocked = new ArrayList<>();

        try {
            // 获取所有激活的成就
            List<AchievementVo> activeAchievements = queryActiveAchievements();

            for (AchievementVo achievement : activeAchievements) {
                // 检查用户是否已有此成就记录
                UserAchievement userAchievement = userAchievementMapper.selectByUserIdAndAchievementId(
                    Long.valueOf(userId), achievement.getId());

                if (userAchievement == null) {
                    // 创建新的用户成就记录
                    userAchievement = createUserAchievement(Long.valueOf(userId), achievement);
                    userAchievementMapper.insert(userAchievement);
                }

                // 如果已经完成，跳过
                if ("1".equals(userAchievement.getIsCompleted())) {
                    continue;
                }

                // 重新计算进度
                Map<String, Object> triggerCondition = JSONUtil.toBean(achievement.getTriggerCondition(), Map.class);
                long currentValue = getCurrentValue(Long.valueOf(userId), triggerCondition);
                long targetValue = getTargetValue(triggerCondition);

                BigDecimal progress = BigDecimal.valueOf((double) currentValue / targetValue * 100);
                if (progress.compareTo(BigDecimal.valueOf(100)) > 0) {
                    progress = BigDecimal.valueOf(100);
                }

                // 更新进度
                userAchievementMapper.updateProgress(Long.valueOf(userId), achievement.getId(), currentValue, progress);

                // 检查是否完成
                if (progress.compareTo(BigDecimal.valueOf(100)) >= 0) {
                    completeAchievement(Long.valueOf(userId), achievement.getId());
                    newlyUnlocked.add(achievement);

                    log.info("用户手动检查完成成就: userId={}, achievementCode={}", userId, achievement.getAchievementCode());
                }
            }

        } catch (Exception e) {
            log.error("手动检查用户成就失败: userId={}", userId, e);
        }

        return newlyUnlocked;
    }

    @Override
    public int initializeUserAchievements(String userId) {
        try {
            // 获取所有激活的成就
            List<AchievementVo> activeAchievements = queryActiveAchievements();
            int initializedCount = 0;

            for (AchievementVo achievement : activeAchievements) {
                // 检查用户是否已有此成就记录
                UserAchievement existing = userAchievementMapper.selectByUserIdAndAchievementId(
                    Long.valueOf(userId), achievement.getId());

                if (existing == null) {
                    // 创建新的用户成就记录
                    UserAchievement userAchievement = createUserAchievement(Long.valueOf(userId), achievement);
                    userAchievementMapper.insert(userAchievement);
                    initializedCount++;
                }
            }

            log.info("用户成就初始化完成: userId={}, count={}", userId, initializedCount);
            return initializedCount;

        } catch (Exception e) {
            log.error("初始化用户成就失败: userId={}", userId, e);
            return 0;
        }
    }

    // ==================== 成就统计和排行榜相关方法 ====================

    @Override
    public AchievementStatsVo getAchievementStats(String userId) {
        try {
            AchievementStatsVo stats = new AchievementStatsVo();

            // 获取总成就数
            int totalAchievements = achievementMapper.countTotalAchievements();
            stats.setTotalAchievements(totalAchievements);

            // 获取用户已完成成就数
            Long completedCount = userAchievementMapper.countCompletedByUserId(Long.valueOf(userId));
            stats.setCompletedAchievements(completedCount != null ? completedCount.intValue() : 0);

            // 获取用户总积分
            Long totalPoints = userAchievementMapper.sumRewardPointsByUserId(Long.valueOf(userId));
            stats.setTotalPoints(totalPoints != null ? totalPoints.intValue() : 0);

            // 计算完成率
            if (totalAchievements > 0) {
                double completionRate = (double) stats.getCompletedAchievements() / totalAchievements * 100;
                stats.setCompletionRate(Math.round(completionRate * 100.0) / 100.0);
            } else {
                stats.setCompletionRate(0.0);
            }

            // 获取各类型成就统计
            Map<String, Integer> categoryStats = new HashMap<>();
            for (String type : Arrays.asList("LOGIN", "LEARNING", "SOCIAL", "TIME", "CUSTOM")) {
                int typeCount = achievementMapper.countAchievementsByType(type);
                categoryStats.put(type, typeCount);
            }
            stats.setCategoryStats(categoryStats);

            return stats;

        } catch (Exception e) {
            log.error("获取成就统计失败: userId={}", userId, e);
            return new AchievementStatsVo();
        }
    }

    @Override
    public List<UserAchievementVo> getRecommendedAchievements(String userId, Integer limit) {
        try {
            // 获取用户进行中的成就，按进度排序
            List<UserAchievementVo> inProgress = userAchievementMapper.selectInProgressByUserId(Long.valueOf(userId));

            // 按进度降序排序，推荐接近完成的成就
            inProgress.sort((a, b) -> b.getProgress().compareTo(a.getProgress()));

            // 限制返回数量
            if (limit != null && limit > 0 && inProgress.size() > limit) {
                return inProgress.subList(0, limit);
            }

            return inProgress;

        } catch (Exception e) {
            log.error("获取推荐成就失败: userId={}, limit={}", userId, limit, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<AchievementStatsVo.LeaderboardEntry> getLeaderboard(String category, Integer limit) {
        try {
            log.debug("获取排行榜: category={}, limit={}", category, limit);
            return new ArrayList<>();

        } catch (Exception e) {
            log.error("获取排行榜失败: category={}, limit={}", category, limit, e);
            return new ArrayList<>();
        }
    }

    @Override
    public AchievementStatsVo.LeaderboardInfo getUserRankingInfo(String userId, String category) {
        try {
            AchievementStatsVo.LeaderboardInfo info = new AchievementStatsVo.LeaderboardInfo();
            info.setRank(0);

            log.debug("获取用户排名信息: userId={}, category={}", userId, category);
            return info;

        } catch (Exception e) {
            log.error("获取用户排名信息失败: userId={}, category={}", userId, category, e);
            return new AchievementStatsVo.LeaderboardInfo();
        }
    }

    @Override
    public Map<String, String> getCategories() {
        Map<String, String> categories = new HashMap<>();
        categories.put("LOGIN", "登录类");
        categories.put("LEARNING", "学习类");
        categories.put("SOCIAL", "社交类");
        categories.put("TIME", "时长类");
        categories.put("CUSTOM", "自定义");
        return categories;
    }

    @Override
    public Map<String, Object> shareAchievements(String userId, String platform) {
        try {
            Map<String, Object> result = new HashMap<>();

            // 获取用户成就统计
            AchievementStatsVo stats = getAchievementStats(userId);

            // 生成分享内容
            String shareText = String.format("我在学习平台上已经获得了%d个成就，累计%d积分！完成率%.1f%%，快来一起学习吧！",
                stats.getCompletedAchievements(), stats.getTotalPoints(), stats.getCompletionRate());

            // 生成分享链接（这里需要根据实际需求生成）
            String shareUrl = "https://your-domain.com/user/" + userId + "/achievements";

            result.put("shareText", shareText);
            result.put("shareUrl", shareUrl);
            result.put("platform", platform);
            result.put("success", true);

            // 记录分享行为
            log.info("用户分享成就墙: userId={}, platform={}", userId, platform);

            return result;

        } catch (Exception e) {
            log.error("分享成就墙失败: userId={}, platform={}", userId, platform, e);

            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("error", "分享失败");
            return result;
        }
    }

    // ==================== 事件处理和进度管理相关方法 ====================

    @Override
    public boolean recordEvent(String userId, String eventType, Map<String, Object> eventData,
                              Integer eventValue, String relatedId, String relatedType) {
        try {
            // 创建TrackEventDto
            TrackEventDto trackEventDto = new TrackEventDto();
            trackEventDto.setUserId(Long.valueOf(userId));
            trackEventDto.setEventType(eventType);
            trackEventDto.setEventData(eventData);
            trackEventDto.setTimestamp(System.currentTimeMillis());

            // 检查成就
            checkAchievements(Long.valueOf(userId), trackEventDto);

            log.debug("记录用户事件: userId={}, eventType={}", userId, eventType);
            return true;

        } catch (Exception e) {
            log.error("记录用户事件失败: userId={}, eventType={}", userId, eventType, e);
            return false;
        }
    }

    @Override
    public boolean unlockAchievement(String userId, String achievementId, String source) {
        try {
            // 检查成就是否存在
            Achievement achievement = achievementMapper.selectById(Long.valueOf(achievementId));
            if (achievement == null) {
                log.warn("成就不存在: achievementId={}", achievementId);
                return false;
            }

            // 检查用户是否已解锁
            UserAchievement userAchievement = userAchievementMapper.selectByUserIdAndAchievementId(
                Long.valueOf(userId), Long.valueOf(achievementId));

            if (userAchievement != null && "1".equals(userAchievement.getIsCompleted())) {
                log.warn("用户已解锁该成就: userId={}, achievementId={}", userId, achievementId);
                return false;
            }

            // 如果没有记录，创建一个
            if (userAchievement == null) {
                AchievementVo achievementVo = MapstructUtils.convert(achievement, AchievementVo.class);
                userAchievement = createUserAchievement(Long.valueOf(userId), achievementVo);
                userAchievementMapper.insert(userAchievement);
            }

            // 解锁成就
            completeAchievement(Long.valueOf(userId), Long.valueOf(achievementId));

            log.info("手动解锁成就: userId={}, achievementId={}, source={}", userId, achievementId, source);
            return true;

        } catch (Exception e) {
            log.error("解锁成就失败: userId={}, achievementId={}, source={}", userId, achievementId, source, e);
            return false;
        }
    }

    @Override
    public boolean updateAchievementProgress(String userId, String achievementId, Integer progress, Map<String, Object> eventData) {
        try {
            // 检查用户成就记录是否存在
            UserAchievement userAchievement = userAchievementMapper.selectByUserIdAndAchievementId(
                Long.valueOf(userId), Long.valueOf(achievementId));

            if (userAchievement == null) {
                log.warn("用户成就记录不存在: userId={}, achievementId={}", userId, achievementId);
                return false;
            }

            // 如果已经完成，不再更新
            if ("1".equals(userAchievement.getIsCompleted())) {
                log.debug("成就已完成，跳过更新: userId={}, achievementId={}", userId, achievementId);
                return false;
            }

            // 更新进度
            BigDecimal progressDecimal = BigDecimal.valueOf(progress);
            userAchievementMapper.updateProgress(Long.valueOf(userId), Long.valueOf(achievementId),
                progress.longValue(), progressDecimal);

            // 检查是否完成
            if (progress >= 100) {
                completeAchievement(Long.valueOf(userId), Long.valueOf(achievementId));
                log.info("成就进度更新并完成: userId={}, achievementId={}, progress={}", userId, achievementId, progress);
                return true;
            }

            log.debug("成就进度更新: userId={}, achievementId={}, progress={}", userId, achievementId, progress);
            return false;

        } catch (Exception e) {
            log.error("更新成就进度失败: userId={}, achievementId={}, progress={}", userId, achievementId, progress, e);
            return false;
        }
    }

    @Override
    public Map<String, Object> getEventStatistics(String userId, String eventType, Integer days) {
        try {
            Map<String, Object> stats = new HashMap<>();

            // 计算时间范围
            Date endTime = new Date();
            Date startTime = new Date(endTime.getTime() - days * 24 * 60 * 60 * 1000L);

            // 获取指定时间范围内的行为记录
            List<org.dromara.app.domain.UserBehavior> behaviors;
            if (eventType != null && !eventType.isEmpty()) {
                behaviors = userBehaviorMapper.selectByUserIdAndBehaviorType(Long.valueOf(userId), eventType);
            } else {
                behaviors = userBehaviorMapper.selectByUserIdAndTimeRange(Long.valueOf(userId), startTime, endTime);
            }

            // 统计数据
            stats.put("totalEvents", behaviors.size());
            stats.put("eventType", eventType);
            stats.put("days", days);
            stats.put("startTime", startTime);
            stats.put("endTime", endTime);

            // 按日期分组统计
            Map<String, Integer> dailyStats = new HashMap<>();
            for (org.dromara.app.domain.UserBehavior behavior : behaviors) {
                String date = behavior.getCreateTime().toString().substring(0, 10);
                dailyStats.put(date, dailyStats.getOrDefault(date, 0) + 1);
            }
            stats.put("dailyStats", dailyStats);

            return stats;

        } catch (Exception e) {
            log.error("获取事件统计失败: userId={}, eventType={}, days={}", userId, eventType, days, e);
            return new HashMap<>();
        }
    }

    // ==================== 系统维护和管理相关方法 ====================

    @Override
    public int processUnhandledEvents(int maxEvents) {
        try {
            // 这里需要实现处理未处理事件的逻辑
            // 由于当前没有专门的未处理事件表，我们先返回0
            // 在实际项目中，可能需要一个事件队列表来存储待处理的事件

            log.debug("处理未处理事件: maxEvents={}", maxEvents);
            return 0;

        } catch (Exception e) {
            log.error("处理未处理事件失败: maxEvents={}", maxEvents, e);
            return 0;
        }
    }

    @Override
    public int cleanupExpiredEvents(int days) {
        try {
            // 这里需要实现清理过期事件的逻辑
            // 可以删除指定天数之前的用户行为记录

            Date cutoffDate = new Date(System.currentTimeMillis() - days * 24 * 60 * 60 * 1000L);

            // 由于当前的Mapper接口中没有批量删除方法，我们先返回0
            // 在实际项目中，需要在UserBehaviorMapper中添加批量删除方法

            log.info("清理过期事件: days={}, cutoffDate={}", days, cutoffDate);
            return 0;

        } catch (Exception e) {
            log.error("清理过期事件失败: days={}", days, e);
            return 0;
        }
    }

    @Override
    public Map<String, Object> getUserAchievementCompletion(String userId) {
        try {
            Map<String, Object> completion = new HashMap<>();

            // 获取成就统计
            AchievementStatsVo stats = getAchievementStats(userId);

            completion.put("totalAchievements", stats.getTotalAchievements());
            completion.put("completedAchievements", stats.getCompletedAchievements());
            completion.put("completionRate", stats.getCompletionRate());
            completion.put("totalPoints", stats.getTotalPoints());

            // 按类型统计完成情况
            Map<String, Object> categoryCompletion = new HashMap<>();
            for (String type : Arrays.asList("LOGIN", "LEARNING", "SOCIAL", "TIME", "CUSTOM")) {
                Map<String, Object> typeStats = new HashMap<>();

                // 获取该类型的总成就数
                int totalInType = achievementMapper.countAchievementsByType(type);

                // 获取用户在该类型中已完成的成就数
                List<UserAchievementVo> userAchievements = userAchievementMapper.selectStatsByUserIdAndType(
                    Long.valueOf(userId), type);

                long completedInType = userAchievements.stream()
                    .filter(ua -> "1".equals(ua.getIsCompleted()))
                    .count();

                typeStats.put("total", totalInType);
                typeStats.put("completed", completedInType);
                typeStats.put("rate", totalInType > 0 ? (double) completedInType / totalInType * 100 : 0.0);

                categoryCompletion.put(type, typeStats);
            }

            completion.put("categoryCompletion", categoryCompletion);

            return completion;

        } catch (Exception e) {
            log.error("获取用户成就完成度失败: userId={}", userId, e);
            return new HashMap<>();
        }
    }

    @Override
    public int recalculateUserProgress(String userId) {
        try {
            int recalculatedCount = 0;

            // 获取用户所有成就记录
            List<UserAchievementVo> userAchievements = userAchievementMapper.selectByUserId(Long.valueOf(userId));

            for (UserAchievementVo userAchievement : userAchievements) {
                // 如果已经完成，跳过
                if ("1".equals(userAchievement.getIsCompleted())) {
                    continue;
                }

                // 获取成就信息
                Achievement achievement = achievementMapper.selectById(userAchievement.getAchievementId());
                if (achievement == null) {
                    continue;
                }

                // 重新计算进度
                Map<String, Object> triggerCondition = JSONUtil.toBean(achievement.getTriggerCondition(), Map.class);
                long currentValue = getCurrentValue(Long.valueOf(userId), triggerCondition);
                long targetValue = getTargetValue(triggerCondition);

                BigDecimal progress = BigDecimal.valueOf((double) currentValue / targetValue * 100);
                if (progress.compareTo(BigDecimal.valueOf(100)) > 0) {
                    progress = BigDecimal.valueOf(100);
                }

                // 更新进度
                userAchievementMapper.updateProgress(Long.valueOf(userId), userAchievement.getAchievementId(),
                    currentValue, progress);

                // 检查是否完成
                if (progress.compareTo(BigDecimal.valueOf(100)) >= 0) {
                    completeAchievement(Long.valueOf(userId), userAchievement.getAchievementId());
                }

                recalculatedCount++;
            }

            log.info("重新计算用户成就进度完成: userId={}, count={}", userId, recalculatedCount);
            return recalculatedCount;

        } catch (Exception e) {
            log.error("重新计算用户成就进度失败: userId={}", userId, e);
            return 0;
        }
    }

    // ==================== 徽章相关方法实现（简化版） ====================

    @Override
    public List<BadgeVo> getUserBadges(String userId, String category, Boolean unlocked, String rarity) {
        // 徽章功能暂时返回空列表，可以后续扩展
        log.debug("获取用户徽章: userId={}, category={}, unlocked={}, rarity={}", userId, category, unlocked, rarity);
        return new ArrayList<>();
    }

    @Override
    public BadgeVo getBadgeDetail(String userId, String badgeId) {
        // 徽章功能暂时返回null，可以后续扩展
        log.debug("获取徽章详情: userId={}, badgeId={}", userId, badgeId);
        return null;
    }

    @Override
    public void setPinStatus(String userId, String badgeId, Boolean isPinned) {
        // 徽章功能暂时为空实现，可以后续扩展
        log.debug("设置徽章置顶状态: userId={}, badgeId={}, isPinned={}", userId, badgeId, isPinned);
    }

    @Override
    public List<BadgeVo> getPinnedBadges(String userId) {
        // 徽章功能暂时返回空列表，可以后续扩展
        log.debug("获取置顶徽章: userId={}", userId);
        return new ArrayList<>();
    }

    @Override
    public List<UserAchievementVo> getRecentUserAchievements(String userId, Integer limit) {
        // 获取用户最近的成就记录
        List<UserAchievementVo> recentAchievements = userAchievementMapper.selectRecentByUserId(Long.valueOf(userId), limit);

        // 获取用户徽章信息
        List<BadgeVo> badges = getUserBadges(userId, null, null, null);


        return recentAchievements;
    }

}
