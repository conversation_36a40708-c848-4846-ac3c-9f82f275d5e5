package org.dromara.app.service;

import org.dromara.app.domain.QuestionComment;
import org.dromara.app.domain.bo.QuestionCommentBo;
import org.dromara.app.domain.vo.QuestionCommentVO;
import org.dromara.app.mapper.QuestionCommentMapper;
import org.dromara.app.service.impl.QuestionCommentServiceImpl;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 题目评论Service测试类
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class QuestionCommentServiceTest {

    @Mock
    private QuestionCommentMapper questionCommentMapper;

    @InjectMocks
    private QuestionCommentServiceImpl questionCommentService;

    private QuestionCommentVO mockCommentVO;
    private QuestionComment mockComment;
    private QuestionCommentBo mockCommentBo;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        mockCommentVO = new QuestionCommentVO();
        mockCommentVO.setId("1");
        mockCommentVO.setQuestionId("1");
        mockCommentVO.setUserId("1");
        mockCommentVO.setContent("测试评论内容");
        mockCommentVO.setLikes(5);
        mockCommentVO.setReplyCount(2);

        mockComment = new QuestionComment();
        mockComment.setCommentId(1L);
        mockComment.setQuestionId(1L);
        mockComment.setUserId(1L);
        mockComment.setContent("测试评论内容");
        mockComment.setLikeCount(5);
        mockComment.setReplyCount(2);
        mockComment.setStatus("0");

        mockCommentBo = new QuestionCommentBo();
        mockCommentBo.setQuestionId(1L);
        mockCommentBo.setUserId(1L);
        mockCommentBo.setContent("测试评论内容");
    }

    @Test
    void testQueryById() {
        // Given
        when(questionCommentMapper.selectVoById(1L)).thenReturn(mockCommentVO);

        // When
        QuestionCommentVO result = questionCommentService.queryById(1L);

        // Then
        assertNotNull(result);
        assertEquals("1", result.getId());
        assertEquals("测试评论内容", result.getContent());
        verify(questionCommentMapper).selectVoById(1L);
    }

    @Test
    void testCreateQuestionComment() {
        // Given
        when(questionCommentMapper.insert(any(QuestionComment.class))).thenReturn(1);
        when(questionCommentMapper.selectVoById(any(Long.class))).thenReturn(mockCommentVO);

        // When
        QuestionCommentVO result = questionCommentService.createQuestionComment(1L, "1", "测试评论", null);

        // Then
        assertNotNull(result);
        assertEquals("测试评论内容", result.getContent());
        verify(questionCommentMapper).insert(any(QuestionComment.class));
        verify(questionCommentMapper).updateQuestionCommentCount(1L, 1);
    }

    @Test
    void testCreateQuestionCommentWithReply() {
        // Given
        when(questionCommentMapper.insert(any(QuestionComment.class))).thenReturn(1);
        when(questionCommentMapper.selectVoById(any(Long.class))).thenReturn(mockCommentVO);

        // When
        QuestionCommentVO result = questionCommentService.createQuestionComment(1L, "1", "测试回复", 1L);

        // Then
        assertNotNull(result);
        verify(questionCommentMapper).insert(any(QuestionComment.class));
        verify(questionCommentMapper).incrementReplyCount(1L); // 父评论回复数+1
        verify(questionCommentMapper).updateQuestionCommentCount(1L, 1);
    }

    @Test
    void testLikeQuestionComment() {
        // Given
        when(questionCommentMapper.selectLikeStatus(1L, 1L)).thenReturn(false);
        when(questionCommentMapper.selectById(1L)).thenReturn(mockComment);

        // When
        Map<String, Object> result = questionCommentService.likeQuestionComment(1L, "1");

        // Then
        assertNotNull(result);
        assertEquals(true, result.get("isLiked"));
        assertEquals("点赞成功", result.get("message"));
        verify(questionCommentMapper).insertLikeRecord(1L, 1L);
        verify(questionCommentMapper).incrementLikeCount(1L);
    }

    @Test
    void testUnlikeQuestionComment() {
        // Given
        when(questionCommentMapper.selectLikeStatus(1L, 1L)).thenReturn(true);
        when(questionCommentMapper.selectById(1L)).thenReturn(mockComment);

        // When
        Map<String, Object> result = questionCommentService.likeQuestionComment(1L, "1");

        // Then
        assertNotNull(result);
        assertEquals(false, result.get("isLiked"));
        assertEquals("取消点赞成功", result.get("message"));
        verify(questionCommentMapper).deleteLikeRecord(1L, 1L);
        verify(questionCommentMapper).decrementLikeCount(1L);
    }

    @Test
    void testDeleteQuestionComment() {
        // Given
        when(questionCommentMapper.selectById(1L)).thenReturn(mockComment);
        when(questionCommentMapper.deleteById(1L)).thenReturn(1);

        // When
        Boolean result = questionCommentService.deleteQuestionComment(1L, "1");

        // Then
        assertTrue(result);
        verify(questionCommentMapper).deleteById(1L);
        verify(questionCommentMapper).updateQuestionCommentCount(1L, -1);
    }

    @Test
    void testDeleteQuestionCommentWithReply() {
        // Given
        mockComment.setParentId(2L); // 这是一个回复
        when(questionCommentMapper.selectById(1L)).thenReturn(mockComment);
        when(questionCommentMapper.deleteById(1L)).thenReturn(1);

        // When
        Boolean result = questionCommentService.deleteQuestionComment(1L, "1");

        // Then
        assertTrue(result);
        verify(questionCommentMapper).deleteById(1L);
        verify(questionCommentMapper).decrementReplyCount(2L); // 父评论回复数-1
        verify(questionCommentMapper).updateQuestionCommentCount(1L, -1);
    }

    @Test
    void testDeleteQuestionCommentUnauthorized() {
        // Given
        mockComment.setUserId(2L); // 不同的用户ID
        when(questionCommentMapper.selectById(1L)).thenReturn(mockComment);

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            questionCommentService.deleteQuestionComment(1L, "1");
        });
        assertEquals("无权限删除此评论", exception.getMessage());
    }

    @Test
    void testGetCommentStatistics() {
        // Given
        when(questionCommentMapper.selectCount(any())).thenReturn(10L, 7L);

        // When
        Map<String, Object> result = questionCommentService.getCommentStatistics("1");

        // Then
        assertNotNull(result);
        assertEquals(10L, result.get("totalComments"));
        assertEquals(7L, result.get("mainComments"));
        assertEquals(3L, result.get("replies"));
    }

    @Test
    void testInsertByBoValidation() {
        // Given
        QuestionCommentBo invalidBo = new QuestionCommentBo();
        invalidBo.setContent(""); // 空内容

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            questionCommentService.insertByBo(invalidBo);
        });
        assertEquals("评论内容不能为空", exception.getMessage());
    }

    @Test
    void testInsertByBoContentTooLong() {
        // Given
        QuestionCommentBo invalidBo = new QuestionCommentBo();
        invalidBo.setQuestionId(1L);
        invalidBo.setUserId(1L);
        invalidBo.setContent("a".repeat(1001)); // 超过1000字符

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            questionCommentService.insertByBo(invalidBo);
        });
        assertEquals("评论内容不能超过1000个字符", exception.getMessage());
    }

    @Test
    void testBatchDeleteComments() {
        // Given
        List<Long> commentIds = Arrays.asList(1L, 2L, 3L);
        List<QuestionComment> comments = Arrays.asList(
            createMockComment(1L, 1L, null),
            createMockComment(2L, 1L, 1L),
            createMockComment(3L, 2L, null)
        );
        when(questionCommentMapper.selectBatchIds(commentIds)).thenReturn(comments);
        when(questionCommentMapper.deleteBatchIds(commentIds)).thenReturn(3);

        // When
        Boolean result = questionCommentService.batchDeleteComments(commentIds, 1L);

        // Then
        assertTrue(result);
        verify(questionCommentMapper).deleteBatchIds(commentIds);
        verify(questionCommentMapper).decrementReplyCount(1L); // 评论2是评论1的回复
        verify(questionCommentMapper).updateQuestionCommentCount(1L, -2); // 题目1减少2个评论
        verify(questionCommentMapper).updateQuestionCommentCount(2L, -1); // 题目2减少1个评论
    }

    private QuestionComment createMockComment(Long commentId, Long questionId, Long parentId) {
        QuestionComment comment = new QuestionComment();
        comment.setCommentId(commentId);
        comment.setQuestionId(questionId);
        comment.setUserId(1L);
        comment.setParentId(parentId);
        comment.setContent("测试评论" + commentId);
        comment.setStatus("0");
        return comment;
    }
}
