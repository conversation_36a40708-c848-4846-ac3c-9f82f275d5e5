package org.dromara.app.service;

import org.dromara.app.domain.bo.JobQueryBo;
import org.dromara.app.domain.vo.InterviewResponseVo;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.List;
import java.util.Map;

/**
 * 面试服务接口
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
public interface IInterviewService {

    /**
     * 获取岗位列表
     *
     * @param queryBo 查询参数
     * @return 岗位列表响应
     */
    TableDataInfo<InterviewResponseVo.JobListResponse> getJobList(JobQueryBo queryBo);

    /**
     * 获取岗位分类列表
     *
     * @param includeJobCount 是否包含岗位数量
     * @return 分类列表响应
     */
    InterviewResponseVo.CategoriesResponse getCategories(Boolean includeJobCount);

    /**
     * 获取面试模式列表
     *
     * @return 面试模式列表响应
     */
    InterviewResponseVo.InterviewModesResponse getInterviewModes();

    /**
     * 获取搜索建议
     *
     * @param keyword 搜索关键词
     * @param limit   返回数量限制
     * @return 搜索建议响应
     */
    InterviewResponseVo.SearchSuggestionsResponse getSearchSuggestions(String keyword, Integer limit);

    /**
     * 创建面试会话
     *
     * @param jobId               岗位ID
     * @param mode                面试模式
     * @param resumeUrl           简历URL
     * @param customizedQuestions 自定义问题列表
     * @return 创建会话响应
     */
    InterviewResponseVo.CreateSessionResponse createInterviewSession(Long jobId, String mode, String resumeUrl, List<String> customizedQuestions);

    /**
     * 收藏/取消收藏岗位
     *
     * @param jobId       岗位ID
     * @param isFavorited 收藏状态
     */
    void favoriteJob(Long jobId, Boolean isFavorited);

    /**
     * 设备检测
     *
     * @return 设备检测结果
     */
    InterviewResponseVo.DeviceCheckResult checkDevice();

    /**
     * 获取统计信息
     *
     * @return 统计信息响应
     */
    InterviewResponseVo.StatisticsResponse getStatistics();

    /**
     * 获取面试历史记录
     *
     * @param page     页码
     * @param pageSize 每页大小
     * @param category 分类筛选
     * @param status   状态筛选
     * @return 历史记录列表响应
     */
    InterviewResponseVo.HistoryListResponse getInterviewHistory(Integer page, Integer pageSize, String category, String status);

    /**
     * 获取用户统计数据
     *
     * @return 用户统计数据
     */
    InterviewResponseVo.Statistics getUserStatistics();

    /**
     * 获取岗位详情
     *
     * @param jobId 岗位ID
     * @return 岗位详情响应
     */
    InterviewResponseVo.JobDetailResponse getJobDetail(Long jobId);

    /**
     * 获取示例问题
     *
     * @param jobId 岗位ID
     * @param count 问题数量
     * @return 示例问题响应
     */
    InterviewResponseVo.SampleQuestionsResponse getSampleQuestions(Long jobId, Integer count);

    /**
     * 获取相关岗位
     *
     * @param jobId 当前岗位ID
     * @param limit 数量限制
     * @return 相关岗位响应
     */
    InterviewResponseVo.RelatedJobsResponse getRelatedJobs(Long jobId, Integer limit);

    /**
     * 获取岗位统计数据
     *
     * @param jobId 岗位ID
     * @return 岗位统计响应
     */
    InterviewResponseVo.JobStatisticsResponse getJobStatistics(Long jobId);

    /**
     * 获取岗位面试模式列表
     *
     * @param jobId 岗位ID
     * @return 岗位面试模式响应
     */
    InterviewResponseVo.JobInterviewModesResponse getJobInterviewModes(Long jobId);

    /**
     * 检查用户准备度
     *
     * @param jobId  岗位ID
     * @param userId 用户ID
     * @return 用户准备度响应
     */
    InterviewResponseVo.UserReadinessResponse checkUserReadiness(Long jobId, String userId);

    /**
     * 分享岗位信息
     *
     * @param jobId    岗位ID
     * @param platform 分享平台
     * @return 分享岗位响应
     */
    InterviewResponseVo.ShareJobResponse shareJob(Long jobId, String platform);

    /**
     * 获取会话信息
     *
     * @param sessionId 会话ID
     * @return 会话信息
     */
    InterviewResponseVo.SessionInfo getSessionInfo(String sessionId);

    /**
     * 获取面试问题列表
     *
     * @param sessionId 会话ID
     * @return 问题列表响应
     */
    InterviewResponseVo.QuestionListResponse getSessionQuestions(String sessionId);

    /**
     * 提交面试回答
     *
     * @param sessionId  会话ID
     * @param questionId 问题ID
     * @param answer     回答内容
     * @param audioUrl   音频URL
     * @param duration   回答时长(秒)
     * @return 回答响应
     */
    InterviewResponseVo.AnswerResponse submitAnswer(String sessionId, String questionId, String answer, String audioUrl, Integer duration);

    /**
     * 结束面试会话
     *
     * @param sessionId 会话ID
     * @param reason    结束原因
     * @return 结束会话响应
     */
    InterviewResponseVo.EndSessionResponse endSession(String sessionId, String reason);

    /**
     * 提交面试反馈
     *
     * @param sessionId 会话ID
     * @param rating    评分
     * @param comments  评论
     * @param tags      标签
     * @return 反馈响应
     */
    InterviewResponseVo.FeedbackResponse submitFeedback(String sessionId, Integer rating, String comments, List<String> tags);

    /**
     * 获取面试结果
     *
     * @param sessionId 会话ID
     * @return 面试结果
     */
    InterviewResponseVo.InterviewResult getInterviewResult(String sessionId);

    /**
     * 检查设备状态
     *
     * @return 设备状态映射
     */
    Map<String, Boolean> checkDevices();

    /**
     * 获取会话状态
     *
     * @param sessionId 会话ID
     * @return 会话状态
     */
    InterviewResponseVo.SessionStatus getSessionStatus(String sessionId);

    /**
     * 获取AI面试官信息
     *
     * @param interviewerId 面试官ID
     * @return 面试官信息
     */
    InterviewResponseVo.InterviewerInfo getInterviewerInfo(String interviewerId);
}
