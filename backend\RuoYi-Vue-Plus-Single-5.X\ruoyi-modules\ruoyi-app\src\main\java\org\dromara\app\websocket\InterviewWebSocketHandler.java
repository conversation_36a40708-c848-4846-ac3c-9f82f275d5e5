package org.dromara.app.websocket;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.service.IInterviewAnalysisService;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.*;

import java.io.IOException;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CompletableFuture;

/**
 * 面试WebSocket处理器
 * 处理实时智能建议和表情分析功能
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class Interview<PERSON>eb<PERSON>ocket<PERSON>and<PERSON> implements WebSocketHandler {

    private final IInterviewAnalysisService interviewAnalysisService;

    // 存储会话信息
    private final ConcurrentHashMap<String, WebSocketSession> sessions = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, InterviewContext> contexts = new ConcurrentHashMap<>();

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        String sessionId = session.getId();
        sessions.put(sessionId, session);
        log.info("面试WebSocket连接建立: {}", sessionId);
    }

    @Override
    public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
        if (message instanceof TextMessage) {
            String payload = ((TextMessage) message).getPayload();

            try {
                JSONObject jsonMessage = JSONUtil.parseObj(payload);
                String type = jsonMessage.getStr("type");
                String sessionId = jsonMessage.getStr("sessionId");

                // 更新会话上下文
                updateContext(session.getId(), sessionId, jsonMessage);

                switch (type) {
                    case "connect":
                        handleConnect(session, jsonMessage);
                        break;
                    case "emotion_request":
                        handleEmotionRequest(session, jsonMessage);
                        break;
                    case "speech_analysis":
                        handleSpeechAnalysis(session, jsonMessage);
                        break;
                    case "heartbeat":
                        handleHeartbeat(session, jsonMessage);
                        break;
                    default:
                        log.warn("未知消息类型: {}", type);
                }
            } catch (Exception e) {
                log.error("处理WebSocket消息失败", e);
                sendErrorMessage(session, "消息处理失败: " + e.getMessage());
            }
        }
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        log.error("面试WebSocket传输错误: {}", session.getId(), exception);
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
        String sessionId = session.getId();
        sessions.remove(sessionId);
        contexts.remove(sessionId);
        log.info("面试WebSocket连接关闭: {}, 状态: {}", sessionId, closeStatus);
    }

    @Override
    public boolean supportsPartialMessages() {
        return false;
    }

    /**
     * 处理连接确认
     */
    private void handleConnect(WebSocketSession session, JSONObject message) {
        log.info("处理连接确认: {}", message.getStr("sessionId"));
        // 可以在这里初始化用户的面试上下文
    }

    /**
     * 处理表情检测请求
     */
    private void handleEmotionRequest(WebSocketSession session, JSONObject message) {
        CompletableFuture.runAsync(() -> {
            try {
                JSONObject data = message.getJSONObject("data");
                String imageData = data.getStr("imageData");
                Integer questionId = data.getInt("questionId");


                // 调用表情分析服务
                var emotionResult = interviewAnalysisService.analyzeEmotion(imageData, questionId);

                // 构建响应消息
                JSONObject response = new JSONObject();
                response.put("type", "emotion_result");
                response.put("timestamp", System.currentTimeMillis());
                response.put("data", emotionResult);

                // 发送结果
                sendMessage(session, response);

                // 基于情绪分析结果生成智能建议
                generateEmotionBasedSuggestion(session, emotionResult, questionId);

            } catch (Exception e) {
                log.error("表情分析处理失败", e);
                sendErrorMessage(session, "表情分析失败: " + e.getMessage());
            }
        });
    }

    /**
     * 处理语音分析请求
     */
    private void handleSpeechAnalysis(WebSocketSession session, JSONObject message) {
        CompletableFuture.runAsync(() -> {
            try {
                JSONObject data = message.getJSONObject("data");
                String audioData = data.getStr("audioData");
                Integer questionId = data.getInt("questionId");


                // 调用语音分析服务
                var speechResult = interviewAnalysisService.analyzeSpeech(audioData, questionId);

                // 基于语音分析结果生成智能建议
                generateSpeechBasedSuggestion(session, speechResult, questionId);

            } catch (Exception e) {
                log.error("语音分析处理失败", e);
                sendErrorMessage(session, "语音分析失败: " + e.getMessage());
            }
        });
    }

    /**
     * 处理心跳
     */
    private void handleHeartbeat(WebSocketSession session, JSONObject message) {
        JSONObject response = new JSONObject();
        response.put("type", "heartbeat");
        response.put("timestamp", System.currentTimeMillis());
        sendMessage(session, response);
    }

    /**
     * 基于情绪分析生成智能建议
     */
    private void generateEmotionBasedSuggestion(WebSocketSession session, JSONObject emotionResult, Integer questionId) {
        try {
            var suggestion = interviewAnalysisService.generateEmotionSuggestion(emotionResult, questionId);
            if (suggestion != null) {
                JSONObject response = new JSONObject();
                response.put("type", "suggestion");
                response.put("timestamp", System.currentTimeMillis());
                response.put("data", suggestion);
                sendMessage(session, response);
            }
        } catch (Exception e) {
            log.error("生成情绪建议失败", e);
        }
    }

    /**
     * 基于语音分析生成智能建议
     */
    private void generateSpeechBasedSuggestion(WebSocketSession session, JSONObject speechResult, Integer questionId) {
        try {
            var suggestion = interviewAnalysisService.generateSpeechSuggestion(speechResult, questionId);
            if (suggestion != null) {
                JSONObject response = new JSONObject();
                response.put("type", "suggestion");
                response.put("timestamp", System.currentTimeMillis());
                response.put("data", suggestion);
                sendMessage(session, response);
            }
        } catch (Exception e) {
            log.error("生成语音建议失败", e);
        }
    }

    /**
     * 更新会话上下文
     */
    private void updateContext(String wsSessionId, String interviewSessionId, JSONObject message) {
        InterviewContext context = contexts.computeIfAbsent(wsSessionId, k -> new InterviewContext());
        context.setInterviewSessionId(interviewSessionId);
        context.setLastActivity(System.currentTimeMillis());
        // 可以在这里更新更多上下文信息
    }

    /**
     * 发送消息
     */
    private void sendMessage(WebSocketSession session, JSONObject message) {
        try {
            if (session.isOpen()) {
                session.sendMessage(new TextMessage(message.toString()));
            }
        } catch (IOException e) {
            log.error("发送WebSocket消息失败", e);
        }
    }

    /**
     * 发送错误消息
     */
    private void sendErrorMessage(WebSocketSession session, String errorMessage) {
        JSONObject response = new JSONObject();
        response.put("type", "error");
        response.put("timestamp", System.currentTimeMillis());
        response.put("message", errorMessage);
        sendMessage(session, response);
    }

    /**
     * 面试上下文
     */
    private static class InterviewContext {
        private String interviewSessionId;
        private Long lastActivity;
        private Integer currentQuestionId;

        // getters and setters
        public String getInterviewSessionId() { return interviewSessionId; }
        public void setInterviewSessionId(String interviewSessionId) { this.interviewSessionId = interviewSessionId; }
        public Long getLastActivity() { return lastActivity; }
        public void setLastActivity(Long lastActivity) { this.lastActivity = lastActivity; }
        public Integer getCurrentQuestionId() { return currentQuestionId; }
        public void setCurrentQuestionId(Integer currentQuestionId) { this.currentQuestionId = currentQuestionId; }
    }
}
