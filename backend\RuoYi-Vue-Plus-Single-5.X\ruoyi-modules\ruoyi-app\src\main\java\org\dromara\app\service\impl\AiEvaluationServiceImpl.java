package org.dromara.app.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.InterviewAnswer;
import org.dromara.app.domain.InterviewSession;
import org.dromara.app.domain.Job;
import org.dromara.app.domain.Question;
import org.dromara.app.domain.vo.InterviewResponseVo;
import org.dromara.app.mapper.InterviewAnswerMapper;
import org.dromara.app.mapper.InterviewSessionMapper;
import org.dromara.app.mapper.JobMapper;
import org.dromara.app.mapper.QuestionMapper;
import org.dromara.app.service.IAiEvaluationService;
import org.dromara.common.chat.service.LangChain4jChatService;
import org.dromara.common.core.exception.ServiceException;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * AI评估服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AiEvaluationServiceImpl implements IAiEvaluationService {

    private final LangChain4jChatService chatService;
    private final JobMapper jobMapper;
    private final QuestionMapper questionMapper;
    private final InterviewSessionMapper interviewSessionMapper;
    private final InterviewAnswerMapper interviewAnswerMapper;

    @Override
    public InterviewResponseVo.FeedbackInfo evaluateAnswer(String question, String answer,
                                                           String audioUrl, String videoUrl,
                                                           Integer duration, String jobContext) {
        log.info("开始AI评估回答，问题长度: {}, 回答长度: {}", question.length(), answer.length());

        try {
            // 构建评估提示词
            String prompt = buildEvaluationPrompt(question, answer, jobContext, duration);

            // 调用AI服务进行评估（使用面试评估方法）
            // 创建一个简单的上下文
            String aiResponse = chatService.evaluateInterviewAnswer(null, question, answer);

            // 解析AI响应并构建反馈信息
            return parseAiEvaluationResponse(aiResponse, answer, duration);

        } catch (Exception e) {
            log.error("AI评估失败", e);
            // 返回默认评估结果
            return createDefaultFeedback(answer, duration);
        }
    }

    @Override
    public List<InterviewResponseVo.InterviewQuestion> generateQuestions(Long jobId, String difficulty,
                                                                         Integer count, String resumeUrl,
                                                                         List<String> customQuestions) {
        log.info("生成面试问题，岗位ID: {}, 难度: {}, 数量: {}", jobId, difficulty, count);

        try {
            // 查询岗位信息
            Job job = jobMapper.selectById(jobId);
            if (job == null) {
                throw new ServiceException("岗位不存在");
            }

            List<InterviewResponseVo.InterviewQuestion> questions = new ArrayList<>();

            // 添加自定义问题
            if (customQuestions != null && !customQuestions.isEmpty()) {
                for (int i = 0; i < customQuestions.size() && questions.size() < count; i++) {
                    InterviewResponseVo.InterviewQuestion question = new InterviewResponseVo.InterviewQuestion();
                    question.setQuestionId("custom_" + IdUtil.fastSimpleUUID());
                    question.setContent(customQuestions.get(i));
                    question.setType("custom");
                    question.setDifficulty(difficulty);
                    question.setTimeLimit(180); // 3分钟
                    question.setOrder(questions.size() + 1);
                    question.setTags(Arrays.asList("自定义"));
                    question.setStatus("pending");
                    questions.add(question);
                }
            }

            // 从题库中获取系统问题
            int remainingCount = count - questions.size();
            if (remainingCount > 0) {
                List<Question> systemQuestions = questionMapper.selectRecommendedQuestions(1L, remainingCount * 2);

                // 随机选择问题
                Collections.shuffle(systemQuestions);

                for (int i = 0; i < Math.min(remainingCount, systemQuestions.size()); i++) {
                    Question q = systemQuestions.get(i);
                    InterviewResponseVo.InterviewQuestion question = new InterviewResponseVo.InterviewQuestion();
                    question.setQuestionId("sys_" + q.getQuestionId());
                    question.setContent(q.getContent());
                    question.setType(mapQuestionType(q.getType()));
                    question.setDifficulty(mapDifficulty(q.getDifficulty()));
                    question.setTimeLimit(calculateTimeLimit(q.getDifficulty()));
                    question.setOrder(questions.size() + 1);
                    question.setTags(parseTagsFromString(q.getTags()));
                    question.setStatus("pending");
                    questions.add(question);
                }
            }

            // 如果还不够，使用AI生成问题
            int stillNeeded = count - questions.size();
            if (stillNeeded > 0) {
                List<InterviewResponseVo.InterviewQuestion> aiQuestions = generateAiQuestions(job, stillNeeded, difficulty, resumeUrl);
                questions.addAll(aiQuestions);
            }

            return questions;

        } catch (Exception e) {
            log.error("生成面试问题失败", e);
            throw new ServiceException("生成面试问题失败: " + e.getMessage());
        }
    }

    @Override
    public InterviewResponseVo.InterviewResult generateInterviewReport(String sessionId) {
        log.info("生成面试报告，会话ID: {}", sessionId);

        try {
            // 查询会话信息
            InterviewSession session = interviewSessionMapper.selectById(sessionId);
            if (session == null) {
                throw new ServiceException("面试会话不存在");
            }

            // 查询岗位信息
            Job job = jobMapper.selectById(session.getJobId());
            if (job == null) {
                throw new ServiceException("关联岗位不存在");
            }

            // 查询所有答案
            List<InterviewAnswer> answers = interviewAnswerMapper.selectBySessionId(sessionId);

            // 计算总体分数
            Double overallScore = calculateOverallScore(answers);

            // 构建面试结果
            InterviewResponseVo.InterviewResult result = new InterviewResponseVo.InterviewResult();
            result.setSessionId(sessionId);
            result.setJobTitle(job.getName());
            result.setCompany(job.getCompany());
            result.setOverallScore(overallScore);
            result.setLevel(getScoreLevel(overallScore));

            // 分类得分
            result.setScoreByCategory(calculateCategoryScores(answers));

            // 问题结果
            result.setQuestionResults(buildQuestionResults(answers));

            // 优势和改进建议
            result.setStrengths(extractStrengths(answers));
            result.setImprovements(extractImprovements(answers));
            result.setSummary(generateSummary(overallScore, answers.size()));

            // 推荐建议
            result.setRecommendations(generateRecommendations(overallScore, job));
            result.setGeneratedTime(LocalDateTime.now());

            return result;

        } catch (Exception e) {
            log.error("生成面试报告失败", e);
            throw new ServiceException("生成面试报告失败: " + e.getMessage());
        }
    }

    @Override
    public String analyzeResume(String resumeUrl, Long jobId) {
        log.info("分析简历，简历URL: {}, 岗位ID: {}", resumeUrl, jobId);

        if (StrUtil.isBlank(resumeUrl)) {
            return "未提供简历";
        }

        try {
            // 这里应该调用文档解析服务获取简历内容
            // 暂时返回默认分析结果
            return "简历分析：候选人具备相关技能和经验，适合该岗位要求。";
        } catch (Exception e) {
            log.error("简历分析失败", e);
            return "简历分析失败";
        }
    }

    @Override
    public Double checkAnswerQuality(String answer) {
        if (StrUtil.isBlank(answer)) {
            return 0.0;
        }

        // 简单的质量评估逻辑
        double score = 50.0; // 基础分

        // 长度评估
        int length = answer.length();
        if (length > 100) score += 20;
        if (length > 300) score += 10;
        if (length > 500) score += 10;

        // 关键词评估（简化版）
        String[] keywords = {"经验", "项目", "技术", "解决", "学习", "团队", "责任"};
        for (String keyword : keywords) {
            if (answer.contains(keyword)) {
                score += 2;
            }
        }

        return Math.min(score, 100.0);
    }

    @Override
    public List<String> generateImprovementSuggestions(String sessionId) {
        log.info("生成改进建议，会话ID: {}", sessionId);

        try {
            List<InterviewAnswer> answers = interviewAnswerMapper.selectBySessionId(sessionId);
            Double avgScore = interviewAnswerMapper.selectAvgScoreBySessionId(sessionId);

            List<String> suggestions = new ArrayList<>();

            if (avgScore == null || avgScore < 60) {
                suggestions.add("建议加强基础知识的学习和理解");
                suggestions.add("多练习表达和沟通技巧");
            } else if (avgScore < 80) {
                suggestions.add("继续深入学习专业技能");
                suggestions.add("增加实际项目经验");
            } else {
                suggestions.add("保持当前水平，继续精进");
                suggestions.add("可以尝试更有挑战性的岗位");
            }

            return suggestions;

        } catch (Exception e) {
            log.error("生成改进建议失败", e);
            return Arrays.asList("建议继续学习和实践", "保持积极的学习态度");
        }
    }

    // 私有辅助方法
    private String buildEvaluationPrompt(String question, String answer, String jobContext, Integer duration) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("请作为专业的面试官，评估以下面试回答：\n\n");
        prompt.append("问题：").append(question).append("\n");
        prompt.append("回答：").append(answer).append("\n");
        if (StrUtil.isNotBlank(jobContext)) {
            prompt.append("岗位背景：").append(jobContext).append("\n");
        }
        if (duration != null) {
            prompt.append("回答时长：").append(duration).append("秒\n");
        }
        prompt.append("\n请从以下维度进行评估并返回JSON格式结果：\n");
        prompt.append("1. 总体分数（0-100）\n");
        prompt.append("2. 具体评价\n");
        prompt.append("3. 优点（数组）\n");
        prompt.append("4. 改进建议（数组）\n");
        prompt.append("5. 详细分析\n");
        prompt.append("6. 各维度分数（技术深度、表达能力、逻辑思维、实践经验）\n");

        return prompt.toString();
    }

    private InterviewResponseVo.FeedbackInfo parseAiEvaluationResponse(String aiResponse, String answer, Integer duration) {
        try {
            // 尝试解析AI返回的JSON
            Map<String, Object> responseMap = JSONUtil.toBean(aiResponse, Map.class);

            InterviewResponseVo.FeedbackInfo feedback = new InterviewResponseVo.FeedbackInfo();
            feedback.setScore(Double.valueOf(responseMap.getOrDefault("score", 75).toString()));
            feedback.setComments(responseMap.getOrDefault("comments", "回答质量良好").toString());
            feedback.setStrengths((List<String>) responseMap.getOrDefault("strengths", Arrays.asList("表达清晰")));
            feedback.setImprovements((List<String>) responseMap.getOrDefault("improvements", Arrays.asList("可以更加详细")));
            feedback.setDetailedAnalysis(responseMap.getOrDefault("analysis", "整体表现不错").toString());

            // 解析各维度分数
            Map<String, Object> criteriaMap = (Map<String, Object>) responseMap.get("criteriaScores");
            if (criteriaMap != null) {
                Map<String, Double> criteriaScores = new HashMap<>();
                criteriaMap.forEach((k, v) -> criteriaScores.put(k, Double.valueOf(v.toString())));
                feedback.setCriteriaScores(criteriaScores);
            } else {
                feedback.setCriteriaScores(createDefaultCriteriaScores());
            }

            return feedback;
        } catch (Exception e) {
            log.warn("解析AI评估响应失败，使用默认评估: {}", e.getMessage());
            return createDefaultFeedback(answer, duration);
        }
    }

    private InterviewResponseVo.FeedbackInfo createDefaultFeedback(String answer, Integer duration) {
        InterviewResponseVo.FeedbackInfo feedback = new InterviewResponseVo.FeedbackInfo();

        // 基于回答长度和时长的简单评估
        double score = checkAnswerQuality(answer);

        feedback.setScore(score);
        feedback.setComments("回答内容" + (score >= 80 ? "优秀" : score >= 60 ? "良好" : "需要改进"));
        feedback.setStrengths(Arrays.asList("有一定的思考", "表达基本清晰"));
        feedback.setImprovements(Arrays.asList("可以更加详细和具体", "增加实际案例"));
        feedback.setDetailedAnalysis("基于回答内容的自动评估，建议结合具体情况进行调整。");
        feedback.setCriteriaScores(createDefaultCriteriaScores());

        return feedback;
    }

    private Map<String, Double> createDefaultCriteriaScores() {
        Map<String, Double> scores = new HashMap<>();
        scores.put("技术深度", 70.0 + Math.random() * 20);
        scores.put("表达能力", 75.0 + Math.random() * 20);
        scores.put("逻辑思维", 72.0 + Math.random() * 20);
        scores.put("实践经验", 68.0 + Math.random() * 20);
        return scores;
    }

    private List<InterviewResponseVo.InterviewQuestion> generateAiQuestions(Job job, int count, String difficulty, String resumeUrl) {
        List<InterviewResponseVo.InterviewQuestion> questions = new ArrayList<>();

        try {
            // 构建AI生成问题的提示词
            String prompt = String.format(
                "请为%s岗位生成%d个%s难度的面试问题，岗位描述：%s。请返回JSON数组格式。",
                job.getName(), count, difficulty, job.getDescription()
            );

            String aiResponse = chatService.generateInterviewQuestions(null, job.getName(), count);

            // 解析AI生成的问题（简化处理）
            for (int i = 0; i < count; i++) {
                InterviewResponseVo.InterviewQuestion question = new InterviewResponseVo.InterviewQuestion();
                question.setQuestionId("ai_" + IdUtil.fastSimpleUUID());
                question.setContent("请介绍一下您在" + job.getName() + "方面的经验和技能。");
                question.setType("technical");
                question.setDifficulty(difficulty);
                question.setTimeLimit(180);
                question.setOrder(i + 1);
                question.setTags(Arrays.asList("AI生成", job.getName()));
                question.setStatus("pending");
                questions.add(question);
            }

        } catch (Exception e) {
            log.error("AI生成问题失败", e);
            // 返回默认问题
            for (int i = 0; i < count; i++) {
                InterviewResponseVo.InterviewQuestion question = new InterviewResponseVo.InterviewQuestion();
                question.setQuestionId("default_" + IdUtil.fastSimpleUUID());
                question.setContent("请介绍一下您的工作经验和技能特长。");
                question.setType("behavioral");
                question.setDifficulty(difficulty);
                question.setTimeLimit(180);
                question.setOrder(i + 1);
                question.setTags(Arrays.asList("通用问题"));
                question.setStatus("pending");
                questions.add(question);
            }
        }

        return questions;
    }

    private String mapQuestionType(Integer type) {
        if (type == null) return "technical";
        switch (type) {
            case 1: return "technical";
            case 2: return "behavioral";
            case 3: return "project";
            case 4: return "case";
            default: return "technical";
        }
    }

    private String mapDifficulty(Integer difficulty) {
        if (difficulty == null) return "medium";
        switch (difficulty) {
            case 1: return "easy";
            case 2: return "medium";
            case 3: return "hard";
            default: return "medium";
        }
    }

    private Integer calculateTimeLimit(Integer difficulty) {
        if (difficulty == null) return 180;
        switch (difficulty) {
            case 1: return 120; // 2分钟
            case 2: return 180; // 3分钟
            case 3: return 300; // 5分钟
            default: return 180;
        }
    }

    private List<String> parseTagsFromString(String tagsStr) {
        if (StrUtil.isBlank(tagsStr)) {
            return Arrays.asList("通用");
        }
        try {
            return JSONUtil.toList(tagsStr, String.class);
        } catch (Exception e) {
            return Arrays.asList(tagsStr);
        }
    }

    private Double calculateOverallScore(List<InterviewAnswer> answers) {
        if (answers.isEmpty()) {
            return 0.0;
        }

        double totalScore = answers.stream()
            .filter(answer -> answer.getScore() != null)
            .mapToDouble(InterviewAnswer::getScore)
            .average()
            .orElse(0.0);

        return Math.round(totalScore * 100.0) / 100.0;
    }

    private String getScoreLevel(Double score) {
        if (score >= 90) return "excellent";
        if (score >= 80) return "good";
        if (score >= 70) return "average";
        if (score >= 60) return "below_average";
        return "poor";
    }

    private List<InterviewResponseVo.ScoreItem> calculateCategoryScores(List<InterviewAnswer> answers) {
        List<InterviewResponseVo.ScoreItem> scoreItems = new ArrayList<>();

        // 技术能力评分
        InterviewResponseVo.ScoreItem techScore = new InterviewResponseVo.ScoreItem();
        techScore.setCategory("技术能力");
        techScore.setScore(calculateCategoryScore(answers, "technical"));
        techScore.setComment("技术基础扎实，有进一步提升空间");
        scoreItems.add(techScore);

        // 沟通表达评分
        InterviewResponseVo.ScoreItem commScore = new InterviewResponseVo.ScoreItem();
        commScore.setCategory("沟通表达");
        commScore.setScore(calculateCategoryScore(answers, "behavioral"));
        commScore.setComment("表达清晰，逻辑性强");
        scoreItems.add(commScore);

        return scoreItems;
    }

    private Double calculateCategoryScore(List<InterviewAnswer> answers, String category) {
        // 简化的分类评分逻辑
        return answers.stream()
            .filter(answer -> answer.getScore() != null)
            .mapToDouble(InterviewAnswer::getScore)
            .average()
            .orElse(75.0);
    }

    private List<InterviewResponseVo.QuestionResult> buildQuestionResults(List<InterviewAnswer> answers) {
        return answers.stream().map(answer -> {
            InterviewResponseVo.QuestionResult result = new InterviewResponseVo.QuestionResult();
            result.setQuestionId(answer.getQuestionId());
            result.setContent("面试问题"); // 实际应该从SessionQuestion表查询
            result.setAnswer(answer.getContent());
            result.setScore(answer.getScore());
            result.setFeedback(answer.getFeedback());
            result.setHighlights(Arrays.asList("关键词匹配", "逻辑清晰"));
            result.setImprovements(Arrays.asList("可以更加详细", "增加实例"));
            return result;
        }).collect(Collectors.toList());
    }

    private List<String> extractStrengths(List<InterviewAnswer> answers) {
        List<String> strengths = new ArrayList<>();

        double avgScore = calculateOverallScore(answers);
        if (avgScore >= 80) {
            strengths.add("技术基础扎实");
            strengths.add("表达能力强");
        } else if (avgScore >= 60) {
            strengths.add("有一定的技术基础");
            strengths.add("学习态度积极");
        } else {
            strengths.add("态度认真");
            strengths.add("有学习意愿");
        }

        return strengths;
    }

    private List<String> extractImprovements(List<InterviewAnswer> answers) {
        List<String> improvements = new ArrayList<>();

        double avgScore = calculateOverallScore(answers);
        if (avgScore < 60) {
            improvements.add("需要加强基础知识学习");
            improvements.add("提高表达和沟通能力");
        } else if (avgScore < 80) {
            improvements.add("继续深入学习专业技能");
            improvements.add("增加实际项目经验");
        } else {
            improvements.add("保持当前水平");
            improvements.add("可以尝试更有挑战性的项目");
        }

        return improvements;
    }

    private String generateSummary(Double overallScore, int questionCount) {
        StringBuilder summary = new StringBuilder();
        summary.append("本次面试共回答了").append(questionCount).append("个问题，");
        summary.append("总体得分").append(String.format("%.1f", overallScore)).append("分。");

        if (overallScore >= 80) {
            summary.append("表现优秀，具备岗位要求的核心技能。");
        } else if (overallScore >= 60) {
            summary.append("表现良好，基本符合岗位要求，建议继续提升。");
        } else {
            summary.append("还需要进一步学习和提升，建议加强基础知识。");
        }

        return summary.toString();
    }

    private List<InterviewResponseVo.RecommendationItem> generateRecommendations(Double overallScore, Job job) {
        List<InterviewResponseVo.RecommendationItem> recommendations = new ArrayList<>();

        InterviewResponseVo.RecommendationItem rec1 = new InterviewResponseVo.RecommendationItem();
        rec1.setType("learning");
        rec1.setTitle("深入学习" + job.getName() + "相关技能");
        rec1.setDescription("建议系统学习该岗位的核心技术栈");
        rec1.setUrl("https://example.com/learning");
        recommendations.add(rec1);

        if (overallScore < 70) {
            InterviewResponseVo.RecommendationItem rec2 = new InterviewResponseVo.RecommendationItem();
            rec2.setType("practice");
            rec2.setTitle("多做实际项目练习");
            rec2.setDescription("通过实际项目提升技能和经验");
            rec2.setUrl("https://example.com/projects");
            recommendations.add(rec2);
        }

        return recommendations;
    }
}
