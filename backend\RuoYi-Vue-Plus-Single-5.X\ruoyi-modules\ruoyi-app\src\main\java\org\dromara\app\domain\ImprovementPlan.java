package org.dromara.app.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 提升计划对象 app_improvement_plan
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@Data
@TableName("app_improvement_plan")
public class ImprovementPlan implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 计划ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 结果ID
     */
    private String resultId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 提升领域（JSON数组）
     */
    private List<ImprovementArea> areas;

    /**
     * 短期目标（JSON数组）
     */
    private List<String> shortTermGoals;

    /**
     * 中期目标（JSON数组）
     */
    private List<String> mediumTermGoals;

    /**
     * 长期目标（JSON数组）
     */
    private List<String> longTermGoals;

    /**
     * 推荐资源（JSON数组）
     */
    private List<LearningResource> recommendedResources;

    /**
     * 状态（active,completed,cancelled）
     */
    private String status;

    /**
     * 完成进度（百分比）
     */
    private Integer progress;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 提升领域内部类
     */
    @Data
    public static class ImprovementArea implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 领域名称
         */
        private String name;

        /**
         * 当前水平
         */
        private Integer currentLevel;

        /**
         * 目标水平
         */
        private Integer targetLevel;

        /**
         * 时间框架
         */
        private String timeframe;

        /**
         * 行动计划
         */
        private List<String> actions;
    }

}
