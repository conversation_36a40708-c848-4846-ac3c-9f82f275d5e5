{"doc": "\n 部门信息\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.system.domain.bo.SysDeptBo"], "doc": "\n 获取部门列表\r\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.Long"], "doc": "\n 查询部门列表（排除节点）\r\n\r\n @param deptId 部门ID\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": "\n 根据部门编号获取详细信息\r\n\r\n @param deptId 部门ID\r\n"}, {"name": "add", "paramTypes": ["org.dromara.system.domain.bo.SysDeptBo"], "doc": "\n 新增部门\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.system.domain.bo.SysDeptBo"], "doc": "\n 修改部门\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Long"], "doc": "\n 删除部门\r\n\r\n @param deptId 部门ID\r\n"}, {"name": "optionselect", "paramTypes": ["java.lang.Long[]"], "doc": "\n 获取部门选择框列表\r\n\r\n @param deptIds 部门ID串\r\n"}], "constructors": []}