package org.dromara.app.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;
import java.time.LocalDateTime;

/**
 * 面试答案对象 app_interview_answer
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_interview_answer")
public class InterviewAnswer extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 答案ID
     */
    @TableId(value = "id")
    private String id;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 问题ID
     */
    private String questionId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 答案内容（文本）
     */
    private String content;

    /**
     * 音频URL
     */
    private String audioUrl;

    /**
     * 视频URL
     */
    private String videoUrl;

    /**
     * 回答时长（秒）
     */
    private Integer duration;

    /**
     * 提交时间
     */
    private LocalDateTime submittedTime;

    /**
     * AI评估分数
     */
    private Double score;

    /**
     * AI评估反馈
     */
    private String feedback;

    /**
     * 评估详情（JSON格式）
     */
    private String evaluationDetails;

    /**
     * 状态（submitted, evaluated, reviewed）
     */
    private String status;

    /**
     * 是否跳过
     */
    private Boolean skipped;

    /**
     * 跳过原因
     */
    private String skipReason;
}
