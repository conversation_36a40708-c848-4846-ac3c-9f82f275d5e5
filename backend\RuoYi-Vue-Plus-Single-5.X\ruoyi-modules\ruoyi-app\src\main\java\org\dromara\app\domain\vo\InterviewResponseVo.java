package org.dromara.app.domain.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 面试相关响应对象
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
public class InterviewResponseVo {

    /**
     * 岗位列表响应
     */
    @Data
    @NoArgsConstructor
    public static class JobListResponse implements Serializable {
        private static final long serialVersionUID = 1L;

        private List<JobVo> jobs;
        private Long total;
        private Integer page;
        private Integer pageSize;
        private Boolean hasMore;
        private List<CategoryInfo> categories;
        private List<JobVo> recommendedJobs;
    }



    /**
     * 分类信息
     */
    @Data
    @NoArgsConstructor
    public static class CategoryInfo implements Serializable {
        private static final long serialVersionUID = 1L;

        private Long categoryId;
        private String name;
        private String icon;
        private Integer jobCount;
        private Integer questionCount;
        private String description;
    }

    /**
     * 分类列表响应
     */
    @Data
    @NoArgsConstructor
    public static class CategoriesResponse implements Serializable {
        private static final long serialVersionUID = 1L;

        private List<CategoryInfo> categories;
        private Integer totalCategories;
    }

    /**
     * 面试模式响应
     */
    @Data
    @NoArgsConstructor
    public static class InterviewModesResponse implements Serializable {
        private static final long serialVersionUID = 1L;

        private List<ModeInfo> modes;
    }

    /**
     * 面试模式信息
     */
    @Data
    @NoArgsConstructor
    public static class ModeInfo implements Serializable {
        private static final long serialVersionUID = 1L;

        private String modeId;
        private String name;
        private String description;
        private String icon;
        private Integer questionCount;
        private Integer duration;
        private Boolean isPremium;
        private Boolean isAvailable;
    }

    /**
     * 搜索建议响应
     */
    @Data
    @NoArgsConstructor
    public static class SearchSuggestionsResponse implements Serializable {
        private static final long serialVersionUID = 1L;

        private List<SuggestionItem> suggestions;
        private String originalQuery;
    }

    /**
     * 搜索建议项
     */
    @Data
    @NoArgsConstructor
    public static class SuggestionItem implements Serializable {
        private static final long serialVersionUID = 1L;

        private String text;
        private String type; // job, category, question
        private Long entityId;
    }

    /**
     * 创建会话响应
     */
    @Data
    @NoArgsConstructor
    public static class CreateSessionResponse implements Serializable {
        private static final long serialVersionUID = 1L;

        private String sessionId;
        private Long jobId;
        private String mode;
        private LocalDateTime createdTime;
        private LocalDateTime expiryTime;
        private String status;
    }

    /**
     * 设备检测结果
     */
    @Data
    @NoArgsConstructor
    public static class DeviceCheckResult implements Serializable {
        private static final long serialVersionUID = 1L;

        private Boolean hasMicrophone;
        private Boolean hasCamera;
        private Boolean hasNetworkConnection;
        private Integer networkStrength; // 1-5
        private Map<String, String> deviceInfo;
    }

    /**
     * 统计响应
     */
    @Data
    @NoArgsConstructor
    public static class StatisticsResponse implements Serializable {
        private static final long serialVersionUID = 1L;

        private Integer totalJobs;
        private Integer totalQuestions;
        private Integer totalUsers;
        private Integer totalInterviews;
        private Map<String, Integer> categoryDistribution;
        private Map<String, Integer> difficultyDistribution;
        private List<TrendPoint> weeklyTrend;
    }

    /**
     * 趋势点
     */
    @Data
    @NoArgsConstructor
    public static class TrendPoint implements Serializable {
        private static final long serialVersionUID = 1L;

        private String date;
        private Integer value;
        private String label;
    }

    /**
     * 岗位详情响应
     */
    @Data
    @NoArgsConstructor
    public static class JobDetailResponse implements Serializable {
        private static final long serialVersionUID = 1L;
        private Long id;  // 改为id以匹配前端
        private String name;  // 改为name以匹配前端
        private String company;
        private Long categoryId;
        private String categoryName;
        private Integer difficulty;  // 改为Integer以匹配前端
        private String description;
        private List<String> requirements;
        private List<String> skills;
        private List<String> responsibilities;
        private String logo;  // 改为logo以匹配前端
        private Integer interviewCount;
        private Integer questionCount;
        private Double rating;
        private Boolean isFavorited;
        private LocalDateTime createdTime;
        private LocalDateTime updatedTime;

        // 新增前端需要的字段
        private Integer duration;  // 面试时长（分钟）
        private Integer interviewers;  // 已练习人数
        private String passRate;  // 通过率
        private List<String> tags;  // 技能标签
        private List<InterviewStep> interviewProcess;  // 面试流程
        private List<SkillPoint> skillPoints;  // 技能考查重点
        private List<String> benefits;  // 练习收益
        private List<String> coreSkills;  // 核心技能
    }

    /**
     * 面试流程步骤
     */
    @Data
    @NoArgsConstructor
    public static class InterviewStep implements Serializable {
        private static final long serialVersionUID = 1L;
        private Integer step;  // 步骤序号
        private String name;  // 步骤名称
        private Integer duration;  // 步骤时长（分钟）
        private String description;  // 步骤描述
    }

    /**
     * 技能考查点
     */
    @Data
    @NoArgsConstructor
    public static class SkillPoint implements Serializable {
        private static final long serialVersionUID = 1L;
        private String name;  // 技能名称
        private Integer weight;  // 权重百分比
        private String level;  // 重要程度：high, medium, low
    }

    /**
     * 示例问题响应
     */
    @Data
    @NoArgsConstructor
    public static class SampleQuestionsResponse implements Serializable {
        private static final long serialVersionUID = 1L;

        private List<QuestionInfo> questions;
        private Integer totalCount;
        private Long jobId;
    }

    /**
     * 问题信息
     */
    @Data
    @NoArgsConstructor
    public static class QuestionInfo implements Serializable {
        private static final long serialVersionUID = 1L;

        private String questionId;
        private String content;
        private String difficulty;
        private String type;
        private Integer timeLimit;
        private List<String> tags;
        private String categoryName;
    }

    /**
     * 相关岗位响应
     */
    @Data
    @NoArgsConstructor
    public static class RelatedJobsResponse implements Serializable {
        private static final long serialVersionUID = 1L;

        private List<JobVo> relatedJobs;
        private Long sourceJobId;
    }

    /**
     * 岗位统计响应
     */
    @Data
    @NoArgsConstructor
    public static class JobStatisticsResponse implements Serializable {
        private static final long serialVersionUID = 1L;

        private Long jobId;
        private Integer totalInterviews;
        private Double avgScore;
        private Integer totalQuestions;
        private Map<String, Integer> difficultyDistribution;
        private Map<String, Double> skillDistribution;
    }

    /**
     * 岗位面试模式响应
     */
    @Data
    @NoArgsConstructor
    public static class JobInterviewModesResponse implements Serializable {
        private static final long serialVersionUID = 1L;

        private List<ModeInfo> availableModes;
        private Long jobId;
    }

    /**
     * 用户准备度响应
     */
    @Data
    @NoArgsConstructor
    public static class UserReadinessResponse implements Serializable {
        private static final long serialVersionUID = 1L;

        private Double readinessScore; // 0-100
        private List<String> strongAreas;
        private List<String> weakAreas;
        private List<RecommendationItem> recommendations;
    }

    /**
     * 推荐项
     */
    @Data
    @NoArgsConstructor
    public static class RecommendationItem implements Serializable {
        private static final long serialVersionUID = 1L;

        private String type; // resource, practice, skill
        private String title;
        private String description;
        private String url;
    }

    /**
     * 分享岗位响应
     */
    @Data
    @NoArgsConstructor
    public static class ShareJobResponse implements Serializable {
        private static final long serialVersionUID = 1L;

        private String shareUrl;
        private String platform;
        private Boolean success;
        private Long jobId;
    }

    /**
     * 会话信息
     */
    @Data
    @NoArgsConstructor
    public static class SessionInfo implements Serializable {
        private static final long serialVersionUID = 1L;

        private String sessionId;
        private Long jobId;
        private String jobTitle;
        private String company;
        private String categoryName;
        private String mode;
        private String status; // preparing, in_progress, completed, cancelled
        private LocalDateTime startTime;
        private LocalDateTime endTime;
        private Integer totalQuestions;
        private Integer completedQuestions;
        private Integer currentQuestionIndex;
        private String interviewerId;
        private String interviewerName;
        private String interviewerAvatar;
        private LocalDateTime createdTime;
        private LocalDateTime expiryTime;
    }

    /**
     * 问题列表响应
     */
    @Data
    @NoArgsConstructor
    public static class QuestionListResponse implements Serializable {
        private static final long serialVersionUID = 1L;

        private List<InterviewQuestion> questions;
        private String sessionId;
        private Integer totalCount;
    }

    /**
     * 面试问题
     */
    @Data
    @NoArgsConstructor
    public static class InterviewQuestion implements Serializable {
        private static final long serialVersionUID = 1L;

        private String questionId;
        private String content;
        private String difficulty;
        private String type;
        private Integer timeLimit;
        private Integer order;
        private List<String> tags;
        private String status; // pending, current, answered, skipped
        private AnswerInfo answer;
        private FeedbackInfo feedback;
    }

    /**
     * 回答信息
     */
    @Data
    @NoArgsConstructor
    public static class AnswerInfo implements Serializable {
        private static final long serialVersionUID = 1L;

        private String answerId;
        private String content;
        private String audioUrl;
        private Integer duration;
        private LocalDateTime submittedTime;
    }

    /**
     * 回答响应
     */
    @Data
    @NoArgsConstructor
    public static class AnswerResponse implements Serializable {
        private static final long serialVersionUID = 1L;

        private String answerId;
        private String questionId;
        private String sessionId;
        private Boolean success;
        private FeedbackInfo feedback;
        private String nextQuestionId;
    }

    /**
     * 反馈信息
     */
    @Data
    @NoArgsConstructor
    public static class FeedbackInfo implements Serializable {
        private static final long serialVersionUID = 1L;

        private Double score;
        private String comments;
        private List<String> strengths;
        private List<String> improvements;
        private String detailedAnalysis;
        private Map<String, Double> criteriaScores;
    }

    /**
     * 结束会话响应
     */
    @Data
    @NoArgsConstructor
    public static class EndSessionResponse implements Serializable {
        private static final long serialVersionUID = 1L;

        private String sessionId;
        private String status;
        private Boolean resultAvailable;
        private Boolean canRestart;
        private String summaryUrl;
        private LocalDateTime endTime;
    }

    /**
     * 反馈响应
     */
    @Data
    @NoArgsConstructor
    public static class FeedbackResponse implements Serializable {
        private static final long serialVersionUID = 1L;

        private String sessionId;
        private Integer rating;
        private Boolean success;
        private String message;
    }

    /**
     * 面试结果
     */
    @Data
    @NoArgsConstructor
    public static class InterviewResult implements Serializable {
        private static final long serialVersionUID = 1L;

        private String sessionId;
        private String jobTitle;
        private String company;
        private Double overallScore;
        private String level; // excellent, good, average, below_average, poor
        private List<ScoreItem> scoreByCategory;
        private List<QuestionResult> questionResults;
        private List<String> strengths;
        private List<String> improvements;
        private String summary;
        private List<RecommendationItem> recommendations;
        private LocalDateTime generatedTime;
    }

    /**
     * 得分项
     */
    @Data
    @NoArgsConstructor
    public static class ScoreItem implements Serializable {
        private static final long serialVersionUID = 1L;

        private String category;
        private Double score;
        private String comment;
    }

    /**
     * 问题结果
     */
    @Data
    @NoArgsConstructor
    public static class QuestionResult implements Serializable {
        private static final long serialVersionUID = 1L;

        private String questionId;
        private String content;
        private String answer;
        private Double score;
        private String feedback;
        private List<String> highlights;
        private List<String> improvements;
    }

    /**
     * 会话状态
     */
    @Data
    @NoArgsConstructor
    public static class SessionStatus implements Serializable {
        private static final long serialVersionUID = 1L;

        private String sessionId;
        private String status; // preparing, in_progress, completed, cancelled, expired
        private Integer currentQuestionIndex;
        private Integer totalQuestions;
        private Boolean isActive;
        private LocalDateTime lastActivity;
        private Integer remainingTime;
    }

    /**
     * AI面试官信息
     */
    @Data
    @NoArgsConstructor
    public static class InterviewerInfo implements Serializable {
        private static final long serialVersionUID = 1L;

        private String interviewerId;
        private String name;
        private String avatar;
        private String title;
        private String company;
        private String background;
        private List<String> specialties;
        private String interviewStyle;
        private String voiceType;
    }

    /**
     * 用户统计信息
     */
    @Data
    @NoArgsConstructor
    public static class UserStats implements Serializable {
        private static final long serialVersionUID = 1L;

        private Long userId;
        private Integer totalInterviews;
        private Integer completedInterviews;
        private Double averageScore;
        private Integer totalTime; // 总时长（分钟）
        private String rank;
        private Integer weeklyProgress;
        private Integer monthlyProgress;
        private Integer currentStreak;
        private Integer longestStreak;
        private LocalDateTime lastInterviewTime;
        private Map<String, Double> skillScores;
        private Map<String, Integer> categoryPerformance;
    }

    /**
     * 岗位统计信息
     */
    @Data
    @NoArgsConstructor
    public static class JobStats implements Serializable {
        private static final long serialVersionUID = 1L;

        private Long jobId;
        private Integer totalInterviews;
        private Double averageScore;
        private Double completionRate;
        private Integer averageDuration; // 平均时长（分钟）
        private Integer popularityRank;
        private Map<String, Integer> difficultyDistribution;
        private Map<String, Integer> scoreDistribution;
    }

    /**
     * 系统统计信息
     */
    @Data
    @NoArgsConstructor
    public static class SystemStats implements Serializable {
        private static final long serialVersionUID = 1L;

        private Integer totalUsers;
        private Integer activeUsers;
        private Integer totalJobs;
        private Integer totalQuestions;
        private Integer totalInterviews;
        private Integer todayInterviews;
        private Double averageScore;
        private Double completionRate;
        private List<Map<String, Object>> userGrowthTrend;
    }

    /**
     * 趋势数据
     */
    @Data
    @NoArgsConstructor
    public static class TrendData implements Serializable {
        private static final long serialVersionUID = 1L;

        private String date;
        private Integer interviewCount;
        private Double averageScore;
        private Double completionRate;
    }

    /**
     * 面试历史记录
     */
    @Data
    @NoArgsConstructor
    public static class InterviewRecord implements Serializable {
        private static final long serialVersionUID = 1L;

        private Long id;
        private String jobName;
        private String company;
        private String icon;
        private String difficulty;
        private Integer questionCount;
        private String date;
        private String time;
        private String duration;
        private Integer totalScore;
        private String status; // completed, in-progress, cancelled, excellent, good, fair, poor
        private String timeAgo;
        private DimensionScores dimensions;
        private String category;
    }

    /**
     * 维度分数
     */
    @Data
    @NoArgsConstructor
    public static class DimensionScores implements Serializable {
        private static final long serialVersionUID = 1L;

        private Integer professional;
        private Integer communication;
        private Integer logic;
        private Integer innovation;
    }

    /**
     * 面试历史记录列表响应
     */
    @Data
    @NoArgsConstructor
    public static class HistoryListResponse implements Serializable {
        private static final long serialVersionUID = 1L;

        private List<InterviewRecord> records;
        private Long total;
        private Boolean hasMore;
        private Integer page;
        private Integer pageSize;
    }

    /**
     * 统计数据响应（用于前端history页面）
     */
    @Data
    @NoArgsConstructor
    public static class Statistics implements Serializable {
        private static final long serialVersionUID = 1L;

        private Integer totalInterviews;
        private Double averageScore;
        private Integer monthlyImprovement;
        private Double improvementPercent;
        private String currentLevel;
        private Integer nextLevelProgress;
    }

    /**
     * 热门问题
     */
    @Data
    @NoArgsConstructor
    public static class PopularQuestion implements Serializable {
        private static final long serialVersionUID = 1L;

        private String questionId;
        private String content;
        private String category;
        private String difficulty;
        private Integer askedCount;
        private Double averageScore;
        private Double correctRate;
        private List<String> tags;
    }

    /**
     * 用户排行
     */
    @Data
    @NoArgsConstructor
    public static class UserRanking implements Serializable {
        private static final long serialVersionUID = 1L;

        private Long userId;
        private String username;
        private String avatar;
        private Integer rank;
        private Double score;
        private Integer totalInterviews;
        private String level;
    }
}
