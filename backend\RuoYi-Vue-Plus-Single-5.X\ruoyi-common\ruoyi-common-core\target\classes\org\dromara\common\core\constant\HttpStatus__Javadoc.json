{"doc": "\n 返回状态码\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "SUCCESS", "doc": "\n 操作成功\r\n"}, {"name": "CREATED", "doc": "\n 对象创建成功\r\n"}, {"name": "ACCEPTED", "doc": "\n 请求已经被接受\r\n"}, {"name": "NO_CONTENT", "doc": "\n 操作已经执行成功，但是没有返回数据\r\n"}, {"name": "MOVED_PERM", "doc": "\n 资源已被移除\r\n"}, {"name": "SEE_OTHER", "doc": "\n 重定向\r\n"}, {"name": "NOT_MODIFIED", "doc": "\n 资源没有被修改\r\n"}, {"name": "BAD_REQUEST", "doc": "\n 参数列表错误（缺少，格式不匹配）\r\n"}, {"name": "UNAUTHORIZED", "doc": "\n 未授权\r\n"}, {"name": "FORBIDDEN", "doc": "\n 访问受限，授权过期\r\n"}, {"name": "NOT_FOUND", "doc": "\n 资源，服务未找到\r\n"}, {"name": "BAD_METHOD", "doc": "\n 不允许的http方法\r\n"}, {"name": "CONFLICT", "doc": "\n 资源冲突，或者资源被锁\r\n"}, {"name": "UNSUPPORTED_TYPE", "doc": "\n 不支持的数据，媒体类型\r\n"}, {"name": "ERROR", "doc": "\n 系统内部错误\r\n"}, {"name": "NOT_IMPLEMENTED", "doc": "\n 接口未实现\r\n"}, {"name": "WARN", "doc": "\n 系统警告消息\r\n"}], "enumConstants": [], "methods": [], "constructors": []}