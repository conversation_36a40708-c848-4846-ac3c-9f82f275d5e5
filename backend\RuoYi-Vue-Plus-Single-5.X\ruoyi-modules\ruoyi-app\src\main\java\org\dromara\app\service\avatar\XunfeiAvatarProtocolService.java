package org.dromara.app.service.avatar;

import com.alibaba.fastjson.JSONObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.config.XunfeiAvatarConfig;
import org.springframework.stereotype.Service;

import java.util.Base64;
import java.util.UUID;

/**
 * 讯飞数字人协议构建服务
 *
 * <AUTHOR>
 * @date 2025-07-20
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class XunfeiAvatarProtocolService {

    private final XunfeiAvatarConfig config;

    /**
     * 构建启动协议
     *
     * @param avatarId   形象ID
     * @param sceneId    场景ID
     * @param vcn        声音ID
     * @param width      视频宽度
     * @param height     视频高度
     * @param background 背景数据（可选）
     * @return 启动协议JSON
     */
    public JSONObject buildStartRequest(String avatarId, String sceneId, String vcn, Integer width, Integer height, String background) {
        JSONObject header = new JSONObject()
                .fluentPut("app_id", config.getAppId())
                .fluentPut("ctrl", "start")
                .fluentPut("request_id", UUID.randomUUID().toString())
                .fluentPut("scene_id", sceneId != null ? sceneId : config.getDefaultSceneId());

        JSONObject parameter = new JSONObject()
                .fluentPut("avatar", new JSONObject()
                        .fluentPut("avatar_id", avatarId != null ? avatarId : config.getDefaultAvatarId())
                        .fluentPut("width", width != null ? width : config.getDefaultWidth())
                        .fluentPut("height", height != null ? height : config.getDefaultHeight())
                        .fluentPut("stream", new JSONObject()
                                .fluentPut("protocol", config.getProtocol())
                                .fluentPut("fps", config.getFps())
                                .fluentPut("bitrate", config.getBitrate())
                                .fluentPut("alpha", config.getEnableAlpha() ? 1 : 0)))
                .fluentPut("tts", new JSONObject()
                        .fluentPut("speed", config.getDefaultSpeed())
                        .fluentPut("vcn", vcn != null ? vcn : config.getDefaultVcn()))
                .fluentPut("subtitle", new JSONObject()
                        .fluentPut("subtitle", 0)
                        .fluentPut("font_color", "#FF0000")
                        .fluentPut("font_size", 10)
                        .fluentPut("position_x", 0)
                        .fluentPut("position_y", 0)
                        .fluentPut("font_name", "mainTitle")
                        .fluentPut("width", 100)
                        .fluentPut("height", 100));

        JSONObject request = new JSONObject()
                .fluentPut("header", header)
                .fluentPut("parameter", parameter);

        // 如果有背景数据，添加payload
        if (background != null && !background.isEmpty()) {
            JSONObject payload = new JSONObject()
                    .fluentPut("background", new JSONObject()
                            .fluentPut("data", background));
            request.fluentPut("payload", payload);
        }

        return request;
    }

    /**
     * 构建文本驱动协议
     *
     * @param text   文本内容
     * @param vcn    声音ID（可选）
     * @param speed  语速（可选）
     * @param pitch  语调（可选）
     * @param volume 音量（可选）
     * @return 文本驱动协议JSON
     */
    public JSONObject buildTextRequest(String text, String vcn, Integer speed, Integer pitch, Integer volume) {
        JSONObject header = new JSONObject()
                .fluentPut("app_id", config.getAppId())
                .fluentPut("ctrl", "text_driver")
                .fluentPut("request_id", UUID.randomUUID().toString());

        JSONObject parameter = new JSONObject()
                .fluentPut("avatar_dispatch", new JSONObject()
                        .fluentPut("interactive_mode", 0))
                .fluentPut("tts", new JSONObject()
                        .fluentPut("vcn", vcn != null ? vcn : config.getDefaultVcn())
                        .fluentPut("speed", speed != null ? speed : config.getDefaultSpeed())
                        .fluentPut("pitch", pitch != null ? pitch : config.getDefaultPitch())
                        .fluentPut("volume", volume != null ? volume : config.getDefaultVolume()))
                .fluentPut("air", new JSONObject()
                        .fluentPut("air", 1)
                        .fluentPut("add_nonsemantic", 1));

        JSONObject payload = new JSONObject()
                .fluentPut("text", new JSONObject()
                        .fluentPut("content", text));

        return new JSONObject()
                .fluentPut("header", header)
                .fluentPut("parameter", parameter)
                .fluentPut("payload", payload);
    }

    /**
     * 构建文本交互协议
     *
     * @param text   文本内容
     * @param vcn    声音ID（可选）
     * @param speed  语速（可选）
     * @param pitch  语调（可选）
     * @param volume 音量（可选）
     * @return 文本交互协议JSON
     */
    public JSONObject buildTextInteractRequest(String text, String vcn, Integer speed, Integer pitch, Integer volume) {
        JSONObject header = new JSONObject()
                .fluentPut("app_id", config.getAppId())
                .fluentPut("ctrl", "text_interact")
                .fluentPut("request_id", UUID.randomUUID().toString());

        JSONObject parameter = new JSONObject()
                .fluentPut("tts", new JSONObject()
                        .fluentPut("vcn", vcn != null ? vcn : config.getDefaultVcn())
                        .fluentPut("speed", speed != null ? speed : config.getDefaultSpeed())
                        .fluentPut("pitch", pitch != null ? pitch : config.getDefaultPitch())
                        .fluentPut("audio", new JSONObject()
                                .fluentPut("sample_rate", 16000)))
                .fluentPut("air", new JSONObject()
                        .fluentPut("air", 1)
                        .fluentPut("add_nonsemantic", 1));

        JSONObject payload = new JSONObject()
                .fluentPut("text", new JSONObject()
                        .fluentPut("content", text));

        return new JSONObject()
                .fluentPut("header", header)
                .fluentPut("parameter", parameter)
                .fluentPut("payload", payload);
    }

    /**
     * 构建音频驱动协议
     *
     * @param requestId  请求ID
     * @param status     数据状态（0开始，1过渡，2结束）
     * @param audioData  音频数据（Base64编码）
     * @return 音频驱动协议JSON
     */
    public JSONObject buildAudioRequest(String requestId, int status, String audioData) {
        JSONObject header = new JSONObject()
                .fluentPut("app_id", config.getAppId())
                .fluentPut("ctrl", "audio_driver")
                .fluentPut("request_id", requestId);

        JSONObject parameter = new JSONObject()
                .fluentPut("avatar_dispatch", new JSONObject()
                        .fluentPut("audio_mode", 0));

        JSONObject payload = new JSONObject()
                .fluentPut("audio", new JSONObject()
                        .fluentPut("status", status)
                        .fluentPut("audio", audioData));

        return new JSONObject()
                .fluentPut("header", header)
                .fluentPut("parameter", parameter)
                .fluentPut("payload", payload);
    }

    /**
     * 构建心跳协议
     *
     * @return 心跳协议JSON
     */
    public JSONObject buildPingRequest() {
        JSONObject header = new JSONObject()
                .fluentPut("app_id", config.getAppId())
                .fluentPut("ctrl", "ping")
                .fluentPut("request_id", UUID.randomUUID().toString());

        return new JSONObject().fluentPut("header", header);
    }

    /**
     * 构建重置（打断）协议
     *
     * @return 重置协议JSON
     */
    public JSONObject buildResetRequest() {
        JSONObject header = new JSONObject()
                .fluentPut("app_id", config.getAppId())
                .fluentPut("ctrl", "reset")
                .fluentPut("request_id", UUID.randomUUID().toString());

        return new JSONObject().fluentPut("header", header);
    }

    /**
     * 构建停止协议
     *
     * @return 停止协议JSON
     */
    public JSONObject buildStopRequest() {
        JSONObject header = new JSONObject()
                .fluentPut("app_id", config.getAppId())
                .fluentPut("ctrl", "stop")
                .fluentPut("request_id", UUID.randomUUID().toString());

        return new JSONObject().fluentPut("header", header);
    }

    /**
     * 构建单独指令协议
     *
     * @param actionType  动作类型
     * @param actionValue 动作值
     * @return 指令协议JSON
     */
    public JSONObject buildCmdRequest(String actionType, String actionValue) {
        JSONObject header = new JSONObject()
                .fluentPut("app_id", config.getAppId())
                .fluentPut("ctrl", "cmd")
                .fluentPut("request_id", UUID.randomUUID().toString());

        JSONObject payload = new JSONObject()
                .fluentPut("cmd_text", new JSONObject()
                        .fluentPut("avatar", new JSONObject()
                                .fluentPut("type", actionType != null ? actionType : "action")
                                .fluentPut("value", actionValue != null ? actionValue : "A_RLH_puzzle_0")));

        return new JSONObject()
                .fluentPut("header", header)
                .fluentPut("payload", payload);
    }
}
