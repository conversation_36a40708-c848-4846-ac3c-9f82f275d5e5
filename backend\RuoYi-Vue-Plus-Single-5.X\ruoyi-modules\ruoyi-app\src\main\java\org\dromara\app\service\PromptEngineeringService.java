package org.dromara.app.service;

import org.dromara.app.domain.VectorEmbedding;

import java.util.List;
import java.util.Map;

/**
 * Prompt工程服务
 * 负责优化和管理AI提示词
 *
 * <AUTHOR>
 */
public interface PromptEngineeringService {

    /**
     * 构建RAG增强提示词
     *
     * @param userQuery        用户查询
     * @param retrievalResults 检索结果
     * @param agentType        Agent类型
     * @param contextWindow    上下文窗口大小
     * @return 增强后的提示词
     */
    String buildRagEnhancedPrompt(String userQuery, List<VectorEmbedding> retrievalResults,
                                  String agentType, int contextWindow);

    /**
     * 构建工具调用提示词
     *
     * @param userQuery           用户查询
     * @param availableTools      可用工具列表
     * @param conversationHistory 对话历史
     * @return 工具调用提示词
     */
    String buildToolCallPrompt(String userQuery, List<Map<String, Object>> availableTools,
                               List<String> conversationHistory);

    /**
     * 优化系统提示词
     *
     * @param originalPrompt 原始提示词
     * @param agentType      Agent类型
     * @param userContext    用户上下文
     * @return 优化后的提示词
     */
    String optimizeSystemPrompt(String originalPrompt, String agentType, Map<String, Object> userContext);

    /**
     * 构建多轮对话提示词
     *
     * @param currentQuery        当前查询
     * @param conversationHistory 对话历史
     * @param maxHistoryLength    最大历史长度
     * @return 多轮对话提示词
     */
    String buildConversationalPrompt(String currentQuery, List<ConversationTurn> conversationHistory,
                                     int maxHistoryLength);

    /**
     * 构建思维链提示词
     *
     * @param userQuery 用户查询
     * @param taskType  任务类型
     * @return 思维链提示词
     */
    String buildChainOfThoughtPrompt(String userQuery, String taskType);

    /**
     * 构建少样本学习提示词
     *
     * @param userQuery       用户查询
     * @param examples        示例列表
     * @param taskDescription 任务描述
     * @return 少样本学习提示词
     */
    String buildFewShotPrompt(String userQuery, List<PromptExample> examples, String taskDescription);

    /**
     * 动态调整提示词长度
     *
     * @param prompt    原始提示词
     * @param maxTokens 最大token数
     * @param priority  优先级策略
     * @return 调整后的提示词
     */
    String adjustPromptLength(String prompt, int maxTokens, LengthAdjustmentStrategy priority);

    /**
     * 提取关键信息
     *
     * @param content   内容
     * @param maxLength 最大长度
     * @return 关键信息
     */
    String extractKeyInformation(String content, int maxLength);

    /**
     * 长度调整策略
     */
    enum LengthAdjustmentStrategy {
        TRUNCATE_END,       // 截断末尾
        TRUNCATE_MIDDLE,    // 截断中间
        COMPRESS_CONTENT,   // 压缩内容
        PRIORITIZE_RECENT,  // 优先保留最近的内容
        PRIORITIZE_RELEVANT // 优先保留相关的内容
    }

    /**
     * 对话轮次
     */
    class ConversationTurn {
        private String role; // user, assistant, system
        private String content;
        private long timestamp;
        private Map<String, Object> metadata;

        public ConversationTurn(String role, String content, long timestamp, Map<String, Object> metadata) {
            this.role = role;
            this.content = content;
            this.timestamp = timestamp;
            this.metadata = metadata;
        }

        // Getters
        public String getRole() {
            return role;
        }

        public String getContent() {
            return content;
        }

        public long getTimestamp() {
            return timestamp;
        }

        public Map<String, Object> getMetadata() {
            return metadata;
        }
    }

    /**
     * 提示词示例
     */
    class PromptExample {
        private String input;
        private String output;
        private String explanation;

        public PromptExample(String input, String output, String explanation) {
            this.input = input;
            this.output = output;
            this.explanation = explanation;
        }

        // Getters
        public String getInput() {
            return input;
        }

        public String getOutput() {
            return output;
        }

        public String getExplanation() {
            return explanation;
        }
    }
}
