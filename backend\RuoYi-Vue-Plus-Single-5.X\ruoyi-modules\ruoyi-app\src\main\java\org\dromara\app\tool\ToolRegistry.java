package org.dromara.app.tool;

import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.AiTool;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 工具注册表
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ToolRegistry {

    private final Map<String, ToolExecutor> executors = new ConcurrentHashMap<>();
    private final Map<String, AiTool> tools = new ConcurrentHashMap<>();

    /**
     * 注册工具执行器
     *
     * @param executor 执行器
     */
    public void registerExecutor(ToolExecutor executor) {
        String toolId = executor.getToolId();
        executors.put(toolId, executor);
        log.info("注册工具执行器: {} - {}", toolId, executor.getToolName());
    }

    /**
     * 注册工具配置
     *
     * @param tool 工具配置
     */
    public void registerTool(AiTool tool) {
        tools.put(tool.getId(), tool);
        log.debug("注册工具配置: {} - {}", tool.getId(), tool.getName());
    }

    /**
     * 更新工具配置
     *
     * @param tool 工具配置
     */
    public void updateTool(AiTool tool) {
        tools.put(tool.getId(), tool);
        log.debug("更新工具配置: {} - {}", tool.getId(), tool.getName());
    }

    /**
     * 移除工具
     *
     * @param toolId 工具ID
     */
    public void unregisterTool(String toolId) {
        tools.remove(toolId);
        executors.remove(toolId);
        log.info("移除工具: {}", toolId);
    }

    /**
     * 获取工具执行器
     *
     * @param toolId 工具ID
     * @return 执行器
     */
    public ToolExecutor getExecutor(String toolId) {
        return executors.get(toolId);
    }

    /**
     * 获取工具配置
     *
     * @param toolId 工具ID
     * @return 工具配置
     */
    public AiTool getTool(String toolId) {
        return tools.get(toolId);
    }

    /**
     * 检查工具是否已注册
     *
     * @param toolId 工具ID
     * @return 是否已注册
     */
    public boolean isRegistered(String toolId) {
        return executors.containsKey(toolId);
    }

    /**
     * 获取所有已注册的工具ID
     *
     * @return 工具ID集合
     */
    public java.util.Set<String> getRegisteredToolIds() {
        return executors.keySet();
    }

    /**
     * 获取所有执行器
     *
     * @return 执行器映射
     */
    public Map<String, ToolExecutor> getAllExecutors() {
        return new ConcurrentHashMap<>(executors);
    }
}
