{"doc": "\n 工作流设计器获取任务执行人\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectRolesByTaskAssigneeList", "paramTypes": ["org.dromara.common.core.domain.model.TaskAssigneeBody"], "doc": "\n 查询角色并返回任务指派的列表，支持分页\r\n\r\n @param taskQuery 查询条件\r\n @return 办理人\r\n"}, {"name": "selectPostsByTaskAssigneeList", "paramTypes": ["org.dromara.common.core.domain.model.TaskAssigneeBody"], "doc": "\n 查询岗位并返回任务指派的列表，支持分页\r\n\r\n @param taskQuery 查询条件\r\n @return 办理人\r\n"}, {"name": "selectDeptsByTaskAssigneeList", "paramTypes": ["org.dromara.common.core.domain.model.TaskAssigneeBody"], "doc": "\n 查询部门并返回任务指派的列表，支持分页\r\n\r\n @param taskQuery 查询条件\r\n @return 办理人\r\n"}, {"name": "selectUsersByTaskAssigneeList", "paramTypes": ["org.dromara.common.core.domain.model.TaskAssigneeBody"], "doc": "\n 查询用户并返回任务指派的列表，支持分页\r\n\r\n @param taskQuery 查询条件\r\n @return 办理人\r\n"}], "constructors": []}