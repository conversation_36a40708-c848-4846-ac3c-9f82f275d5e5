package org.dromara.app.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.dromara.app.domain.LearningProgress;

import java.util.List;
import java.util.Map;

/**
 * 学习进度服务接口
 *
 * <AUTHOR>
 */
public interface ILearningProgressService {

    /**
     * 开始学习
     *
     * @param userId 用户ID
     * @param learningPathId 学习路径ID
     * @param resourceId 资源ID
     * @return 学习进度
     */
    LearningProgress startLearning(Long userId, String learningPathId, Long resourceId);

    /**
     * 更新学习进度
     *
     * @param progressId 进度ID
     * @param completionPercentage 完成百分比
     * @param studyMinutes 学习时长
     * @return 是否成功
     */
    boolean updateProgress(Long progressId, Integer completionPercentage, Integer studyMinutes);

    /**
     * 完成学习
     *
     * @param progressId 进度ID
     * @param effectivenessRating 效果评分
     * @param satisfactionRating 满意度评分
     * @param notes 学习笔记
     * @return 是否成功
     */
    boolean completeLearning(Long progressId, Double effectivenessRating, Double satisfactionRating, String notes);

    /**
     * 暂停学习
     *
     * @param progressId 进度ID
     * @param reason 暂停原因
     * @return 是否成功
     */
    boolean pauseLearning(Long progressId, String reason);

    /**
     * 恢复学习
     *
     * @param progressId 进度ID
     * @return 是否成功
     */
    boolean resumeLearning(Long progressId);

    /**
     * 获取用户学习进度列表
     *
     * @param userId 用户ID
     * @param status 状态筛选
     * @return 学习进度列表
     */
    List<LearningProgress> getUserLearningProgress(Long userId, String status);

    /**
     * 分页查询学习进度
     *
     * @param userId 用户ID
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param status 状态筛选
     * @return 分页结果
     */
    Page<LearningProgress> getLearningProgressPage(Long userId, Integer pageNum, Integer pageSize, String status);

    /**
     * 获取学习进度详情
     *
     * @param progressId 进度ID
     * @param userId 用户ID
     * @return 学习进度
     */
    LearningProgress getLearningProgressDetail(Long progressId, Long userId);

    /**
     * 根据学习路径获取进度
     *
     * @param learningPathId 学习路径ID
     * @param userId 用户ID
     * @return 学习进度
     */
    LearningProgress getProgressByLearningPath(String learningPathId, Long userId);

    /**
     * 根据资源获取进度
     *
     * @param resourceId 资源ID
     * @param userId 用户ID
     * @return 学习进度
     */
    LearningProgress getProgressByResource(Long resourceId, Long userId);

    /**
     * 获取用户学习统计
     *
     * @param userId 用户ID
     * @return 统计信息
     */
    Map<String, Object> getUserLearningStatistics(Long userId);

    /**
     * 获取学习效果评估
     *
     * @param userId 用户ID
     * @return 效果评估
     */
    Map<String, Object> getLearningEffectivenessAssessment(Long userId);

    /**
     * 获取学习时间统计
     *
     * @param userId 用户ID
     * @return 时间统计
     */
    Map<String, Object> getStudyTimeStatistics(Long userId);

    /**
     * 获取学习趋势
     *
     * @param userId 用户ID
     * @param days 天数
     * @return 趋势数据
     */
    List<Map<String, Object>> getLearningTrend(Long userId, Integer days);

    /**
     * 获取最近学习记录
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 学习记录列表
     */
    List<LearningProgress> getRecentLearningRecords(Long userId, Integer limit);

    /**
     * 获取正在进行的学习
     *
     * @param userId 用户ID
     * @return 学习进度列表
     */
    List<LearningProgress> getOngoingLearning(Long userId);

    /**
     * 获取已完成的学习
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 学习进度列表
     */
    List<LearningProgress> getCompletedLearning(Long userId, Integer limit);

    /**
     * 获取超期学习项目
     *
     * @param userId 用户ID
     * @return 超期学习列表
     */
    List<LearningProgress> getOverdueLearning(Long userId);

    /**
     * 创建学习计划
     *
     * @param progressId 进度ID
     * @param learningPlan 学习计划
     * @return 是否成功
     */
    boolean createLearningPlan(Long progressId, LearningProgress.LearningPlan learningPlan);

    /**
     * 更新学习计划
     *
     * @param progressId 进度ID
     * @param learningPlan 学习计划
     * @return 是否成功
     */
    boolean updateLearningPlan(Long progressId, LearningProgress.LearningPlan learningPlan);

    /**
     * 添加学习反馈
     *
     * @param progressId 进度ID
     * @param feedback 学习反馈
     * @return 是否成功
     */
    boolean addLearningFeedback(Long progressId, LearningProgress.LearningFeedback feedback);

    /**
     * 更新里程碑完成状态
     *
     * @param progressId 进度ID
     * @param milestone 里程碑名称
     * @param completed 是否完成
     * @return 是否成功
     */
    boolean updateMilestoneStatus(Long progressId, String milestone, boolean completed);

    /**
     * 计算学习效率
     *
     * @param progressId 进度ID
     * @return 学习效率评分
     */
    Double calculateLearningEfficiency(Long progressId);

    /**
     * 生成学习报告
     *
     * @param userId 用户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 学习报告
     */
    Map<String, Object> generateLearningReport(Long userId, String startDate, String endDate);

    /**
     * 推荐学习内容调整
     *
     * @param userId 用户ID
     * @return 调整建议
     */
    List<Map<String, Object>> recommendLearningAdjustments(Long userId);

    /**
     * 获取学习提醒列表
     *
     * @param userId 用户ID
     * @return 提醒列表
     */
    List<Map<String, Object>> getLearningReminders(Long userId);

    /**
     * 设置学习提醒
     *
     * @param progressId 进度ID
     * @param reminderSettings 提醒设置
     * @return 是否成功
     */
    boolean setLearningReminder(Long progressId, LearningProgress.ReminderSettings reminderSettings);

    /**
     * 批量更新学习统计
     *
     * @param userId 用户ID
     * @return 更新数量
     */
    int batchUpdateLearningStatistics(Long userId);

    /**
     * 删除学习进度
     *
     * @param progressId 进度ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean deleteLearningProgress(Long progressId, Long userId);

    /**
     * 导出学习数据
     *
     * @param userId 用户ID
     * @param format 导出格式
     * @return 导出文件路径
     */
    String exportLearningData(Long userId, String format);

    /**
     * 学习数据分析
     *
     * @param userId 用户ID
     * @return 分析结果
     */
    Map<String, Object> analyzeLearningData(Long userId);

    /**
     * 获取学习成就
     *
     * @param userId 用户ID
     * @return 成就列表
     */
    List<Map<String, Object>> getLearningAchievements(Long userId);

    /**
     * 获取个性化推荐
     *
     * @param userId 用户ID
     * @return 个性化推荐列表
     */
    List<Map<String, Object>> getPersonalizedRecommendations(Long userId);

    /**
     * 批量更新学习进度
     *
     * @param progressIds 进度ID列表
     * @param updateData 更新数据
     * @return 是否成功
     */
    boolean batchUpdateProgress(List<Long> progressIds, Map<String, Object> updateData);

    /**
     * 导入学习数据
     *
     * @param userId 用户ID
     * @param importData 导入数据
     * @return 是否成功
     */
    boolean importLearningData(Long userId, Map<String, Object> importData);

    /**
     * 获取学习进度对比
     *
     * @param userId 用户ID
     * @param compareUserIds 对比用户ID列表
     * @return 对比结果
     */
    List<Map<String, Object>> getProgressComparison(Long userId, List<Long> compareUserIds);

    /**
     * 获取学习洞察
     *
     * @param userId 用户ID
     * @return 学习洞察数据
     */
    Map<String, Object> getLearningInsights(Long userId);
}
