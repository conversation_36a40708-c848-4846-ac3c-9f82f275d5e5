package org.dromara.app.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 评估问题视图对象
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Schema(description = "评估问题视图对象")
public class AssessmentQuestionVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 问题ID
     */
    @Schema(description = "问题ID")
    private String id;

    /**
     * 问题类型：single-单选题，scale-量表题
     */
    @Schema(description = "问题类型：single-单选题，scale-量表题")
    private String type;

    /**
     * 问题类别
     */
    @Schema(description = "问题类别")
    private String category;

    /**
     * 问题内容
     */
    @Schema(description = "问题内容")
    private String question;

    /**
     * 选项列表（单选题）
     */
    @Schema(description = "选项列表（单选题）")
    private List<OptionVo> options;

    /**
     * 量表最小值（量表题）
     */
    @Schema(description = "量表最小值（量表题）")
    private Integer minValue;

    /**
     * 量表最大值（量表题）
     */
    @Schema(description = "量表最大值（量表题）")
    private Integer maxValue;
}
