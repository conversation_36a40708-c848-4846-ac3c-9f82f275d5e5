package org.dromara.app.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * 视频映射工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class VideoMappingUtils {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * Integer转Boolean映射方法
     * 0 -> false, 1 -> true
     *
     * @param value Integer值
     * @return Boolean值
     */
    public static Boolean map(Integer value) {
        if (value == null) {
            return false;
        }
        return value == 1;
    }

    /**
     * Boolean转Integer映射方法
     * false -> 0, true -> 1
     *
     * @param value Boolean值
     * @return Integer值
     */
    public static Integer map(Boolean value) {
        if (value == null) {
            return 0;
        }
        return value ? 1 : 0;
    }

    /**
     * String转List<String>映射方法
     * 将JSON字符串转换为字符串列表
     *
     * @param value JSON字符串
     * @return 字符串列表
     */
    public static List<String> map(String value) {
        if (value == null || value.trim().isEmpty()) {
            return new ArrayList<>();
        }

        try {
            // 尝试解析JSON字符串
            return objectMapper.readValue(value, new TypeReference<List<String>>() {
            });
        } catch (JsonProcessingException e) {
            log.warn("Failed to parse tags JSON: {}, error: {}", value, e.getMessage());
            // 如果解析失败，返回空列表
            return new ArrayList<>();
        }
    }

    /**
     * List<String>转String映射方法
     * 将字符串列表转换为JSON字符串
     *
     * @param value 字符串列表
     * @return JSON字符串
     */
    public static String map(List<String> value) {
        if (value == null || value.isEmpty()) {
            return "[]";
        }

        try {
            return objectMapper.writeValueAsString(value);
        } catch (JsonProcessingException e) {
            log.warn("Failed to serialize tags to JSON: {}, error: {}", value, e.getMessage());
            return "[]";
        }
    }
}
