package org.dromara.app.domain.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * 分析任务数据传输对象
 *
 * <AUTHOR>
 * @date 2025-01-19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AnalysisTaskDto {

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 任务类型：audio/video/text/comprehensive
     */
    private String taskType;

    /**
     * 任务优先级：1-10，数字越大优先级越高
     */
    private Integer priority;

    /**
     * 任务状态：pending/running/completed/failed/cancelled
     */
    private String status;

    /**
     * 音频文件路径
     */
    private String audioFilePath;

    /**
     * 视频文件路径
     */
    private String videoFilePath;

    /**
     * 文本内容
     */
    private String textContent;

    /**
     * 岗位信息
     */
    private String jobPosition;

    /**
     * 任务创建时间
     */
    private LocalDateTime createTime;

    /**
     * 任务开始时间
     */
    private LocalDateTime startTime;

    /**
     * 任务完成时间
     */
    private LocalDateTime completeTime;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 最大重试次数
     */
    private Integer maxRetries;

    /**
     * 任务超时时间（秒）
     */
    private Integer timeoutSeconds;

    /**
     * 任务结果数据
     */
    private String resultData;
}