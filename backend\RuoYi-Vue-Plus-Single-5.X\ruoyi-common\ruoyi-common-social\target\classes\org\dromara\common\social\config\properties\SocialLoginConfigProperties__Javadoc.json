{"doc": "\n 社交登录配置\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "clientId", "doc": "\n 应用 ID\r\n"}, {"name": "clientSecret", "doc": "\n 应用密钥\r\n"}, {"name": "redirectUri", "doc": "\n 回调地址\r\n"}, {"name": "unionId", "doc": "\n 是否获取unionId\r\n"}, {"name": "codingGroupName", "doc": "\n Coding 企业名称\r\n"}, {"name": "alipayPublicKey", "doc": "\n 支付宝公钥\r\n"}, {"name": "agentId", "doc": "\n 企业微信应用ID\r\n"}, {"name": "stackOverflowKey", "doc": "\n stackoverflow api key\r\n"}, {"name": "deviceId", "doc": "\n 设备ID\r\n"}, {"name": "clientOsType", "doc": "\n 客户端系统类型\r\n"}, {"name": "serverUrl", "doc": "\n maxkey 服务器地址\r\n"}, {"name": "scopes", "doc": "\n 请求范围\r\n"}], "enumConstants": [], "methods": [], "constructors": []}