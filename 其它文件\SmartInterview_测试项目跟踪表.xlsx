<?xml version="1.0" encoding="UTF-8"?>
<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet"
 xmlns:o="urn:schemas-microsoft-com:office:office"
 xmlns:x="urn:schemas-microsoft-com:office:excel"
 xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet"
 xmlns:html="http://www.w3.org/TR/REC-html40">
 <DocumentProperties xmlns="urn:schemas-microsoft-com:office:office">
  <Title>SmartInterview AI智能面试系统测试项目跟踪表</Title>
  <Author>TTB9开发团队</Author>
  <Created>2025-01-17T21:30:00Z</Created>
 </DocumentProperties>
 <ExcelWorkbook xmlns="urn:schemas-microsoft-com:office:excel">
  <WindowHeight>12000</WindowHeight>
  <WindowWidth>18000</WindowWidth>
  <WindowTopX>0</WindowTopX>
  <WindowTopY>0</WindowTopY>
  <ProtectStructure>False</ProtectStructure>
  <ProtectWindows>False</ProtectWindows>
 </ExcelWorkbook>
 <Styles>
  <Style ss:ID="Default" ss:Name="Normal">
   <Alignment ss:Vertical="Bottom"/>
   <Borders/>
   <Font ss:FontName="宋体" x:CharSet="134" ss:Size="12"/>
   <Interior/>
   <NumberFormat/>
   <Protection/>
  </Style>
  <Style ss:ID="s62">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="宋体" x:CharSet="134" ss:Size="12" ss:Bold="1"/>
   <Interior ss:Color="#4472C4" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="s63">
   <Alignment ss:Horizontal="Left" ss:Vertical="Center"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="宋体" x:CharSet="134" ss:Size="11"/>
  </Style>
  <Style ss:ID="s64">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="宋体" x:CharSet="134" ss:Size="11"/>
   <Interior ss:Color="#E7E6E6" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="s65">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="宋体" x:CharSet="134" ss:Size="11"/>
   <Interior ss:Color="#D5E8D4" ss:Pattern="Solid"/>
  </Style>
  <Style ss:ID="s66">
   <Alignment ss:Horizontal="Center" ss:Vertical="Center"/>
   <Borders>
    <Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>
    <Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>
   </Borders>
   <Font ss:FontName="宋体" x:CharSet="134" ss:Size="11"/>
   <Interior ss:Color="#FFE6CC" ss:Pattern="Solid"/>
  </Style>
 </Styles>
 <Worksheet ss:Name="测试项目跟踪表">
  <Table ss:ExpandedColumnCount="12" ss:ExpandedRowCount="45" x:FullColumns="1" x:FullRows="1">
   <Column ss:AutoFitWidth="0" ss:Width="60"/>
   <Column ss:AutoFitWidth="0" ss:Width="120"/>
   <Column ss:AutoFitWidth="0" ss:Width="100"/>
   <Column ss:AutoFitWidth="0" ss:Width="150"/>
   <Column ss:AutoFitWidth="0" ss:Width="80"/>
   <Column ss:AutoFitWidth="0" ss:Width="300"/>
   <Column ss:AutoFitWidth="0" ss:Width="80"/>
   <Column ss:AutoFitWidth="0" ss:Width="100"/>
   <Column ss:AutoFitWidth="0" ss:Width="80"/>
   <Column ss:AutoFitWidth="0" ss:Width="100"/>
   <Column ss:AutoFitWidth="0" ss:Width="80"/>
   <Column ss:AutoFitWidth="0" ss:Width="150"/>
   <Row ss:AutoFitHeight="0" ss:Height="25">
    <Cell ss:StyleID="s62"><Data ss:Type="String">编号</Data></Cell>
    <Cell ss:StyleID="s62"><Data ss:Type="String">测试阶段</Data></Cell>
    <Cell ss:StyleID="s62"><Data ss:Type="String">发现阶段</Data></Cell>
    <Cell ss:StyleID="s62"><Data ss:Type="String">产品功能</Data></Cell>
    <Cell ss:StyleID="s62"><Data ss:Type="String">严重程度</Data></Cell>
    <Cell ss:StyleID="s62"><Data ss:Type="String">错误描述</Data></Cell>
    <Cell ss:StyleID="s62"><Data ss:Type="String">测试人</Data></Cell>
    <Cell ss:StyleID="s62"><Data ss:Type="String">测试时间</Data></Cell>
    <Cell ss:StyleID="s62"><Data ss:Type="String">解决人</Data></Cell>
    <Cell ss:StyleID="s62"><Data ss:Type="String">解决时间</Data></Cell>
    <Cell ss:StyleID="s62"><Data ss:Type="String">状态</Data></Cell>
    <Cell ss:StyleID="s62"><Data ss:Type="String">备注</Data></Cell>
   </Row>
   <Row ss:AutoFitHeight="0" ss:Height="20">
    <Cell ss:StyleID="s63"><Data ss:Type="String">001</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">系统测试（黑盒）</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">测试阶段</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">AI对话-通用助手</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">低</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">Agent切换功能测试，验证7个AI助手间的切换流畅性</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">段傲宇</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-15</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">韩家乐</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-15</Data></Cell>
    <Cell ss:StyleID="s65"><Data ss:Type="String">通过</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">功能正常，用户体验良好</Data></Cell>
   </Row>
   <Row ss:AutoFitHeight="0" ss:Height="20">
    <Cell ss:StyleID="s63"><Data ss:Type="String">002</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">系统测试（黑盒）</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">测试阶段</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">AI对话-简历顾问</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">中</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">PDF简历上传解析失败，返回"文件格式不支持"错误</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">韩家乐</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-15</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">范硕博</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-16</Data></Cell>
    <Cell ss:StyleID="s65"><Data ss:Type="String">已解决</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">修复PDF解析库版本兼容性问题</Data></Cell>
   </Row>
   <Row ss:AutoFitHeight="0" ss:Height="20">
    <Cell ss:StyleID="s63"><Data ss:Type="String">003</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">系统测试（黑盒）</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">测试阶段</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">AI对话-面试教练</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">高</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">SSE连接在网络波动时断开，消息传输中断</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">范硕博</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-15</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">段傲宇</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-17</Data></Cell>
    <Cell ss:StyleID="s65"><Data ss:Type="String">已解决</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">增加自动重连机制和心跳检测</Data></Cell>
   </Row>
   <Row ss:AutoFitHeight="0" ss:Height="20">
    <Cell ss:StyleID="s63"><Data ss:Type="String">004</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">系统测试（黑盒）</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">测试阶段</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">AI对话-技能评估师</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">中</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">AI响应时间过长，平均响应时间超过15秒</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">段傲宇</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-16</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">韩家乐</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-18</Data></Cell>
    <Cell ss:StyleID="s65"><Data ss:Type="String">已解决</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">优化模型调用参数，响应时间降至5秒内</Data></Cell>
   </Row>
   <Row ss:AutoFitHeight="0" ss:Height="20">
    <Cell ss:StyleID="s63"><Data ss:Type="String">005</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">系统测试（黑盒）</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">测试阶段</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">AI对话-模拟面试官</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">中</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">历史对话记录分页显示不完整，部分消息丢失</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">韩家乐</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-16</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">范硕博</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-17</Data></Cell>
    <Cell ss:StyleID="s65"><Data ss:Type="String">已解决</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">修复分页查询SQL语句的排序问题</Data></Cell>
   </Row>
   <Row ss:AutoFitHeight="0" ss:Height="20">
    <Cell ss:StyleID="s63"><Data ss:Type="String">006</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">系统测试（黑盒）</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">测试阶段</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">AI对话-学习导师</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">高</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">多轮对话中上下文信息丢失，AI回答不连贯</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">范硕博</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-16</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">段傲宇</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-19</Data></Cell>
    <Cell ss:StyleID="s65"><Data ss:Type="String">已解决</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">优化会话状态管理，增加上下文缓存</Data></Cell>
   </Row>
   <Row ss:AutoFitHeight="0" ss:Height="20">
    <Cell ss:StyleID="s63"><Data ss:Type="String">007</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">系统测试（黑盒）</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">测试阶段</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">面试训练-岗位选择</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">中</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">岗位列表加载缓慢，首次加载时间超过8秒</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">段傲宇</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-17</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">韩家乐</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-18</Data></Cell>
    <Cell ss:StyleID="s65"><Data ss:Type="String">已解决</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">添加Redis缓存，加载时间降至2秒</Data></Cell>
   </Row>
   <Row ss:AutoFitHeight="0" ss:Height="20">
    <Cell ss:StyleID="s63"><Data ss:Type="String">008</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">系统测试（黑盒）</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">测试阶段</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">面试训练-面试房间</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">高</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">进入面试房间后页面白屏，控制台报路由错误</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">韩家乐</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-17</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">范硕博</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-17</Data></Cell>
    <Cell ss:StyleID="s65"><Data ss:Type="String">已解决</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">修复Vue Router配置问题</Data></Cell>
   </Row>
   <Row ss:AutoFitHeight="0" ss:Height="20">
    <Cell ss:StyleID="s63"><Data ss:Type="String">009</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">系统测试（黑盒）</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">测试阶段</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">面试训练-问题生成</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">中</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">面试问题重复率高达40%，缺乏多样性</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">范硕博</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-17</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">段傲宇</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-20</Data></Cell>
    <Cell ss:StyleID="s65"><Data ss:Type="String">已解决</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">改进问题生成算法，增加随机性</Data></Cell>
   </Row>
   <Row ss:AutoFitHeight="0" ss:Height="20">
    <Cell ss:StyleID="s63"><Data ss:Type="String">010</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">系统测试（黑盒）</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">测试阶段</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">面试训练-答案评估</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">高</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">评分结果偏差较大，与人工评分差异超过20分</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">段傲宇</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-18</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">韩家乐</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-22</Data></Cell>
    <Cell ss:StyleID="s65"><Data ss:Type="String">已解决</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">重新训练评分模型，调整权重参数</Data></Cell>
   </Row>
   <Row ss:AutoFitHeight="0" ss:Height="20">
    <Cell ss:StyleID="s63"><Data ss:Type="String">011</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">系统测试（黑盒）</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">测试阶段</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">面试训练-结果报告</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">高</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">面试报告生成失败，服务器返回500内部错误</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">韩家乐</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-18</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">范硕博</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-19</Data></Cell>
    <Cell ss:StyleID="s65"><Data ss:Type="String">已解决</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">修复报告模板渲染引擎配置</Data></Cell>
   </Row>
   <Row ss:AutoFitHeight="0" ss:Height="20">
    <Cell ss:StyleID="s63"><Data ss:Type="String">012</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">系统测试（黑盒）</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">测试阶段</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">技能评估-初始评估</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">低</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">技能评估测试创建和执行流程验证</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">范硕博</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-18</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">段傲宇</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-18</Data></Cell>
    <Cell ss:StyleID="s65"><Data ss:Type="String">通过</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">评估流程完整，数据准确</Data></Cell>
   </Row>
   <Row ss:AutoFitHeight="0" ss:Height="20">
    <Cell ss:StyleID="s63"><Data ss:Type="String">013</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">系统测试（黑盒）</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">测试阶段</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">技能评估-结果分析</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">中</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">能力雷达图显示异常，数据与实际评估结果不匹配</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">段傲宇</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-19</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">韩家乐</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-20</Data></Cell>
    <Cell ss:StyleID="s65"><Data ss:Type="String">已解决</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">修复ECharts图表数据绑定逻辑</Data></Cell>
   </Row>
   <Row ss:AutoFitHeight="0" ss:Height="20">
    <Cell ss:StyleID="s63"><Data ss:Type="String">014</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">系统测试（黑盒）</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">测试阶段</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">用户管理-注册登录</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">高</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">手机验证码发送失败，短信服务响应超时</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">韩家乐</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-19</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">范硕博</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-21</Data></Cell>
    <Cell ss:StyleID="s65"><Data ss:Type="String">已解决</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">更换为阿里云短信服务，稳定性提升</Data></Cell>
   </Row>
   <Row ss:AutoFitHeight="0" ss:Height="20">
    <Cell ss:StyleID="s63"><Data ss:Type="String">015</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">系统测试（黑盒）</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">测试阶段</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">用户管理-个人信息</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">低</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">用户头像上传功能测试，支持JPG/PNG/GIF格式</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">范硕博</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-19</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">段傲宇</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-19</Data></Cell>
    <Cell ss:StyleID="s65"><Data ss:Type="String">通过</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">上传功能正常，图片压缩效果良好</Data></Cell>
   </Row>
   <Row ss:AutoFitHeight="0" ss:Height="20">
    <Cell ss:StyleID="s63"><Data ss:Type="String">016</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">系统测试（黑盒）</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">测试阶段</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">用户管理-简历管理</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">中</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">简历在线预览功能格式错乱，中文显示异常</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">段傲宇</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-20</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">韩家乐</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-21</Data></Cell>
    <Cell ss:StyleID="s65"><Data ss:Type="String">已解决</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">更新PDF.js版本，修复中文字体问题</Data></Cell>
   </Row>
   <Row ss:AutoFitHeight="0" ss:Height="20">
    <Cell ss:StyleID="s63"><Data ss:Type="String">017</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">系统测试（黑盒）</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">测试阶段</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">学习中心-学习计划</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">低</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">个性化学习计划创建和进度跟踪功能验证</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">韩家乐</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-20</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">范硕博</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-20</Data></Cell>
    <Cell ss:StyleID="s65"><Data ss:Type="String">通过</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">计划创建流程顺畅，进度统计准确</Data></Cell>
   </Row>
   <Row ss:AutoFitHeight="0" ss:Height="20">
    <Cell ss:StyleID="s63"><Data ss:Type="String">018</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">系统测试（黑盒）</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">测试阶段</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">学习中心-资源推荐</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">中</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">推荐算法效果不佳，推荐内容相关性低于60%</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">范硕博</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-20</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">段傲宇</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-25</Data></Cell>
    <Cell ss:StyleID="s65"><Data ss:Type="String">已解决</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">重新训练推荐模型，相关性提升至85%</Data></Cell>
   </Row>
   <Row ss:AutoFitHeight="0" ss:Height="20">
    <Cell ss:StyleID="s63"><Data ss:Type="String">019</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">系统测试（黑盒）</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">测试阶段</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">多端兼容-小程序</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">中</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">微信小程序端AI对话功能受限，部分API不可用</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">段傲宇</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-21</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">韩家乐</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-23</Data></Cell>
    <Cell ss:StyleID="s65"><Data ss:Type="String">已解决</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">适配小程序环境，使用兼容性API</Data></Cell>
   </Row>
   <Row ss:AutoFitHeight="0" ss:Height="20">
    <Cell ss:StyleID="s63"><Data ss:Type="String">020</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">系统测试（黑盒）</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">测试阶段</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">多端兼容-H5</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">低</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">H5端在iPad横屏模式下布局显示异常</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">韩家乐</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-21</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">范硕博</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-22</Data></Cell>
    <Cell ss:StyleID="s65"><Data ss:Type="String">已解决</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">优化响应式CSS，适配平板设备</Data></Cell>
   </Row>
   <Row ss:AutoFitHeight="0" ss:Height="20">
    <Cell ss:StyleID="s63"><Data ss:Type="String">021</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">系统测试（白盒）</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">开发阶段</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">API接口-用户认证</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">中</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">/api/auth/login接口参数验证不完整，缺少邮箱格式校验</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">范硕博</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-22</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">段傲宇</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-23</Data></Cell>
    <Cell ss:StyleID="s65"><Data ss:Type="String">已解决</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">增加@Valid注解和自定义校验器</Data></Cell>
   </Row>
   <Row ss:AutoFitHeight="0" ss:Height="20">
    <Cell ss:StyleID="s63"><Data ss:Type="String">022</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">系统测试（白盒）</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">开发阶段</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">API接口-AI对话</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">高</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">/api/chat/send接口在高并发下出现线程安全问题</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">段傲宇</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-22</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">韩家乐</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-24</Data></Cell>
    <Cell ss:StyleID="s65"><Data ss:Type="String">已解决</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">使用ThreadLocal和并发控制</Data></Cell>
   </Row>
   <Row ss:AutoFitHeight="0" ss:Height="20">
    <Cell ss:StyleID="s63"><Data ss:Type="String">023</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">系统测试（白盒）</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">开发阶段</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">数据库操作-会话管理</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">高</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">chat_session表查询性能差，全表扫描耗时5秒+</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">韩家乐</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-22</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">范硕博</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-25</Data></Cell>
    <Cell ss:StyleID="s65"><Data ss:Type="String">已解决</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">添加复合索引(user_id, create_time)</Data></Cell>
   </Row>
   <Row ss:AutoFitHeight="0" ss:Height="20">
    <Cell ss:StyleID="s63"><Data ss:Type="String">024</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">系统测试（白盒）</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">开发阶段</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">数据库操作-消息存储</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">高</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">chat_message表高并发插入时出现死锁</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">范硕博</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-23</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">段傲宇</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-26</Data></Cell>
    <Cell ss:StyleID="s65"><Data ss:Type="String">已解决</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">调整事务隔离级别为READ_COMMITTED</Data></Cell>
   </Row>
   <Row ss:AutoFitHeight="0" ss:Height="20">
    <Cell ss:StyleID="s63"><Data ss:Type="String">025</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">系统测试（白盒）</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">开发阶段</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">业务逻辑-Agent路由</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">高</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">AgentType枚举判断逻辑存在漏洞，可能导致空指针</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">段傲宇</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-23</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">韩家乐</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-24</Data></Cell>
    <Cell ss:StyleID="s65"><Data ss:Type="String">已解决</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">完善枚举验证和默认值处理</Data></Cell>
   </Row>
   <Row ss:AutoFitHeight="0" ss:Height="20">
    <Cell ss:StyleID="s63"><Data ss:Type="String">026</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">系统测试（白盒）</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">开发阶段</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">业务逻辑-权限控制</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">高</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">部分管理接口缺少@PreAuthorize权限验证</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">韩家乐</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-23</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">范硕博</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-25</Data></Cell>
    <Cell ss:StyleID="s65"><Data ss:Type="String">已解决</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">补充权限注解，完善RBAC控制</Data></Cell>
   </Row>
   <Row ss:AutoFitHeight="0" ss:Height="20">
    <Cell ss:StyleID="s63"><Data ss:Type="String">027</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">系统测试（白盒）</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">开发阶段</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">异常处理-网络异常</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">中</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">AI服务调用失败时异常处理不当，用户体验差</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">范硕博</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-24</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">段傲宇</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-26</Data></Cell>
    <Cell ss:StyleID="s65"><Data ss:Type="String">已解决</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">增加重试机制和友好错误提示</Data></Cell>
   </Row>
   <Row ss:AutoFitHeight="0" ss:Height="20">
    <Cell ss:StyleID="s63"><Data ss:Type="String">028</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">系统测试（白盒）</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">开发阶段</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">异常处理-数据异常</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">高</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">空指针异常未正确捕获，导致系统崩溃</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">段傲宇</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-24</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">韩家乐</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-25</Data></Cell>
    <Cell ss:StyleID="s65"><Data ss:Type="String">已解决</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">添加全局异常处理器和空值检查</Data></Cell>
   </Row>
   <Row ss:AutoFitHeight="0" ss:Height="20">
    <Cell ss:StyleID="s63"><Data ss:Type="String">029</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">系统测试（白盒）</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">开发阶段</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">缓存机制-Redis</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">中</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">Redis连接池配置不合理，连接数不足</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">韩家乐</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-24</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">范硕博</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-27</Data></Cell>
    <Cell ss:StyleID="s65"><Data ss:Type="String">已解决</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">调整Redisson连接池参数</Data></Cell>
   </Row>
   <Row ss:AutoFitHeight="0" ss:Height="20">
    <Cell ss:StyleID="s63"><Data ss:Type="String">030</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">系统测试（白盒）</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">开发阶段</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">日志记录-操作日志</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">中</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">关键业务操作缺少详细日志记录</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">范硕博</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-25</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">段傲宇</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-26</Data></Cell>
    <Cell ss:StyleID="s65"><Data ss:Type="String">已解决</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">使用AOP切面记录操作日志</Data></Cell>
   </Row>
   <Row ss:AutoFitHeight="0" ss:Height="20">
    <Cell ss:StyleID="s63"><Data ss:Type="String">031</Data></Cell>
    <Cell ss:StyleID="s66"><Data ss:Type="String">压力测试</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">测试阶段</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">并发用户-AI对话</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">低</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">100并发用户同时进行AI对话，系统响应时间测试</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">段傲宇</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-25</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">韩家乐</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-25</Data></Cell>
    <Cell ss:StyleID="s65"><Data ss:Type="String">通过</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">平均响应时间2.3秒，满足要求</Data></Cell>
   </Row>
   <Row ss:AutoFitHeight="0" ss:Height="20">
    <Cell ss:StyleID="s63"><Data ss:Type="String">032</Data></Cell>
    <Cell ss:StyleID="s66"><Data ss:Type="String">压力测试</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">测试阶段</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">并发用户-面试功能</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">中</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">50用户同时进行模拟面试，出现响应超时</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">韩家乐</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-25</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">范硕博</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-28</Data></Cell>
    <Cell ss:StyleID="s65"><Data ss:Type="String">已解决</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">优化数据库连接池和查询语句</Data></Cell>
   </Row>
   <Row ss:AutoFitHeight="0" ss:Height="20">
    <Cell ss:StyleID="s63"><Data ss:Type="String">033</Data></Cell>
    <Cell ss:StyleID="s66"><Data ss:Type="String">压力测试</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">测试阶段</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">大数据量-历史记录</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">中</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">10万条聊天记录查询性能测试</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">范硕博</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-26</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">段傲宇</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String"></Data></Cell>
    <Cell ss:StyleID="s66"><Data ss:Type="String">待优化</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">查询时间3.2秒，需进一步优化</Data></Cell>
   </Row>
   <Row ss:AutoFitHeight="0" ss:Height="20">
    <Cell ss:StyleID="s63"><Data ss:Type="String">034</Data></Cell>
    <Cell ss:StyleID="s66"><Data ss:Type="String">压力测试</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">测试阶段</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">大数据量-文件上传</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">中</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">同时上传100个大文件（每个10MB）压力测试</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">段傲宇</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-26</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">韩家乐</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-29</Data></Cell>
    <Cell ss:StyleID="s65"><Data ss:Type="String">已解决</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">增加文件上传队列和进度控制</Data></Cell>
   </Row>
   <Row ss:AutoFitHeight="0" ss:Height="20">
    <Cell ss:StyleID="s63"><Data ss:Type="String">035</Data></Cell>
    <Cell ss:StyleID="s66"><Data ss:Type="String">压力测试</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">测试阶段</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">长时间运行-稳定性</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">高</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">系统连续运行24小时稳定性测试</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">韩家乐</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-27</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">范硕博</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-28</Data></Cell>
    <Cell ss:StyleID="s66"><Data ss:Type="String">发现问题</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">发现内存泄漏，正在定位原因</Data></Cell>
   </Row>
   <Row ss:AutoFitHeight="0" ss:Height="20">
    <Cell ss:StyleID="s63"><Data ss:Type="String">036</Data></Cell>
    <Cell ss:StyleID="s66"><Data ss:Type="String">压力测试</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">测试阶段</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">长时间运行-内存管理</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">高</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">内存使用量持续增长，24小时后增长40%</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">范硕博</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-27</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">段傲宇</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-30</Data></Cell>
    <Cell ss:StyleID="s65"><Data ss:Type="String">已解决</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">修复SSE连接对象引用问题</Data></Cell>
   </Row>
   <Row ss:AutoFitHeight="0" ss:Height="20">
    <Cell ss:StyleID="s63"><Data ss:Type="String">037</Data></Cell>
    <Cell ss:StyleID="s66"><Data ss:Type="String">压力测试</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">测试阶段</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">资源消耗-CPU</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">高</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">高并发时CPU使用率达到90%，影响响应速度</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">段傲宇</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-28</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">韩家乐</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-31</Data></Cell>
    <Cell ss:StyleID="s65"><Data ss:Type="String">已解决</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">优化AI调用算法，CPU使用率降至70%</Data></Cell>
   </Row>
   <Row ss:AutoFitHeight="0" ss:Height="20">
    <Cell ss:StyleID="s63"><Data ss:Type="String">038</Data></Cell>
    <Cell ss:StyleID="s66"><Data ss:Type="String">压力测试</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">测试阶段</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">资源消耗-数据库</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">高</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">数据库连接数达到上限，新请求被拒绝</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">韩家乐</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-28</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">范硕博</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-30</Data></Cell>
    <Cell ss:StyleID="s65"><Data ss:Type="String">已解决</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">调整连接池配置，增加最大连接数</Data></Cell>
   </Row>
   <Row ss:AutoFitHeight="0" ss:Height="20">
    <Cell ss:StyleID="s63"><Data ss:Type="String">039</Data></Cell>
    <Cell ss:StyleID="s66"><Data ss:Type="String">压力测试</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">测试阶段</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">网络带宽-SSE流</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">中</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">大量SSE连接占用带宽过高，影响其他服务</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">范硕博</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-29</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">段傲宇</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String"></Data></Cell>
    <Cell ss:StyleID="s66"><Data ss:Type="String">处理中</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">正在优化数据传输格式</Data></Cell>
   </Row>
   <Row ss:AutoFitHeight="0" ss:Height="20">
    <Cell ss:StyleID="s63"><Data ss:Type="String">040</Data></Cell>
    <Cell ss:StyleID="s66"><Data ss:Type="String">压力测试</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">测试阶段</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">存储空间-文件存储</Data></Cell>
    <Cell ss:StyleID="s64"><Data ss:Type="String">低</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">用户上传文件占用存储空间快速增长</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">段傲宇</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">2025-01-29</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">韩家乐</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String"></Data></Cell>
    <Cell ss:StyleID="s66"><Data ss:Type="String">计划中</Data></Cell>
    <Cell ss:StyleID="s63"><Data ss:Type="String">计划实施文件清理策略</Data></Cell>
   </Row>
  </Table>
  <WorksheetOptions xmlns="urn:schemas-microsoft-com:office:excel">
   <PageSetup>
    <Header x:Margin="0.3"/>
    <Footer x:Margin="0.3"/>
    <PageMargins x:Bottom="0.75" x:Left="0.7" x:Right="0.7" x:Top="0.75"/>
   </PageSetup>
   <Print>
    <ValidPrinterInfo/>
    <PaperSizeIndex>9</PaperSizeIndex>
    <HorizontalResolution>600</HorizontalResolution>
    <VerticalResolution>600</VerticalResolution>
   </Print>
   <Selected/>
   <Panes>
    <Pane>
     <Number>3</Number>
     <ActiveRow>1</ActiveRow>
     <ActiveCol>1</ActiveCol>
    </Pane>
   </Panes>
   <ProtectObjects>False</ProtectObjects>
   <ProtectScenarios>False</ProtectScenarios>
  </WorksheetOptions>
 </Worksheet>
</Workbook>