package org.dromara.app.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.UserBehavior;
import org.dromara.app.domain.bo.UserBehaviorBo;
import org.dromara.app.domain.dto.TrackEventDto;
import org.dromara.app.mapper.UserBehaviorMapper;
import org.dromara.app.service.IAchievementService;
import org.dromara.app.service.IUserBehaviorService;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.rabbitmq.core.RabbitMqTemplate;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 用户行为记录Service业务层处理
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserBehaviorServiceImpl implements IUserBehaviorService {

    private final UserBehaviorMapper userBehaviorMapper;
    private final Optional<RabbitMqTemplate> rabbitMqTemplate;
    private final IAchievementService achievementService;

    @Override
    @Transactional
    public void processUserBehavior(TrackEventDto trackEventDto) {
        try {
            // 1. 保存用户行为记录
            UserBehaviorBo userBehaviorBo = convertToUserBehaviorBo(trackEventDto);
            insertUserBehavior(userBehaviorBo);

            // 2. 发送到MQ进行异步成就检查
            sendToAchievementQueue(trackEventDto);

        } catch (Exception e) {
            log.error("处理用户行为数据失败: {}", trackEventDto, e);
            throw new RuntimeException("处理用户行为数据失败", e);
        }
    }

    @Override
    @Transactional
    public void batchProcessUserBehavior(List<TrackEventDto> trackEventDtos) {
        try {
            // 1. 批量保存用户行为记录
            List<UserBehaviorBo> userBehaviorBos = trackEventDtos.stream()
                .map(this::convertToUserBehaviorBo)
                .collect(Collectors.toList());
            batchInsertUserBehavior(userBehaviorBos);

            // 2. 批量发送到MQ进行异步成就检查
            for (TrackEventDto trackEventDto : trackEventDtos) {
                sendToAchievementQueue(trackEventDto);
            }

        } catch (Exception e) {
            log.error("批量处理用户行为数据失败: {}", trackEventDtos.size(), e);
            throw new RuntimeException("批量处理用户行为数据失败", e);
        }
    }

    @Override
    public Boolean insertUserBehavior(UserBehaviorBo userBehaviorBo) {
        UserBehavior userBehavior = MapstructUtils.convert(userBehaviorBo, UserBehavior.class);
        userBehavior.setCreateTime(new Date());
        return userBehaviorMapper.insert(userBehavior) > 0;
    }

    @Override
    public Boolean batchInsertUserBehavior(List<UserBehaviorBo> userBehaviorBos) {
        List<UserBehavior> userBehaviors = userBehaviorBos.stream()
            .map(bo -> {
                UserBehavior behavior = MapstructUtils.convert(bo, UserBehavior.class);
                behavior.setCreateTime(new Date());
                return behavior;
            })
            .collect(Collectors.toList());
        
        return userBehaviorMapper.batchInsert(userBehaviors) > 0;
    }

    @Override
    public List<UserBehavior> queryByUserIdAndBehaviorType(Long userId, String behaviorType) {
        return userBehaviorMapper.selectByUserIdAndBehaviorType(userId, behaviorType);
    }

    @Override
    public Long countByUserIdAndBehaviorType(Long userId, String behaviorType) {
        return userBehaviorMapper.countByUserIdAndBehaviorType(userId, behaviorType);
    }

    @Override
    public Integer getConsecutiveLoginDays(Long userId) {
        return userBehaviorMapper.selectConsecutiveLoginDays(userId);
    }

    @Override
    public Long getTotalStudyMinutes(Long userId) {
        return userBehaviorMapper.selectTotalStudyMinutes(userId);
    }

    /**
     * 将TrackEventDto转换为UserBehaviorBo
     */
    private UserBehaviorBo convertToUserBehaviorBo(TrackEventDto trackEventDto) {
        UserBehaviorBo userBehaviorBo = new UserBehaviorBo();
        userBehaviorBo.setUserId(trackEventDto.getUserId());
        userBehaviorBo.setBehaviorType(trackEventDto.getEventType());
        userBehaviorBo.setBehaviorData(JSONUtil.toJsonStr(trackEventDto.getEventData()));
        userBehaviorBo.setIpAddress(trackEventDto.getIpAddress());
        userBehaviorBo.setUserAgent(trackEventDto.getUserAgent());
        userBehaviorBo.setSessionId(trackEventDto.getSessionId());
        return userBehaviorBo;
    }

    /**
     * 发送到成就检查队列
     */
    private void sendToAchievementQueue(TrackEventDto trackEventDto) {
        if (rabbitMqTemplate.isPresent()) {
            try {
                rabbitMqTemplate.get().send(
                    "achievement.exchange",
                    "achievement.check",
                    trackEventDto
                );
                log.debug("用户行为数据已发送到成就检查队列: userId={}, eventType={}", 
                    trackEventDto.getUserId(), trackEventDto.getEventType());
            } catch (Exception e) {
                log.warn("发送到成就检查队列失败: {}", trackEventDto, e);
                // 如果MQ发送失败，可以考虑直接调用成就检查服务
                try {
                    achievementService.checkAchievements(trackEventDto.getUserId(), trackEventDto);
                } catch (Exception ex) {
                    log.error("直接调用成就检查服务也失败: {}", trackEventDto, ex);
                }
            }
        } else {
            // 如果没有配置MQ，直接调用成就检查服务
            try {
                achievementService.checkAchievements(trackEventDto.getUserId(), trackEventDto);
            } catch (Exception e) {
                log.error("直接调用成就检查服务失败: {}", trackEventDto, e);
            }
        }
    }

}
