package org.dromara.app.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.util.List;

/**
 * 聊天会话对象 app_chat_session
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_chat_session")
public class ChatSession extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 会话ID
     */
    @TableId(value = "id")
    private String id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 会话标题
     */
    private String title;

    /**
     * Agent类型：general/interviewer/resume_analyzer/skill_assessor/career_advisor/mock_interviewer/learning_guide
     */
    private String agentType;

    /**
     * 消息总数
     */
    private Integer messageCount;

    /**
     * 最后一条消息内容预览
     */
    private String lastMessage;

    /**
     * 最后活跃时间
     */
    private Long lastActiveTime;

    /**
     * 会话状态：0-已归档，1-活跃
     */
    private Integer status;

    /**
     * 会话配置（JSON格式）
     */
    private String sessionConfig;

    /**
     * 消息列表（不存储到数据库，用于返回给前端）
     */
    @TableField(exist = false)
    private List<ChatMessage> messages;

    /**
     * 会话统计信息（不存储到数据库）
     */
    @TableField(exist = false)
    private SessionStats stats;

    /**
     * 删除标志（0-正常，1-删除）
     */
    private String delFlag;

    /**
     * 会话统计信息内部类
     */
    @Data
    public static class SessionStats {
        private Integer userMessageCount;
        private Integer assistantMessageCount;
        private Long totalDuration;
        private String mostUsedFeature;
    }
}
