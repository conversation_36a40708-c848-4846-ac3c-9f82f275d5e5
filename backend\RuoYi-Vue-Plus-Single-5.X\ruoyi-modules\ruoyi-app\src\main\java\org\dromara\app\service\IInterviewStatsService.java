package org.dromara.app.service;

import org.dromara.app.domain.vo.InterviewResponseVo;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 面试统计服务接口
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
public interface IInterviewStatsService {

    /**
     * 获取用户面试统计
     *
     * @param userId 用户ID
     * @return 统计信息
     */
    InterviewResponseVo.UserStats getUserStats(Long userId);

    /**
     * 获取岗位面试统计
     *
     * @param jobId 岗位ID
     * @return 统计信息
     */
    InterviewResponseVo.JobStats getJobStats(Long jobId);

    /**
     * 获取系统整体统计
     *
     * @return 系统统计
     */
    InterviewResponseVo.SystemStats getSystemStats();

    /**
     * 获取面试趋势数据
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param granularity 粒度（day, week, month）
     * @return 趋势数据
     */
    List<InterviewResponseVo.TrendData> getInterviewTrends(LocalDateTime startTime, 
                                                           LocalDateTime endTime, 
                                                           String granularity);

    /**
     * 获取热门问题统计
     *
     * @param limit 限制数量
     * @return 热门问题列表
     */
    List<InterviewResponseVo.PopularQuestion> getPopularQuestions(Integer limit);

    /**
     * 获取用户表现排行
     *
     * @param limit 限制数量
     * @return 排行榜
     */
    List<InterviewResponseVo.UserRanking> getUserRankings(Integer limit);

    /**
     * 获取面试完成率统计
     *
     * @param jobId 岗位ID（可选）
     * @return 完成率统计
     */
    Map<String, Double> getCompletionRates(Long jobId);

    /**
     * 获取平均分数统计
     *
     * @param jobId 岗位ID（可选）
     * @return 平均分数统计
     */
    Map<String, Double> getAverageScores(Long jobId);

    /**
     * 记录面试事件
     *
     * @param sessionId 会话ID
     * @param eventType 事件类型
     * @param eventData 事件数据
     */
    void recordInterviewEvent(String sessionId, String eventType, Map<String, Object> eventData);

    /**
     * 清理过期统计数据
     *
     * @param beforeTime 清理时间点之前的数据
     * @return 清理的记录数
     */
    Integer cleanExpiredStats(LocalDateTime beforeTime);
}
