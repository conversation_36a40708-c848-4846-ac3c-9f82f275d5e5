package org.dromara.app.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.Job;
import org.dromara.app.domain.JobCategory;
import org.dromara.app.domain.JobFavorite;
import org.dromara.app.domain.bo.JobQueryBo;
import org.dromara.app.domain.vo.JobVo;
import org.dromara.app.mapper.JobCategoryMapper;
import org.dromara.app.mapper.JobFavoriteMapper;
import org.dromara.app.mapper.JobMapper;
import org.dromara.app.service.IJobService;
import org.dromara.common.core.utils.MapstructUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 岗位信息Service业务层处理
 *
 * <AUTHOR> Assistant
 * @date 2025-07-19
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class JobServiceImpl implements IJobService {

    private final JobMapper jobMapper;
    private final JobCategoryMapper jobCategoryMapper;
    private final JobFavoriteMapper jobFavoriteMapper;

    @Override
    public Page<JobVo> selectJobPageWithFavorite(Page<JobVo> page, JobQueryBo queryBo) {
        Page<JobVo> jobVoPage = jobMapper.selectJobPageWithFavorite(page, queryBo);
        return jobVoPage;
    }

    @Override
    public List<JobVo> selectByTechnicalDomain(String technicalDomain, String level, Integer limit) {
        LambdaQueryWrapper<Job> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Job::getTechnicalDomain, technicalDomain)
               .eq(Job::getStatus, "0")
               .eq(Job::getDelFlag, "0");

        if (level != null && !level.isEmpty()) {
            wrapper.eq(Job::getLevel, level);
        }

        wrapper.orderByDesc(Job::getViewCount, Job::getFavoriteCount);

        if (limit != null && limit > 0) {
            wrapper.last("LIMIT " + limit);
        }

        List<Job> jobs = jobMapper.selectList(wrapper);
        List<JobVo> jobVos = MapstructUtils.convert(jobs, JobVo.class);

        // 填充分类信息
        fillCategoryInfo(jobVos);

        return jobVos;
    }

    @Override
    public List<JobVo> selectRecommendedJobs(Integer limit, Long userId) {
        return jobMapper.selectRecommendedJobs(limit, userId);
    }

    @Override
    public List<JobVo> selectHotJobs(Integer limit, Long userId) {
        return jobMapper.selectHotJobs(limit, userId);
    }

    @Override
    public JobVo selectJobDetail(Long jobId, Long userId) {
        Job job = jobMapper.selectById(jobId);
        if (job == null) {
            return null;
        }

        JobVo jobVo = MapstructUtils.convert(job, JobVo.class);

        // 填充分类信息
        if (job.getCategoryId() != null) {
            JobCategory category = jobCategoryMapper.selectById(job.getCategoryId());
            if (category != null) {
                jobVo.setCategoryName(category.getName());
                jobVo.setCategoryIcon(category.getIcon());
                jobVo.setCategoryColor(category.getColor());
            }
        }

        // 检查用户是否收藏
        if (userId != null) {
            LambdaQueryWrapper<JobFavorite> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(JobFavorite::getUserId, userId)
                   .eq(JobFavorite::getJobId, jobId);
            JobFavorite favorite = jobFavoriteMapper.selectOne(wrapper);
            jobVo.setIsFavorited(favorite != null);
        }

        return jobVo;
    }

    @Override
    public boolean incrementViewCount(Long jobId) {
        try {
            int result = jobMapper.incrementViewCount(jobId);
            return result > 0;
        } catch (Exception e) {
            log.error("增加浏览次数失败，jobId: {}", jobId, e);
            return false;
        }
    }

    @Override
    public boolean updateFavoriteCount(Long jobId, Integer increment) {
        try {
            int result = jobMapper.updateFavoriteCount(jobId, increment);
            return result > 0;
        } catch (Exception e) {
            log.error("更新收藏次数失败，jobId: {}, increment: {}", jobId, increment, e);
            return false;
        }
    }

    @Override
    public List<TechnicalDomainStats> getTechnicalDomainStats() {
        // 这里需要在JobMapper中添加相应的查询方法
        // 暂时返回空列表，实际实现需要添加SQL查询
        return List.of();
    }

    /**
     * 填充分类信息
     */
    private void fillCategoryInfo(List<JobVo> jobVos) {
        if (jobVos.isEmpty()) {
            return;
        }

        // 获取所有分类ID
        List<Long> categoryIds = jobVos.stream()
            .map(JobVo::getCategoryId)
            .filter(id -> id != null)
            .distinct()
            .collect(Collectors.toList());

        if (categoryIds.isEmpty()) {
            return;
        }

        // 批量查询分类信息
        List<JobCategory> categories = jobCategoryMapper.selectBatchIds(categoryIds);
        Map<Long, JobCategory> categoryMap = categories.stream()
            .collect(Collectors.toMap(JobCategory::getId, category -> category));

        // 填充分类信息
        jobVos.forEach(jobVo -> {
            if (jobVo.getCategoryId() != null) {
                JobCategory category = categoryMap.get(jobVo.getCategoryId());
                if (category != null) {
                    jobVo.setCategoryName(category.getName());
                    jobVo.setCategoryIcon(category.getIcon());
                    jobVo.setCategoryColor(category.getColor());
                }
            }
        });
    }

}
