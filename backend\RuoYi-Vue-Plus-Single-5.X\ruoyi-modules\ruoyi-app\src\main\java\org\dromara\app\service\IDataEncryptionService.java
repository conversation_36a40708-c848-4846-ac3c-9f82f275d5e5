package org.dromara.app.service;

import org.dromara.app.domain.vo.EncryptionStatusVo;

import java.util.Map;

/**
 * 数据加密服务接口
 * 用于敏感数据的加密存储和解密访问
 *
 * <AUTHOR>
 * @date 2025-01-19
 */
public interface IDataEncryptionService {

    /**
     * 加密字符串
     *
     * @param plainText 明文
     * @return 密文
     */
    String encrypt(String plainText);

    /**
     * 解密字符串
     *
     * @param cipherText 密文
     * @return 明文
     */
    String decrypt(String cipherText);

    /**
     * 加密字节数组
     *
     * @param plainBytes 明文字节数组
     * @return 密文字节数组
     */
    byte[] encrypt(byte[] plainBytes);

    /**
     * 解密字节数组
     *
     * @param cipherBytes 密文字节数组
     * @return 明文字节数组
     */
    byte[] decrypt(byte[] cipherBytes);

    /**
     * 批量加密
     *
     * @param plainTexts 明文列表
     * @return 密文列表
     */
    Map<String, String> batchEncrypt(Map<String, String> plainTexts);

    /**
     * 批量解密
     *
     * @param cipherTexts 密文列表
     * @return 明文列表
     */
    Map<String, String> batchDecrypt(Map<String, String> cipherTexts);

    /**
     * 生成数据摘要（哈希）
     *
     * @param data 原始数据
     * @return 数据摘要
     */
    String generateHash(String data);

    /**
     * 验证数据摘要
     *
     * @param data 原始数据
     * @param hash 数据摘要
     * @return 是否匹配
     */
    boolean verifyHash(String data, String hash);

    /**
     * 生成随机密钥
     *
     * @param keyLength 密钥长度
     * @return 密钥
     */
    String generateRandomKey(int keyLength);

    /**
     * 加密文件
     *
     * @param inputFilePath 输入文件路径
     * @param outputFilePath 输出文件路径
     * @return 是否成功
     */
    boolean encryptFile(String inputFilePath, String outputFilePath);

    /**
     * 解密文件
     *
     * @param inputFilePath 输入文件路径
     * @param outputFilePath 输出文件路径
     * @return 是否成功
     */
    boolean decryptFile(String inputFilePath, String outputFilePath);

    /**
     * 获取加密状态
     *
     * @return 加密状态信息
     */
    EncryptionStatusVo getEncryptionStatus();

    /**
     * 轮换加密密钥
     *
     * @return 是否成功
     */
    boolean rotateEncryptionKey();

    /**
     * 验证加密完整性
     *
     * @param data 数据
     * @return 是否完整
     */
    boolean verifyEncryptionIntegrity(String data);

    /**
     * 设置加密算法
     *
     * @param algorithm 算法名称
     */
    void setEncryptionAlgorithm(String algorithm);

    /**
     * 获取支持的加密算法列表
     *
     * @return 算法列表
     */
    java.util.List<String> getSupportedAlgorithms();
}