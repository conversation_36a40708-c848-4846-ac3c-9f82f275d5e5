{"doc": "\n mybatis-plus配置类(下方注释有插件介绍)\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "dataPermissionInterceptor", "paramTypes": [], "doc": "\n 数据权限拦截器\r\n"}, {"name": "dataPermissionAspect", "paramTypes": [], "doc": "\n 数据权限切面处理器\r\n"}, {"name": "paginationInnerInterceptor", "paramTypes": [], "doc": "\n 分页插件，自动识别数据库类型\r\n"}, {"name": "optimisticLockerInnerInterceptor", "paramTypes": [], "doc": "\n 乐观锁插件\r\n"}, {"name": "metaObjectHandler", "paramTypes": [], "doc": "\n 元对象字段填充控制器\r\n"}, {"name": "idGenerator", "paramTypes": [], "doc": "\n 使用网卡信息绑定雪花生成器\r\n 防止集群雪花ID重复\r\n"}, {"name": "mybatisExceptionHandler", "paramTypes": [], "doc": "\n 异常处理器\r\n"}, {"name": "postInitTableInfoHandler", "paramTypes": [], "doc": "\n 初始化表对象处理器\r\n"}], "constructors": []}