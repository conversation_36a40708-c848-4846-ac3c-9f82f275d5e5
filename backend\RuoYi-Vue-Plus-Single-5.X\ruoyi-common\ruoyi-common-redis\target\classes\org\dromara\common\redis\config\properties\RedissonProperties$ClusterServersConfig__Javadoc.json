{"doc": "", "fields": [{"name": "clientName", "doc": "\n 客户端名称\r\n"}, {"name": "masterConnectionMinimumIdleSize", "doc": "\n master最小空闲连接数\r\n"}, {"name": "masterConnectionPoolSize", "doc": "\n master连接池大小\r\n"}, {"name": "slaveConnectionMinimumIdleSize", "doc": "\n slave最小空闲连接数\r\n"}, {"name": "slaveConnectionPoolSize", "doc": "\n slave连接池大小\r\n"}, {"name": "idleConnectionTimeout", "doc": "\n 连接空闲超时，单位：毫秒\r\n"}, {"name": "timeout", "doc": "\n 命令等待超时，单位：毫秒\r\n"}, {"name": "subscriptionConnectionPoolSize", "doc": "\n 发布和订阅连接池大小\r\n"}, {"name": "readMode", "doc": "\n 读取模式\r\n"}, {"name": "subscriptionMode", "doc": "\n 订阅模式\r\n"}], "enumConstants": [], "methods": [], "constructors": []}