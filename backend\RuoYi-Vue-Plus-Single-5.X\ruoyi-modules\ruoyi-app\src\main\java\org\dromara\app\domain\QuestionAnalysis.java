package org.dromara.app.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 问题分析对象 app_question_analysis
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@Data
@TableName("app_question_analysis")
public class QuestionAnalysis implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 分析ID
     */
    @TableId(value = "id")
    private String id;

    /**
     * 结果ID
     */
    private String resultId;

    /**
     * 问题ID
     */
    private String questionId;

    /**
     * 问题内容
     */
    private String question;

    /**
     * 问题分类
     */
    private String category;

    /**
     * 难度等级（1-5）
     */
    private Integer difficulty;

    /**
     * 用户答案
     */
    private String answer;

    /**
     * 得分
     */
    private Integer score;

    /**
     * 音频得分
     */
    private Integer audioScore;

    /**
     * 视频得分
     */
    private Integer videoScore;

    /**
     * 文本得分
     */
    private Integer textScore;

    /**
     * 反馈
     */
    private String feedback;

    /**
     * 优势点（JSON数组）
     */
    private List<String> strengths;

    /**
     * 劣势点（JSON数组）
     */
    private List<String> weaknesses;

    /**
     * 关键词匹配（JSON数组）
     */
    private List<String> keywordMatches;

    /**
     * 理想答案
     */
    private String idealAnswer;

    /**
     * 用时（秒）
     */
    private Integer timeSpent;

    /**
     * 时间限制（秒）
     */
    private Integer timeLimit;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

}
