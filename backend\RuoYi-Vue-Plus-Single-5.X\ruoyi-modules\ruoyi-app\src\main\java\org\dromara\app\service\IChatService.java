package org.dromara.app.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.dromara.app.domain.ChatMessage;
import org.dromara.app.domain.ChatSession;
import org.dromara.app.domain.dto.ChatRequestDto;
import org.dromara.app.domain.dto.ChatResponseDto;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.Map;

/**
 * 聊天服务接口
 *
 * <AUTHOR>
 */
public interface IChatService {

    /**
     * 发送消息（同步响应）
     *
     * @param request 聊天请求
     * @param userId  用户ID
     * @return 聊天响应
     */
    ChatResponseDto sendMessage(ChatRequestDto request, Long userId);

    /**
     * 发送消息（流式响应）
     *
     * @param request 聊天请求
     * @param userId  用户ID
     * @return SSE流对象
     */
    SseEmitter sendMessageStream(ChatRequestDto request, Long userId);

    /**
     * 创建新会话
     *
     * @param userId    用户ID
     * @param agentType 代理类型
     * @param title     会话标题（可选）
     * @return 会话信息
     */
    ChatSession createSession(Long userId, String agentType, String title);

    /**
     * 获取用户会话列表
     *
     * @param userId   用户ID
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @return 会话分页结果
     */
    Page<ChatSession> getUserSessions(Long userId, Integer pageNum, Integer pageSize);

    /**
     * 获取会话详情（包含消息列表）
     *
     * @param sessionId 会话ID
     * @param userId    用户ID
     * @return 会话详情
     */
    ChatSession getSessionDetail(String sessionId, Long userId);

    /**
     * 获取会话消息列表
     *
     * @param sessionId 会话ID
     * @param userId    用户ID
     * @param pageNum   页码
     * @param pageSize  每页大小
     * @return 消息分页结果
     */
    Page<ChatMessage> getSessionMessages(String sessionId, Long userId, Integer pageNum, Integer pageSize);

    /**
     * 删除会话
     *
     * @param sessionId 会话ID
     * @param userId    用户ID
     * @return 是否成功
     */
    boolean deleteSession(String sessionId, Long userId);

    /**
     * 清空会话消息
     *
     * @param sessionId 会话ID
     * @param userId    用户ID
     * @return 是否成功
     */
    boolean clearSessionMessages(String sessionId, Long userId);

    /**
     * 更新会话标题
     *
     * @param sessionId 会话ID
     * @param userId    用户ID
     * @param title     新标题
     * @return 是否成功
     */
    boolean updateSessionTitle(String sessionId, Long userId, String title);

    /**
     * 归档/取消归档会话
     *
     * @param sessionId 会话ID
     * @param userId    用户ID
     * @param archived  是否归档
     * @return 是否成功
     */
    boolean archiveSession(String sessionId, Long userId, boolean archived);

    /**
     * 获取用户聊天统计信息
     *
     * @param userId 用户ID
     * @return 统计信息
     */
    Map<String, Object> getUserChatStats(Long userId);

    /**
     * 检查会话权限
     *
     * @param sessionId 会话ID
     * @param userId    用户ID
     * @return 是否有权限
     */
    boolean checkSessionPermission(String sessionId, Long userId);

    /**
     * 生成会话标题（基于首条消息）
     *
     * @param firstMessage 首条消息内容
     * @return 生成的标题
     */
    String generateSessionTitle(String firstMessage);
}
