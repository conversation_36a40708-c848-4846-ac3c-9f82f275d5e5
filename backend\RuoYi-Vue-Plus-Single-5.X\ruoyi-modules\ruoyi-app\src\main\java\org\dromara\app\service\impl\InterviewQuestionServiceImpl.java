package org.dromara.app.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.InterviewQuestion;
import org.dromara.app.mapper.InterviewQuestionMapper;
import org.dromara.app.service.IInterviewQuestionService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 面试问题Service业务层处理
 *
 * <AUTHOR> Assistant
 * @date 2025-07-19
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class InterviewQuestionServiceImpl implements IInterviewQuestionService {

    private final InterviewQuestionMapper interviewQuestionMapper;

    @Override
    public List<InterviewQuestion> selectByJobId(Long jobId, Integer difficulty, Integer limit) {
        return interviewQuestionMapper.selectByJobId(jobId, difficulty, limit);
    }

    @Override
    public List<InterviewQuestion> selectByTechnicalDomain(String technicalDomain, String questionType, Integer limit) {
        return interviewQuestionMapper.selectByTechnicalDomain(technicalDomain, questionType, limit);
    }

    @Override
    public List<InterviewQuestion> selectMultimodalQuestions(Long jobId, Integer limit) {
        return interviewQuestionMapper.selectMultimodalQuestions(jobId, limit);
    }

    @Override
    public List<InterviewQuestion> selectByTags(List<String> tags, Integer limit) {
        return interviewQuestionMapper.selectByTags(tags, limit);
    }

    @Override
    public IPage<InterviewQuestion> selectQuestionPage(Page<InterviewQuestion> page,
                                                     Long jobId,
                                                     String questionType,
                                                     Integer difficulty,
                                                     String category) {
        return interviewQuestionMapper.selectQuestionPage(page, jobId, questionType, difficulty, category);
    }

    @Override
    public QuestionsByDifficulty getQuestionsByDifficulty(Long jobId, Integer easyCount, Integer mediumCount, Integer hardCount) {
        QuestionsByDifficulty result = new QuestionsByDifficulty();
        
        try {
            // 查询简单题
            if (easyCount != null && easyCount > 0) {
                List<InterviewQuestion> easyQuestions = interviewQuestionMapper.selectByJobId(jobId, 1, easyCount);
                if (easyQuestions.size() < easyCount) {
                    // 如果岗位专属题目不够，补充通用题目
                    List<InterviewQuestion> generalEasyQuestions = interviewQuestionMapper.selectByJobId(null, 1, easyCount - easyQuestions.size());
                    easyQuestions.addAll(generalEasyQuestions);
                }
                result.setEasyQuestions(easyQuestions);
            } else {
                result.setEasyQuestions(new ArrayList<>());
            }
            
            // 查询中等题
            if (mediumCount != null && mediumCount > 0) {
                List<InterviewQuestion> mediumQuestions = interviewQuestionMapper.selectByJobId(jobId, 3, mediumCount);
                if (mediumQuestions.size() < mediumCount) {
                    List<InterviewQuestion> generalMediumQuestions = interviewQuestionMapper.selectByJobId(null, 3, mediumCount - mediumQuestions.size());
                    mediumQuestions.addAll(generalMediumQuestions);
                }
                result.setMediumQuestions(mediumQuestions);
            } else {
                result.setMediumQuestions(new ArrayList<>());
            }
            
            // 查询困难题
            if (hardCount != null && hardCount > 0) {
                List<InterviewQuestion> hardQuestions = interviewQuestionMapper.selectByJobId(jobId, 5, hardCount);
                if (hardQuestions.size() < hardCount) {
                    List<InterviewQuestion> generalHardQuestions = interviewQuestionMapper.selectByJobId(null, 5, hardCount - hardQuestions.size());
                    hardQuestions.addAll(generalHardQuestions);
                }
                result.setHardQuestions(hardQuestions);
            } else {
                result.setHardQuestions(new ArrayList<>());
            }
            
        } catch (Exception e) {
            log.error("获取分级问题失败，jobId: {}", jobId, e);
            result.setEasyQuestions(new ArrayList<>());
            result.setMediumQuestions(new ArrayList<>());
            result.setHardQuestions(new ArrayList<>());
        }
        
        return result;
    }

}