{"doc": "\n 缓存组名称常量\r\n <p>\r\n key 格式为 cacheNames#ttl#maxIdleTime#maxSize\r\n <p>\r\n ttl 过期时间 如果设置为0则不过期 默认为0\r\n maxIdleTime 最大空闲时间 根据LRU算法清理空闲数据 如果设置为0则不检测 默认为0\r\n maxSize 组最大长度 根据LRU算法清理溢出数据 如果设置为0则无限长 默认为0\r\n <p>\r\n 例子: test#60s、test#0#60s、test#0#1m#1000、test#1h#0#500\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "DEMO_CACHE", "doc": "\n 演示案例\r\n"}, {"name": "SYS_CONFIG", "doc": "\n 系统配置\r\n"}, {"name": "SYS_DICT", "doc": "\n 数据字典\r\n"}, {"name": "SYS_DICT_TYPE", "doc": "\n 数据字典类型\r\n"}, {"name": "SYS_CLIENT", "doc": "\n 客户端\r\n"}, {"name": "SYS_USER_NAME", "doc": "\n 用户账户\r\n"}, {"name": "SYS_NICKNAME", "doc": "\n 用户名称\r\n"}, {"name": "SYS_DEPT", "doc": "\n 部门\r\n"}, {"name": "SYS_OSS", "doc": "\n OSS内容\r\n"}, {"name": "SYS_ROLE_CUSTOM", "doc": "\n 角色自定义权限\r\n"}, {"name": "SYS_DEPT_AND_CHILD", "doc": "\n 部门及以下权限\r\n"}, {"name": "SYS_OSS_CONFIG", "doc": "\n OSS配置\r\n"}, {"name": "ONLINE_TOKEN", "doc": "\n 在线用户\r\n"}, {"name": "SYS_TENANT", "doc": "\n 租户\r\n"}], "enumConstants": [], "methods": [], "constructors": []}