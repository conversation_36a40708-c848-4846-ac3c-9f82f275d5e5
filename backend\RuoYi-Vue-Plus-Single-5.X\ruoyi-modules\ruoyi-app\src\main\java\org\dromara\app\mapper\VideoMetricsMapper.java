package org.dromara.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.dromara.app.domain.VideoMetrics;

import java.util.List;

/**
 * 视频指标Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@Mapper
public interface VideoMetricsMapper extends BaseMapper<VideoMetrics> {

    /**
     * 根据结果ID查询视频指标
     *
     * @param resultId 结果ID
     * @return 视频指标
     */
    VideoMetrics selectByResultId(@Param("resultId") String resultId);

    /**
     * 根据结果ID删除视频指标
     *
     * @param resultId 结果ID
     * @return 删除数量
     */
    int deleteByResultId(@Param("resultId") String resultId);

    /**
     * 查询用户视频指标历史记录
     *
     * @param userId 用户ID
     * @param limit  限制数量
     * @return 视频指标列表
     */
    List<VideoMetrics> selectHistoryByUserId(@Param("userId") Long userId, @Param("limit") Integer limit);

    /**
     * 查询视频指标平均值
     *
     * @return 视频指标平均值
     */
    VideoMetrics selectAverageMetrics();

    /**
     * 查询用户视频指标平均值
     *
     * @param userId 用户ID
     * @return 用户视频指标平均值
     */
    VideoMetrics selectAverageMetricsByUserId(@Param("userId") Long userId);

}
