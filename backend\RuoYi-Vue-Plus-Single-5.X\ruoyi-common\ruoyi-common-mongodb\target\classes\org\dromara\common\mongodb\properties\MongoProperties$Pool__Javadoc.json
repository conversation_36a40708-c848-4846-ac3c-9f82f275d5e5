{"doc": "\n 连接池配置\r\n", "fields": [{"name": "maxSize", "doc": "\n 最大连接数\r\n"}, {"name": "minSize", "doc": "\n 最小连接数\r\n"}, {"name": "maxConnectionTimeoutMs", "doc": "\n 连接超时时间(毫秒)\r\n"}, {"name": "maxReadTimeoutMs", "doc": "\n 读取超时时间(毫秒)\r\n"}, {"name": "maxWaitTimeMs", "doc": "\n 最大等待时间(毫秒)\r\n"}, {"name": "maxConnectionIdleTimeMs", "doc": "\n 连接最大空闲时间(毫秒)\r\n"}, {"name": "maxConnectionLifeTimeMs", "doc": "\n 连接最大生存时间(毫秒)\r\n"}], "enumConstants": [], "methods": [], "constructors": []}