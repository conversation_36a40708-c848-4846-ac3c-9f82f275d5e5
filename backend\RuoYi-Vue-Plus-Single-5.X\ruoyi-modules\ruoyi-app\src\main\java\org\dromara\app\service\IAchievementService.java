package org.dromara.app.service;

import org.dromara.app.domain.Achievement;
import org.dromara.app.domain.dto.TrackEventDto;
import org.dromara.app.domain.vo.AchievementStatsVo;
import org.dromara.app.domain.vo.AchievementVo;
import org.dromara.app.domain.vo.BadgeVo;
import org.dromara.app.domain.vo.UserAchievementVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.List;
import java.util.Map;

/**
 * 成就系统服务接口
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
public interface IAchievementService {

    /**
     * 查询成就列表
     *
     * @param pageQuery 分页查询条件
     * @return 成就列表
     */
    TableDataInfo<AchievementVo> queryPageList(PageQuery pageQuery);

    /**
     * 查询激活的成就列表
     *
     * @return 激活的成就列表
     */
    List<AchievementVo> queryActiveAchievements();

    /**
     * 根据成就类型查询成就列表
     *
     * @param achievementType 成就类型
     * @return 成就列表
     */
    List<AchievementVo> queryByAchievementType(String achievementType);

    /**
     * 根据成就代码查询成就
     *
     * @param achievementCode 成就代码
     * @return 成就信息
     */
    Achievement queryByAchievementCode(String achievementCode);

    /**
     * 检查用户成就
     *
     * @param userId        用户ID
     * @param trackEventDto 用户行为事件
     */
    void checkAchievements(Long userId, TrackEventDto trackEventDto);

    /**
     * 获取用户徽章列表
     *
     * @param userId   用户ID
     * @param category 徽章类别（可选）
     * @param unlocked 是否解锁（可选）
     * @param rarity   稀有度（可选）
     * @return 徽章列表
     */
    List<BadgeVo> getUserBadges(String userId, String category, Boolean unlocked, String rarity);

    /**
     * 获取徽章详情
     *
     * @param userId  用户ID
     * @param badgeId 徽章ID
     * @return 徽章详情
     */
    BadgeVo getBadgeDetail(String userId, String badgeId);

    /**
     * 设置徽章置顶状态
     *
     * @param userId   用户ID
     * @param badgeId  徽章ID
     * @param isPinned 是否置顶
     */
    void setPinStatus(String userId, String badgeId, Boolean isPinned);

    /**
     * 获取置顶徽章列表
     *
     * @param userId 用户ID
     * @return 置顶徽章列表
     */
    List<BadgeVo> getPinnedBadges(String userId);

    /**
     * 获取成就统计信息
     *
     * @param userId 用户ID
     * @return 成就统计信息
     */
    AchievementStatsVo getAchievementStats(String userId);

    List<UserAchievementVo> getUserAchievements(String userId);

    /**
     * 获取最近解锁的成就
     *
     * @param userId 用户ID
     * @param limit  数量限制
     * @return 最近解锁的成就
     */
    List<UserAchievementVo> getRecentAchievements(String userId, Integer limit);

    /**
     * 获取成就分类列表
     *
     * @return 成就分类列表（键为类别代码，值为类别名称）
     */
    Map<String, String> getCategories();

    /**
     * 获取进行中的成就
     *
     * @param userId 用户ID
     * @return 进行中的成就
     */
    List<UserAchievementVo> getInProgressAchievements(String userId);

    /**
     * 获取用户成就详情
     *
     * @param userId        用户ID
     * @param achievementId 成就ID
     * @return 用户成就详情
     */
    UserAchievementVo getUserAchievementDetail(String userId, String achievementId);

    /**
     * 检查并更新用户成就进度
     *
     * @param userId 用户ID
     * @return 新解锁的成就列表
     */
    List<AchievementVo> checkAndUpdateAchievements(String userId);

    /**
     * 分享成就墙
     *
     * @param userId   用户ID
     * @param platform 平台（wechat, qq, weibo, link）
     * @return 分享结果
     */
    Map<String, Object> shareAchievements(String userId, String platform);

    /**
     * 记录用户行为事件
     *
     * @param userId      用户ID
     * @param eventType   事件类型
     * @param eventData   事件数据
     * @param eventValue  事件值
     * @param relatedId   关联对象ID
     * @param relatedType 关联对象类型
     * @return 是否触发了新成就
     */
    boolean recordEvent(String userId, String eventType, Map<String, Object> eventData,
                        Integer eventValue, String relatedId, String relatedType);

    /**
     * 解锁特定成就
     *
     * @param userId        用户ID
     * @param achievementId 成就ID
     * @param source        解锁来源
     * @return 是否解锁成功
     */
    boolean unlockAchievement(String userId, String achievementId, String source);

    /**
     * 获取推荐接下来完成的成就
     *
     * @param userId 用户ID
     * @param limit  数量限制
     * @return 推荐成就列表
     */
    List<UserAchievementVo> getRecommendedAchievements(String userId, Integer limit);

    /**
     * 获取成就排行榜
     *
     * @param category 类别（可选）
     * @param limit    数量限制
     * @return 排行榜列表
     */
    List<AchievementStatsVo.LeaderboardEntry> getLeaderboard(String category, Integer limit);

    /**
     * 获取用户在排行榜中的位置
     *
     * @param userId   用户ID
     * @param category 类别（可选）
     * @return 排行榜信息
     */
    AchievementStatsVo.LeaderboardInfo getUserRankingInfo(String userId, String category);

    /**
     * 处理未处理的事件
     *
     * @param maxEvents 最大处理事件数
     * @return 处理的事件数量
     */
    int processUnhandledEvents(int maxEvents);

    /**
     * 更新用户成就进度
     *
     * @param userId        用户ID
     * @param achievementId 成就ID
     * @param progress      进度值
     * @param eventData     事件数据
     * @return 是否触发了成就解锁
     */
    boolean updateAchievementProgress(String userId, String achievementId, Integer progress, Map<String, Object> eventData);

    /**
     * 批量初始化用户成就
     *
     * @param userId 用户ID
     * @return 初始化的成就数量
     */
    int initializeUserAchievements(String userId);

    /**
     * 获取成就事件统计
     *
     * @param userId    用户ID
     * @param eventType 事件类型（可选）
     * @param days      统计天数
     * @return 事件统计信息
     */
    Map<String, Object> getEventStatistics(String userId, String eventType, Integer days);

    /**
     * 清理过期事件
     *
     * @param days 保留天数
     * @return 清理的事件数量
     */
    int cleanupExpiredEvents(int days);

    /**
     * 获取用户成就完成度
     *
     * @param userId 用户ID
     * @return 完成度信息
     */
    Map<String, Object> getUserAchievementCompletion(String userId);

    /**
     * 重新计算用户成就进度
     *
     * @param userId 用户ID
     * @return 重新计算的成就数量
     */
    int recalculateUserProgress(String userId);

    /**
     * 获取最近解锁的成就
     *
     * @param userId 用户ID
     * @param limit  数量限制
     * @return 最近解锁的成就列表
     */
    List<UserAchievementVo> getRecentUserAchievements(String userId, Integer limit);
}
