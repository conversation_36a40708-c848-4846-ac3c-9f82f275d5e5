package org.dromara.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.Achievement;
import org.dromara.app.domain.UserAchievement;
import org.dromara.app.domain.bo.AchievementBo;
import org.dromara.app.domain.dto.TrackEventDto;
import org.dromara.app.domain.vo.AchievementVo;
import org.dromara.app.engine.AchievementRuleEngine;
import org.dromara.app.mapper.AchievementMapper;
import org.dromara.app.mapper.UserAchievementMapper;
import org.dromara.app.service.IAchievementManageService;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 成就管理Service业务层处理
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AchievementManageServiceImpl implements IAchievementManageService {

    private final AchievementMapper achievementMapper;
    private final UserAchievementMapper userAchievementMapper;
    private final AchievementRuleEngine achievementRuleEngine;

    @Override
    public AchievementVo queryById(Long id) {
        return achievementMapper.selectVoById(id);
    }

    @Override
    public TableDataInfo<AchievementVo> queryPageList(PageQuery pageQuery) {
        Page<Achievement> achievementPage = achievementMapper.selectPage(new Page<>(pageQuery.getPageNum(), pageQuery.getPageSize()),
            new LambdaQueryWrapper<>());
        return TableDataInfo.build(MapstructUtils.convert(achievementPage.getRecords(), AchievementVo.class));
    }

    @Override
    public List<AchievementVo> queryList(AchievementBo achievementBo) {
        LambdaQueryWrapper<Achievement> lqw = buildQueryWrapper(achievementBo);
        return achievementMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<Achievement> buildQueryWrapper(AchievementBo bo) {
        LambdaQueryWrapper<Achievement> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getAchievementCode()), Achievement::getAchievementCode, bo.getAchievementCode());
        lqw.like(StringUtils.isNotBlank(bo.getAchievementName()), Achievement::getAchievementName, bo.getAchievementName());
        lqw.eq(StringUtils.isNotBlank(bo.getAchievementType()), Achievement::getAchievementType, bo.getAchievementType());
        lqw.eq(StringUtils.isNotBlank(bo.getIsActive()), Achievement::getIsActive, bo.getIsActive());
        lqw.orderByAsc(Achievement::getSortOrder);
        lqw.orderByDesc(Achievement::getCreateTime);
        return lqw;
    }

    @Override
    @Transactional
    public Boolean insertByBo(AchievementBo achievementBo) {
        Achievement achievement = MapstructUtils.convert(achievementBo, Achievement.class);

        // 验证成就代码唯一性
        if (achievement != null && achievementMapper.selectByAchievementCode(achievement.getAchievementCode()) != null) {
            throw new RuntimeException("成就代码已存在: " + achievement.getAchievementCode());
        }

        // 验证触发条件JSON格式
        validateTriggerCondition(achievement.getTriggerCondition());

        // 设置默认值
        if (achievement.getIsActive() == null) {
            achievement.setIsActive("1");
        }
        if (achievement.getSortOrder() == null) {
            achievement.setSortOrder(0);
        }
        if (achievement.getRewardPoints() == null) {
            achievement.setRewardPoints(0);
        }

        boolean result = achievementMapper.insert(achievement) > 0;

        if (result) {
            log.info("新增成就成功: {}", achievement.getAchievementCode());
        }

        return result;
    }

    @Override
    @Transactional
    public Boolean updateByBo(AchievementBo achievementBo) {
        Achievement achievement = MapstructUtils.convert(achievementBo, Achievement.class);

        // 验证成就是否存在
        Achievement existing = achievementMapper.selectById(achievement.getId());
        if (existing == null) {
            throw new RuntimeException("成就不存在: " + achievement.getId());
        }

        // 如果修改了成就代码，验证唯一性
        if (!existing.getAchievementCode().equals(achievement.getAchievementCode())) {
            Achievement codeCheck = achievementMapper.selectByAchievementCode(achievement.getAchievementCode());
            if (codeCheck != null && !codeCheck.getId().equals(achievement.getId())) {
                throw new RuntimeException("成就代码已存在: " + achievement.getAchievementCode());
            }
        }

        // 验证触发条件JSON格式
        validateTriggerCondition(achievement.getTriggerCondition());

        boolean result = achievementMapper.updateById(achievement) > 0;

        if (result) {
            log.info("修改成就成功: {}", achievement.getAchievementCode());
        }

        return result;
    }

    @Override
    @Transactional
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 检查是否有用户已获得这些成就
            for (Long id : ids) {
                List<UserAchievement> userAchievements = userAchievementMapper.selectList(
                    Wrappers.<UserAchievement>lambdaQuery()
                        .eq(UserAchievement::getAchievementId, id)
                        .eq(UserAchievement::getIsCompleted, "1")
                );

                if (!userAchievements.isEmpty()) {
                    Achievement achievement = achievementMapper.selectById(id);
                    throw new RuntimeException("成就已被用户获得，无法删除: " +
                        (achievement != null ? achievement.getAchievementName() : id));
                }
            }
        }

        // 删除成就
        boolean result = achievementMapper.deleteBatchIds(ids) > 0;

        if (result) {
            // 删除相关的用户成就记录
            userAchievementMapper.delete(
                Wrappers.<UserAchievement>lambdaQuery()
                    .in(UserAchievement::getAchievementId, ids)
            );

            log.info("删除成就成功: {}", ids);
        }

        return result;
    }

    @Override
    @Transactional
    public Boolean batchUpdateStatus(List<Long> ids, String isActive) {
        return achievementMapper.batchUpdateStatus(ids, isActive) > 0;
    }

    @Override
    @Transactional
    public Boolean copyAchievement(Long id) {
        Achievement original = achievementMapper.selectById(id);
        if (original == null) {
            throw new RuntimeException("原成就不存在: " + id);
        }

        // 创建副本
        Achievement copy = new Achievement();
        BeanUtil.copyProperties(original, copy);
        copy.setId(null);
        copy.setAchievementCode(original.getAchievementCode() + "_copy_" + System.currentTimeMillis());
        copy.setAchievementName(original.getAchievementName() + " (副本)");
        copy.setIsActive("0"); // 副本默认为未激活状态
        copy.setCreateTime(null);
        copy.setUpdateTime(null);

        boolean result = achievementMapper.insert(copy) > 0;

        if (result) {
            log.info("复制成就成功: {} -> {}", original.getAchievementCode(), copy.getAchievementCode());
        }

        return result;
    }

    @Override
    public Map<String, Object> previewTriggerCondition(String triggerCondition, Long userId) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 解析触发条件
            Map<String, Object> condition = JSONUtil.toBean(triggerCondition, Map.class);

            result.put("valid", true);
            result.put("condition", condition);
            result.put("behaviorType", condition.get("behaviorType"));

            // 如果提供了用户ID，计算当前进度
            if (userId != null) {
                // 这里需要根据条件计算用户当前的进度
                // 由于没有完整的Achievement对象，我们只能做基本的预览
                result.put("userId", userId);
                result.put("preview", "条件解析成功，可以正常使用");
            }

        } catch (Exception e) {
            result.put("valid", false);
            result.put("error", "触发条件格式错误: " + e.getMessage());
        }

        return result;
    }

    @Override
    public Map<String, Object> testAchievementRule(Long id, Long userId, String eventType, Object eventData) {
        Map<String, Object> result = new HashMap<>();

        try {
            Achievement achievement = achievementMapper.selectById(id);
            if (achievement == null) {
                result.put("success", false);
                result.put("error", "成就不存在");
                return result;
            }

            // 创建测试事件
            TrackEventDto testEvent = new TrackEventDto();
            testEvent.setUserId(userId);
            testEvent.setEventType(eventType);
            testEvent.setEventData((Map<String, Object>) eventData);
            testEvent.setTimestamp(System.currentTimeMillis());

            // 测试规则
            boolean matches = achievementRuleEngine.checkAchievementCondition(userId, achievement, testEvent);
            AchievementRuleEngine.AchievementProgress progress = achievementRuleEngine.calculateProgress(userId, achievement, testEvent);

            result.put("success", true);
            result.put("matches", matches);
            result.put("currentValue", progress.getCurrentValue());
            result.put("targetValue", progress.getTargetValue());
            result.put("progressPercentage", progress.getProgressPercentage());
            result.put("isCompleted", progress.isCompleted());

        } catch (Exception e) {
            result.put("success", false);
            result.put("error", "测试失败: " + e.getMessage());
        }

        return result;
    }

    @Override
    public Map<String, Object> getAchievementStats() {
        Map<String, Object> stats = new HashMap<>();

        // 总成就数
        int totalAchievements = achievementMapper.countTotalAchievements();
        stats.put("totalAchievements", totalAchievements);

        // 激活成就数
        long activeAchievements = achievementMapper.selectCount(
            Wrappers.<Achievement>lambdaQuery().eq(Achievement::getIsActive, "1"));
        stats.put("activeAchievements", activeAchievements);

        // 各类型成就统计
        Map<String, Integer> typeStats = new HashMap<>();
        for (String type : Arrays.asList("LOGIN", "LEARNING", "SOCIAL", "TIME", "CUSTOM")) {
            int count = achievementMapper.countAchievementsByType(type);
            typeStats.put(type, count);
        }
        stats.put("typeStats", typeStats);

        // 总奖励积分
        int totalRewardPoints = achievementMapper.sumTotalRewardPoints();
        stats.put("totalRewardPoints", totalRewardPoints);

        return stats;
    }

    @Override
    @Transactional
    public String importAchievement(List<AchievementBo> achievementList, Boolean isUpdateSupport, String operName) {
        if (achievementList == null || achievementList.isEmpty()) {
            throw new RuntimeException("导入成就数据不能为空！");
        }

        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        for (AchievementBo achievementBo : achievementList) {
            try {
                // 验证必填字段
                if (StringUtils.isBlank(achievementBo.getAchievementCode())) {
                    failureNum++;
                    failureMsg.append("<br/>").append(failureNum).append("、成就代码不能为空");
                    continue;
                }

                // 检查是否存在
                Achievement existing = achievementMapper.selectByAchievementCode(achievementBo.getAchievementCode());

                if (existing == null) {
                    // 新增
                    insertByBo(achievementBo);
                    successNum++;
                    successMsg.append("<br/>").append(successNum).append("、成就 ").append(achievementBo.getAchievementName()).append(" 导入成功");
                } else if (isUpdateSupport) {
                    // 更新
                    achievementBo.setId(existing.getId());
                    updateByBo(achievementBo);
                    successNum++;
                    successMsg.append("<br/>").append(successNum).append("、成就 ").append(achievementBo.getAchievementName()).append(" 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>").append(failureNum).append("、成就 ").append(achievementBo.getAchievementName()).append(" 已存在");
                }

            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、成就 " + achievementBo.getAchievementName() + " 导入失败：";
                failureMsg.append(msg).append(e.getMessage());
                log.error("导入成就失败", e);
            }
        }

        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new RuntimeException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }

        return successMsg.toString();
    }

    @Override
    public List<AchievementVo> exportAchievement(List<AchievementVo> list) {
        // 可以在这里对导出数据进行处理，比如数据转换、格式化等
        return list;
    }

    /**
     * 验证触发条件JSON格式
     */
    private void validateTriggerCondition(String triggerCondition) {
        try {
            Map<String, Object> condition = JSONUtil.toBean(triggerCondition, Map.class);

            // 验证必填字段
            if (!condition.containsKey("behaviorType")) {
                throw new RuntimeException("触发条件必须包含 behaviorType 字段");
            }

            String behaviorType = (String) condition.get("behaviorType");
            if (StringUtils.isBlank(behaviorType)) {
                throw new RuntimeException("behaviorType 不能为空");
            }

            // 根据不同的行为类型验证相应的字段
            switch (behaviorType) {
                case "LOGIN":
                    if (!condition.containsKey("count") && !condition.containsKey("consecutiveDays")) {
                        throw new RuntimeException("LOGIN 类型必须包含 count 或 consecutiveDays 字段");
                    }
                    break;
                case "VIDEO_WATCH":
                case "COMMENT":
                case "LIKE":
                    if (!condition.containsKey("count")) {
                        throw new RuntimeException(behaviorType + " 类型必须包含 count 字段");
                    }
                    break;
                case "STUDY_TIME":
                    if (!condition.containsKey("totalMinutes")) {
                        throw new RuntimeException("STUDY_TIME 类型必须包含 totalMinutes 字段");
                    }
                    break;
            }

        } catch (Exception e) {
            throw new RuntimeException("触发条件JSON格式错误: " + e.getMessage());
        }
    }

}
