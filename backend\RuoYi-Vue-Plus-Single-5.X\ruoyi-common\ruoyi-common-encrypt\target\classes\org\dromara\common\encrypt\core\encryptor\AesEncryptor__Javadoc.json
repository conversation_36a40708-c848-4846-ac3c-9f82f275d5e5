{"doc": "\n AES算法实现\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "algorithm", "paramTypes": [], "doc": "\n 获得当前算法\r\n"}, {"name": "encrypt", "paramTypes": ["java.lang.String", "org.dromara.common.encrypt.enumd.EncodeType"], "doc": "\n 加密\r\n\r\n @param value      待加密字符串\r\n @param encodeType 加密后的编码格式\r\n"}, {"name": "decrypt", "paramTypes": ["java.lang.String"], "doc": "\n 解密\r\n\r\n @param value 待加密字符串\r\n"}], "constructors": []}