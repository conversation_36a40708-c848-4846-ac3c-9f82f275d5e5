{"doc": "\n Caffeine缓存客户端\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "get", "paramTypes": ["java.lang.String"], "doc": "\n 获取缓存值\r\n\r\n @param key 缓存键\r\n @return 缓存值\r\n"}, {"name": "get", "paramTypes": ["java.lang.String", "java.util.function.Function"], "doc": "\n 获取缓存值，如果不存在则使用loader加载\r\n\r\n @param key    缓存键\r\n @param loader 加载器\r\n @return 缓存值\r\n"}, {"name": "put", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": "\n 设置缓存值\r\n\r\n @param key   缓存键\r\n @param value 缓存值\r\n"}, {"name": "put", "paramTypes": ["java.lang.String", "java.lang.Object", "long"], "doc": "\n 设置带过期时间的缓存值\r\n\r\n @param key        缓存键\r\n @param value      缓存值\r\n @param ttlSeconds 过期时间（秒）\r\n"}, {"name": "putAll", "paramTypes": ["java.util.Map"], "doc": "\n 批量设置缓存\r\n\r\n @param map 缓存键值对\r\n"}, {"name": "evict", "paramTypes": ["java.lang.String"], "doc": "\n 删除缓存\r\n\r\n @param key 缓存键\r\n"}, {"name": "evictAll", "paramTypes": ["java.util.Collection"], "doc": "\n 批量删除缓存\r\n\r\n @param keys 缓存键集合\r\n"}, {"name": "clear", "paramTypes": [], "doc": "\n 清空所有缓存\r\n"}, {"name": "<PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.String"], "doc": "\n 检查缓存是否存在\r\n\r\n @param key 缓存键\r\n @return 是否存在\r\n"}, {"name": "size", "paramTypes": [], "doc": "\n 获取缓存大小\r\n\r\n @return 缓存大小\r\n"}, {"name": "keys", "paramTypes": [], "doc": "\n 获取所有缓存键\r\n\r\n @return 缓存键集合\r\n"}, {"name": "stats", "paramTypes": [], "doc": "\n 获取缓存统计信息\r\n\r\n @return 统计信息\r\n"}, {"name": "cleanUp", "paramTypes": [], "doc": "\n 执行缓存清理\r\n"}, {"name": "getTtl", "paramTypes": ["java.lang.String"], "doc": "\n 获取缓存剩余过期时间\r\n\r\n @param key 缓存键\r\n @return 剩余秒数，-1表示永不过期，0表示已过期或不存在\r\n"}, {"name": "expire", "paramTypes": ["java.lang.String", "long"], "doc": "\n 设置缓存过期时间\r\n\r\n @param key        缓存键\r\n @param ttlSeconds 过期时间（秒）\r\n @return 是否设置成功\r\n"}, {"name": "getNativeCache", "paramTypes": [], "doc": "\n 获取原始缓存对象\r\n\r\n @return 原始缓存\r\n"}], "constructors": []}