package org.dromara.app.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.app.domain.BookReadingRecord;

import java.util.List;
import java.util.Map;

/**
 * 书籍阅读记录Service接口
 *
 * <AUTHOR>
 */
public interface IBookReadingRecordService extends IService<BookReadingRecord> {

    /**
     * 根据用户ID和书籍ID查询阅读记录
     *
     * @param userId 用户ID
     * @param bookId 书籍ID
     * @return 阅读记录
     */
    BookReadingRecord queryByUserIdAndBookId(Long userId, Long bookId);

    /**
     * 查询用户的阅读历史（分页）
     *
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @param userId   用户ID
     * @return 阅读记录分页
     */
    Page<BookReadingRecord> queryUserReadingHistory(Integer pageNum, Integer pageSize, Long userId);

    /**
     * 查询用户最近阅读的书籍
     *
     * @param userId 用户ID
     * @param limit  限制数量
     * @return 阅读记录列表
     */
    List<BookReadingRecord> queryRecentReading(Long userId, Integer limit);

    /**
     * 统计用户阅读数据
     *
     * @param userId 用户ID
     * @return 阅读统计数据
     */
    Map<String, Object> queryUserReadingStats(Long userId);

    /**
     * 保存或更新阅读记录
     *
     * @param userId              用户ID
     * @param bookId              书籍ID
     * @param currentChapterId    当前章节ID
     * @param currentChapterIndex 当前章节索引
     * @param readingProgress     阅读进度
     * @param readingSettings     阅读设置
     * @return 是否成功
     */
    boolean saveOrUpdateReadingRecord(Long userId, Long bookId, String currentChapterId,
                                      Integer currentChapterIndex, Integer readingProgress,
                                      Map<String, Object> readingSettings);

    /**
     * 标记章节已完成
     *
     * @param userId    用户ID
     * @param bookId    书籍ID
     * @param chapterId 章节ID
     * @return 是否成功
     */
    boolean markChapterCompleted(Long userId, Long bookId, String chapterId);

    /**
     * 标记书籍已读完
     *
     * @param userId 用户ID
     * @param bookId 书籍ID
     * @return 是否成功
     */
    boolean markBookFinished(Long userId, Long bookId);

    /**
     * 增加阅读时长
     *
     * @param userId      用户ID
     * @param bookId      书籍ID
     * @param readingTime 阅读时长（分钟）
     * @return 是否成功
     */
    boolean addReadingTime(Long userId, Long bookId, Integer readingTime);

    /**
     * 删除阅读记录
     *
     * @param userId 用户ID
     * @param bookId 书籍ID
     * @return 是否成功
     */
    boolean deleteReadingRecord(Long userId, Long bookId);
}
