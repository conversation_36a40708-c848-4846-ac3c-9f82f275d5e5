<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.app.mapper.BookMapper">

    <resultMap type="org.dromara.app.domain.Book" id="BookResult">
        <id property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="author" column="author"/>
        <result property="cover" column="cover"/>
        <result property="category" column="category"/>
        <result property="rating" column="rating"/>
        <result property="readCount" column="read_count"/>
        <result property="chapters" column="chapters"/>
        <result property="pages" column="pages"/>
        <result property="isCompleted" column="is_completed"/>
        <result property="tags" column="tags"/>
        <result property="description" column="description"/>
        <result property="difficulty" column="difficulty"/>
        <result property="price" column="price"/>
        <result property="isFree" column="is_free"/>
        <result property="status" column="status"/>
        <result property="sortOrder" column="sort_order"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="readingProgress" column="reading_progress"/>
        <result property="isPurchased" column="is_purchased"/>
    </resultMap>

    <!-- 分页查询书籍列表（支持分类筛选和搜索） -->
    <select id="selectBookPageWithUserInfo" resultMap="BookResult">
        SELECT
        b.*,
        COALESCE(r.reading_progress, 0) as reading_progress,
        CASE
        WHEN b.is_free = 1 THEN 1
        ELSE 0
        END as is_purchased
        FROM app_book b
        LEFT JOIN app_book_reading_record r ON b.id = r.book_id AND r.user_id = #{userId}
        WHERE b.del_flag = '0' AND b.status = 1
        <if test="category != null and category != '' and category != 'all'">
            AND b.category = #{category}
        </if>
        <if test="searchQuery != null and searchQuery != ''">
            AND (
            b.title LIKE CONCAT('%', #{searchQuery}, '%')
            OR b.author LIKE CONCAT('%', #{searchQuery}, '%')
            OR b.description LIKE CONCAT('%', #{searchQuery}, '%')
            OR b.tags LIKE CONCAT('%', #{searchQuery}, '%')
            )
        </if>
        ORDER BY b.sort_order DESC, b.rating DESC, b.read_count DESC, b.create_time DESC
    </select>

    <!-- 根据ID查询书籍详情（包含用户阅读信息） -->
    <select id="selectBookByIdWithUserInfo" resultMap="BookResult">
        SELECT b.*,
               COALESCE(r.reading_progress, 0) as reading_progress,
               CASE
                   WHEN b.is_free = 1 THEN 1
                   ELSE 0
                   END                         as is_purchased
        FROM app_book b
                 LEFT JOIN app_book_reading_record r ON b.id = r.book_id AND r.user_id = #{userId}
        WHERE b.id = #{id}
          AND b.del_flag = '0'
          AND b.status = 1
    </select>

    <!-- 增加书籍阅读次数 -->
    <update id="incrementReadCount">
        UPDATE app_book
        SET read_count  = read_count + 1,
            update_time = NOW()
        WHERE id = #{bookId}
          AND del_flag = '0'
    </update>

    <!-- 查询热门书籍列表 -->
    <select id="selectHotBooks" resultMap="BookResult">
        SELECT *
        FROM app_book
        WHERE del_flag = '0'
          AND status = 1
        ORDER BY read_count DESC, rating DESC, create_time DESC
        LIMIT #{limit}
    </select>

    <!-- 查询推荐书籍列表（基于用户阅读历史） -->
    <select id="selectRecommendedBooks" resultMap="BookResult">
        SELECT DISTINCT b.*
        FROM app_book b
                 LEFT JOIN app_book_reading_record r ON b.id = r.book_id
        WHERE b.del_flag = '0'
          AND b.status = 1
          AND (
            b.category IN (SELECT DISTINCT b2.category
                           FROM app_book b2
                                    INNER JOIN app_book_reading_record r2 ON b2.id = r2.book_id
                           WHERE r2.user_id = #{userId}
                           LIMIT 3)
                OR b.read_count > (SELECT AVG(read_count)
                                   FROM app_book
                                   WHERE del_flag = '0'
                                     AND status = 1)
            )
          AND b.id NOT IN (SELECT book_id
                           FROM app_book_reading_record
                           WHERE user_id = #{userId})
        ORDER BY b.rating DESC, b.read_count DESC
        LIMIT #{limit}
    </select>

    <!-- 根据分类查询书籍数量统计 -->
    <select id="selectCategoryStats" resultMap="BookResult">
        SELECT category,
               COUNT(*) as read_count
        FROM app_book
        WHERE del_flag = '0'
          AND status = 1
        GROUP BY category
        ORDER BY read_count DESC
    </select>

</mapper>
