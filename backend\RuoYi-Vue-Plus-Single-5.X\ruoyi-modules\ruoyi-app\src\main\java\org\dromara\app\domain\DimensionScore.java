package org.dromara.app.domain;

import lombok.Data;

import java.util.List;

/**
 * 维度评分实体
 *
 * <AUTHOR>
 */
@Data
public class DimensionScore {

    /**
     * 维度名称
     */
    private String dimension;

    /**
     * 得分
     */
    private Integer score;

    /**
     * 最大分数
     */
    private Integer maxScore;

    /**
     * 百分位
     */
    private Integer percentile;

    /**
     * 描述
     */
    private String description;

    /**
     * 优势
     */
    private List<String> strengths;

    /**
     * 劣势
     */
    private List<String> weaknesses;

    /**
     * 建议
     */
    private List<String> recommendations;

    /**
     * 权重
     */
    private Double weight;

    /**
     * 等级
     */
    private String level;

    /**
     * 详细分析
     */
    private String detailedAnalysis;
}