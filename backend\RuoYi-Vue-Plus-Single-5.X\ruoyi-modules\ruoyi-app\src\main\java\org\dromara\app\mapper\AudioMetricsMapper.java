package org.dromara.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.dromara.app.domain.AudioMetrics;

import java.util.List;

/**
 * 音频指标Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@Mapper
public interface AudioMetricsMapper extends BaseMapper<AudioMetrics> {

    /**
     * 根据结果ID查询音频指标
     *
     * @param resultId 结果ID
     * @return 音频指标
     */
    AudioMetrics selectByResultId(@Param("resultId") String resultId);

    /**
     * 根据结果ID删除音频指标
     *
     * @param resultId 结果ID
     * @return 删除数量
     */
    int deleteByResultId(@Param("resultId") String resultId);

    /**
     * 查询用户音频指标历史记录
     *
     * @param userId 用户ID
     * @param limit  限制数量
     * @return 音频指标列表
     */
    List<AudioMetrics> selectHistoryByUserId(@Param("userId") Long userId, @Param("limit") Integer limit);

    /**
     * 查询音频指标平均值
     *
     * @return 音频指标平均值
     */
    AudioMetrics selectAverageMetrics();

    /**
     * 查询用户音频指标平均值
     *
     * @param userId 用户ID
     * @return 用户音频指标平均值
     */
    AudioMetrics selectAverageMetricsByUserId(@Param("userId") Long userId);

}
