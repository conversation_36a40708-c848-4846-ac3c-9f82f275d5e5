package org.dromara.app.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.AiTool;
import org.dromara.app.domain.ToolCall;
import org.dromara.app.domain.enums.ErrorCode;
import org.dromara.app.exception.BaseBusinessException;
import org.dromara.app.mapper.AiToolMapper;
import org.dromara.app.mapper.ToolCallMapper;
import org.dromara.app.service.IToolCallService;
import org.dromara.app.service.tool.ToolExecutor;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 工具调用服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ToolCallServiceImpl implements IToolCallService {

    private final AiToolMapper aiToolMapper;
    private final ToolCallMapper toolCallMapper;
    private final ApplicationContext applicationContext;

    /**
     * 工具执行器注册表
     */
    private final Map<String, ToolExecutor> toolExecutors = new ConcurrentHashMap<>();

    /**
     * 初始化方法，自动注册所有工具执行器
     */
    @PostConstruct
    public void initToolExecutors() {
        try {
            // 从Spring容器中获取所有ToolExecutor实现
            Map<String, ToolExecutor> executorBeans = applicationContext.getBeansOfType(ToolExecutor.class);

            for (ToolExecutor executor : executorBeans.values()) {
                registerToolExecutor(executor);
            }
        } catch (Exception e) {
            log.error("初始化工具执行器失败", e);
        }
    }

    /**
     * 注册工具执行器
     */
    public void registerToolExecutor(ToolExecutor executor) {
        if (executor == null) {
            return;
        }

        String toolName = executor.getToolName();
        if (toolName == null || toolName.trim().isEmpty()) {
            return;
        }

        toolExecutors.put(toolName, executor);
    }

    /**
     * 获取已注册的工具执行器列表
     */
    public Map<String, ToolExecutor> getRegisteredExecutors() {
        return new HashMap<>(toolExecutors);
    }

    /**
     * 获取工具执行器
     */
    public ToolExecutor getToolExecutor(String toolName) {
        return toolExecutors.get(toolName);
    }

    /**
     * 检查工具执行器是否已注册
     */
    public boolean isExecutorRegistered(String toolName) {
        return toolExecutors.containsKey(toolName);
    }

    @Override
    public List<AiTool> getAvailableTools(Long userId) {
        try {
            // 从数据库获取启用的工具
            List<AiTool> tools = aiToolMapper.selectEnabledTools();

            // 过滤用户有权限的工具
            return tools.stream()
                .filter(tool -> hasToolPermission(tool.getId(), userId))
                .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("获取可用工具列表失败: userId={}", userId, e);
            throw new BaseBusinessException(ErrorCode.SYSTEM_ERROR, "获取工具列表失败");
        }
    }

    @Override
    public AiTool getToolById(String toolId) {
        if (StrUtil.isBlank(toolId)) {
            throw new BaseBusinessException(ErrorCode.PARAM_ERROR, "工具ID不能为空");
        }

        AiTool tool = aiToolMapper.selectById(toolId);
        if (tool == null) {
            throw new BaseBusinessException(ErrorCode.TOOL_NOT_FOUND);
        }

        return tool;
    }

    @Override
    public AiTool getToolByName(String toolName) {
        if (StrUtil.isBlank(toolName)) {
            throw new BaseBusinessException(ErrorCode.PARAM_ERROR, "工具名称不能为空");
        }

        AiTool tool = aiToolMapper.selectByName(toolName);
        if (tool == null) {
            throw new BaseBusinessException(ErrorCode.TOOL_NOT_FOUND);
        }

        return tool;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ToolCall.ToolCallResult executeToolCall(String toolId, Map<String, Object> parameters,
                                                   Long userId, String sessionId, String messageId) {
        // 验证工具和权限
        AiTool tool = getToolById(toolId);
        if (!tool.getEnabled()) {
            throw new BaseBusinessException(ErrorCode.TOOL_DISABLED);
        }

        if (!hasToolPermission(toolId, userId)) {
            throw new BaseBusinessException(ErrorCode.TOOL_PERMISSION_DENIED);
        }

        // 验证参数
        ParameterValidationResult validation = validateParameters(toolId, parameters);
        if (!validation.isValid()) {
            throw new BaseBusinessException(ErrorCode.PARAM_ERROR, validation.getErrorMessage());
        }

        // 创建调用记录
        ToolCall toolCall = createToolCallRecord(tool, parameters, userId, sessionId, messageId);

        try {
            // 执行工具
            ToolCall.ToolCallResult result = executeToolInternal(tool, parameters, userId, sessionId, messageId);

            // 更新调用记录
            updateToolCallRecord(toolCall, result, null);

            return result;

        } catch (Exception e) {
            log.error("工具执行失败: toolId={}, userId={}", toolId, userId, e);

            // 更新调用记录
            updateToolCallRecord(toolCall, null, e.getMessage());

            throw new BaseBusinessException(ErrorCode.TOOL_EXECUTION_FAILED, e.getMessage());
        }
    }

    @Override
    @Async
    public String executeToolCallAsync(String toolId, Map<String, Object> parameters,
                                       Long userId, String sessionId, String messageId) {
        String callId = IdUtil.fastSimpleUUID();

        CompletableFuture.runAsync(() -> {
            try {
                executeToolCall(toolId, parameters, userId, sessionId, messageId);
            } catch (Exception e) {
                log.error("异步工具执行失败: toolId={}, callId={}", toolId, callId, e);
            }
        });

        return callId;
    }

    @Override
    public ToolCall getToolCallRecord(String callId) {
        if (StrUtil.isBlank(callId)) {
            throw new BaseBusinessException(ErrorCode.PARAM_ERROR, "调用记录ID不能为空");
        }

        ToolCall toolCall = toolCallMapper.selectById(callId);
        if (toolCall == null) {
            throw new BaseBusinessException(ErrorCode.NOT_FOUND, "调用记录不存在");
        }

        return toolCall;
    }

    @Override
    public List<ToolCall> getUserToolCallHistory(Long userId, Integer pageNum, Integer pageSize) {
        if (pageNum == null || pageNum < 1) pageNum = 1;
        if (pageSize == null || pageSize < 1) pageSize = 20;

        return toolCallMapper.selectUserHistory(userId, (pageNum - 1) * pageSize, pageSize);
    }

    @Override
    public List<ToolCall> getSessionToolCalls(String sessionId) {
        if (StrUtil.isBlank(sessionId)) {
            return Collections.emptyList();
        }

        return toolCallMapper.selectBySessionId(sessionId);
    }

    @Override
    public boolean hasToolPermission(String toolId, Long userId) {
        try {
            AiTool tool = getToolById(toolId);

            // 检查权限级别
            Integer permissionLevel = tool.getPermissionLevel();
            if (permissionLevel == null || permissionLevel == 0) {
                // 公开工具，所有人都可以使用
                return true;
            } else if (permissionLevel == 1) {
                // 登录用户可以使用
                return userId != null;
            } else if (permissionLevel == 2) {
                // 需要特定权限 - 这里可以根据具体业务需求实现权限检查
                // 例如：检查用户角色、权限码等
                if (userId == null) {
                    return false;
                }

                // 可以在这里添加具体的权限检查逻辑
                // 例如：检查用户是否有特定的权限码
                // return permissionService.hasPermission(userId, tool.getRequiredPermissions());

                // 暂时返回true，表示登录用户都有权限
                return true;
            }

            return false;
        } catch (Exception e) {
            log.warn("检查工具权限失败: toolId={}, userId={}", toolId, userId, e);
            return false;
        }
    }

    @Override
    public ParameterValidationResult validateParameters(String toolId, Map<String, Object> parameters) {
        try {
            AiTool tool = getToolById(toolId);

            // 获取工具执行器
            ToolExecutor executor = toolExecutors.get(tool.getName());
            if (executor != null) {
                ToolExecutor.ValidationResult result = executor.validateParameters(parameters);
                return new ParameterValidationResult(
                    result.isValid(),
                    result.getErrorMessage(),
                    result.getFieldErrors()
                );
            }

            // 如果没有执行器，使用基本验证
            return ParameterValidationResult.success();

        } catch (Exception e) {
            log.error("参数验证失败: toolId={}", toolId, e);
            return ParameterValidationResult.failure("参数验证失败: " + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getToolFunctionDefinitions(List<String> toolIds) {
        List<Map<String, Object>> definitions = new ArrayList<>();

        for (String toolId : toolIds) {
            try {
                AiTool tool = getToolById(toolId);
                if (tool.getEnabled()) {
                    Map<String, Object> definition = new HashMap<>();
                    definition.put("name", tool.getName());
                    definition.put("description", tool.getDescription());

                    // 解析参数Schema
                    if (StrUtil.isNotBlank(tool.getParameterSchema())) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> parameters = JSONUtil.toBean(tool.getParameterSchema(), Map.class);
                        definition.put("parameters", parameters);
                    }

                    definitions.add(definition);
                }
            } catch (Exception e) {
                log.warn("获取工具定义失败: toolId={}", toolId, e);
            }
        }

        return definitions;
    }

    @Override
    public List<ToolCall.ToolCallResult> processAiToolCalls(List<Map<String, Object>> toolCalls,
                                                            Long userId, String sessionId, String messageId) {
        List<ToolCall.ToolCallResult> results = new ArrayList<>();

        for (Map<String, Object> toolCall : toolCalls) {
            try {
                String toolName = (String) toolCall.get("name");
                Map<String, Object> parameters = (Map<String, Object>) toolCall.get("parameters");

                // 根据工具名称获取工具
                AiTool tool = getToolByName(toolName);
                ToolCall.ToolCallResult result = executeToolCall(tool.getId(), parameters, userId, sessionId, messageId);
                results.add(result);

            } catch (Exception e) {
                log.error("处理AI工具调用失败", e);
                ToolCall.ToolCallResult errorResult = ToolCall.ToolCallResult.builder()
                    .data(null)
                    .success(false)
                    .message("处理AI工具调用失败: " + e.getMessage())
                    .type("error")
                    .metadata(null)
                    .build();
                results.add(errorResult);
            }
        }

        return results;
    }

    private ToolCall.ToolCallResult executeToolInternal(AiTool tool, Map<String, Object> parameters,
                                                        Long userId, String sessionId, String messageId) {
        String toolName = tool.getName();

        ToolExecutor executor = toolExecutors.get(toolName);
        if (executor == null) {
            return ToolCall.ToolCallResult.builder()
                .success(false)
                .data(null)
                .message("未找到工具执行器: " + toolName)
                .type("text")
                .metadata(null)
                .build();
        }

        try {
            // 创建执行上下文
            Map<String, Object> metadata = new HashMap<>();
            metadata.put("toolId", tool.getId());
            metadata.put("toolName", toolName);
            metadata.put("executionTime", System.currentTimeMillis());

            ToolExecutor.ExecutionContext context = new ToolExecutor.ExecutionContext(
                userId, sessionId, messageId, metadata
            );

            // 执行工具
            ToolCall.ToolCallResult result = executor.execute(parameters, context);

            return result;

        } catch (Exception e) {
            log.error("工具执行异常: toolName={}, error={}", toolName, e.getMessage(), e);
            return ToolCall.ToolCallResult.builder()
                .success(false)
                .data(null)
                .message("工具执行异常: " + e.getMessage())
                .type("text")
                .metadata(null)
                .build();
        }
    }

    private ToolCall createToolCallRecord(AiTool tool, Map<String, Object> parameters,
                                          Long userId, String sessionId, String messageId) {
        ToolCall toolCall = new ToolCall();
        toolCall.setId(IdUtil.fastSimpleUUID());
        toolCall.setSessionId(sessionId);
        toolCall.setMessageId(messageId);
        toolCall.setUserId(userId);
        toolCall.setToolId(tool.getId());
        toolCall.setToolName(tool.getName());
        toolCall.setParameters(JSONUtil.toJsonStr(parameters));
        toolCall.setStatus(0); // 调用中
        toolCall.setStartTime(System.currentTimeMillis());
        toolCall.setRetryCount(0);
        toolCall.setSource("user");
        // 注意：BaseEntity 的 createTime 字段类型是 Date，不是 LocalDateTime
        // toolCall.setCreateTime(LocalDateTime.now());

        toolCallMapper.insert(toolCall);
        return toolCall;
    }

    private void updateToolCallRecord(ToolCall toolCall, ToolCall.ToolCallResult result, String errorMessage) {
        long endTime = System.currentTimeMillis();
        toolCall.setEndTime(endTime);
        toolCall.setExecutionTime(endTime - toolCall.getStartTime());

        if (result != null && result.isSuccess()) {
            toolCall.setStatus(1); // 成功
            toolCall.setResult(JSONUtil.toJsonStr(result));
        } else {
            toolCall.setStatus(2); // 失败
            toolCall.setErrorMessage(errorMessage);
        }

        // 注意：BaseEntity 的 updateTime 字段类型是 Date，不是 LocalDateTime
        // toolCall.setUpdateTime(LocalDateTime.now());
        toolCallMapper.updateById(toolCall);
    }

    /**
     * 获取工具执行统计信息
     */
    public Map<String, Object> getToolExecutionStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("registeredExecutors", toolExecutors.size());
        stats.put("executorNames", new ArrayList<>(toolExecutors.keySet()));

        // 可以添加更多统计信息
        Map<String, String> executorInfo = new HashMap<>();
        for (Map.Entry<String, ToolExecutor> entry : toolExecutors.entrySet()) {
            ToolExecutor executor = entry.getValue();
            executorInfo.put(entry.getKey(), executor.getClass().getSimpleName());
        }
        stats.put("executorInfo", executorInfo);

        return stats;
    }

    /**
     * 重新加载工具执行器
     */
    public void reloadToolExecutors() {
        log.info("开始重新加载工具执行器");
        toolExecutors.clear();
        initToolExecutors();
        log.info("工具执行器重新加载完成");
    }
}
