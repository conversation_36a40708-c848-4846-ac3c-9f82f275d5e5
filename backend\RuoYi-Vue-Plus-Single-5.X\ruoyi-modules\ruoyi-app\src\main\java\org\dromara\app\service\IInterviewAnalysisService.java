package org.dromara.app.service;

import cn.hutool.json.JSONObject;

/**
 * 面试分析服务接口
 * 
 * <AUTHOR>
 */
public interface IInterviewAnalysisService {

    /**
     * 分析表情情绪
     * 
     * @param imageData base64编码的图像数据
     * @param questionId 问题ID
     * @return 情绪分析结果
     */
    JSONObject analyzeEmotion(String imageData, Integer questionId);

    /**
     * 分析语音内容
     * 
     * @param audioData base64编码的音频数据
     * @param questionId 问题ID
     * @return 语音分析结果
     */
    JSONObject analyzeSpeech(String audioData, Integer questionId);

    /**
     * 基于情绪分析生成智能建议
     * 
     * @param emotionResult 情绪分析结果
     * @param questionId 问题ID
     * @return 智能建议
     */
    JSONObject generateEmotionSuggestion(JSONObject emotionResult, Integer questionId);

    /**
     * 基于语音分析生成智能建议
     * 
     * @param speechResult 语音分析结果
     * @param questionId 问题ID
     * @return 智能建议
     */
    JSONObject generateSpeechSuggestion(JSONObject speechResult, Integer questionId);

    /**
     * 生成综合面试建议
     * 
     * @param context 面试上下文
     * @return 综合建议
     */
    JSONObject generateComprehensiveSuggestion(JSONObject context);
}
