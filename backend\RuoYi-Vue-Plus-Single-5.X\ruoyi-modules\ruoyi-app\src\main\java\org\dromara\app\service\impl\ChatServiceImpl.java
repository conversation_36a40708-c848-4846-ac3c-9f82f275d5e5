package org.dromara.app.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.Agent;
import org.dromara.app.domain.ChatMessage;
import org.dromara.app.domain.ChatSession;
import org.dromara.app.domain.dto.ChatRequestDto;
import org.dromara.app.domain.dto.ChatResponseDto;
import org.dromara.app.mapper.ChatMessageMapper;
import org.dromara.app.mapper.ChatSessionMapper;
import org.dromara.app.service.IAgentService;
import org.dromara.app.service.IChatService;
import org.dromara.app.service.IOllamaService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 聊天服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ChatServiceImpl implements IChatService {

    private final IOllamaService ollamaService;
    private final IAgentService agentService;
    private final ChatSessionMapper chatSessionMapper;
    private final ChatMessageMapper chatMessageMapper;

    @Override
    public ChatResponseDto sendMessage(ChatRequestDto request, Long userId) {
        try {
            // 获取或创建会话
            String sessionId = request.getSessionId();
            if (StrUtil.isBlank(sessionId)) {
                ChatSession session = createSession(userId, request.getAgentType(), null);
                sessionId = session.getId();
            }

            // 检查会话权限
            if (!checkSessionPermission(sessionId, userId)) {
                return ChatResponseDto.error("会话不存在或无权限访问");
            }

            // 保存用户消息
            ChatMessage userMessage = createUserMessage(sessionId, userId, request);
            saveMessage(userMessage);

            // 获取Agent配置
            Agent agent = agentService.getAgentByType(request.getAgentType());
            if (agent == null) {
                return ChatResponseDto.error("rjb-sias类型不存在");
            }

            // 构建对话上下文
            List<IOllamaService.ChatMessage> messages = buildChatContext(sessionId, agent, request.getContextLimit());

            // 调用Ollama
            String modelName = StrUtil.isBlank(request.getModelName()) ?
                agent.getModelConfigObject().getPrimaryModel() : request.getModelName();

            IOllamaService.OllamaResponse response = ollamaService.chat(
                modelName,
                messages,
                request.getTemperature(),
                null
            );

            if (response.isSuccess()) {
                // 保存AI回复
                ChatMessage assistantMessage = createAssistantMessage(sessionId, userId, response.getMessage(), response.getMetadata());
                saveMessage(assistantMessage);

                // 更新会话信息
                updateSessionAfterMessage(sessionId, response.getMessage());

                // 增加Agent使用次数
                agentService.incrementUsageCount(request.getAgentType());

                return ChatResponseDto.success(response.getMessage(), assistantMessage.getId(), sessionId);
            } else {
                return ChatResponseDto.error(response.getError());
            }

        } catch (Exception e) {
            log.error("发送消息失败", e);
            return ChatResponseDto.error("发送消息失败: " + e.getMessage());
        }
    }

    @Override
    public SseEmitter sendMessageStream(ChatRequestDto request, Long userId) {
        SseEmitter emitter = new SseEmitter(300000L); // 5分钟超时

        CompletableFuture.runAsync(() -> {
            try {
                // 获取或创建会话
                String sessionId = request.getSessionId();
                if (StrUtil.isBlank(sessionId)) {
                    ChatSession session = createSession(userId, request.getAgentType(), null);
                    sessionId = session.getId();

                    // 发送会话ID
                    emitter.send(SseEmitter.event()
                        .name("session")
                        .data(Map.of("sessionId", sessionId)));
                }

                // 检查会话权限
                if (!checkSessionPermission(sessionId, userId)) {
                    emitter.send(SseEmitter.event()
                        .name("error")
                        .data("会话不存在或无权限访问"));
                    emitter.complete();
                    return;
                }

                // 保存用户消息
                ChatMessage userMessage = createUserMessage(sessionId, userId, request);
                saveMessage(userMessage);

                // 发送用户消息确认
                emitter.send(SseEmitter.event()
                    .name("user_message")
                    .data(Map.of("messageId", userMessage.getId())));

                // 获取Agent配置
                Agent agent = agentService.getAgentByType(request.getAgentType());
                if (agent == null) {
                    emitter.send(SseEmitter.event()
                        .name("error")
                        .data("rjb-sias类型不存在"));
                    emitter.complete();
                    return;
                }

                // 构建对话上下文
                List<IOllamaService.ChatMessage> messages = buildChatContext(sessionId, agent, request.getContextLimit());

                // 创建临时的AI消息
                String assistantMessageId = IdUtil.fastSimpleUUID();
                StringBuilder fullResponse = new StringBuilder();

                // 发送开始事件
                emitter.send(SseEmitter.event()
                    .name("start")
                    .data(Map.of("messageId", assistantMessageId)));

                // 调用流式Ollama
                String modelName = StrUtil.isBlank(request.getModelName()) ?
                    agent.getModelConfigObject().getPrimaryModel() : request.getModelName();

                // 创建新的SseEmitter来处理Ollama流式响应
                SseEmitter ollamaEmitter = new SseEmitter();

                String finalSessionId = sessionId;
                ollamaEmitter.onCompletion(() -> {
                    try {
                        // 保存完整的AI回复
                        ChatMessage assistantMessage = createAssistantMessage(finalSessionId, userId, fullResponse.toString(), null);
                        assistantMessage.setId(assistantMessageId);
                        saveMessage(assistantMessage);

                        // 更新会话信息
                        updateSessionAfterMessage(finalSessionId, fullResponse.toString());

                        // 增加Agent使用次数
                        agentService.incrementUsageCount(request.getAgentType());

                        emitter.complete();
                    } catch (Exception e) {
                        log.error("完成流式响应时出错", e);
                        emitter.completeWithError(e);
                    }
                });

                ollamaEmitter.onError((ex) -> {
                    log.error("Ollama流式响应出错", ex);
                    try {
                        emitter.send(SseEmitter.event()
                            .name("error")
                            .data("AI响应失败: " + ex.getMessage()));
                    } catch (IOException e) {
                        log.error("发送错误事件失败", e);
                    }
                    emitter.completeWithError(ex);
                });

                // 代理Ollama的流式事件到前端
                ollamaEmitter.onTimeout(() -> {
                    try {
                        emitter.send(SseEmitter.event()
                            .name("error")
                            .data("响应超时"));
                        emitter.complete();
                    } catch (IOException e) {
                        log.error("发送超时事件失败", e);
                    }
                });

                // 重写流式处理逻辑，直接转发到前端
                ollamaService.chatStream(modelName, messages, request.getTemperature(), null,
                    createProxyEmitter(emitter, fullResponse));

            } catch (Exception e) {
                log.error("流式发送消息失败", e);
                try {
                    emitter.send(SseEmitter.event()
                        .name("error")
                        .data("发送失败: " + e.getMessage()));
                } catch (IOException ioException) {
                    log.error("发送错误事件失败", ioException);
                }
                emitter.completeWithError(e);
            }
        });

        return emitter;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ChatSession createSession(Long userId, String agentType, String title) {
        ChatSession session = new ChatSession();
        session.setId(IdUtil.fastSimpleUUID());
        session.setUserId(userId);
        session.setAgentType(StrUtil.isBlank(agentType) ? "general" : agentType);
        session.setTitle(StrUtil.isBlank(title) ? "新的对话" : title);
        session.setMessageCount(0);
        session.setLastActiveTime(System.currentTimeMillis());
        session.setStatus(1); // 活跃状态
        session.setCreateTime(DateTime.now());
        session.setUpdateTime(DateTime.now());

        // 保存到数据库
        chatSessionMapper.insert(session);

        log.info("创建新会话: {} for user: {}", session.getId(), userId);
        return session;
    }

    @Override
    public Page<ChatSession> getUserSessions(Long userId, Integer pageNum, Integer pageSize) {
        Page<ChatSession> page = new Page<>(pageNum, pageSize);
        return chatSessionMapper.selectUserSessions(page, userId, null);
    }

    @Override
    public ChatSession getSessionDetail(String sessionId, Long userId) {
        if (!checkSessionPermission(sessionId, userId)) {
            return null;
        }

        ChatSession session = chatSessionMapper.selectByIdAndUserId(sessionId, userId);
        if (session != null) {
            // 加载消息列表
            Page<ChatMessage> messagePage = new Page<>(1, 50);
            Page<ChatMessage> messages = chatMessageMapper.selectSessionMessages(messagePage, sessionId, userId);
            session.setMessages(messages.getRecords());
        }

        return session;
    }

    @Override
    public Page<ChatMessage> getSessionMessages(String sessionId, Long userId, Integer pageNum, Integer pageSize) {
        if (!checkSessionPermission(sessionId, userId)) {
            return new Page<>();
        }

        Page<ChatMessage> page = new Page<>(pageNum, pageSize);
        return chatMessageMapper.selectSessionMessages(page, sessionId, userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteSession(String sessionId, Long userId) {
        if (!checkSessionPermission(sessionId, userId)) {
            return false;
        }

        try {
            // 先删除会话下的所有消息
            chatMessageMapper.deleteSessionMessages(sessionId, userId);

            // 再删除会话
            int result = chatSessionMapper.deleteByIdAndUserId(sessionId, userId);

            log.info("删除会话: {} for user: {}", sessionId, userId);
            return result > 0;
        } catch (Exception e) {
            log.error("删除会话失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean clearSessionMessages(String sessionId, Long userId) {
        if (!checkSessionPermission(sessionId, userId)) {
            return false;
        }

        try {
            // 删除会话消息
            int deletedMessages = chatMessageMapper.deleteSessionMessages(sessionId, userId);

            // 更新会话统计
            chatSessionMapper.updateSessionStats(sessionId, 0, null, System.currentTimeMillis());

            log.info("清空会话消息: {} for user: {}, 删除消息数: {}", sessionId, userId, deletedMessages);
            return deletedMessages >= 0;
        } catch (Exception e) {
            log.error("清空会话消息失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSessionTitle(String sessionId, Long userId, String title) {
        if (!checkSessionPermission(sessionId, userId)) {
            return false;
        }

        try {
            int result = chatSessionMapper.updateSessionTitle(sessionId, userId, title);

            log.info("更新会话标题: {} -> {} for user: {}", sessionId, title, userId);
            return result > 0;
        } catch (Exception e) {
            log.error("更新会话标题失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean archiveSession(String sessionId, Long userId, boolean archived) {
        if (!checkSessionPermission(sessionId, userId)) {
            return false;
        }

        try {
            int result = chatSessionMapper.updateSessionStatus(sessionId, userId, archived ? 0 : 1);

            log.info("归档会话: {} -> {} for user: {}", sessionId, archived, userId);
            return result > 0;
        } catch (Exception e) {
            log.error("归档会话失败", e);
            return false;
        }
    }

    @Override
    public Map<String, Object> getUserChatStats(Long userId) {
        return chatSessionMapper.getUserChatStats(userId);
    }

    @Override
    public boolean checkSessionPermission(String sessionId, Long userId) {
        ChatSession session = chatSessionMapper.selectByIdAndUserId(sessionId, userId);
        return session != null;
    }

    @Override
    public String generateSessionTitle(String firstMessage) {
        if (StrUtil.isBlank(firstMessage)) {
            return "新的对话";
        }

        // 简单的标题生成逻辑
        String title = firstMessage.length() > 20 ? firstMessage.substring(0, 20) + "..." : firstMessage;
        return title.replaceAll("\n", " ").trim();
    }

    // =================  私有方法  =================

    private ChatMessage createUserMessage(String sessionId, Long userId, ChatRequestDto request) {
        ChatMessage message = new ChatMessage();
        message.setId(IdUtil.fastSimpleUUID());
        message.setSessionId(sessionId);
        message.setUserId(userId);
        message.setRole("user");
        message.setContent(request.getMessage());
        message.setMessageType(request.getMessageType());
        message.setStatus(1); // 发送成功
        message.setIsRead(true);
        message.setCreateTime(DateTime.now());
        message.setUpdateTime(DateTime.now());

        if (request.getAttachments() != null && !request.getAttachments().isEmpty()) {
            message.setAttachments(JSONUtil.toJsonStr(request.getAttachments()));
        }

        return message;
    }

    private ChatMessage createAssistantMessage(String sessionId, Long userId, String content, Map<String, Object> metadata) {
        ChatMessage message = new ChatMessage();
        message.setId(IdUtil.fastSimpleUUID());
        message.setSessionId(sessionId);
        message.setUserId(userId);
        message.setRole("assistant");
        message.setContent(content);
        message.setMessageType("text");
        message.setStatus(1); // 发送成功
        message.setIsRead(true);
        message.setCreateTime(DateTime.now());
        message.setUpdateTime(DateTime.now());

        if (metadata != null && !metadata.isEmpty()) {
            message.setMetadata(JSONUtil.toJsonStr(metadata));
        }

        return message;
    }

    private void saveMessage(ChatMessage message) {
        // 保存到数据库
        chatMessageMapper.insert(message);
    }

    private void updateSessionAfterMessage(String sessionId, String lastMessage) {
        // 获取会话信息以获取用户ID
        ChatSession session = chatSessionMapper.selectById(sessionId);
        if (session != null) {
            // 获取当前消息数量
            Integer messageCount = chatMessageMapper.countSessionMessages(sessionId, session.getUserId());

            // 更新会话统计
            String truncatedMessage = lastMessage.length() > 100 ? lastMessage.substring(0, 100) + "..." : lastMessage;
            chatSessionMapper.updateSessionStats(sessionId, messageCount, truncatedMessage, System.currentTimeMillis());
        }
    }

    private List<IOllamaService.ChatMessage> buildChatContext(String sessionId, Agent agent, Integer contextLimit) {
        List<IOllamaService.ChatMessage> messages = new ArrayList<>();

        // 添加系统提示词
        if (StrUtil.isNotBlank(agent.getSystemPrompt())) {
            messages.add(new IOllamaService.ChatMessage("system", agent.getSystemPrompt()));
        }

        // 获取会话信息以获取用户ID
        ChatSession session = chatSessionMapper.selectById(sessionId);
        if (session != null) {
            // 获取历史消息
            int limit = contextLimit != null ? contextLimit : 10;
            List<ChatMessage> history = chatMessageMapper.selectRecentMessages(sessionId, session.getUserId(), limit);

            // 按时间顺序排列（最早的在前）
            history.sort((a, b) -> a.getCreateTime().compareTo(b.getCreateTime()));

            for (ChatMessage msg : history) {
                messages.add(new IOllamaService.ChatMessage(msg.getRole(), msg.getContent()));
            }
        }

        return messages;
    }

    private SseEmitter createProxyEmitter(SseEmitter frontendEmitter, StringBuilder fullResponse) {
        SseEmitter proxyEmitter = new SseEmitter();

        // 代理完成事件到前端
        proxyEmitter.onCompletion(frontendEmitter::complete);

        // 代理错误事件到前端
        proxyEmitter.onError(frontendEmitter::completeWithError);

        // 这里需要重写SseEmitter的send方法来代理事件
        // 由于SseEmitter的限制，这里简化处理
        return proxyEmitter;
    }
}
