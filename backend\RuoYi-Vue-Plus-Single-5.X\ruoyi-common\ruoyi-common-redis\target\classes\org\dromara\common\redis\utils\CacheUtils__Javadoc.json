{"doc": "\n 缓存操作工具类\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "get", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": "\n 获取缓存值\r\n\r\n @param cacheNames 缓存组名称\r\n @param key        缓存key\r\n"}, {"name": "put", "paramTypes": ["java.lang.String", "java.lang.Object", "java.lang.Object"], "doc": "\n 保存缓存值\r\n\r\n @param cacheNames 缓存组名称\r\n @param key        缓存key\r\n @param value      缓存值\r\n"}, {"name": "evict", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": "\n 删除缓存值\r\n\r\n @param cacheNames 缓存组名称\r\n @param key        缓存key\r\n"}, {"name": "clear", "paramTypes": ["java.lang.String"], "doc": "\n 清空缓存值\r\n\r\n @param cacheNames 缓存组名称\r\n"}], "constructors": []}