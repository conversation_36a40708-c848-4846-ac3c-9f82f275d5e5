package org.dromara.app.utils;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import org.dromara.app.config.InterviewConfig;
import org.dromara.app.domain.vo.JobVo;

import java.time.LocalDateTime;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 面试系统工具类
 *
 * <AUTHOR> Assistant
 * @date 2025-01-17
 */
public class InterviewUtils {

    private static final Pattern KEYWORD_PATTERN = Pattern.compile("^[\\u4e00-\\u9fa5a-zA-Z0-9\\s]+$");

    /**
     * 生成会话ID
     *
     * @return 会话ID
     */
    public static String generateSessionId() {
        return "session_" + System.currentTimeMillis() + "_" + IdUtil.fastSimpleUUID();
    }

    /**
     * 生成会话令牌
     *
     * @param sessionId 会话ID
     * @return 会话令牌
     */
    public static String generateSessionToken(String sessionId) {
        return "token_" + sessionId;
    }

    /**
     * 计算会话过期时间
     *
     * @param hours 过期小时数
     * @return 过期时间
     */
    public static LocalDateTime calculateExpirationTime(int hours) {
        return LocalDateTime.now().plusHours(hours);
    }

    /**
     * 验证搜索关键词
     *
     * @param keyword 关键词
     * @param config  配置
     * @return 是否有效
     */
    public static boolean isValidKeyword(String keyword, InterviewConfig config) {
        if (StrUtil.isBlank(keyword)) {
            return false;
        }

        int length = keyword.trim().length();
        InterviewConfig.Search searchConfig = config.getSearch();

        return length >= searchConfig.getMinKeywordLength()
            && length <= searchConfig.getMaxKeywordLength()
            && KEYWORD_PATTERN.matcher(keyword).matches();
    }

    /**
     * 计算智能排序分数
     *
     * @param job    岗位信息
     * @param config 配置
     * @return 智能分数
     */
    public static double calculateSmartScore(JobVo job, InterviewConfig config) {
        InterviewConfig.SmartSort sortConfig = config.getSort().getSmartSort();

        // 热度分数
        double hotScore = (job.getInterviewers() / 300.0) * sortConfig.getHotWeight() * 100;

        // 通过率分数 - 适中的通过率更好
        double passRate = parsePassRate(String.valueOf(job.getPassRate()));
        double passRateScore = ((100 - Math.abs(passRate - sortConfig.getOptimalPassRate())) / 100.0)
            * sortConfig.getPassRateWeight() * 100;

        // 难度分数 - 中等难度优先
        double difficultyScore = ((5 - Math.abs(job.getDifficulty() - sortConfig.getOptimalDifficulty())) / 5.0)
            * sortConfig.getDifficultyWeight() * 100;

        // 时长分数 - 30-45分钟为最佳
        double durationScore = ((60 - Math.abs(job.getDuration() - sortConfig.getOptimalDuration())) / 60.0)
            * sortConfig.getDurationWeight() * 100;

        // 题目数量分数 - 10-15题为最佳
        double questionScore = ((20 - Math.abs(job.getQuestionCount() - sortConfig.getOptimalQuestionCount())) / 20.0)
            * sortConfig.getQuestionCountWeight() * 100;

        return hotScore + passRateScore + difficultyScore + durationScore + questionScore;
    }

    /**
     * 解析通过率字符串
     *
     * @param passRateStr 通过率字符串（如"68%"）
     * @return 通过率数值
     */
    public static double parsePassRate(String passRateStr) {
        if (StrUtil.isBlank(passRateStr)) {
            return 0.0;
        }

        try {
            return Double.parseDouble(passRateStr.replace("%", ""));
        } catch (NumberFormatException e) {
            return 0.0;
        }
    }

    /**
     * 格式化通过率
     *
     * @param passRate 通过率数值
     * @return 格式化后的字符串
     */
    public static String formatPassRate(double passRate) {
        return String.format("%.1f%%", passRate);
    }

    /**
     * 检查会话是否过期
     *
     * @param expiresAt 过期时间
     * @return 是否过期
     */
    public static boolean isSessionExpired(LocalDateTime expiresAt) {
        return expiresAt != null && LocalDateTime.now().isAfter(expiresAt);
    }

    /**
     * 验证岗位标签
     *
     * @param tags 标签列表
     * @return 是否有效
     */
    public static boolean isValidTags(List<String> tags) {
        if (tags == null || tags.isEmpty()) {
            return true; // 允许空标签
        }

        return tags.stream()
            .allMatch(tag -> StrUtil.isNotBlank(tag) && tag.length() <= 20);
    }

    /**
     * 清理和标准化关键词
     *
     * @param keyword 原始关键词
     * @return 清理后的关键词
     */
    public static String cleanKeyword(String keyword) {
        if (StrUtil.isBlank(keyword)) {
            return "";
        }

        return keyword.trim()
            .replaceAll("\\s+", " ") // 多个空格替换为单个空格
            .toLowerCase(); // 转为小写
    }

    /**
     * 生成设备检测结果（模拟）
     *
     * @param config 配置
     * @return 检测结果
     */
    public static boolean simulateDeviceCheck(InterviewConfig config) {
        if (!config.getDevice().getEnabled()) {
            return true;
        }

        // 根据配置的失败率模拟检测结果
        return Math.random() > config.getDevice().getMockFailureRate();
    }

    /**
     * 验证面试模式ID
     *
     * @param modeId 模式ID
     * @return 是否有效
     */
    public static boolean isValidModeId(String modeId) {
        if (StrUtil.isBlank(modeId)) {
            return false;
        }

        // 检查模式ID格式（字母、数字、下划线）
        return modeId.matches("^[a-zA-Z0-9_]+$") && modeId.length() <= 50;
    }

    /**
     * 计算推荐分数
     *
     * @param job 岗位信息
     * @return 推荐分数
     */
    public static double calculateRecommendScore(JobVo job) {
        // 简单的推荐算法：综合考虑热度和通过率
        double hotScore = Math.min(job.getInterviewers() / 100.0, 1.0) * 0.6;
        double passRateScore = parsePassRate(String.valueOf(job.getPassRate())) / 100.0 * 0.4;

        return hotScore + passRateScore;
    }

    /**
     * 获取难度描述
     *
     * @param difficulty 难度等级
     * @return 难度描述
     */
    public static String getDifficultyDescription(Integer difficulty) {
        if (difficulty == null) {
            return "未知";
        }

        return switch (difficulty) {
            case 1 -> "入门";
            case 2 -> "简单";
            case 3 -> "中等";
            case 4 -> "困难";
            case 5 -> "专家";
            default -> "未知";
        };
    }

    /**
     * 验证自定义问题
     *
     * @param questions 问题列表
     * @return 是否有效
     */
    public static boolean isValidCustomQuestions(List<String> questions) {
        if (questions == null || questions.isEmpty()) {
            return true; // 允许空问题
        }

        if (questions.size() > 10) {
            return false; // 限制最多10个自定义问题
        }

        return questions.stream()
            .allMatch(question -> StrUtil.isNotBlank(question) && question.length() <= 500);
    }

}
