package org.dromara.app.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 性能指标对象 app_performance_metrics
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@Data
@TableName("app_performance_metrics")
public class PerformanceMetrics implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 性能指标ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 结果ID
     */
    private String resultId;

    /**
     * 技术能力
     */
    private Integer technical;

    /**
     * 沟通能力
     */
    private Integer communication;

    /**
     * 问题解决
     */
    private Integer problemSolving;

    /**
     * 团队合作
     */
    private Integer teamwork;

    /**
     * 领导力
     */
    private Integer leadership;

    /**
     * 创造力
     */
    private Integer creativity;

    /**
     * 详细指标（JSON对象）
     */
    private Map<String, Integer> detailedMetrics;

    /**
     * 行业平均值（JSON对象）
     */
    private Map<String, Integer> industryAverage;

    /**
     * 技能差距（JSON数组）
     */
    private List<String> skillGaps;

    /**
     * 技能优势（JSON数组）
     */
    private List<String> skillStrengths;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

}
