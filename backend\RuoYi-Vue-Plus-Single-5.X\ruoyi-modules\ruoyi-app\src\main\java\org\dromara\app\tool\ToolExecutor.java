package org.dromara.app.tool;

import org.dromara.app.service.IToolService;

import java.util.Map;

/**
 * 工具执行器接口
 *
 * <AUTHOR>
 */
public interface ToolExecutor {

    /**
     * 执行工具
     *
     * @param parameters 参数
     * @param context    上下文
     * @return 执行结果
     */
    IToolService.ToolCallResult execute(Map<String, Object> parameters, IToolService.ToolCallContext context);

    /**
     * 获取工具ID
     *
     * @return 工具ID
     */
    String getToolId();

    /**
     * 获取工具名称
     *
     * @return 工具名称
     */
    String getToolName();

    /**
     * 获取工具描述
     *
     * @return 工具描述
     */
    String getDescription();

    /**
     * 检查工具是否可用
     *
     * @return 是否可用
     */
    default boolean isAvailable() {
        return true;
    }

    /**
     * 获取工具配置
     *
     * @return 工具配置
     */
    default Map<String, Object> getConfig() {
        return null;
    }
}
