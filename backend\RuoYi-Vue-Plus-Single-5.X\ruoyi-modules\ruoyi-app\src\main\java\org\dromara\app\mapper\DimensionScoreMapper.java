package org.dromara.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.dromara.app.domain.DimensionScore;

import java.util.List;

/**
 * 维度评分Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@Mapper
public interface DimensionScoreMapper extends BaseMapper<DimensionScore> {

    /**
     * 根据结果ID查询维度评分列表
     *
     * @param resultId 结果ID
     * @return 维度评分列表
     */
    List<DimensionScore> selectByResultId(@Param("resultId") String resultId);

    /**
     * 根据结果ID和维度名称查询维度评分
     *
     * @param resultId  结果ID
     * @param dimension 维度名称
     * @return 维度评分
     */
    DimensionScore selectByResultIdAndDimension(@Param("resultId") String resultId, @Param("dimension") String dimension);

    /**
     * 批量插入维度评分
     *
     * @param dimensionScores 维度评分列表
     * @return 插入数量
     */
    int batchInsert(@Param("list") List<DimensionScore> dimensionScores);

    /**
     * 根据结果ID删除维度评分
     *
     * @param resultId 结果ID
     * @return 删除数量
     */
    int deleteByResultId(@Param("resultId") String resultId);

    /**
     * 查询维度平均分数
     *
     * @param dimension 维度名称
     * @return 平均分数
     */
    Double selectAvgScoreByDimension(@Param("dimension") String dimension);

    /**
     * 查询用户在某维度的历史分数
     *
     * @param userId    用户ID
     * @param dimension 维度名称
     * @param limit     限制数量
     * @return 分数列表
     */
    List<Integer> selectUserDimensionHistory(@Param("userId") Long userId, @Param("dimension") String dimension, @Param("limit") Integer limit);

}
