package org.dromara.app.domain.vo;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 加密状态视图对象
 *
 * <AUTHOR>
 * @date 2025-01-19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EncryptionStatusVo {

    /**
     * 加密服务是否启用
     */
    private Boolean encryptionEnabled;

    /**
     * 当前使用的加密算法
     */
    private String currentAlgorithm;

    /**
     * 密钥版本
     */
    private String keyVersion;

    /**
     * 密钥创建时间
     */
    private LocalDateTime keyCreatedTime;

    /**
     * 密钥最后轮换时间
     */
    private LocalDateTime lastKeyRotationTime;

    /**
     * 下次密钥轮换时间
     */
    private LocalDateTime nextKeyRotationTime;

    /**
     * 加密操作总数
     */
    private Long totalEncryptionCount;

    /**
     * 解密操作总数
     */
    private Long totalDecryptionCount;

    /**
     * 加密失败次数
     */
    private Long encryptionFailureCount;

    /**
     * 解密失败次数
     */
    private Long decryptionFailureCount;

    /**
     * 加密成功率（百分比）
     */
    private Double encryptionSuccessRate;

    /**
     * 解密成功率（百分比）
     */
    private Double decryptionSuccessRate;

    /**
     * 平均加密时间（毫秒）
     */
    private Double averageEncryptionTime;

    /**
     * 平均解密时间（毫秒）
     */
    private Double averageDecryptionTime;

    /**
     * 支持的算法列表
     */
    private List<String> supportedAlgorithms;

    /**
     * 加密数据统计
     */
    private Map<String, Long> encryptedDataStats;

    /**
     * 密钥强度等级：weak/medium/strong
     */
    private String keyStrength;

    /**
     * 加密状态：healthy/warning/error
     */
    private String encryptionHealth;

    /**
     * 安全建议
     */
    private List<String> securityRecommendations;

    /**
     * 最后检查时间
     */
    private LocalDateTime lastCheckTime;

    /**
     * 是否需要密钥轮换
     */
    private Boolean needsKeyRotation;

    /**
     * 加密配置信息
     */
    private Map<String, Object> encryptionConfig;
}