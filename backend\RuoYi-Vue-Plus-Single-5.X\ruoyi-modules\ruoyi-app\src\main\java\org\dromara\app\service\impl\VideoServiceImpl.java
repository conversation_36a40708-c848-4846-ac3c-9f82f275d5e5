package org.dromara.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.*;
import org.dromara.app.domain.bo.CommentQueryBo;
import org.dromara.app.domain.bo.VideoCommentBo;
import org.dromara.app.domain.bo.VideoQueryBo;
import org.dromara.app.domain.vo.*;
import org.dromara.app.mapper.*;
import org.dromara.app.service.IVideoService;
import org.dromara.common.caffeine.annotation.CaffeineCache;
import org.dromara.common.caffeine.annotation.CaffeineEvict;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 视频课程Service业务层处理
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class VideoServiceImpl implements IVideoService {

    private final VideoMapper videoMapper;
    private final VideoCommentMapper videoCommentMapper;
    private final VideoLikeMapper videoLikeMapper;
    private final VideoProgressMapper videoProgressMapper;
    private final VideoCollectMapper videoCollectMapper;
    private final VideoShareMapper videoShareMapper;
    private final VideoCommentLikeMapper videoCommentLikeMapper;
    private final InstructorFollowMapper instructorFollowMapper;
    private final PaymentOrderMapper paymentOrderMapper;
    private final VideoPlayRecordMapper videoPlayRecordMapper;
    private final VideoPlayRecordCacheService videoPlayRecordCacheService;

    @Override
    public VideoListResultVo queryVideoList(VideoQueryBo bo) {
        if (ObjectUtil.isNull(bo)) {
            throw new ServiceException("查询条件不能为空");
        }
        // 构建查询条件
        LambdaQueryWrapper<Video> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Video::getStatus, 1);

        // 搜索关键词
        if (StrUtil.isNotBlank(bo.getKeyword())) {
            wrapper.and(w -> w.like(Video::getTitle, bo.getKeyword())
                .or().like(Video::getDescription, bo.getKeyword())
                .or().like(Video::getInstructor, bo.getKeyword()));
        }

        // 分类筛选
        if (StrUtil.isNotBlank(bo.getCategory())) {
            wrapper.eq(Video::getCategory, bo.getCategory());
        }

        // 难度筛选
        if (StrUtil.isNotBlank(bo.getDifficulty())) {
            wrapper.eq(Video::getDifficulty, bo.getDifficulty());
        }

        // 免费筛选
        if (ObjectUtil.isNotNull(bo.getFree()) && bo.getFree()) {
            wrapper.eq(Video::getFree, 1);
        }

        // 排序
        String sortBy = StrUtil.isNotBlank(bo.getSortBy()) ? bo.getSortBy() : "latest";
        String sortOrder = StrUtil.isNotBlank(bo.getSortOrder()) ? bo.getSortOrder() : "desc";
        boolean isAsc = "asc".equals(sortOrder);

        switch (sortBy) {
            case "popular":
                wrapper.orderBy(true, isAsc, Video::getViewCount);
                break;
            case "rating":
                wrapper.orderBy(true, isAsc, Video::getRating);
                break;
            case "price":
                wrapper.orderBy(true, isAsc, Video::getPrice);
                break;
            default: // latest
                wrapper.orderBy(true, isAsc, Video::getCreateTime);
                break;
        }

        // 分页查询
        Page<Video> videoPage = new Page<>();
        videoPage.setCurrent(bo.getPage());
        videoPage.setSize(bo.getPageSize());
        Page<Video> page = videoMapper.selectPage(videoPage, wrapper);

        // 转换为VO
        List<VideoListResultVo.VideoItemVo> videoItems = page.getRecords().stream()
            .map(this::convertToVideoItemVo)
            .collect(Collectors.toList());

        VideoListResultVo result = new VideoListResultVo();
        result.setVideos(videoItems);
        result.setTotal(page.getTotal());
        result.setHasMore(page.getCurrent() < page.getPages());

        return result;
    }

    @Override
    @CaffeineCache(cacheName = "videoDetail", key = "#videoId", expire = "3600")
    public VideoDetailVo getVideoDetail(Long videoId, Long userId) {
        if (ObjectUtil.isNull(videoId)) {
            throw new ServiceException("视频ID不能为空");
        }

        // 查询视频信息
        Video video = videoMapper.selectById(videoId);
        if (ObjectUtil.isNull(video) || video.getStatus() != 1) {
            throw new ServiceException("视频不存在或已下架");
        }

        // 转换为VO
        VideoDetailVo detail = convertToVideoDetailVo(video, userId);

        return detail;
    }

    @Override
    @CaffeineCache(cacheName = "videoLearningStats", key = "#userId", expire = "3600")
    public VideoLearningStatsVo getLearningStats(Long userId) {
        if (ObjectUtil.isNull(userId)) {
            throw new ServiceException("用户ID不能为空");
        }

        VideoLearningStatsVo stats = new VideoLearningStatsVo();

        // 查询用户学习进度
        LambdaQueryWrapper<VideoProgress> progressWrapper = Wrappers.lambdaQuery();
        progressWrapper.eq(VideoProgress::getUserId, userId);
        List<VideoProgress> progressList = videoProgressMapper.selectList(progressWrapper);

        // 统计总数据
        stats.setTotalVideos(progressList.size());

        // 统计已完成视频数
        long completedCount = progressList.stream()
            .filter(p -> p.getIsCompleted() == 1)
            .count();
        stats.setCompletedVideos((int) completedCount);

        // 统计总学习时长（小时）
        BigDecimal totalHours = progressList.stream()
            .filter(p -> p.getDuration() != null && p.getDuration() > 0)
            .map(p -> BigDecimal.valueOf(p.getDuration()))
            .reduce(BigDecimal.ZERO, BigDecimal::add)
            .divide(BigDecimal.valueOf(3600), 2, BigDecimal.ROUND_HALF_UP);
        stats.setTotalHours(totalHours);

        // 统计今日学习时长
        LocalDate today = LocalDate.now();
        BigDecimal todayHours = progressList.stream()
            .filter(p -> p.getUpdateTime() != null &&
                p.getUpdateTime().toLocalDate().equals(today))
            .filter(p -> p.getDuration() != null && p.getDuration() > 0)
            .map(p -> BigDecimal.valueOf(p.getDuration()))
            .reduce(BigDecimal.ZERO, BigDecimal::add)
            .divide(BigDecimal.valueOf(3600), 2, BigDecimal.ROUND_HALF_UP);
        stats.setStudiedToday(todayHours);

        // 默认周目标
        stats.setWeeklyGoal(BigDecimal.valueOf(10));

        // 计算本周进度（这里简化处理）
        BigDecimal weeklyProgress = todayHours.multiply(BigDecimal.valueOf(7));
        if (weeklyProgress.compareTo(stats.getWeeklyGoal()) > 0) {
            weeklyProgress = stats.getWeeklyGoal();
        }
        stats.setWeeklyProgress(weeklyProgress);

        // 分类统计（暂时返回空列表）
        stats.setCategoryStats(List.of());

        return stats;
    }

    @Override
    public VideoListResultVo getBookmarkedVideos(Long userId, VideoQueryBo bo, PageQuery pageQuery) {
        if (ObjectUtil.isNull(userId)) {
            throw new ServiceException("用户ID不能为空");
        }

        // 查询用户收藏的视频ID列表
        LambdaQueryWrapper<VideoCollect> collectWrapper = Wrappers.lambdaQuery();
        collectWrapper.eq(VideoCollect::getUserId, userId)
            .orderByDesc(VideoCollect::getCreateTime);

        Page<VideoCollect> collectPage = videoCollectMapper.selectPage(pageQuery.build(), collectWrapper);

        if (collectPage.getRecords().isEmpty()) {
            VideoListResultVo result = new VideoListResultVo();
            result.setVideos(List.of());
            result.setTotal(0L);
            result.setHasMore(false);
            return result;
        }

        // 获取视频ID列表
        List<Long> videoIds = collectPage.getRecords().stream()
            .map(VideoCollect::getVideoId)
            .collect(Collectors.toList());

        // 查询视频详情
        LambdaQueryWrapper<Video> videoWrapper = Wrappers.lambdaQuery();
        videoWrapper.in(Video::getId, videoIds)
            .eq(Video::getStatus, 1);

        // 应用搜索和筛选条件
        if (StrUtil.isNotBlank(bo.getKeyword())) {
            videoWrapper.and(w -> w.like(Video::getTitle, bo.getKeyword())
                .or().like(Video::getDescription, bo.getKeyword()));
        }

        if (StrUtil.isNotBlank(bo.getCategory())) {
            videoWrapper.eq(Video::getCategory, bo.getCategory());
        }

        if (StrUtil.isNotBlank(bo.getDifficulty())) {
            videoWrapper.eq(Video::getDifficulty, bo.getDifficulty());
        }

        List<Video> videos = videoMapper.selectList(videoWrapper);

        // 转换为VO
        List<VideoListResultVo.VideoItemVo> videoItems = videos.stream()
            .map(this::convertToVideoItemVo)
            .collect(Collectors.toList());

        VideoListResultVo result = new VideoListResultVo();
        result.setVideos(videoItems);
        result.setTotal(collectPage.getTotal());
        result.setHasMore(collectPage.getCurrent() < collectPage.getPages());

        return result;
    }

    @Override
    @CaffeineEvict(cacheName = "videoDetail", key = "#userId", allEntries = true)
    public VideoListResultVo getPurchasedVideos(Long userId, VideoQueryBo bo, PageQuery pageQuery) {
        if (ObjectUtil.isNull(userId)) {
            throw new ServiceException("用户ID不能为空");
        }

        // 查询已支付的视频订单
        LambdaQueryWrapper<PaymentOrder> orderWrapper = Wrappers.lambdaQuery();
        orderWrapper.eq(PaymentOrder::getUserId, userId)
            .eq(PaymentOrder::getStatus, "paid")
            .eq(PaymentOrder::getProductType, "video")
            .orderByDesc(PaymentOrder::getPayTime);

        Page<PaymentOrder> orderPage = paymentOrderMapper.selectPage(pageQuery.build(), orderWrapper);

        if (orderPage.getRecords().isEmpty()) {
            VideoListResultVo result = new VideoListResultVo();
            result.setVideos(List.of());
            result.setTotal(0L);
            result.setHasMore(false);
            return result;
        }

        // 获取已购买的视频ID列表
        List<Long> videoIds = orderPage.getRecords().stream()
            .map(PaymentOrder::getProductId)
            .collect(Collectors.toList());

        // 查询视频详情
        LambdaQueryWrapper<Video> videoWrapper = Wrappers.lambdaQuery();
        videoWrapper.in(Video::getId, videoIds)
            .eq(Video::getStatus, 1);

        // 应用搜索和筛选条件
        if (StrUtil.isNotBlank(bo.getKeyword())) {
            videoWrapper.and(w -> w.like(Video::getTitle, bo.getKeyword())
                .or().like(Video::getDescription, bo.getKeyword()));
        }

        if (StrUtil.isNotBlank(bo.getCategory())) {
            videoWrapper.eq(Video::getCategory, bo.getCategory());
        }

        if (StrUtil.isNotBlank(bo.getDifficulty())) {
            videoWrapper.eq(Video::getDifficulty, bo.getDifficulty());
        }

        List<Video> videos = videoMapper.selectList(videoWrapper);

        // 转换为VO
        List<VideoListResultVo.VideoItemVo> videoItems = videos.stream()
            .map(this::convertToVideoItemVo)
            .collect(Collectors.toList());

        VideoListResultVo result = new VideoListResultVo();
        result.setVideos(videoItems);
        result.setTotal(orderPage.getTotal());
        result.setHasMore(orderPage.getCurrent() < orderPage.getPages());

        return result;
    }

    @Override
    public VideoListResultVo getLearningHistory(Long userId, VideoQueryBo bo, PageQuery pageQuery) {
        if (ObjectUtil.isNull(userId)) {
            throw new ServiceException("用户ID不能为空");
        }

        // 查询用户的学习进度记录
        LambdaQueryWrapper<VideoProgress> progressWrapper = Wrappers.lambdaQuery();
        progressWrapper.eq(VideoProgress::getUserId, userId)
            .orderByDesc(VideoProgress::getUpdateTime);

        Page<VideoProgress> progressPage = videoProgressMapper.selectPage(pageQuery.build(), progressWrapper);

        if (progressPage.getRecords().isEmpty()) {
            VideoListResultVo result = new VideoListResultVo();
            result.setVideos(List.of());
            result.setTotal(0L);
            result.setHasMore(false);
            return result;
        }

        // 获取视频ID列表
        List<Long> videoIds = progressPage.getRecords().stream()
            .map(VideoProgress::getVideoId)
            .collect(Collectors.toList());

        // 查询视频详情
        LambdaQueryWrapper<Video> videoWrapper = Wrappers.lambdaQuery();
        videoWrapper.in(Video::getId, videoIds)
            .eq(Video::getStatus, 1);

        // 应用搜索和筛选条件
        if (StrUtil.isNotBlank(bo.getKeyword())) {
            videoWrapper.and(w -> w.like(Video::getTitle, bo.getKeyword())
                .or().like(Video::getDescription, bo.getKeyword()));
        }

        if (StrUtil.isNotBlank(bo.getCategory())) {
            videoWrapper.eq(Video::getCategory, bo.getCategory());
        }

        if (StrUtil.isNotBlank(bo.getDifficulty())) {
            videoWrapper.eq(Video::getDifficulty, bo.getDifficulty());
        }

        List<Video> videos = videoMapper.selectList(videoWrapper);

        // 转换为VO并设置学习进度
        List<VideoListResultVo.VideoItemVo> videoItems = videos.stream()
            .map(video -> {
                VideoListResultVo.VideoItemVo vo = convertToVideoItemVo(video);
                // 设置学习进度
                progressPage.getRecords().stream()
                    .filter(p -> p.getVideoId().equals(video.getId()))
                    .findFirst().ifPresent(progress -> vo.setCompletionRate(progress.getCompletionRate()));
                return vo;
            })
            .collect(Collectors.toList());

        VideoListResultVo result = new VideoListResultVo();
        result.setVideos(videoItems);
        result.setTotal(progressPage.getTotal());
        result.setHasMore(progressPage.getCurrent() < progressPage.getPages());

        return result;
    }

    @Override
    @CaffeineCache(cacheName = "relatedVideos", key = "#videoId + '_' + #limit", expire = "3600")
    public List<RelatedVideoVo> getRelatedVideos(Long videoId, Integer limit) {
        if (ObjectUtil.isNull(videoId)) {
            throw new ServiceException("视频ID不能为空");
        }

        // 查询当前视频信息
        Video currentVideo = videoMapper.selectById(videoId);
        if (ObjectUtil.isNull(currentVideo)) {
            return List.of();
        }

        // 构建推荐查询条件
        LambdaQueryWrapper<Video> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(Video::getStatus, 1)
            .ne(Video::getId, videoId)
            .orderByDesc(Video::getViewCount);

        // 优先推荐同分类的视频
        if (StrUtil.isNotBlank(currentVideo.getCategory())) {
            wrapper.eq(Video::getCategory, currentVideo.getCategory());
        }

        // 设置查询限制
        if (ObjectUtil.isNull(limit) || limit <= 0) {
            limit = 6;
        }

        // 查询推荐视频
        Page<Video> page = new Page<>(1, limit);
        Page<Video> relatedVideos = videoMapper.selectPage(page, wrapper);

        // 如果同分类视频数量不足，补充其他视频
        if (relatedVideos.getRecords().size() < limit) {
            int remaining = limit - relatedVideos.getRecords().size();

            LambdaQueryWrapper<Video> supplementWrapper = Wrappers.lambdaQuery();
            supplementWrapper.eq(Video::getStatus, 1)
                .ne(Video::getId, videoId)
                .orderByDesc(Video::getViewCount);

            // 排除已查询的视频
            if (!relatedVideos.getRecords().isEmpty()) {
                List<Long> excludeIds = relatedVideos.getRecords().stream()
                    .map(Video::getId)
                    .collect(Collectors.toList());
                supplementWrapper.notIn(Video::getId, excludeIds);
            }

            Page<Video> supplementPage = new Page<>(1, remaining);
            Page<Video> supplementVideos = videoMapper.selectPage(supplementPage, supplementWrapper);

            // 合并结果
            List<Video> allVideos = relatedVideos.getRecords();
            allVideos.addAll(supplementVideos.getRecords());
            relatedVideos.setRecords(allVideos);
        }

        // 转换为VO
        return relatedVideos.getRecords().stream()
            .map(this::convertToRelatedVideoVo)
            .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void toggleVideoLike(Long videoId, Long userId, Boolean isLike) {
        if (ObjectUtil.isNull(videoId) || ObjectUtil.isNull(userId)) {
            throw new ServiceException("参数不能为空");
        }

        // 查询现有点赞记录
        LambdaQueryWrapper<VideoLike> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(VideoLike::getVideoId, videoId)
            .eq(VideoLike::getUserId, userId);
        VideoLike existingLike = videoLikeMapper.selectOne(wrapper);

        if (isLike) {
            // 点赞
            if (existingLike == null) {
                VideoLike videoLike = new VideoLike();
                videoLike.setVideoId(videoId);
                videoLike.setUserId(userId);
                videoLike.setCreateTime(LocalDateTime.now());
                videoLikeMapper.insert(videoLike);

                // 增加视频点赞数
                LambdaUpdateWrapper<Video> updateWrapper = Wrappers.lambdaUpdate();
                updateWrapper.eq(Video::getId, videoId)
                    .setSql("like_count = like_count + 1");
                videoMapper.update(null, updateWrapper);
            }
        } else {
            // 取消点赞
            if (existingLike != null) {
                videoLikeMapper.deleteById(existingLike.getId());

                // 减少视频点赞数
                LambdaUpdateWrapper<Video> updateWrapper = Wrappers.lambdaUpdate();
                updateWrapper.eq(Video::getId, videoId)
                    .setSql("like_count = GREATEST(like_count - 1, 0)");
                videoMapper.update(null, updateWrapper);
            }
        }

        log.info("用户 {} {} 视频 {}", userId, isLike ? "点赞" : "取消点赞", videoId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void toggleVideoCollect(Long videoId, Long userId, Boolean isCollect) {
        if (ObjectUtil.isNull(videoId) || ObjectUtil.isNull(userId)) {
            throw new ServiceException("参数不能为空");
        }

        // 查询现有收藏记录
        LambdaQueryWrapper<VideoCollect> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(VideoCollect::getVideoId, videoId)
            .eq(VideoCollect::getUserId, userId);
        VideoCollect existingCollect = videoCollectMapper.selectOne(wrapper);

        if (isCollect) {
            // 收藏
            if (existingCollect == null) {
                VideoCollect videoCollect = new VideoCollect();
                videoCollect.setVideoId(videoId);
                videoCollect.setUserId(userId);
                videoCollect.setCreateTime(LocalDateTime.now());
                videoCollectMapper.insert(videoCollect);

                // 增加视频收藏数
                LambdaUpdateWrapper<Video> updateWrapper = Wrappers.lambdaUpdate();
                updateWrapper.eq(Video::getId, videoId)
                    .setSql("collect_count = collect_count + 1");
                videoMapper.update(null, updateWrapper);
            }
        } else {
            // 取消收藏
            if (existingCollect != null) {
                videoCollectMapper.deleteById(existingCollect.getId());

                // 减少视频收藏数
                LambdaUpdateWrapper<Video> updateWrapper = Wrappers.lambdaUpdate();
                updateWrapper.eq(Video::getId, videoId)
                    .setSql("collect_count = GREATEST(collect_count - 1, 0)");
                videoMapper.update(null, updateWrapper);
            }
        }

        log.info("用户 {} {} 视频 {}", userId, isCollect ? "收藏" : "取消收藏", videoId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void shareVideo(Long videoId, Long userId, String platform) {
        if (ObjectUtil.isNull(videoId) || ObjectUtil.isNull(userId)) {
            throw new ServiceException("参数不能为空");
        }

        // 验证分享平台
        if (StrUtil.isBlank(platform)) {
            throw new ServiceException("分享平台不能为空");
        }

        // 记录分享
        VideoShare videoShare = new VideoShare();
        videoShare.setVideoId(videoId);
        videoShare.setUserId(userId);
        videoShare.setPlatform(platform);
        videoShare.setCreateTime(LocalDateTime.now());
        videoShareMapper.insert(videoShare);

        // 增加视频分享数
        LambdaUpdateWrapper<Video> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(Video::getId, videoId)
            .setSql("share_count = share_count + 1");
        videoMapper.update(null, updateWrapper);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void incrementVideoView(Long videoId) {
        if (ObjectUtil.isNull(videoId)) {
            throw new ServiceException("视频ID不能为空");
        }

        // 更新视频播放次数
        LambdaUpdateWrapper<Video> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(Video::getId, videoId)
            .setSql("view_count = view_count + 1");

        int result = videoMapper.update(null, updateWrapper);
        if (result > 0) {
            log.info("成功增加视频 {} 播放次数", videoId);
        } else {
            log.warn("视频 {} 播放次数增加失败", videoId);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateVideoProgress(Long videoId, Long userId, Integer currentTime, Integer duration) {
        if (ObjectUtil.isNull(videoId) || ObjectUtil.isNull(userId)) {
            throw new ServiceException("参数不能为空");
        }

        // 查询现有进度记录
        LambdaQueryWrapper<VideoProgress> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(VideoProgress::getVideoId, videoId)
            .eq(VideoProgress::getUserId, userId);
        VideoProgress existingProgress = videoProgressMapper.selectOne(wrapper);

        // 计算完成率
        BigDecimal completionRate = BigDecimal.ZERO;
        boolean isCompleted = false;
        if (duration != null && duration > 0) {
            completionRate = BigDecimal.valueOf(currentTime)
                .divide(BigDecimal.valueOf(duration), 4, BigDecimal.ROUND_HALF_UP)
                .multiply(BigDecimal.valueOf(100));

            // 如果播放进度超过90%，则认为已完成
            if (completionRate.compareTo(BigDecimal.valueOf(90)) >= 0) {
                isCompleted = true;
                completionRate = BigDecimal.valueOf(100);
            }
        }

        if (existingProgress == null) {
            // 新增进度记录
            VideoProgress progress = new VideoProgress();
            progress.setVideoId(videoId);
            progress.setUserId(userId);
            progress.setCurrentTime(currentTime);
            progress.setDuration(duration);
            progress.setCompletionRate(completionRate);
            progress.setIsCompleted(isCompleted ? 1 : 0);
            progress.setUpdateTime(LocalDateTime.now());

            videoProgressMapper.insert(progress);
        } else {
            // 更新进度记录
            existingProgress.setCurrentTime(currentTime);
            existingProgress.setDuration(duration);
            existingProgress.setCompletionRate(completionRate);
            existingProgress.setIsCompleted(isCompleted ? 1 : 0);
            existingProgress.setUpdateTime(LocalDateTime.now());

            videoProgressMapper.updateById(existingProgress);
        }

        log.info("更新用户 {} 视频 {} 播放进度: {}/{}, 完成率: {}%",
            userId, videoId, currentTime, duration, completionRate);
    }

    @Override
    public VideoPlayRecordVo getVideoPlayRecord(Long videoId, Long userId) {
        if (ObjectUtil.isNull(videoId) || ObjectUtil.isNull(userId)) {
            throw new ServiceException("参数不能为空");
        }

        VideoPlayRecordVo record = new VideoPlayRecordVo();
        record.setVideoId(videoId);
        record.setUserId(userId);

        try {
            // 1. 先从Redis缓存中获取播放记录
            VideoPlayRecord cachedRecord = videoPlayRecordCacheService.getCachedPlayRecord(videoId, userId);

            if (cachedRecord != null) {
                // 2. 缓存命中，直接返回缓存数据
                record.setLastPlayTime(0); // 播放记录中没有当前播放时间，使用0
                record.setDuration(cachedRecord.getTotalPlayDuration() != null ? cachedRecord.getTotalPlayDuration() : 0);
                record.setCompletionRate(BigDecimal.ZERO); // 播放记录中没有完成率，需要从VideoProgress获取
                record.setIsCompleted(false);

                log.debug("从缓存获取播放记录: videoId={}, userId={}", videoId, userId);
            } else {
                // 3. 缓存未命中，查询数据库
                LambdaQueryWrapper<VideoPlayRecord> wrapper = Wrappers.lambdaQuery();
                wrapper.eq(VideoPlayRecord::getVideoId, videoId)
                    .eq(VideoPlayRecord::getUserId, userId);
                VideoPlayRecord dbRecord = videoPlayRecordMapper.selectOne(wrapper);

                if (dbRecord != null) {
                    // 4. 数据库中存在记录，缓存并返回
                    videoPlayRecordCacheService.cachePlayRecord(dbRecord);

                    record.setLastPlayTime(0);
                    record.setDuration(dbRecord.getTotalPlayDuration() != null ? dbRecord.getTotalPlayDuration() : 0);
                    record.setCompletionRate(BigDecimal.ZERO);
                    record.setIsCompleted(false);

                    log.debug("从数据库获取播放记录并缓存: videoId={}, userId={}", videoId, userId);
                } else {
                    // 5. 数据库中也没有记录，返回默认值
                    record.setLastPlayTime(0);
                    record.setDuration(0);
                    record.setCompletionRate(BigDecimal.ZERO);
                    record.setIsCompleted(false);

                    log.debug("无播放记录，返回默认值: videoId={}, userId={}", videoId, userId);
                }
            }

            // 6. 补充获取播放进度信息（完成率等）
            LambdaQueryWrapper<VideoProgress> progressWrapper = Wrappers.lambdaQuery();
            progressWrapper.eq(VideoProgress::getVideoId, videoId)
                .eq(VideoProgress::getUserId, userId);
            VideoProgress progress = videoProgressMapper.selectOne(progressWrapper);

            if (progress != null) {
                record.setLastPlayTime(progress.getCurrentTime());
                record.setDuration(progress.getDuration());
                record.setCompletionRate(progress.getCompletionRate());
                record.setIsCompleted(progress.getIsCompleted() == 1);
            }

        } catch (Exception e) {
            log.error("获取视频播放记录失败: videoId={}, userId={}", videoId, userId, e);
            // 发生异常时返回默认值
            record.setLastPlayTime(0);
            record.setDuration(0);
            record.setCompletionRate(BigDecimal.ZERO);
            record.setIsCompleted(false);
        }

        return record;
    }

    @Override
    public VideoPurchaseStatusVo checkVideoPurchaseStatus(Long videoId, Long userId) {
        if (ObjectUtil.isNull(videoId) || ObjectUtil.isNull(userId)) {
            throw new ServiceException("参数不能为空");
        }

        VideoPurchaseStatusVo status = new VideoPurchaseStatusVo();
        status.setVideoId(videoId);
        status.setUserId(userId);

        // 检查视频是否免费
        Video video = videoMapper.selectById(videoId);
        if (ObjectUtil.isNotNull(video) && video.getFree() == 1) {
            status.setIsPurchased(true);
            status.setFree(true);
            return status;
        }

        // 查询用户是否购买过此视频
        LambdaQueryWrapper<PaymentOrder> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(PaymentOrder::getUserId, userId)
            .eq(PaymentOrder::getProductId, videoId)
            .eq(PaymentOrder::getProductType, "video")
            .eq(PaymentOrder::getStatus, "paid");

        PaymentOrder order = paymentOrderMapper.selectOne(wrapper);
        boolean isPurchased = ObjectUtil.isNotNull(order);

        status.setIsPurchased(isPurchased);
        status.setFree(false);

        if (isPurchased) {
            status.setPurchaseTime(order.getPayTime());
            status.setOrderNo(order.getOrderNo());
            status.setAmount(order.getAmount());
        }

        return status;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void toggleInstructorFollow(Long instructorId, Long userId, Boolean isFollow) {
        if (ObjectUtil.isNull(instructorId) || ObjectUtil.isNull(userId)) {
            throw new ServiceException("参数不能为空");
        }

        // 查询现有关注记录
        LambdaQueryWrapper<InstructorFollow> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(InstructorFollow::getInstructorId, instructorId)
            .eq(InstructorFollow::getUserId, userId);
        InstructorFollow existingFollow = instructorFollowMapper.selectOne(wrapper);

        if (isFollow) {
            // 关注
            if (existingFollow == null) {
                InstructorFollow follow = new InstructorFollow();
                follow.setInstructorId(instructorId);
                follow.setUserId(userId);
                follow.setCreateTime(DateTime.now());
                instructorFollowMapper.insert(follow);
            }
        } else {
            // 取关
            if (existingFollow != null) {
                instructorFollowMapper.deleteById(existingFollow.getId());
            }
        }

        log.info("用户 {} {} 讲师 {}", userId, isFollow ? "关注" : "取关", instructorId);
    }

    @Override
    public VideoCommentListVo getVideoComments(CommentQueryBo bo, PageQuery pageQuery) {
        if (ObjectUtil.isNull(bo) || ObjectUtil.isNull(bo.getVideoId())) {
            throw new ServiceException("视频ID不能为空");
        }

        // 构建查询条件
        LambdaQueryWrapper<VideoComment> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(VideoComment::getVideoId, bo.getVideoId())
            .eq(VideoComment::getStatus, 1);

        // 根据评论类型查询
        if (ObjectUtil.isNotNull(bo.getParentId())) {
            wrapper.eq(VideoComment::getParentId, bo.getParentId());
        } else {
            wrapper.isNull(VideoComment::getParentId);
        }

        // 排序
        String sortType = StrUtil.isNotBlank(bo.getSortType()) ? bo.getSortType() : "latest";
        switch (sortType) {
            case "hottest":
                wrapper.orderByDesc(VideoComment::getLikeCount);
                break;
            case "latest":
            default:
                wrapper.orderByDesc(VideoComment::getCreateTime);
                break;
        }

        // 分页查询
        Page<VideoComment> page = videoCommentMapper.selectPage(pageQuery.build(), wrapper);

        // 转换为VO
        List<VideoCommentVo> comments = page.getRecords().stream()
            .map(comment -> convertToVideoCommentVo(comment, bo.getCurrentUserId()))
            .collect(Collectors.toList());

        VideoCommentListVo result = new VideoCommentListVo();
        result.setComments(comments);
        result.setTotal(page.getTotal());
        result.setHasMore(page.getCurrent() < page.getPages());

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public VideoCommentVo publishVideoComment(VideoCommentBo bo) {
        if (ObjectUtil.isNull(bo.getVideoId()) || ObjectUtil.isNull(bo.getUserId()) || StrUtil.isBlank(bo.getContent())) {
            throw new ServiceException("参数不能为空");
        }

        // 验证视频是否存在
        Video video = videoMapper.selectById(bo.getVideoId());
        if (ObjectUtil.isNull(video) || video.getStatus() != 1) {
            throw new ServiceException("视频不存在或已下架");
        }

        // 验证父评论是否存在
        if (ObjectUtil.isNotNull(bo.getParentId())) {
            VideoComment parentComment = videoCommentMapper.selectById(bo.getParentId());
            if (ObjectUtil.isNull(parentComment) || parentComment.getStatus() != 1) {
                throw new ServiceException("父评论不存在");
            }
        }

        // 创建评论记录
        VideoComment comment = new VideoComment();
        comment.setVideoId(bo.getVideoId());
        comment.setUserId(bo.getUserId());
        comment.setParentId(bo.getParentId());
        comment.setContent(bo.getContent());
        comment.setLikeCount(0);
        comment.setStatus(1);
        comment.setCreateTime(DateTime.now());
        comment.setUpdateTime(DateTime.now());

        videoCommentMapper.insert(comment);

        // 更新视频评论数
        LambdaUpdateWrapper<Video> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(Video::getId, bo.getVideoId())
            .setSql("comment_count = comment_count + 1");
        videoMapper.update(null, updateWrapper);

        log.info("用户 {} 发布视频 {} 评论成功", bo.getUserId(), bo.getVideoId());

        // 返回评论VO
        return convertToVideoCommentVo(comment, bo.getUserId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void toggleCommentLike(Long commentId, Long userId, Boolean isLike) {
        if (ObjectUtil.isNull(commentId) || ObjectUtil.isNull(userId)) {
            throw new ServiceException("参数不能为空");
        }

        // 验证评论是否存在
        VideoComment comment = videoCommentMapper.selectById(commentId);
        if (ObjectUtil.isNull(comment) || comment.getStatus() != 1) {
            throw new ServiceException("评论不存在");
        }

        // 查询现有点赞记录
        LambdaQueryWrapper<VideoCommentLike> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(VideoCommentLike::getCommentId, commentId)
            .eq(VideoCommentLike::getUserId, userId);
        VideoCommentLike existingLike = videoCommentLikeMapper.selectOne(wrapper);

        if (isLike) {
            // 点赞
            if (existingLike == null) {
                VideoCommentLike commentLike = new VideoCommentLike();
                commentLike.setCommentId(commentId);
                commentLike.setUserId(userId);
                commentLike.setCreateTime(DateTime.now());
                videoCommentLikeMapper.insert(commentLike);

                // 增加评论点赞数
                LambdaUpdateWrapper<VideoComment> updateWrapper = Wrappers.lambdaUpdate();
                updateWrapper.eq(VideoComment::getId, commentId)
                    .setSql("like_count = like_count + 1");
                videoCommentMapper.update(null, updateWrapper);
            }
        } else {
            // 取消点赞
            if (existingLike != null) {
                videoCommentLikeMapper.deleteById(existingLike.getId());

                // 减少评论点赞数
                LambdaUpdateWrapper<VideoComment> updateWrapper = Wrappers.lambdaUpdate();
                updateWrapper.eq(VideoComment::getId, commentId)
                    .setSql("like_count = GREATEST(like_count - 1, 0)");
                videoCommentMapper.update(null, updateWrapper);
            }
        }

        log.info("用户 {} {} 评论 {}", userId, isLike ? "点赞" : "取消点赞", commentId);
    }

    /**
     * 转换为视频项VO
     */
    private VideoListResultVo.VideoItemVo convertToVideoItemVo(Video video) {
        VideoListResultVo.VideoItemVo vo = new VideoListResultVo.VideoItemVo();
        vo.setId(video.getId());
        vo.setTitle(video.getTitle());
        vo.setDescription(video.getDescription());
        vo.setInstructor(video.getInstructor());
        vo.setDuration(video.getDuration());
        vo.setThumbnail(video.getThumbnail());
        vo.setCategory(video.getCategory());
        vo.setDifficulty(video.getDifficulty());
        vo.setRating(video.getRating());
        vo.setStudentCount(video.getStudentCount());
        vo.setPrice(video.getPrice());
        vo.setFree(video.getFree() == 1);
        vo.setViewCount(video.getViewCount());
//        vo.setCreateTime();

        // 解析标签
        if (StrUtil.isNotBlank(video.getTags())) {
            try {
                // JSON转换为List<String>
                JSONArray objects = JSONUtil.parseArray(video.getTags());
                List<String> tags = objects.stream()
                    .map(Object::toString)
                    .collect(Collectors.toList());
                vo.setTags(tags);
            } catch (Exception e) {
                vo.setTags(List.of());
            }
        } else {
            vo.setTags(List.of());
        }

        // TODO: 设置用户相关状态（收藏、完成率等）
        vo.setIsBookmarked(false);
        vo.setCompletionRate(BigDecimal.ZERO);

        return vo;
    }

    /**
     * 转换为视频详情VO
     */
    private VideoDetailVo convertToVideoDetailVo(Video video, Long userId) {
        VideoDetailVo vo = BeanUtil.toBean(video, VideoDetailVo.class);
        vo.setFree(video.getFree() == 1);

        // 解析标签
        if (StrUtil.isNotBlank(video.getTags())) {
            try {
                List<String> tags = Arrays.asList(video.getTags().split(","));
                vo.setTags(tags);
            } catch (Exception e) {
                vo.setTags(List.of());
            }
        } else {
            vo.setTags(List.of());
        }

        // TODO: 设置用户相关状态
        vo.setIsLiked(false);
        vo.setIsCollected(false);
        vo.setIsPurchased(false);
        vo.setIsCompleted(false);
        vo.setCompletionRate(BigDecimal.ZERO);
        vo.setIsFollowed(false);
        vo.setInstructorFollowers(0);

        return vo;
    }

    /**
     * 转换为相关视频VO
     */
    private RelatedVideoVo convertToRelatedVideoVo(Video video) {
        RelatedVideoVo vo = new RelatedVideoVo();
        vo.setId(video.getId());
        vo.setTitle(video.getTitle());
        vo.setInstructor(video.getInstructor());
        vo.setDuration(video.getDuration());
        vo.setThumbnail(video.getThumbnail());
        vo.setViewCount(video.getViewCount());
        vo.setRating(video.getRating());
        vo.setCategory(video.getCategory());
        vo.setFree(video.getFree() == 1);
        vo.setPrice(video.getPrice());

        return vo;
    }

    /**
     * 转换为视频评论VO
     */
    private VideoCommentVo convertToVideoCommentVo(VideoComment comment, Long currentUserId) {
        VideoCommentVo vo = new VideoCommentVo();
        vo.setId(comment.getId());
        vo.setVideoId(comment.getVideoId());
        vo.setUserId(comment.getUserId());
        vo.setParentId(comment.getParentId());
        vo.setContent(comment.getContent());
        vo.setLikeCount(comment.getLikeCount());
        vo.setCreateTime(DateTime.of(comment.getCreateTime()).toLocalDateTime());

        // 简化时间格式
        vo.setPublishTime(comment.getCreateTime().toString());

        // 查询用户信息 (简化处理)
        vo.setUserName("用户" + comment.getUserId());
        vo.setUserAvatar("");

        // 检查当前用户是否已点赞
        boolean isLiked = false;
        if (ObjectUtil.isNotNull(currentUserId)) {
            LambdaQueryWrapper<VideoCommentLike> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(VideoCommentLike::getCommentId, comment.getId())
                .eq(VideoCommentLike::getUserId, currentUserId);
            isLiked = videoCommentLikeMapper.selectCount(wrapper) > 0;
        }
        vo.setIsLiked(isLiked);

        // 查询回复（简化处理，这里暂时不加载回复）
        vo.setReplies(List.of());

        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveVideoPlayRecord(Long videoId, Long userId) {
        if (ObjectUtil.isNull(videoId) || ObjectUtil.isNull(userId)) {
            throw new ServiceException("视频ID和用户ID不能为空");
        }

        try {
            // 1. 先从Redis缓存中获取记录
            VideoPlayRecord cachedRecord = videoPlayRecordCacheService.getCachedPlayRecord(videoId, userId);

            if (cachedRecord != null) {
                // 2. 缓存中存在，更新缓存
                videoPlayRecordCacheService.updatePlayRecord(videoId, userId);

                // 3. 异步更新数据库（这里同步处理，实际可以异步）
                updateDatabaseRecord(videoId, userId, cachedRecord);
            } else {
                // 4. 缓存中不存在，查询数据库
                LambdaQueryWrapper<VideoPlayRecord> wrapper = Wrappers.lambdaQuery();
                wrapper.eq(VideoPlayRecord::getVideoId, videoId)
                    .eq(VideoPlayRecord::getUserId, userId);
                VideoPlayRecord existingRecord = videoPlayRecordMapper.selectOne(wrapper);

                if (existingRecord == null) {
                    // 5. 数据库中也不存在，新增记录
                    VideoPlayRecord record = new VideoPlayRecord();
                    record.setVideoId(videoId);
                    record.setUserId(userId);
                    record.setLastPlayTime(LocalDateTime.now());
                    record.setPlayCount(1);
                    record.setTotalPlayDuration(0);

                    try {
                        // 插入数据库
                        videoPlayRecordMapper.insert(record);

                        // 缓存到Redis
                        videoPlayRecordCacheService.cachePlayRecord(record);
                    } catch (org.springframework.dao.DuplicateKeyException e) {
                        // 处理重复键异常，重新查询并更新记录
                        log.warn("播放记录重复插入，转为更新操作: videoId={}, userId={}", videoId, userId);

                        LambdaQueryWrapper<VideoPlayRecord> retryWrapper = Wrappers.lambdaQuery();
                        retryWrapper.eq(VideoPlayRecord::getVideoId, videoId)
                            .eq(VideoPlayRecord::getUserId, userId);
                        VideoPlayRecord retryRecord = videoPlayRecordMapper.selectOne(retryWrapper);

                        if (retryRecord != null) {
                            retryRecord.setLastPlayTime(LocalDateTime.now());
                            retryRecord.setPlayCount(retryRecord.getPlayCount() + 1);

                            // 更新数据库
                            videoPlayRecordMapper.updateById(retryRecord);

                            // 缓存到Redis
                            videoPlayRecordCacheService.cachePlayRecord(retryRecord);
                        }
                    }
                } else {
                    // 6. 数据库中存在，更新记录
                    existingRecord.setLastPlayTime(LocalDateTime.now());
                    existingRecord.setPlayCount(existingRecord.getPlayCount() + 1);

                    // 更新数据库
                    videoPlayRecordMapper.updateById(existingRecord);

                    // 缓存到Redis
                    videoPlayRecordCacheService.cachePlayRecord(existingRecord);
                }
            }

        } catch (Exception e) {
            log.error("保存视频播放记录失败: videoId={}, userId={}", videoId, userId, e);
            throw new ServiceException("保存播放记录失败");
        }
    }

    /**
     * 更新数据库记录（从缓存数据同步到数据库）
     */
    private void updateDatabaseRecord(Long videoId, Long userId, VideoPlayRecord cachedRecord) {
        try {
            LambdaQueryWrapper<VideoPlayRecord> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(VideoPlayRecord::getVideoId, videoId)
                .eq(VideoPlayRecord::getUserId, userId);
            VideoPlayRecord dbRecord = videoPlayRecordMapper.selectOne(wrapper);

            if (dbRecord == null) {
                // 数据库中不存在，插入新记录
                try {
                    cachedRecord.setId(null);
                    videoPlayRecordMapper.insert(cachedRecord);
                } catch (org.springframework.dao.DuplicateKeyException e) {
                    // 处理重复键异常，重新查询并更新记录
                    log.warn("同步播放记录时重复插入，转为更新操作: videoId={}, userId={}", videoId, userId);

                    LambdaQueryWrapper<VideoPlayRecord> retryWrapper = Wrappers.lambdaQuery();
                    retryWrapper.eq(VideoPlayRecord::getVideoId, videoId)
                        .eq(VideoPlayRecord::getUserId, userId);
                    VideoPlayRecord retryRecord = videoPlayRecordMapper.selectOne(retryWrapper);

                    if (retryRecord != null) {
                        retryRecord.setLastPlayTime(cachedRecord.getLastPlayTime());
                        retryRecord.setPlayCount(cachedRecord.getPlayCount());
                        retryRecord.setTotalPlayDuration(cachedRecord.getTotalPlayDuration());
                        videoPlayRecordMapper.updateById(retryRecord);
                    }
                }
            } else {
                // 数据库中存在，更新记录
                dbRecord.setLastPlayTime(cachedRecord.getLastPlayTime());
                dbRecord.setPlayCount(cachedRecord.getPlayCount());
                dbRecord.setTotalPlayDuration(cachedRecord.getTotalPlayDuration());
                videoPlayRecordMapper.updateById(dbRecord);
            }
        } catch (Exception e) {
            log.error("同步播放记录到数据库失败: videoId={}, userId={}", videoId, userId, e);
        }
    }

    @Override
    public List<VideoDetailVo> getHotVideos(Integer limit) {
        // 设置默认限制
        if (ObjectUtil.isNull(limit) || limit <= 0) {
            limit = 10; // 默认返回10个热门视频
        }
        QueryWrapper<Video> videoQueryWrapper = new QueryWrapper<>();
        videoQueryWrapper.eq("status", 1) // 只查询状态为1的视频
            .orderByDesc("view_count") // 按照观看次数降序
            .last("LIMIT " + limit); // 限制返回数量
        List<Video> videos = videoMapper.selectList(videoQueryWrapper);
        if (videos.isEmpty()) {
            return List.of(); // 如果没有热门视频，返回空列表
        }
        // 转换为VideoDetailVo列表
        return videos.stream()
            .map(video -> convertToVideoDetailVo(video, null)) // 不需要用户ID
            .collect(Collectors.toList());
    }
}
