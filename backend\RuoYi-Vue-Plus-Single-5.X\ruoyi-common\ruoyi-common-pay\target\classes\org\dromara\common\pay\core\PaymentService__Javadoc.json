{"doc": "\n 支付核心服务类\r\n 提供统一的支付服务接口，支持多种支付方式\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "createPayment", "paramTypes": ["org.dromara.common.pay.enums.PaymentMethod", "java.lang.String", "java.lang.String", "java.math.BigDecimal", "java.lang.String"], "doc": "\n 创建支付订单\r\n\r\n @param paymentMethod 支付方式\r\n @param orderNo       订单号\r\n @param subject       商品标题\r\n @param totalAmount   支付金额（元）\r\n @param body          商品描述\r\n @return 支付结果（支付宝返回HTML表单，微信返回支付参数等）\r\n"}, {"name": "queryPaymentStatus", "paramTypes": ["org.dromara.common.pay.enums.PaymentMethod", "java.lang.String"], "doc": "\n 查询支付订单状态\r\n\r\n @param paymentMethod 支付方式\r\n @param orderNo       订单号\r\n @return 支付状态\r\n"}, {"name": "verifyPaymentNotify", "paramTypes": ["org.dromara.common.pay.enums.PaymentMethod", "java.util.Map"], "doc": "\n 验证支付异步通知\r\n\r\n @param paymentMethod 支付方式\r\n @param params        通知参数\r\n @return 通知结果\r\n"}, {"name": "verifyPaymentCallback", "paramTypes": ["org.dromara.common.pay.enums.PaymentMethod", "java.util.Map"], "doc": "\n 验证支付同步回调\r\n\r\n @param paymentMethod 支付方式\r\n @param params        回调参数\r\n @return 回调结果\r\n"}, {"name": "createAlipayPayment", "paramTypes": ["java.lang.String", "java.lang.String", "java.math.BigDecimal", "java.lang.String"], "doc": "\n 创建支付宝支付\r\n"}, {"name": "queryAlipayStatus", "paramTypes": ["java.lang.String"], "doc": "\n 查询支付宝支付状态\r\n"}, {"name": "verifyAlipayNotify", "paramTypes": ["java.util.Map"], "doc": "\n 验证支付宝异步通知\r\n"}, {"name": "verifyAlipayCallback", "paramTypes": ["java.util.Map"], "doc": "\n 验证支付宝同步回调\r\n"}, {"name": "validatePaymentParams", "paramTypes": ["org.dromara.common.pay.enums.PaymentMethod", "java.lang.String", "java.lang.String", "java.math.BigDecimal"], "doc": "\n 验证支付参数\r\n"}], "constructors": []}