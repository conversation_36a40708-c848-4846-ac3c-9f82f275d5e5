package org.dromara.app.domain.vo;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * 慢查询视图对象
 *
 * <AUTHOR>
 * @date 2025-01-19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SlowQueryVo {

    /**
     * 查询ID
     */
    private String queryId;

    /**
     * SQL语句
     */
    private String sqlText;

    /**
     * 执行时间（秒）
     */
    private Double executionTime;

    /**
     * 锁定时间（秒）
     */
    private Double lockTime;

    /**
     * 扫描行数
     */
    private Long rowsExamined;

    /**
     * 返回行数
     */
    private Long rowsSent;

    /**
     * 执行次数
     */
    private Long executionCount;

    /**
     * 平均执行时间（秒）
     */
    private Double averageExecutionTime;

    /**
     * 最大执行时间（秒）
     */
    private Double maxExecutionTime;

    /**
     * 最小执行时间（秒）
     */
    private Double minExecutionTime;

    /**
     * 数据库名
     */
    private String databaseName;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 客户端IP
     */
    private String clientIp;

    /**
     * 查询类型：SELECT/INSERT/UPDATE/DELETE
     */
    private String queryType;

    /**
     * 涉及的表
     */
    private String tablesUsed;

    /**
     * 是否使用索引
     */
    private Boolean indexUsed;

    /**
     * 是否使用临时表
     */
    private Boolean tempTableUsed;

    /**
     * 是否使用文件排序
     */
    private Boolean fileSortUsed;

    /**
     * 查询计划
     */
    private String executionPlan;

    /**
     * 优化建议
     */
    private String optimizationSuggestion;

    /**
     * 首次执行时间
     */
    private LocalDateTime firstExecutionTime;

    /**
     * 最后执行时间
     */
    private LocalDateTime lastExecutionTime;

    /**
     * 查询频率：high/medium/low
     */
    private String frequency;

    /**
     * 影响级别：critical/high/medium/low
     */
    private String impactLevel;

    /**
     * 是否已优化
     */
    private Boolean isOptimized;
}