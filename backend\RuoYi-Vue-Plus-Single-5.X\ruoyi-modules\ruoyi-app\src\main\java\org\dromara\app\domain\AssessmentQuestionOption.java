package org.dromara.app.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serializable;

/**
 * 评估问题选项实体
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("assessment_question_option")
public class AssessmentQuestionOption extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 选项ID
     */
    @TableId(type = IdType.AUTO)
    private Long optionId;

    /**
     * 问题ID
     */
    private Long questionId;

    /**
     * 选项编码（a,b,c,d等）
     */
    private String optionCode;

    /**
     * 选项文本
     */
    private String optionText;

    /**
     * 选项分数
     */
    private Integer optionScore;

    /**
     * 排序顺序
     */
    private Integer sortOrder;
}
