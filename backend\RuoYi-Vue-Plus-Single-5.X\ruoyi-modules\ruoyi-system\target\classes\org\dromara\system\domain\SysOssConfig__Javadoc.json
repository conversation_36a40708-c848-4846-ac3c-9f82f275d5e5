{"doc": "\n 对象存储配置对象 sys_oss_config\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "ossConfigId", "doc": "\n 主键\r\n"}, {"name": "config<PERSON><PERSON>", "doc": "\n 配置key\r\n"}, {"name": "accessKey", "doc": "\n accessKey\r\n"}, {"name": "secret<PERSON>ey", "doc": "\n 秘钥\r\n"}, {"name": "bucketName", "doc": "\n 桶名称\r\n"}, {"name": "prefix", "doc": "\n 前缀\r\n"}, {"name": "endpoint", "doc": "\n 访问站点\r\n"}, {"name": "domain", "doc": "\n 自定义域名\r\n"}, {"name": "isHttps", "doc": "\n 是否https（0否 1是）\r\n"}, {"name": "region", "doc": "\n 域\r\n"}, {"name": "status", "doc": "\n 是否默认（0=是,1=否）\r\n"}, {"name": "ext1", "doc": "\n 扩展字段\r\n"}, {"name": "remark", "doc": "\n 备注\r\n"}, {"name": "accessPolicy", "doc": "\n 桶权限类型(0private 1public 2custom)\r\n"}], "enumConstants": [], "methods": [], "constructors": []}