package org.dromara.app.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 意见反馈对象 feedback
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_feedback")
public class Feedback extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 反馈ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 反馈类型
     */
    private String type;

    /**
     * 反馈内容
     */
    private String content;

    /**
     * 联系方式
     */
    private String contactInfo;

    /**
     * 设备信息
     */
    private String deviceInfo;

    /**
     * 应用版本
     */
    private String appVersion;

    /**
     * 平台信息
     */
    private String platform;

    /**
     * 状态 (PENDING-待处理, PROCESSING-处理中, RESOLVED-已解决, CLOSED-已关闭)
     */
    private String status;

    /**
     * 处理回复
     */
    private String reply;

    /**
     * 处理人
     */
    private String handler;
}
