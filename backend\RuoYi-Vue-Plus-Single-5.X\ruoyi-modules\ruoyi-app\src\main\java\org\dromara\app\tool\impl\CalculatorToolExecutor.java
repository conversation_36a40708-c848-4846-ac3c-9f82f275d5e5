package org.dromara.app.tool.impl;

import org.dromara.app.domain.ToolCall;
import org.dromara.app.service.tool.ToolExecutor;
import org.springframework.stereotype.Component;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * 计算器工具
 *
 * <AUTHOR>
 */
@Component
public class CalculatorToolExecutor implements ToolExecutor {

    private static final Pattern SAFE_EXPRESSION_PATTERN = Pattern.compile("^[0-9+\\-*/().\\s]+$");
    private final ScriptEngine scriptEngine;

    public CalculatorToolExecutor() {
        ScriptEngineManager manager = new ScriptEngineManager();
        this.scriptEngine = manager.getEngineByName("JavaScript");
    }

    @Override
    public String getToolName() {
        return "calculator";
    }

    @Override
    public String getDisplayName() {
        return "计算器";
    }

    @Override
    public String getDescription() {
        return "执行数学计算，支持基本的四则运算";
    }

    @Override
    public String getCategory() {
        return "calculation";
    }

    @Override
    public Map<String, Object> getParameterSchema() {
        Map<String, Object> schema = new HashMap<>();
        schema.put("type", "object");

        Map<String, Object> properties = new HashMap<>();

        // expression参数
        Map<String, Object> expressionParam = new HashMap<>();
        expressionParam.put("type", "string");
        expressionParam.put("description", "要计算的数学表达式，例如：2 + 3 * 4");
        properties.put("expression", expressionParam);

        // precision参数
        Map<String, Object> precisionParam = new HashMap<>();
        precisionParam.put("type", "integer");
        precisionParam.put("description", "小数点后保留位数，默认为2位");
        precisionParam.put("minimum", 0);
        precisionParam.put("maximum", 10);
        precisionParam.put("default", 2);
        properties.put("precision", precisionParam);

        schema.put("properties", properties);
        schema.put("required", new String[]{"expression"});

        return schema;
    }

    @Override
    public ValidationResult validateParameters(Map<String, Object> parameters) {
        if (parameters == null || !parameters.containsKey("expression")) {
            return ValidationResult.failure("缺少必需参数: expression");
        }

        String expression = (String) parameters.get("expression");
        if (expression == null || expression.trim().isEmpty()) {
            return ValidationResult.failure("表达式不能为空");
        }

        // 安全检查：只允许数字、运算符和括号
        if (!SAFE_EXPRESSION_PATTERN.matcher(expression.trim()).matches()) {
            return ValidationResult.failure("表达式包含不安全的字符，只允许数字、+、-、*、/、()和空格");
        }

        // 检查精度参数
        Object precisionObj = parameters.get("precision");
        if (precisionObj != null) {
            try {
                int precision = Integer.parseInt(precisionObj.toString());
                if (precision < 0 || precision > 10) {
                    return ValidationResult.failure("精度必须在0-10之间");
                }
            } catch (NumberFormatException e) {
                return ValidationResult.failure("精度必须是整数");
            }
        }

        return ValidationResult.success();
    }

    @Override
    public ToolCall.ToolCallResult execute(Map<String, Object> parameters, ExecutionContext context) {
        try {
            String expression = (String) parameters.get("expression");
            int precision = 2;

            Object precisionObj = parameters.get("precision");
            if (precisionObj != null) {
                precision = Integer.parseInt(precisionObj.toString());
            }

            // 执行计算
            Object result = scriptEngine.eval(expression.trim());

            if (result == null) {
                return new ToolCall.ToolCallResult(
                    false,
                    null,
                    "计算结果为空",
                    "text",
                    null
                );
            }

            // 格式化结果
            BigDecimal decimal = new BigDecimal(result.toString());
            BigDecimal rounded = decimal.setScale(precision, RoundingMode.HALF_UP);

            Map<String, Object> resultData = new HashMap<>();
            resultData.put("expression", expression);
            resultData.put("result", rounded.doubleValue());
            resultData.put("result_string", rounded.toString());
            resultData.put("precision", precision);

            Map<String, Object> metadata = new HashMap<>();
            metadata.put("tool_name", getToolName());
            metadata.put("execution_time", System.currentTimeMillis());
            metadata.put("expression_length", expression.length());

            return new ToolCall.ToolCallResult(
                true,
                resultData,
                String.format("计算结果: %s = %s", expression, rounded.toString()),
                "json",
                metadata
            );

        } catch (Exception e) {
            return new ToolCall.ToolCallResult(
                false,
                null,
                "计算失败: " + e.getMessage(),
                "text",
                null
            );
        }
    }

    @Override
    public int getTimeoutSeconds() {
        return 10;
    }
}
