<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.system.mapper.SysDeptMapper">

    <resultMap type="org.dromara.system.domain.vo.SysDeptVo" id="SysDeptResult">
    </resultMap>

    <select id="selectDeptList" resultMap="SysDeptResult">
        select
        <if test="ew.getSqlSelect != null">
            ${ew.getSqlSelect}
        </if>
        <if test="ew.getSqlSelect == null">
            *
        </if>
        from sys_dept ${ew.getCustomSqlSegment}
    </select>

    <select id="selectPageDeptList" resultMap="SysDeptResult">
        select
        <if test="ew.getSqlSelect != null">
            ${ew.getSqlSelect}
        </if>
        <if test="ew.getSqlSelect == null">
            *
        </if>
        from sys_dept ${ew.getCustomSqlSegment}
    </select>

    <select id="countDeptById" resultType="Long">
        select count(*)
        from sys_dept
        where del_flag = '0'
          and dept_id = #{deptId}
    </select>

    <select id="selectDeptListByRoleId" resultType="Long">
        select d.dept_id
        from sys_dept d
        left join sys_role_dept rd on d.dept_id = rd.dept_id
        where rd.role_id = #{roleId}
        <if test="deptCheckStrictly">
            and d.dept_id not in (select d.parent_id from sys_dept d inner join sys_role_dept rd on d.dept_id =
            rd.dept_id and rd.role_id = #{roleId})
        </if>
        order by d.parent_id, d.order_num
    </select>

</mapper>
