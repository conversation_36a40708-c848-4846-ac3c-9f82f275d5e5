# 问题管理功能CRUD测试清单

## 测试环境准备

### 1. 数据库准备
```sql
-- 执行建表脚本
source backend/RuoYi-Vue-Plus-Single-5.X/script/sql/question_management.sql;

-- 验证表是否创建成功
SHOW TABLES LIKE 'app_%';
DESC app_question_comment;
DESC app_comment_like;
```

### 2. 应用启动
```bash
cd backend/RuoYi-Vue-Plus-Single-5.X
mvn clean compile
mvn spring-boot:run
```

## API接口测试

### 基础CRUD测试

#### 1. 查询评论列表
```http
GET /system/questioncomment/list?pageNum=1&pageSize=10
Authorization: Bearer {token}
```

#### 2. 获取评论详情
```http
GET /system/questioncomment/1
Authorization: Bearer {token}
```

#### 3. 新增评论
```http
POST /system/questioncomment
Authorization: Bearer {token}
Content-Type: application/json

{
    "questionId": 1,
    "userId": 1,
    "content": "这是一个测试评论",
    "parentId": null
}
```

#### 4. 修改评论
```http
PUT /system/questioncomment
Authorization: Bearer {token}
Content-Type: application/json

{
    "commentId": 1,
    "content": "修改后的评论内容"
}
```

#### 5. 删除评论
```http
DELETE /system/questioncomment/1
Authorization: Bearer {token}
```

### 高级功能测试

#### 6. 获取题目评论（含回复）
```http
GET /system/questioncomment/question/1?page=1&pageSize=10
Authorization: Bearer {token}
```

#### 7. 搜索评论
```http
GET /system/questioncomment/search?keyword=测试&pageNum=1&pageSize=10
Authorization: Bearer {token}
```

#### 8. 评论统计
```http
GET /system/questioncomment/statistics/1
Authorization: Bearer {token}
```

#### 9. 导出评论
```http
POST /system/questioncomment/export
Authorization: Bearer {token}
Content-Type: application/json

{
    "questionId": 1
}
```

### 管理功能测试

#### 10. 审核评论
```http
POST /system/questioncomment/1/audit
Authorization: Bearer {token}
Content-Type: application/json

{
    "status": "1",
    "reason": "审核通过"
}
```

#### 11. 置顶评论
```http
POST /system/questioncomment/1/toggle-top
Authorization: Bearer {token}
Content-Type: application/json

{
    "isTop": true
}
```

#### 12. 批量删除评论
```http
POST /system/questioncomment/batch-delete
Authorization: Bearer {token}
Content-Type: application/json

[1, 2, 3]
```

## 数据库验证测试

### 1. 评论数据一致性
```sql
-- 检查评论数与题目表中的comment_count是否一致
SELECT 
    q.question_id,
    q.comment_count as question_comment_count,
    COUNT(c.comment_id) as actual_comment_count
FROM app_question q
LEFT JOIN app_question_comment c ON q.question_id = c.question_id AND c.status = '0'
GROUP BY q.question_id
HAVING question_comment_count != actual_comment_count;
```

### 2. 回复数据一致性
```sql
-- 检查父评论的reply_count是否正确
SELECT 
    parent.comment_id,
    parent.reply_count as parent_reply_count,
    COUNT(child.comment_id) as actual_reply_count
FROM app_question_comment parent
LEFT JOIN app_question_comment child ON parent.comment_id = child.parent_id AND child.status = '0'
WHERE parent.parent_id IS NULL AND parent.status = '0'
GROUP BY parent.comment_id
HAVING parent_reply_count != actual_reply_count;
```

### 3. 点赞数据一致性
```sql
-- 检查评论的like_count是否正确
SELECT 
    c.comment_id,
    c.like_count as comment_like_count,
    COUNT(l.like_id) as actual_like_count
FROM app_question_comment c
LEFT JOIN app_comment_like l ON c.comment_id = l.comment_id AND l.status = '1'
GROUP BY c.comment_id
HAVING comment_like_count != actual_like_count;
```

## 业务逻辑测试

### 1. 评论层级测试
- 创建主评论
- 创建回复评论
- 验证父评论的reply_count自动增加
- 删除回复评论
- 验证父评论的reply_count自动减少

### 2. 点赞功能测试
- 对评论进行点赞
- 验证like_count增加
- 验证点赞记录插入
- 取消点赞
- 验证like_count减少
- 验证点赞记录状态更新

### 3. 权限控制测试
- 用户只能修改/删除自己的评论
- 管理员可以审核/置顶/批量删除任何评论
- 未登录用户无法访问接口

## 性能测试

### 1. 大数据量测试
```sql
-- 插入大量测试数据
INSERT INTO app_question_comment (question_id, user_id, content, like_count, reply_count, status, create_time)
SELECT 
    1 as question_id,
    (RAND() * 100 + 1) as user_id,
    CONCAT('测试评论内容 ', @row_number := @row_number + 1) as content,
    FLOOR(RAND() * 50) as like_count,
    0 as reply_count,
    '0' as status,
    NOW() as create_time
FROM 
    (SELECT @row_number := 0) r,
    information_schema.tables t1,
    information_schema.tables t2
LIMIT 10000;
```

### 2. 分页查询性能
```http
GET /system/questioncomment/question/1?page=1&pageSize=100
GET /system/questioncomment/question/1?page=50&pageSize=100
GET /system/questioncomment/question/1?page=100&pageSize=100
```

## 异常处理测试

### 1. 参数验证
- 传入无效的commentId
- 传入空的content
- 传入不存在的questionId
- 传入超长的content（>1000字符）

### 2. 权限异常
- 尝试修改他人评论
- 尝试删除他人评论
- 无权限访问管理接口

### 3. 数据库异常
- 外键约束测试
- 并发操作测试

## 测试结果记录

### 通过的测试项
- [ ] 基础CRUD功能
- [ ] 高级查询功能
- [ ] 管理功能
- [ ] 数据一致性
- [ ] 业务逻辑
- [ ] 权限控制
- [ ] 性能表现
- [ ] 异常处理

### 发现的问题
1. 
2. 
3. 

### 修复建议
1. 
2. 
3. 

## 总结

测试完成度：____%
整体评价：
下一步计划：
