package org.dromara.app.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.digest.BCrypt;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.AppUser;
import org.dromara.app.domain.dto.AppAuthDto;
import org.dromara.app.domain.vo.AppUserInfoVo;
import org.dromara.app.mapper.AppUserMapper;
import org.dromara.app.service.IAppUserService;
import org.dromara.common.core.constant.GlobalConstants;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.exception.user.CaptchaExpireException;
import org.dromara.common.core.exception.user.UserException;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.redis.utils.RedisUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 应用用户Service业务层处理
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class AppUserServiceImpl implements IAppUserService {

    private final AppUserMapper appUserMapper;

    @Override
    public AppUser selectByPhone(String phone) {
        return appUserMapper.selectOne(new LambdaQueryWrapper<AppUser>()
            .eq(AppUser::getPhone, phone)
            .eq(AppUser::getDelFlag, "0"));
    }

    @Override
    public AppUser selectByEmail(String email) {
        return appUserMapper.selectOne(new LambdaQueryWrapper<AppUser>()
            .eq(AppUser::getEmail, email)
            .eq(AppUser::getDelFlag, "0"));
    }

    @Override
    public boolean checkPhoneExists(String phone) {
        return ObjectUtil.isNotNull(selectByPhone(phone));
    }

    @Override
    public boolean checkEmailExists(String email) {
        return ObjectUtil.isNotNull(selectByEmail(email));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AppUserInfoVo register(AppAuthDto authDto) {
        // 验证邮箱验证码
        if (!validateEmailCode(authDto.getEmail(), authDto.getCode())) {
            throw new CaptchaExpireException();
        }

        // 检查邮箱是否已注册
        if (checkEmailExists(authDto.getEmail())) {
            throw new ServiceException("邮箱已被注册");
        }

        // 检查学号是否已存在
        AppUser existingUser = appUserMapper.selectOne(new LambdaQueryWrapper<AppUser>()
            .eq(AppUser::getStudentId, authDto.getStudentId())
            .eq(AppUser::getDelFlag, "0"));
        if (ObjectUtil.isNotNull(existingUser)) {
            throw new ServiceException("学号已被注册");
        }

        // 创建新用户
        AppUser user = new AppUser();
        user.setEmail(authDto.getEmail());
        user.setRealName(authDto.getRealName());
        user.setStudentId(authDto.getStudentId());
        user.setMajor(authDto.getMajor());
        user.setGrade(authDto.getGrade());
        user.setPassword(BCrypt.hashpw(authDto.getPassword()));
        user.setStatus("0");
        user.setDelFlag("0");
        user.setRegisteredAt(DateUtils.getNowDate());
        user.setCreateTime(DateUtils.getNowDate());

        appUserMapper.insert(user);

        // 删除验证码
        RedisUtils.deleteObject(GlobalConstants.CAPTCHA_CODE_KEY + authDto.getEmail());

        // 构建返回信息
        AppUserInfoVo userInfo = BeanUtil.toBean(user, AppUserInfoVo.class);
        userInfo.setId(user.getUserId().toString());
        userInfo.setName(user.getRealName());
        return userInfo;
    }

    @Override
    public AppUserInfoVo loginByPhone(String phone, String code) {
        // 验证短信验证码
        if (!validateSmsCode(phone, code)) {
            throw new CaptchaExpireException();
        }

        // 查找用户
        AppUser user = selectByPhone(phone);
        if (ObjectUtil.isNull(user)) {
            throw new UserException("用户不存在");
        }

        if ("1".equals(user.getStatus())) {
            throw new UserException("用户已被停用");
        }

        // 删除验证码
        RedisUtils.deleteObject(GlobalConstants.CAPTCHA_CODE_KEY + phone);

        // 构建返回信息
        AppUserInfoVo userInfo = BeanUtil.toBean(user, AppUserInfoVo.class);
        userInfo.setId(user.getUserId().toString());
        userInfo.setName(user.getRealName());
        userInfo.setTokenValue(StpUtil.getTokenValue());
        userInfo.setTokenName(StpUtil.getTokenName());
        return userInfo;
    }

    @Override
    public AppUserInfoVo loginByPassword(String phone, String password) {
        // 查找用户
        AppUser user = selectByPhone(phone);
        if (ObjectUtil.isNull(user)) {
            throw new UserException("用户不存在");
        }

        if ("1".equals(user.getStatus())) {
            throw new UserException("用户已被停用");
        }
        String hashpw = BCrypt.hashpw(password);
        log.info("hashpw: {}", hashpw);

        // 验证密码
        if (StringUtils.isBlank(user.getPassword())) {
            throw new UserException("用户未设置密码，请使用验证码登录");
        }

        if (!BCrypt.checkpw(password, user.getPassword())) {
            throw new UserException("密码错误");
        }

        // 构建返回信息
        AppUserInfoVo userInfo = BeanUtil.toBean(user, AppUserInfoVo.class);
        userInfo.setId(user.getUserId().toString());
        userInfo.setName(user.getRealName());
        return userInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resetPassword(String phone, String code, String newPassword) {
        // 验证短信验证码
        if (!validateSmsCode(phone, code)) {
            throw new CaptchaExpireException();
        }

        // 查找用户
        AppUser user = selectByPhone(phone);
        if (ObjectUtil.isNull(user)) {
            throw new UserException("用户不存在");
        }

        // 更新密码
        user.setPassword(BCrypt.hashpw(newPassword));
        user.setUpdateTime(DateUtils.getNowDate());
        appUserMapper.updateById(user);

        // 删除验证码
        RedisUtils.deleteObject(GlobalConstants.CAPTCHA_CODE_KEY + phone);
    }

    @Override
    public AppUserInfoVo getUserInfo(Long userId) {
        AppUser user = appUserMapper.selectById(userId);
        if (ObjectUtil.isNull(user)) {
            throw new UserException("用户不存在");
        }

        AppUserInfoVo userInfo = BeanUtil.toBean(user, AppUserInfoVo.class);
        userInfo.setId(user.getUserId().toString());
        userInfo.setName(user.getRealName());
        return userInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateLoginInfo(Long userId, String loginIp) {
        AppUser user = new AppUser();
        user.setUserId(userId);
        user.setLoginIp(loginIp);
        user.setLoginDate(DateUtils.getNowDate());
        user.setUpdateTime(DateUtils.getNowDate());
        appUserMapper.updateById(user);
    }

    /**
     * 验证短信验证码
     *
     * @param phone   手机号
     * @param smsCode 短信验证码
     * @return 验证结果
     */
    private boolean validateSmsCode(String phone, String smsCode) {
        String code = RedisUtils.getCacheObject(GlobalConstants.CAPTCHA_CODE_KEY + phone);
        return StringUtils.isNotBlank(code) && code.equals(smsCode);
    }

    /**
     * 验证邮箱验证码
     *
     * @param email     邮箱
     * @param emailCode 邮箱验证码
     * @return 验证结果
     */
    private boolean validateEmailCode(String email, String emailCode) {
        String code = RedisUtils.getCacheObject(GlobalConstants.CAPTCHA_CODE_KEY + email);
        return StringUtils.isNotBlank(code) && code.equals(emailCode);
    }
}
