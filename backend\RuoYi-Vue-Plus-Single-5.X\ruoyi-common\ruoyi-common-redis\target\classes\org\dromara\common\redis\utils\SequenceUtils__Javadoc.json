{"doc": "\n 发号器工具类\r\n\r\n <AUTHOR>\r\n @date 2024-12-10\r\n", "fields": [{"name": "DEFAULT_INIT_VALUE", "doc": "\n 默认初始值\r\n"}, {"name": "DEFAULT_STEP_VALUE", "doc": "\n 默认步长\r\n"}, {"name": "DEFAULT_EXPIRE_TIME_DAY", "doc": "\n 默认过期时间-天\r\n"}, {"name": "DEFAULT_EXPIRE_TIME_MINUTE", "doc": "\n 默认过期时间-分钟\r\n"}, {"name": "REDISSON_CLIENT", "doc": "\n 获取Redisson客户端实例\r\n"}], "enumConstants": [], "methods": [{"name": "getIdGenerator", "paramTypes": ["java.lang.String", "java.time.Duration", "java.lang.Long", "java.lang.Long"], "doc": "\n 获取ID生成器\r\n\r\n @param key        业务key\r\n @param expireTime 过期时间\r\n @param initValue  ID初始值\r\n @param stepValue  ID步长\r\n @return ID生成器\r\n"}, {"name": "nextId", "paramTypes": ["java.lang.String", "java.time.Duration", "java.lang.Long", "java.lang.Long"], "doc": "\n 获取指定业务key的唯一id\r\n\r\n @param key        业务key\r\n @param expireTime 过期时间\r\n @param initValue  ID初始值\r\n @param stepValue  ID步长\r\n @return 唯一id\r\n"}, {"name": "nextIdStr", "paramTypes": ["java.lang.String", "java.time.Duration", "java.lang.Long", "java.lang.Long"], "doc": "\n 获取指定业务key的唯一id字符串\r\n\r\n @param key        业务key\r\n @param expireTime 过期时间\r\n @param initValue  ID初始值\r\n @param stepValue  ID步长\r\n @return 唯一id\r\n"}, {"name": "nextId", "paramTypes": ["java.lang.String", "java.time.Duration"], "doc": "\n 获取指定业务key的唯一id (ID初始值=1,ID步长=1)\r\n\r\n @param key        业务key\r\n @param expireTime 过期时间\r\n @return 唯一id\r\n"}, {"name": "nextIdStr", "paramTypes": ["java.lang.String", "java.time.Duration"], "doc": "\n 获取指定业务key的唯一id字符串 (ID初始值=1,ID步长=1)\r\n\r\n @param key        业务key\r\n @param expireTime 过期时间\r\n @return 唯一id\r\n"}, {"name": "nextPaddedIdStr", "paramTypes": ["java.lang.String", "java.time.Duration", "java.lang.Integer"], "doc": "\n 获取指定业务key的唯一id字符串 (ID初始值=1,ID步长=1)，不足位数自动补零\r\n\r\n @param key        业务key\r\n @param expireTime 过期时间\r\n @param width      位数，不足左补0\r\n @return 补零后的唯一id字符串\r\n"}, {"name": "nextIdDate", "paramTypes": [], "doc": "\n 获取 yyyyMMdd 开头的唯一id\r\n\r\n @return 唯一id\r\n"}, {"name": "nextIdDate", "paramTypes": ["java.lang.String"], "doc": "\n 获取 prefix + yyyyMMdd 开头的唯一id\r\n\r\n @param prefix 业务前缀\r\n @return 唯一id\r\n"}, {"name": "nextIdDateTime", "paramTypes": [], "doc": "\n 获取 yyyyMMddHHmmss 开头的唯一id\r\n\r\n @return 唯一id\r\n"}, {"name": "nextIdDateTime", "paramTypes": ["java.lang.String"], "doc": "\n 获取 prefix + yyyyMMddHHmmss 开头的唯一id\r\n\r\n @param prefix 业务前缀\r\n @return 唯一id\r\n"}], "constructors": []}