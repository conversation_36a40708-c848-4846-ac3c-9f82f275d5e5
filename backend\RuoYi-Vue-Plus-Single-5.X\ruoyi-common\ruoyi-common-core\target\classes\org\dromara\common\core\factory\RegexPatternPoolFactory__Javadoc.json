{"doc": "\n 正则表达式模式池工厂\r\n <p>初始化的时候将正则表达式加入缓存池当中</p>\r\n <p>提高正则表达式的性能，避免重复编译相同的正则表达式</p>\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "DICTIONARY_TYPE", "doc": "\n 字典类型必须以字母开头，且只能为（小写字母，数字，下滑线）\r\n"}, {"name": "ID_CARD_LAST_6", "doc": "\n 身份证号码（后6位）\r\n"}, {"name": "QQ_NUMBER", "doc": "\n QQ号码\r\n"}, {"name": "POSTAL_CODE", "doc": "\n 邮政编码\r\n"}, {"name": "ACCOUNT", "doc": "\n 注册账号\r\n"}, {"name": "PASSWORD", "doc": "\n 密码：包含至少8个字符，包括大写字母、小写字母、数字和特殊字符\r\n"}, {"name": "STATUS", "doc": "\n 通用状态（0表示正常，1表示停用）\r\n"}], "enumConstants": [], "methods": [], "constructors": []}