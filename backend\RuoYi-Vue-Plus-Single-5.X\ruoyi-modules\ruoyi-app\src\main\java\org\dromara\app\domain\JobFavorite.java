package org.dromara.app.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户岗位收藏对象 app_job_favorite
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Data
@TableName("app_job_favorite")
public class JobFavorite implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 收藏ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 岗位ID
     */
    private Long jobId;

    /**
     * 收藏时间
     */
    private LocalDateTime createTime;

}
