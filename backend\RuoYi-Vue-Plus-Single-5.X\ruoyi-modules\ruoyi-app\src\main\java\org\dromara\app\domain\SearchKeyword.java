package org.dromara.app.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 搜索关键词统计对象 app_search_keyword
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Data
@TableName("app_search_keyword")
public class SearchKeyword implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关键词ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 搜索关键词
     */
    private String keyword;

    /**
     * 搜索次数
     */
    private Integer searchCount;

    /**
     * 最后搜索时间
     */
    private LocalDateTime lastSearchTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

}
