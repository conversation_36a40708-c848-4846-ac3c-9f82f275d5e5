{"doc": "\n 对象存储配置Service接口\r\n\r\n <AUTHOR>\r\n <AUTHOR>\r\n @date 2021-08-13\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "init", "paramTypes": [], "doc": "\n 初始化OSS配置\r\n"}, {"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": "\n 查询单个\r\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.system.domain.bo.SysOssConfigBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 查询列表\r\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.system.domain.bo.SysOssConfigBo"], "doc": "\n 根据新增业务对象插入对象存储配置\r\n\r\n @param bo 对象存储配置新增业务对象\r\n @return 结果\r\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.system.domain.bo.SysOssConfigBo"], "doc": "\n 根据编辑业务对象修改对象存储配置\r\n\r\n @param bo 对象存储配置编辑业务对象\r\n @return 结果\r\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": "\n 校验并删除数据\r\n\r\n @param ids     主键集合\r\n @param isValid 是否校验,true-删除前校验,false-不校验\r\n @return 结果\r\n"}, {"name": "updateOssConfigStatus", "paramTypes": ["org.dromara.system.domain.bo.SysOssConfigBo"], "doc": "\n 启用停用状态\r\n"}], "constructors": []}