package org.dromara.app.domain.vo;

import lombok.Data;
import org.dromara.app.domain.enums.ActivityType;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 活动会话响应VO
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Data
public class ActivitySessionVO {

    /**
     * 会话ID
     */
    private String id;

    /**
     * 活动类型
     */
    private ActivityType type;

    /**
     * 开始时间戳
     */
    private Long startTime;

    /**
     * 结束时间戳
     */
    private Long endTime;

    /**
     * 持续时长(毫秒)
     */
    private Long duration;

    /**
     * 是否活跃
     */
    private Boolean isActive;

    /**
     * 活动ID
     */
    private String activityId;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 分类ID
     */
    private String categoryId;

    /**
     * 分类名称
     */
    private String categoryName;

    /**
     * 额外元数据
     */
    private Map<String, Object> metadata;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
