package org.dromara.app.service.impl;

import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.VideoPlayRecord;
import org.dromara.common.redis.utils.RedisUtils;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 视频播放记录缓存服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class VideoPlayRecordCacheService {

    // Redis键前缀
    private static final String VIDEO_PLAY_RECORD_KEY = "video:play:record:";
    private static final String USER_VIDEO_HISTORY_KEY = "user:video:history:";
    private static final Duration CACHE_EXPIRE_TIME = Duration.ofDays(7); // 缓存7天

    /**
     * 构建播放记录缓存键
     */
    private String buildRecordKey(Long videoId, Long userId) {
        return VIDEO_PLAY_RECORD_KEY + videoId + ":" + userId;
    }

    /**
     * 构建用户历史记录缓存键
     */
    private String buildUserHistoryKey(Long userId) {
        return USER_VIDEO_HISTORY_KEY + userId;
    }

    /**
     * 缓存播放记录
     */
    public void cachePlayRecord(VideoPlayRecord record) {
        if (ObjectUtil.isNull(record) || ObjectUtil.isNull(record.getVideoId()) || ObjectUtil.isNull(record.getUserId())) {
            return;
        }

        String recordKey = buildRecordKey(record.getVideoId(), record.getUserId());
        String userHistoryKey = buildUserHistoryKey(record.getUserId());

        try {
            // 缓存详细播放记录
            RedisUtils.setCacheObject(recordKey, record, CACHE_EXPIRE_TIME);

            // 添加到用户历史记录集合中（使用score为时间戳的有序集合）
            long timestamp = System.currentTimeMillis();
            RedisUtils.getClient().getScoredSortedSet(userHistoryKey)
                .add(timestamp, record.getVideoId());

            // 设置用户历史记录过期时间
            RedisUtils.expire(userHistoryKey, CACHE_EXPIRE_TIME);

            log.debug("视频播放记录已缓存: videoId={}, userId={}", record.getVideoId(), record.getUserId());
        } catch (Exception e) {
            log.error("缓存视频播放记录失败: videoId={}, userId={}", record.getVideoId(), record.getUserId(), e);
        }
    }

    /**
     * 获取播放记录缓存
     */
    public VideoPlayRecord getCachedPlayRecord(Long videoId, Long userId) {
        if (ObjectUtil.isNull(videoId) || ObjectUtil.isNull(userId)) {
            return null;
        }

        String recordKey = buildRecordKey(videoId, userId);

        try {
            return RedisUtils.getCacheObject(recordKey);
        } catch (Exception e) {
            log.error("获取视频播放记录缓存失败: videoId={}, userId={}", videoId, userId, e);
            return null;
        }
    }

    /**
     * 更新播放记录缓存
     */
    public void updatePlayRecord(Long videoId, Long userId) {
        String recordKey = buildRecordKey(videoId, userId);
        String userHistoryKey = buildUserHistoryKey(userId);

        try {
            // 获取现有记录
            VideoPlayRecord cachedRecord = RedisUtils.getCacheObject(recordKey);

            if (cachedRecord != null) {
                // 更新现有记录
                cachedRecord.setLastPlayTime(LocalDateTime.now());
                cachedRecord.setPlayCount(cachedRecord.getPlayCount() + 1);
                RedisUtils.setCacheObject(recordKey, cachedRecord, CACHE_EXPIRE_TIME);
            } else {
                // 创建新记录
                VideoPlayRecord newRecord = new VideoPlayRecord();
                newRecord.setVideoId(videoId);
                newRecord.setUserId(userId);
                newRecord.setLastPlayTime(LocalDateTime.now());
                newRecord.setPlayCount(1);
                newRecord.setTotalPlayDuration(0);
                RedisUtils.setCacheObject(recordKey, newRecord, CACHE_EXPIRE_TIME);
            }

            // 更新用户历史记录
            long timestamp = System.currentTimeMillis();
            RedisUtils.getClient().getScoredSortedSet(userHistoryKey)
                .add(timestamp, videoId);
            RedisUtils.expire(userHistoryKey, CACHE_EXPIRE_TIME);

            log.debug("视频播放记录缓存已更新: videoId={}, userId={}", videoId, userId);
        } catch (Exception e) {
            log.error("更新视频播放记录缓存失败: videoId={}, userId={}", videoId, userId, e);
        }
    }

    /**
     * 获取用户观看历史视频ID列表（按时间倒序）
     */
    public List<Long> getUserVideoHistory(Long userId, int offset, int limit) {
        if (ObjectUtil.isNull(userId)) {
            return List.of();
        }

        String userHistoryKey = buildUserHistoryKey(userId);

        try {
            // 从有序集合中获取最新的视频ID（按分数倒序）
            Set<Object> videoIds = (Set<Object>) RedisUtils.getClient().getScoredSortedSet(userHistoryKey)
                .valueRangeReversed(offset, offset + limit - 1);

            return videoIds.stream()
                .map(id -> Long.valueOf(id.toString()))
                .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取用户视频历史记录失败: userId={}", userId, e);
            return List.of();
        }
    }

    /**
     * 删除播放记录缓存
     */
    public void deletePlayRecord(Long videoId, Long userId) {
        if (ObjectUtil.isNull(videoId) || ObjectUtil.isNull(userId)) {
            return;
        }

        String recordKey = buildRecordKey(videoId, userId);
        String userHistoryKey = buildUserHistoryKey(userId);

        try {
            RedisUtils.deleteObject(recordKey);
            RedisUtils.getClient().getScoredSortedSet(userHistoryKey).remove(videoId);

            log.debug("视频播放记录缓存已删除: videoId={}, userId={}", videoId, userId);
        } catch (Exception e) {
            log.error("删除视频播放记录缓存失败: videoId={}, userId={}", videoId, userId, e);
        }
    }

    /**
     * 批量预热用户历史记录到缓存
     */
    public void preloadUserHistory(Long userId, List<VideoPlayRecord> records) {
        if (ObjectUtil.isNull(userId) || ObjectUtil.isEmpty(records)) {
            return;
        }

        try {
            for (VideoPlayRecord record : records) {
                cachePlayRecord(record);
            }
            log.debug("用户历史记录缓存预热完成: userId={}, recordCount={}", userId, records.size());
        } catch (Exception e) {
            log.error("用户历史记录缓存预热失败: userId={}", userId, e);
        }
    }

    /**
     * 检查播放记录是否存在于缓存中
     */
    public boolean isRecordCached(Long videoId, Long userId) {
        if (ObjectUtil.isNull(videoId) || ObjectUtil.isNull(userId)) {
            return false;
        }

        String recordKey = buildRecordKey(videoId, userId);
        return RedisUtils.isExistsObject(recordKey);
    }
}
