package org.dromara.app.domain;

import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;
import java.math.BigDecimal;
import java.util.List;

/**
 * 岗位信息对象 app_job
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "app_job",autoResultMap = true)
public class Job extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 岗位ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 分类ID
     */
    private Long categoryId;

    /**
     * 岗位名称
     */
    private String name;

    /**
     * 公司名称
     */
    private String company;

    /**
     * 公司Logo
     */
    private String logo;

    /**
     * 难度等级（1-5）
     */
    private Integer difficulty;

    /**
     * 面试时长（分钟）
     */
    private Integer duration;

    /**
     * 题目数量
     */
    private Integer questionCount;

    /**
     * 标签（JSON数组）
     */
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private List<String>  tags;

    /**
     * 岗位描述
     */
    private String description;

    /**
     * 面试人数
     */
    private Integer interviewers;

    /**
     * 通过率（百分比）
     */
    private BigDecimal passRate;

    /**
     * 浏览次数
     */
    private Integer viewCount;

    /**
     * 收藏次数
     */
    private Integer favoriteCount;

    /**
     * 排序号
     */
    @OrderBy(asc = true, sort = 1)
    private Integer sortOrder;

    /**
     * 状态（0正常 1停用）
     */
    private String status;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    private String delFlag;

    /**
     * 技术领域分类
     */
    private String technicalDomain;

    /**
     * 岗位级别（初级/中级/高级/专家）
     */
    private String level;

    /**
     * 工作经验要求（年）
     */
    private Integer experienceYears;

    /**
     * 学历要求
     */
    private String educationRequirement;

    /**
     * 薪资范围
     */
    private String salaryRange;

    /**
     * 工作地点
     */
    private String location;

    /**
     * 核心技能要求（JSON数组）
     */
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private List<String>  coreSkills;

    /**
     * 岗位职责
     */
    private String responsibilities;

    /**
     * 任职要求
     */
    private String requirements;

}
