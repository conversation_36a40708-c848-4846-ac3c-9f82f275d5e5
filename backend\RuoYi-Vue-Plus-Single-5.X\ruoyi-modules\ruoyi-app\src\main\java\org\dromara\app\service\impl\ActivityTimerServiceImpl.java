package org.dromara.app.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.ActivitySession;
import org.dromara.app.domain.ActivityStatistics;
import org.dromara.app.domain.ActivitySummary;
import org.dromara.app.domain.dto.*;
import org.dromara.app.domain.enums.ActivityType;
import org.dromara.app.domain.vo.ActivityHistoryVO;
import org.dromara.app.domain.vo.ActivitySessionVO;
import org.dromara.app.domain.vo.ActivityStatisticsVO;
import org.dromara.app.mapper.ActivitySessionMapper;
import org.dromara.app.mapper.ActivityStatisticsMapper;
import org.dromara.app.mapper.ActivitySummaryMapper;
import org.dromara.app.service.IActivityTimerService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 活动时长记录服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ActivityTimerServiceImpl implements IActivityTimerService {

    private final ActivitySessionMapper sessionMapper;
    private final ActivityStatisticsMapper statisticsMapper;
    private final ActivitySummaryMapper summaryMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean startActivity(ActivityStartRequest request) {
        try {
            // 检查是否已存在相同的会话ID
            ActivitySession existingSession = sessionMapper.selectBySessionId(request.getSessionId());
            if (existingSession != null) {
                log.warn("会话ID已存在: {}", request.getSessionId());
                return false;
            }

            // 结束用户的其他活跃会话
            List<ActivitySession> activeSessions = sessionMapper.selectActiveSessionsByUserId(request.getUserId());
            for (ActivitySession activeSession : activeSessions) {
                activeSession.endSession();
                sessionMapper.updateById(activeSession);

                // 更新统计数据
                updateStatisticsAfterSession(activeSession);
            }

            // 创建新的活动会话
            ActivitySession session = new ActivitySession();
            session.setSessionId(request.getSessionId());
            session.setUserId(request.getUserId());
            session.setActivityType(request.getType());
            session.setActivityId(request.getActivityId());
            session.setActivityName(request.getActivityName());
            session.setCategoryId(request.getCategoryId());
            session.setCategoryName(request.getCategoryName());
            session.setStartTime(LocalDateTime.now());
            session.setIsActive(true);
            session.setDuration(0L);
            session.setMetadata(request.getMetadata());

            int result = sessionMapper.insert(session);

            // 确保用户有活动总览记录
            ensureUserSummaryExists(request.getUserId());

            return result > 0;
        } catch (Exception e) {
            log.error("开始活动记录失败", e);
            throw new RuntimeException("开始活动记录失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean pauseActivity(ActivityPauseRequest request) {
        try {
            ActivitySession session = sessionMapper.selectBySessionId(request.getSessionId());
            if (session == null) {
                log.warn("会话不存在: {}", request.getSessionId());
                return false;
            }

            if (!session.getUserId().equals(request.getUserId())) {
                log.warn("用户ID不匹配: 会话用户ID={}, 请求用户ID={}",
                    session.getUserId(), request.getUserId());
                return false;
            }

            if (!Boolean.TRUE.equals(session.getIsActive())) {
                log.warn("会话已非活跃: {}", request.getSessionId());
                return false;
            }

            // 更新会话状态
            session.setDuration(request.getDuration());
            session.setIsActive(false);
            session.setEndTime(LocalDateTime.now());

            int result = sessionMapper.updateById(session);

            // 更新统计数据
            updateStatisticsAfterSession(session);


            return result > 0;
        } catch (Exception e) {
            log.error("暂停活动记录失败", e);
            throw new RuntimeException("暂停活动记录失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean endActivity(ActivityEndRequest request) {
        try {
            ActivitySession session = sessionMapper.selectBySessionId(request.getSessionId());
            if (session == null) {
                log.warn("会话不存在: {}", request.getSessionId());
                return false;
            }

            if (!session.getUserId().equals(request.getUserId())) {
                log.warn("用户ID不匹配: 会话用户ID={}, 请求用户ID={}",
                    session.getUserId(), request.getUserId());
                return false;
            }

            // 如果会话还在活跃状态，先计算时长
            if (Boolean.TRUE.equals(session.getIsActive())) {
                session.setDuration(session.calculateDuration());
            }

            session.endSession();
            int result = sessionMapper.updateById(session);

            // 更新统计数据
            updateStatisticsAfterSession(session);


            return result > 0;
        } catch (Exception e) {
            log.error("结束活动记录失败", e);
            throw new RuntimeException("结束活动记录失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean syncSession(ActivitySyncRequest request) {
        try {
            ActivitySession session = sessionMapper.selectBySessionId(request.getSessionId());
            if (session == null) {
                log.warn("会话不存在: {}", request.getSessionId());
                return false;
            }

            if (!session.getUserId().equals(request.getUserId())) {
                log.warn("用户ID不匹配: 会话用户ID={}, 请求用户ID={}",
                    session.getUserId(), request.getUserId());
                return false;
            }

            // 更新会话时长
            int result = sessionMapper.updateSessionDuration(request.getSessionId(), request.getDuration());

            log.debug("同步会话成功: 用户ID={}, 会话ID={}, 时长={}ms",
                request.getUserId(), request.getSessionId(), request.getDuration());

            return result > 0;
        } catch (Exception e) {
            log.error("同步会话失败", e);
            throw new RuntimeException("同步会话失败: " + e.getMessage());
        }
    }

    @Override
    public ActivityStatisticsVO getStatistics(ActivityStatisticsRequest request) {
        try {
            ActivityStatisticsVO result = new ActivityStatisticsVO();

            // 查询各时间段的活动时长
            result.setToday(sessionMapper.selectTodayDuration(request.getUserId(), request.getType()));
            result.setWeek(sessionMapper.selectWeekDuration(request.getUserId(), request.getType()));
            result.setMonth(sessionMapper.selectMonthDuration(request.getUserId(), request.getType()));
            result.setTotal(sessionMapper.selectTotalDuration(request.getUserId(), request.getType()));

            // 如果没有指定类型，查询各类型的统计
            if (request.getType() == null) {
                Map<ActivityType, ActivityStatisticsVO.ActivityTypeStatistics> byType = new HashMap<>();
                for (ActivityType type : ActivityType.values()) {
                    Long totalDuration = sessionMapper.selectTotalDuration(request.getUserId(), type);
                    ActivityStatisticsVO.ActivityTypeStatistics typeStats =
                        new ActivityStatisticsVO.ActivityTypeStatistics();
                    typeStats.setTotal(totalDuration);
                    byType.put(type, typeStats);
                }
                result.setByType(byType);
            }

            // 设置格式化的时长字符串
            result.setFormattedDurations();

            return result;
        } catch (Exception e) {
            log.error("获取活动统计失败", e);
            throw new RuntimeException("获取活动统计失败: " + e.getMessage());
        }
    }

    @Override
    public ActivityHistoryVO getHistory(ActivityHistoryRequest request) {
        try {
            Page<ActivitySession> page = new Page<>(request.getPage(), request.getPageSize());

            Page<ActivitySession> sessionPage = sessionMapper.selectUserActivityHistory(
                page, request.getUserId(), request.getType(),
                request.getStartDate(), request.getEndDate());

            List<ActivitySessionVO> sessionVOs = sessionPage.getRecords().stream()
                .map(this::convertToSessionVO)
                .collect(Collectors.toList());

            ActivityHistoryVO result = new ActivityHistoryVO();
            result.setSessions(sessionVOs);
            result.setTotal(sessionPage.getTotal());
            result.setPage(request.getPage());
            result.setPageSize(request.getPageSize());
            result.setTotalPages((int) Math.ceil((double) sessionPage.getTotal() / request.getPageSize()));

            return result;
        } catch (Exception e) {
            log.error("获取活动历史失败", e);
            throw new RuntimeException("获取活动历史失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean clearRecords(Long userId, ActivityType activityType) {
        try {
            // 删除会话记录
            sessionMapper.deleteUserActivityRecords(userId, activityType);

            // 删除统计记录
            statisticsMapper.deleteUserStatistics(userId, activityType);

            // 重置总览记录
            summaryMapper.resetSummary(userId, activityType);

            return true;
        } catch (Exception e) {
            log.error("清除活动记录失败", e);
            throw new RuntimeException("清除活动记录失败: " + e.getMessage());
        }
    }

    @Override
    public ActivityStatisticsVO getStatsByType(Long userId, ActivityType activityType) {
        ActivityStatisticsRequest request = new ActivityStatisticsRequest();
        request.setUserId(userId);
        request.setType(activityType);
        return getStatistics(request);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean initializeUserSummary(Long userId) {
        try {
            ActivitySummary existingSummary = summaryMapper.selectByUserId(userId);
            if (existingSummary == null) {
                ActivitySummary summary = ActivitySummary.initialize(userId);
                summaryMapper.insert(summary);
                log.info("初始化用户活动总览成功: 用户ID={}", userId);
            }
            return true;
        } catch (Exception e) {
            log.error("初始化用户活动总览失败", e);
            throw new RuntimeException("初始化用户活动总览失败: " + e.getMessage());
        }
    }

    /**
     * 更新会话结束后的统计数据
     *
     * @param session 活动会话
     */
    private void updateStatisticsAfterSession(ActivitySession session) {
        if (session.getDuration() == null || session.getDuration() <= 0) {
            return;
        }

        try {
            LocalDate statDate = session.getStartTime().toLocalDate();

            // 更新或创建统计记录
            ActivityStatistics statistics = statisticsMapper.selectByUserAndTypeAndDate(
                session.getUserId(), session.getActivityType(), statDate);

            if (statistics == null) {
                // 创建新的统计记录
                statistics = ActivityStatistics.initialize(
                    session.getUserId(), session.getActivityType(), statDate, session.getDuration());
                statisticsMapper.insert(statistics);
            } else {
                // 更新现有统计记录
                statistics.updateStatistics(session.getDuration());
                statisticsMapper.updateById(statistics);
            }

            // 更新用户活动总览
            summaryMapper.updateSummary(session.getUserId(), session.getActivityType(), session.getDuration());

        } catch (Exception e) {
            log.error("更新统计数据失败", e);
        }
    }

    /**
     * 确保用户活动总览记录存在
     *
     * @param userId 用户ID
     */
    private void ensureUserSummaryExists(Long userId) {
        try {
            ActivitySummary summary = summaryMapper.selectByUserId(userId);
            if (summary == null) {
                summary = ActivitySummary.initialize(userId);
                summaryMapper.insert(summary);
            }
        } catch (Exception e) {
            log.error("确保用户活动总览存在失败", e);
        }
    }

    /**
     * 转换ActivitySession为ActivitySessionVO
     *
     * @param session 活动会话
     * @return 会话VO
     */
    private ActivitySessionVO convertToSessionVO(ActivitySession session) {
        ActivitySessionVO vo = new ActivitySessionVO();
        vo.setId(session.getSessionId());
        vo.setType(session.getActivityType());
        vo.setStartTime(session.getStartTime() != null ?
            session.getStartTime().atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli() : null);
        vo.setEndTime(session.getEndTime() != null ?
            session.getEndTime().atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli() : null);
        vo.setDuration(session.getDuration());
        vo.setIsActive(session.getIsActive());
        vo.setActivityId(session.getActivityId());
        vo.setActivityName(session.getActivityName());
        vo.setCategoryId(session.getCategoryId());
        vo.setCategoryName(session.getCategoryName());
        vo.setMetadata(session.getMetadata());
        vo.setCreateTime(session.getCreateTime() != null ?
            session.getCreateTime().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDateTime() : null);
        return vo;
    }
}
