{"doc": "\n 支付宝支付工具类\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "createPayPage", "paramTypes": ["com.alipay.api.AlipayClient", "org.dromara.common.pay.properties.AlipayProperties", "java.lang.String", "java.lang.String", "java.math.BigDecimal", "java.lang.String"], "doc": "\n 创建支付宝支付页面\r\n\r\n @param alipayClient     支付宝客户端\r\n @param alipayProperties 支付宝配置\r\n @param orderNo          订单号\r\n @param subject          商品标题\r\n @param totalAmount      支付金额\r\n @param body             商品描述\r\n @return 支付页面HTML\r\n"}, {"name": "validateCreatePayPageParams", "paramTypes": ["com.alipay.api.AlipayClient", "org.dromara.common.pay.properties.AlipayProperties", "java.lang.String", "java.lang.String", "java.math.BigDecimal"], "doc": "\n 验证创建支付页面的参数\r\n\r\n @param alipayClient     支付宝客户端\r\n @param alipayProperties 支付宝配置\r\n @param orderNo          订单号\r\n @param subject          商品标题\r\n @param totalAmount      支付金额\r\n"}, {"name": "validateAlipayConfig", "paramTypes": ["org.dromara.common.pay.properties.AlipayProperties"], "doc": "\n 验证支付宝关键配置\r\n\r\n @param alipayProperties 支付宝配置\r\n"}, {"name": "isValidUrl", "paramTypes": ["java.lang.String"], "doc": "\n 验证URL格式是否正确\r\n\r\n @param url URL地址\r\n @return true-格式正确 false-格式错误\r\n"}, {"name": "queryTradeStatus", "paramTypes": ["com.alipay.api.AlipayClient", "java.lang.String"], "doc": "\n 查询支付宝订单状态\r\n\r\n @param alipayClient 支付宝客户端\r\n @param orderNo      订单号\r\n @return 查询响应\r\n"}, {"name": "verifyNotify", "paramTypes": ["java.util.Map", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 验证支付宝异步通知签名\r\n\r\n @param params          通知参数\r\n @param alipayPublicKey 支付宝公钥\r\n @param charset         字符编码\r\n @param signType        签名类型\r\n @return true-验证成功 false-验证失败\r\n"}, {"name": "verifyReturn", "paramTypes": ["java.util.Map", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 验证支付宝同步回调签名\r\n\r\n @param params          回调参数\r\n @param alipayPublicKey 支付宝公钥\r\n @param charset         字符编码\r\n @param signType        签名类型\r\n @return true-验证成功 false-验证失败\r\n"}, {"name": "convertTradeStatus", "paramTypes": ["java.lang.String"], "doc": "\n 根据支付宝交易状态转换为系统支付状态\r\n\r\n @param tradeStatus 支付宝交易状态\r\n @return 系统支付状态\r\n"}, {"name": "formatAmount", "paramTypes": ["java.math.BigDecimal"], "doc": "\n 格式化金额为支付宝格式（保留两位小数）\r\n\r\n @param amount 金额\r\n @return 格式化后的金额字符串\r\n"}, {"name": "validateAmount", "paramTypes": ["java.math.BigDecimal"], "doc": "\n 验证支付金额范围\r\n\r\n @param amount 支付金额（元）\r\n @throws PaymentException 金额超出范围时抛出异常\r\n"}], "constructors": []}