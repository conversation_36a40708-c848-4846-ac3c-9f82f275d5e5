package org.dromara.app.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 能力评估结果视图对象
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Schema(description = "能力评估结果视图对象")
public class InitialAbilityAssessmentVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 专业知识得分
     */
    @Schema(description = "专业知识得分")
    private Integer professionalKnowledge;

    /**
     * 逻辑思维得分
     */
    @Schema(description = "逻辑思维得分")
    private Integer logicalThinking;

    /**
     * 语言表达得分
     */
    @Schema(description = "语言表达得分")
    private Integer languageExpression;

    /**
     * 抗压能力得分
     */
    @Schema(description = "抗压能力得分")
    private Integer stressResistance;

    /**
     * 团队协作得分
     */
    @Schema(description = "团队协作得分")
    private Integer teamCollaboration;

    /**
     * 创新能力得分
     */
    @Schema(description = "创新能力得分")
    private Integer innovation;

    /**
     * 总体得分
     */
    @Schema(description = "总体得分")
    private Integer overallScore;
}
