{"doc": "\n 全局异常处理器\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "handleHttpRequestMethodNotSupported", "paramTypes": ["org.springframework.web.HttpRequestMethodNotSupportedException", "jakarta.servlet.http.HttpServletRequest"], "doc": "\n 请求方式不支持\r\n"}, {"name": "handleServiceException", "paramTypes": ["org.dromara.common.core.exception.ServiceException", "jakarta.servlet.http.HttpServletRequest"], "doc": "\n 业务异常\r\n"}, {"name": "handleNotLoginException", "paramTypes": ["org.dromara.common.core.exception.SseException", "jakarta.servlet.http.HttpServletRequest"], "doc": "\n 认证失败\r\n"}, {"name": "handleServletException", "paramTypes": ["jakarta.servlet.ServletException", "jakarta.servlet.http.HttpServletRequest"], "doc": "\n servlet异常\r\n"}, {"name": "handleBaseException", "paramTypes": ["org.dromara.common.core.exception.base.BaseException", "jakarta.servlet.http.HttpServletRequest"], "doc": "\n 业务异常\r\n"}, {"name": "handleMissingPathVariableException", "paramTypes": ["org.springframework.web.bind.MissingPathVariableException", "jakarta.servlet.http.HttpServletRequest"], "doc": "\n 请求路径中缺少必需的路径变量\r\n"}, {"name": "handleMethodArgumentTypeMismatchException", "paramTypes": ["org.springframework.web.method.annotation.MethodArgumentTypeMismatchException", "jakarta.servlet.http.HttpServletRequest"], "doc": "\n 请求参数类型不匹配\r\n"}, {"name": "handleNoHandlerFoundException", "paramTypes": ["org.springframework.web.servlet.NoHandlerFoundException", "jakarta.servlet.http.HttpServletRequest"], "doc": "\n 找不到路由\r\n"}, {"name": "handleRuntimeException", "paramTypes": ["java.io.IOException", "jakarta.servlet.http.HttpServletRequest"], "doc": "\n 拦截未知的运行时异常\r\n"}, {"name": "handleRuntimeException", "paramTypes": ["java.lang.RuntimeException", "jakarta.servlet.http.HttpServletRequest"], "doc": "\n 拦截未知的运行时异常\r\n"}, {"name": "handleException", "paramTypes": ["java.lang.Exception", "jakarta.servlet.http.HttpServletRequest"], "doc": "\n 系统异常\r\n"}, {"name": "handleBindException", "paramTypes": ["org.springframework.validation.BindException"], "doc": "\n 自定义验证异常\r\n"}, {"name": "constraintViolationException", "paramTypes": ["jakarta.validation.ConstraintViolationException"], "doc": "\n 自定义验证异常\r\n"}, {"name": "handleMethodArgumentNotValidException", "paramTypes": ["org.springframework.web.bind.MethodArgumentNotValidException"], "doc": "\n 自定义验证异常\r\n"}, {"name": "handleJsonParseException", "paramTypes": ["com.fasterxml.jackson.core.JsonParseException", "jakarta.servlet.http.HttpServletRequest"], "doc": "\n JSON 解析异常（Jackson 在处理 JSON 格式出错时抛出）\r\n 可能是请求体格式非法，也可能是服务端反序列化失败\r\n"}, {"name": "handleHttpMessageNotReadableException", "paramTypes": ["org.springframework.http.converter.HttpMessageNotReadableException", "jakarta.servlet.http.HttpServletRequest"], "doc": "\n 请求体读取异常（通常是请求参数格式非法、字段类型不匹配等）\r\n"}], "constructors": []}