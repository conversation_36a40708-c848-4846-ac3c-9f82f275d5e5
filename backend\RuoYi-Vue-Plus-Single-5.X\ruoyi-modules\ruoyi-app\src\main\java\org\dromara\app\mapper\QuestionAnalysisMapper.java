package org.dromara.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.dromara.app.domain.QuestionAnalysis;

import java.util.List;

/**
 * 问题分析Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@Mapper
public interface QuestionAnalysisMapper extends BaseMapper<QuestionAnalysis> {

    /**
     * 根据结果ID查询问题分析列表
     *
     * @param resultId 结果ID
     * @return 问题分析列表
     */
    List<QuestionAnalysis> selectByResultId(@Param("resultId") String resultId);

    /**
     * 根据结果ID和问题ID查询问题分析
     *
     * @param resultId   结果ID
     * @param questionId 问题ID
     * @return 问题分析
     */
    QuestionAnalysis selectByResultIdAndQuestionId(@Param("resultId") String resultId, @Param("questionId") String questionId);

    /**
     * 根据分类查询问题分析列表
     *
     * @param resultId 结果ID
     * @param category 分类
     * @return 问题分析列表
     */
    List<QuestionAnalysis> selectByResultIdAndCategory(@Param("resultId") String resultId, @Param("category") String category);

    /**
     * 批量插入问题分析
     *
     * @param questionAnalyses 问题分析列表
     * @return 插入数量
     */
    int batchInsert(@Param("list") List<QuestionAnalysis> questionAnalyses);

    /**
     * 根据结果ID删除问题分析
     *
     * @param resultId 结果ID
     * @return 删除数量
     */
    int deleteByResultId(@Param("resultId") String resultId);

    /**
     * 查询用户在某分类问题上的平均分数
     *
     * @param userId   用户ID
     * @param category 分类
     * @return 平均分数
     */
    Double selectAvgScoreByUserIdAndCategory(@Param("userId") Long userId, @Param("category") String category);

    /**
     * 查询用户答题时间统计
     *
     * @param userId 用户ID
     * @return 平均答题时间
     */
    Double selectAvgTimeSpentByUserId(@Param("userId") Long userId);

    /**
     * 查询高分问题分析（用于学习参考）
     *
     * @param category 分类
     * @param minScore 最低分数
     * @param limit    限制数量
     * @return 问题分析列表
     */
    List<QuestionAnalysis> selectHighScoreAnalyses(@Param("category") String category, @Param("minScore") Integer minScore, @Param("limit") Integer limit);

}
