{"doc": "\n 数据权限处理\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "doBefore", "paramTypes": ["org.aspectj.lang.JoinPoint", "org.dromara.common.mybatis.annotation.DataPermission"], "doc": "\n 处理请求前执行\r\n"}, {"name": "doAfterReturning", "paramTypes": ["org.aspectj.lang.JoinPoint", "org.dromara.common.mybatis.annotation.DataPermission"], "doc": "\n 处理完请求后执行\r\n\r\n @param joinPoint 切点\r\n"}, {"name": "doAfterThrowing", "paramTypes": ["org.aspectj.lang.JoinPoint", "org.dromara.common.mybatis.annotation.DataPermission", "java.lang.Exception"], "doc": "\n 拦截异常操作\r\n\r\n @param joinPoint 切点\r\n @param e         异常\r\n"}], "constructors": []}