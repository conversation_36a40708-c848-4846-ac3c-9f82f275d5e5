package org.dromara.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.dromara.app.domain.AchievementEvent;

import java.util.List;

/**
 * 成就事件数据层
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
public interface AchievementEventMapper extends BaseMapper<AchievementEvent> {

    /**
     * 获取未处理的事件
     *
     * @param limit 最大数量
     * @return 未处理的事件列表
     */
    List<AchievementEvent> selectUnhandledEvents(@Param("limit") int limit);

    /**
     * 查询用户特定类型的事件
     *
     * @param userId    用户ID
     * @param eventType 事件类型
     * @param limit     最大数量
     * @return 事件列表
     */
    List<AchievementEvent> selectUserEvents(@Param("userId") String userId,
                                          @Param("eventType") String eventType,
                                          @Param("limit") Integer limit);

    /**
     * 统计用户特定类型的事件数量
     *
     * @param userId    用户ID
     * @param eventType 事件类型
     * @return 事件数量
     */
    int countUserEvents(@Param("userId") String userId, @Param("eventType") String eventType);

    /**
     * 统计用户特定类型的事件值总和
     *
     * @param userId    用户ID
     * @param eventType 事件类型
     * @return 事件值总和
     */
    int sumUserEventValues(@Param("userId") String userId, @Param("eventType") String eventType);

    /**
     * 获取用户最近的事件
     *
     * @param userId 用户ID
     * @param limit  最大数量
     * @return 最近的事件列表
     */
    List<AchievementEvent> selectRecentEvents(@Param("userId") String userId, @Param("limit") int limit);

    /**
     * 批量更新事件处理状态
     *
     * @param eventIds 事件ID列表
     * @param status   处理状态
     * @return 更新行数
     */
    int batchUpdateStatus(@Param("eventIds") List<String> eventIds, @Param("status") int status);

    /**
     * 删除过期事件
     *
     * @param days 保留天数
     * @return 删除行数
     */
    int deleteExpiredEvents(@Param("days") int days);
}
