package org.dromara.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.dromara.app.domain.Agent;

import java.util.List;

/**
 * AI代理Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface AgentMapper extends BaseMapper<Agent> {

    /**
     * 获取启用的代理列表（按排序号排序）
     *
     * @return 代理列表
     */
    List<Agent> selectEnabledAgentsOrdered();

    /**
     * 根据代理类型获取启用的代理
     *
     * @param agentType 代理类型
     * @return 代理信息
     */
    Agent selectEnabledByType(@Param("agentType") String agentType);

    /**
     * 增加代理使用次数
     *
     * @param agentType 代理类型
     * @return 影响行数
     */
    @Update("UPDATE app_agent SET usage_count = usage_count + 1, update_time = NOW() WHERE agent_type = #{agentType}")
    int incrementUsageCount(@Param("agentType") String agentType);

    /**
     * 更新代理评分
     *
     * @param agentType     代理类型
     * @param averageRating 平均评分
     * @return 影响行数
     */
    @Update("UPDATE app_agent SET average_rating = #{averageRating}, update_time = NOW() WHERE agent_type = #{agentType}")
    int updateAverageRating(@Param("agentType") String agentType, @Param("averageRating") Double averageRating);

    /**
     * 批量初始化默认代理
     *
     * @param agents 代理列表
     * @return 影响行数
     */
    int batchInsertDefaultAgents(@Param("agents") List<Agent> agents);

    /**
     * 根据代理ID获取启用的代理
     *
     * @param agentTypeId 代理类型ID
     * @return 代理信息
     */
    @Select("SELECT * FROM app_agent WHERE id = #{agentTypeId} AND enabled = 1")
    Agent selectEnabledById(String agentTypeId);
}
