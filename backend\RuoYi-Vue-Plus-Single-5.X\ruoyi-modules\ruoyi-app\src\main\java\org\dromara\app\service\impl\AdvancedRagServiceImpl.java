package org.dromara.app.service.impl;

import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.VectorEmbedding;
import org.dromara.app.service.AdvancedRagService;
import org.dromara.app.service.IRagService;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 高级RAG服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AdvancedRagServiceImpl implements AdvancedRagService {

    private final IRagService ragService;

    @Override
    public SmartRetrievalResult smartRetrieval(String query, List<Long> knowledgeBaseIds, RetrievalOptions options) {
        try {
            String processedQuery = query;
            List<String> expandedTerms = new ArrayList<>();

            // 查询扩展
            if (options.isEnableQueryExpansion()) {
                QueryExpansionResult expansion = expandQuery(query, options.getExpansionType());
                processedQuery = expansion.getExpandedQuery();
                expandedTerms = expansion.getExpandedTerms();
            }

            // 混合检索
            List<VectorEmbedding> candidates = hybridRetrieval(
                processedQuery,
                knowledgeBaseIds,
                options.getVectorWeight(),
                options.getKeywordWeight(),
                options.getTopK() * 2 // 获取更多候选结果用于重排序
            );

            // 重排序
            List<VectorEmbedding> finalResults = candidates;
            if (options.isEnableRerank() && candidates.size() > options.getTopK()) {
                finalResults = rerank(processedQuery, candidates, options.getTopK());
            } else if (candidates.size() > options.getTopK()) {
                finalResults = candidates.subList(0, options.getTopK());
            }

            // 计算平均相似度
            double avgSimilarity = finalResults.stream()
                .filter(r -> r.getSimilarity() != null)
                .mapToDouble(VectorEmbedding::getSimilarity)
                .average()
                .orElse(0.0);

            // 构建元数据
            Map<String, Object> metadata = new HashMap<>();
            metadata.put("original_query", query);
            metadata.put("processed_query", processedQuery);
            metadata.put("expanded_terms", expandedTerms);
            metadata.put("rerank_enabled", options.isEnableRerank());
            metadata.put("query_expansion_enabled", options.isEnableQueryExpansion());
            metadata.put("vector_weight", options.getVectorWeight());
            metadata.put("keyword_weight", options.getKeywordWeight());

            return new SmartRetrievalResult(
                finalResults,
                processedQuery,
                metadata,
                avgSimilarity,
                candidates.size()
            );

        } catch (Exception e) {
            log.error("智能检索失败: query={}", query, e);
            // 降级到基本检索
            List<VectorEmbedding> fallbackResults = new ArrayList<>();
            for (Long knowledgeBaseId : knowledgeBaseIds) {
                fallbackResults.addAll(ragService.searchKnowledge(knowledgeBaseId, query, options.getTopK()));
            }

            return new SmartRetrievalResult(
                fallbackResults.stream().limit(options.getTopK()).collect(Collectors.toList()),
                query,
                Map.of("fallback", true),
                0.0,
                fallbackResults.size()
            );
        }
    }

    @Override
    public List<VectorEmbedding> hybridRetrieval(String query, List<Long> knowledgeBaseIds,
                                                 double vectorWeight, double keywordWeight, int topK) {
        try {
            Map<String, VectorEmbedding> resultMap = new HashMap<>();

            // 向量检索
            for (Long knowledgeBaseId : knowledgeBaseIds) {
                List<VectorEmbedding> vectorResults = ragService.searchKnowledge(knowledgeBaseId, query, topK);
                for (VectorEmbedding result : vectorResults) {
                    String key = knowledgeBaseId + "_" + result.getId();
                    if (result.getSimilarity() != null) {
                        result.setSimilarity((float) (result.getSimilarity() * vectorWeight));
                    }
                    resultMap.put(key, result);
                }
            }

            // 关键词检索（使用混合搜索）
            for (Long knowledgeBaseId : knowledgeBaseIds) {
                List<VectorEmbedding> keywordResults = ragService.hybridSearch(knowledgeBaseId, query, topK);
                for (VectorEmbedding result : keywordResults) {
                    String key = knowledgeBaseId + "_" + result.getId();
                    VectorEmbedding existing = resultMap.get(key);

                    if (existing != null) {
                        // 合并分数
                        if (result.getSimilarity() != null && existing.getSimilarity() != null) {
                            double combinedScore = existing.getSimilarity() + (result.getSimilarity() * keywordWeight);
                            existing.setSimilarity((float) combinedScore);
                        }
                    } else {
                        // 新结果
                        if (result.getSimilarity() != null) {
                            result.setSimilarity((float) (result.getSimilarity() * keywordWeight));
                        }
                        resultMap.put(key, result);
                    }
                }
            }

            // 排序并返回前topK个结果
            return resultMap.values().stream()
                .sorted((a, b) -> {
                    Float scoreA = a.getSimilarity();
                    Float scoreB = b.getSimilarity();
                    if (scoreA == null && scoreB == null) return 0;
                    if (scoreA == null) return 1;
                    if (scoreB == null) return -1;
                    return Double.compare(scoreB, scoreA);
                })
                .limit(topK)
                .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("混合检索失败: query={}", query, e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<VectorEmbedding> rerank(String query, List<VectorEmbedding> candidates, int topK) {
        try {
            // 简单的重排序实现：基于内容长度和相似度的综合评分
            List<VectorEmbedding> reranked = new ArrayList<>(candidates);

            reranked.sort((a, b) -> {
                double scoreA = calculateRerankScore(query, a);
                double scoreB = calculateRerankScore(query, b);
                return Double.compare(scoreB, scoreA);
            });

            return reranked.stream().limit(topK).collect(Collectors.toList());

        } catch (Exception e) {
            log.error("重排序失败: query={}", query, e);
            return candidates.stream().limit(topK).collect(Collectors.toList());
        }
    }

    @Override
    public QueryExpansionResult expandQuery(String query, QueryExpansionType expansionType) {
        try {
            List<String> expandedTerms = new ArrayList<>();
            String expandedQuery = query;

            switch (expansionType) {
                case SYNONYM:
                    expandedTerms = expandWithSynonyms(query);
                    break;
                case RELATED_TERMS:
                    expandedTerms = expandWithRelatedTerms(query);
                    break;
                case CONTEXT_AWARE:
                    expandedTerms = expandWithContext(query);
                    break;
                case SEMANTIC:
                    expandedTerms = expandWithSemantics(query);
                    break;
            }

            if (!expandedTerms.isEmpty()) {
                expandedQuery = query + " " + String.join(" ", expandedTerms);
            }

            return new QueryExpansionResult(query, expandedQuery, expandedTerms, expansionType);

        } catch (Exception e) {
            log.error("查询扩展失败: query={}, type={}", query, expansionType, e);
            return new QueryExpansionResult(query, query, Collections.emptyList(), expansionType);
        }
    }

    @Override
    public String compressContext(String query, List<String> contexts, int maxLength) {
        try {
            if (contexts.isEmpty()) {
                return "";
            }

            // 简单的上下文压缩：选择最相关的句子
            List<String> sentences = new ArrayList<>();
            for (String context : contexts) {
                sentences.addAll(Arrays.asList(context.split("[。！？.!?]")));
            }

            // 按相关性排序句子
            sentences.sort((a, b) -> {
                int scoreA = calculateRelevanceScore(query, a);
                int scoreB = calculateRelevanceScore(query, b);
                return Integer.compare(scoreB, scoreA);
            });

            // 构建压缩后的上下文
            StringBuilder compressed = new StringBuilder();
            for (String sentence : sentences) {
                if (compressed.length() + sentence.length() + 1 <= maxLength) {
                    if (compressed.length() > 0) {
                        compressed.append(" ");
                    }
                    compressed.append(sentence.trim());
                } else {
                    break;
                }
            }

            return compressed.toString();

        } catch (Exception e) {
            log.error("上下文压缩失败: query={}", query, e);
            // 降级：简单截断
            String combined = String.join(" ", contexts);
            return combined.length() <= maxLength ? combined : combined.substring(0, maxLength);
        }
    }

    @Override
    public String generateEnhancedPrompt(String query, List<VectorEmbedding> retrievalResults, String promptTemplate) {
        try {
            if (retrievalResults.isEmpty()) {
                return promptTemplate.replace("{context}", "").replace("{query}", query);
            }

            // 构建上下文
            StringBuilder contextBuilder = new StringBuilder();
            contextBuilder.append("【相关知识】\n");

            for (int i = 0; i < retrievalResults.size(); i++) {
                VectorEmbedding result = retrievalResults.get(i);
                contextBuilder.append(String.format("%d. %s", i + 1, result.getContent()));

                if (result.getSimilarity() != null) {
                    contextBuilder.append(String.format(" (相似度: %.2f)", result.getSimilarity()));
                }

                contextBuilder.append("\n\n");
            }

            // 替换模板变量
            String enhancedPrompt = promptTemplate
                .replace("{context}", contextBuilder.toString())
                .replace("{query}", query);

            return enhancedPrompt;

        } catch (Exception e) {
            log.error("生成增强提示词失败: query={}", query, e);
            return promptTemplate.replace("{context}", "").replace("{query}", query);
        }
    }

    // ========== 私有方法 ==========

    private double calculateRerankScore(String query, VectorEmbedding embedding) {
        double similarityScore = embedding.getSimilarity() != null ? embedding.getSimilarity() : 0.0;

        // 内容长度因子（适中长度的内容得分更高）
        int contentLength = embedding.getContent() != null ? embedding.getContent().length() : 0;
        double lengthScore = 1.0 - Math.abs(contentLength - 500.0) / 1000.0;
        lengthScore = Math.max(0.1, lengthScore);

        // 综合评分
        return similarityScore * 0.8 + lengthScore * 0.2;
    }

    private List<String> expandWithSynonyms(String query) {
        // 简单的同义词扩展实现
        Map<String, List<String>> synonyms = Map.of(
            "问题", List.of("疑问", "难题", "课题"),
            "方法", List.of("办法", "途径", "手段"),
            "技术", List.of("技能", "工艺", "科技"),
            "系统", List.of("体系", "制度", "机制")
        );

        List<String> expanded = new ArrayList<>();
        for (Map.Entry<String, List<String>> entry : synonyms.entrySet()) {
            if (query.contains(entry.getKey())) {
                expanded.addAll(entry.getValue());
            }
        }

        return expanded;
    }

    private List<String> expandWithRelatedTerms(String query) {
        // 简单的相关词扩展
        return Collections.emptyList();
    }

    private List<String> expandWithContext(String query) {
        // 上下文感知扩展
        return Collections.emptyList();
    }

    private List<String> expandWithSemantics(String query) {
        // 语义扩展
        return Collections.emptyList();
    }

    private int calculateRelevanceScore(String query, String sentence) {
        if (StrUtil.isBlank(sentence)) {
            return 0;
        }

        String[] queryTerms = query.toLowerCase().split("\\s+");
        String lowerSentence = sentence.toLowerCase();

        int score = 0;
        for (String term : queryTerms) {
            if (lowerSentence.contains(term)) {
                score += term.length();
            }
        }

        return score;
    }
}
