package org.dromara.app.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.app.domain.InterviewMode;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 面试模式视图对象 app_interview_mode
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Data
@AutoMapper(target = InterviewMode.class)
public class InterviewModeVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 模式ID
     */
    private String id;

    /**
     * 模式名称
     */
    private String name;

    /**
     * 模式描述
     */
    private String description;

    /**
     * 模式图标
     */
    private String icon;

    /**
     * 模式颜色
     */
    private String color;

    /**
     * 默认时长（分钟）
     */
    private Integer duration;

    /**
     * 难度等级（1-5）
     */
    private Integer difficulty;

    /**
     * 模式特性
     */
    private List<String> features;

    /**
     * 排序号
     */
    private Integer sortOrder;

    /**
     * 状态（0正常 1停用）
     */
    private String status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

}
