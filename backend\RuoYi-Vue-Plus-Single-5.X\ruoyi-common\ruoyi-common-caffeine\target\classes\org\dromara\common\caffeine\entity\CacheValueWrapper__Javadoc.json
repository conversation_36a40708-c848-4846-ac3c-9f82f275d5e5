{"doc": "\n 缓存值包装类\r\n 支持自定义过期时间\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "value", "doc": "\n 实际缓存的值\r\n"}, {"name": "createTime", "doc": "\n 创建时间戳（毫秒）\r\n"}, {"name": "expireTime", "doc": "\n 过期时间戳（毫秒）\r\n"}, {"name": "ttl", "doc": "\n 过期时长（秒）\r\n"}, {"name": "neverExpire", "doc": "\n 是否永不过期\r\n"}], "enumConstants": [], "methods": [{"name": "of", "paramTypes": ["java.lang.Object"], "doc": "\n 创建永不过期的缓存值\r\n\r\n @param value 值\r\n @return 包装对象\r\n"}, {"name": "of", "paramTypes": ["java.lang.Object", "long"], "doc": "\n 创建带过期时间的缓存值\r\n\r\n @param value      值\r\n @param ttlSeconds 过期时间（秒）\r\n @return 包装对象\r\n"}, {"name": "isExpired", "paramTypes": [], "doc": "\n 检查是否已过期\r\n\r\n @return true如果已过期\r\n"}, {"name": "getRemainingTtl", "paramTypes": [], "doc": "\n 获取剩余生存时间（秒）\r\n\r\n @return 剩余秒数，-1表示永不过期，0表示已过期\r\n"}, {"name": "getCreateDateTime", "paramTypes": [], "doc": "\n 获取创建时间\r\n\r\n @return 创建时间\r\n"}, {"name": "getExpireDateTime", "paramTypes": [], "doc": "\n 获取过期时间\r\n\r\n @return 过期时间，null表示永不过期\r\n"}], "constructors": []}