package org.dromara.app.service;

import org.dromara.app.domain.BookChapter;

import java.util.List;

/**
 * 书籍章节Service接口
 *
 * <AUTHOR>
 */
public interface IBookChapterService {

    /**
     * 根据书籍ID查询章节列表
     *
     * @param bookId 书籍ID
     * @param userId 用户ID（用于判断解锁状态和完成状态）
     * @return 章节列表
     */
    List<BookChapter> queryChaptersByBookId(Long bookId, Long userId);

    /**
     * 根据章节ID查询章节内容
     *
     * @param chapterId 章节ID
     * @param userId    用户ID（用于权限校验）
     * @return 章节内容
     */
    BookChapter queryChapterContent(String chapterId, Long userId);

    /**
     * 根据书籍ID和章节序号查询章节
     *
     * @param bookId       书籍ID
     * @param chapterOrder 章节序号
     * @param userId       用户ID
     * @return 章节信息
     */
    BookChapter queryChapterByOrder(Long bookId, Integer chapterOrder, Long userId);

    /**
     * 查询试读章节列表
     *
     * @param bookId 书籍ID
     * @return 试读章节列表
     */
    List<BookChapter> queryPreviewChapters(Long bookId);

    /**
     * 新增章节
     *
     * @param chapter 章节信息
     * @return 是否成功
     */
    boolean insertChapter(BookChapter chapter);

    /**
     * 修改章节
     *
     * @param chapter 章节信息
     * @return 是否成功
     */
    boolean updateChapter(BookChapter chapter);

    /**
     * 删除章节
     *
     * @param chapterId 章节ID
     * @return 是否成功
     */
    boolean deleteChapter(String chapterId);

    /**
     * 根据书籍ID删除所有章节
     *
     * @param bookId 书籍ID
     * @return 是否成功
     */
    boolean deleteChaptersByBookId(Long bookId);

    /**
     * 检查用户是否有权限访问章节
     *
     * @param chapterId 章节ID
     * @param userId    用户ID
     * @return 是否有权限
     */
    boolean checkChapterAccess(String chapterId, Long userId);

    /**
     * 更新章节解锁状态
     *
     * @param chapterId  章节ID
     * @param isUnlocked 是否解锁
     * @return 是否成功
     */
    boolean updateChapterUnlockStatus(String chapterId, Boolean isUnlocked);
}
