{"doc": "\n 用户信息\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.system.domain.bo.SysUserBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 获取用户列表\r\n"}, {"name": "export", "paramTypes": ["org.dromara.system.domain.bo.SysUserBo", "jakarta.servlet.http.HttpServletResponse"], "doc": "\n 导出用户列表\r\n"}, {"name": "importData", "paramTypes": ["org.springframework.web.multipart.MultipartFile", "boolean"], "doc": "\n 导入数据\r\n\r\n @param file          导入文件\r\n @param updateSupport 是否更新已存在数据\r\n"}, {"name": "importTemplate", "paramTypes": ["jakarta.servlet.http.HttpServletResponse"], "doc": "\n 获取导入模板\r\n"}, {"name": "getInfo", "paramTypes": [], "doc": "\n 获取用户信息\r\n\r\n @return 用户信息\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": "\n 根据用户编号获取详细信息\r\n\r\n @param userId 用户ID\r\n"}, {"name": "add", "paramTypes": ["org.dromara.system.domain.bo.SysUserBo"], "doc": "\n 新增用户\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.system.domain.bo.SysUserBo"], "doc": "\n 修改用户\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": "\n 删除用户\r\n\r\n @param userIds 角色ID串\r\n"}, {"name": "optionselect", "paramTypes": ["java.lang.Long[]", "java.lang.Long"], "doc": "\n 根据用户ID串批量获取用户基础信息\r\n\r\n @param userIds 用户ID串\r\n @param deptId  部门ID\r\n"}, {"name": "resetPwd", "paramTypes": ["org.dromara.system.domain.bo.SysUserBo"], "doc": "\n 重置密码\r\n"}, {"name": "changeStatus", "paramTypes": ["org.dromara.system.domain.bo.SysUserBo"], "doc": "\n 状态修改\r\n"}, {"name": "authRole", "paramTypes": ["java.lang.Long"], "doc": "\n 根据用户编号获取授权角色\r\n\r\n @param userId 用户ID\r\n"}, {"name": "insertAuthRole", "paramTypes": ["java.lang.Long", "java.lang.Long[]"], "doc": "\n 用户授权角色\r\n\r\n @param userId  用户Id\r\n @param roleIds 角色ID串\r\n"}, {"name": "deptTree", "paramTypes": ["org.dromara.system.domain.bo.SysDeptBo"], "doc": "\n 获取部门树列表\r\n"}, {"name": "listByDept", "paramTypes": ["java.lang.Long"], "doc": "\n 获取部门下的所有用户信息\r\n"}], "constructors": []}