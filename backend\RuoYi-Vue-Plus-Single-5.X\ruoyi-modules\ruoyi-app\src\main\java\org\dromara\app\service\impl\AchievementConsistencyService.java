//package org.dromara.app.service.impl;
//
//import cn.hutool.core.collection.CollUtil;
//import cn.hutool.core.util.StrUtil;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.dromara.app.domain.Achievement;
//import org.dromara.app.domain.Badge;
//import org.dromara.app.domain.UserAchievement;
//import org.dromara.app.domain.UserBadge;
//import org.dromara.app.mapper.AchievementMapper;
//import org.dromara.app.mapper.BadgeMapper;
//import org.dromara.app.mapper.UserAchievementMapper;
//import org.dromara.app.mapper.UserBadgeMapper;
//import org.dromara.common.core.utils.DateUtils;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//
//import java.time.LocalDateTime;
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
///**
// * 成就系统数据一致性服务
// * 负责检查和修复成就系统中的数据一致性问题
// *
// * <AUTHOR>
// * @date 2025-07-18
// */
//@Slf4j
//@Service
//@RequiredArgsConstructor
//public class AchievementConsistencyService {
//
//    private final AchievementMapper achievementMapper;
//    private final BadgeMapper badgeMapper;
//    private final UserAchievementMapper userAchievementMapper;
//    private final UserBadgeMapper userBadgeMapper;
//
//    /**
//     * 执行完整的数据一致性检查
//     *
//     * @return 检查结果报告
//     */
//    @Transactional(rollbackFor = Exception.class)
//    public Map<String, Object> performConsistencyCheck() {
//        log.info("开始执行成就系统数据一致性检查");
//
//        Map<String, Object> report = new HashMap<>();
//        List<String> issues = new ArrayList<>();
//        List<String> fixes = new ArrayList<>();
//
//        // 1. 检查成就与徽章的关联关系
//        checkAchievementBadgeConsistency(issues, fixes);
//
//        // 2. 检查用户成就数据完整性
//        checkUserAchievementIntegrity(issues, fixes);
//
//        // 3. 检查用户徽章数据完整性
//        checkUserBadgeIntegrity(issues, fixes);
//
//        // 4. 检查进度数据的准确性
//        checkProgressAccuracy(issues, fixes);
//
//        // 5. 检查置顶徽章数量限制
//        checkPinnedBadgeLimit(issues, fixes);
//
//        // 6. 检查孤儿数据
//        checkOrphanedData(issues, fixes);
//
//        report.put("checkTime", LocalDateTime.now());
//        report.put("totalIssues", issues.size());
//        report.put("totalFixes", fixes.size());
//        report.put("issues", issues);
//        report.put("fixes", fixes);
//
//        log.info("数据一致性检查完成，发现 {} 个问题，修复 {} 个问题", issues.size(), fixes.size());
//        return report;
//    }
//
//    /**
//     * 检查成就与徽章的关联关系
//     */
//    private void checkAchievementBadgeConsistency(List<String> issues, List<String> fixes) {
//        log.info("检查成就与徽章的关联关系");
//
//        // 查找所有有徽章关联的成就
//        List<Achievement> achievementsWithBadges = achievementMapper.selectList(null)
//            .stream()
//            .filter(a -> StrUtil.isNotBlank(a.getBadgeId()))
//            .toList();
//
//        for (Achievement achievement : achievementsWithBadges) {
//            Badge badge = badgeMapper.selectById(achievement.getBadgeId());
//            if (badge == null) {
//                issues.add("成就 " + achievement.getId() + " 关联的徽章 " + achievement.getBadgeId() + " 不存在");
//                // 清除无效的徽章关联
//                achievement.setBadgeId(null);
//                achievementMapper.updateById(achievement);
//                fixes.add("清除成就 " + achievement.getId() + " 的无效徽章关联");
//            }
//        }
//
//        // 检查徽章是否有对应的成就
//        List<Badge> badgesWithAchievements = badgeMapper.selectList(null)
//            .stream()
//            .filter(b -> StrUtil.isNotBlank(b.getAchievementId()))
//            .toList();
//
//        for (Badge badge : badgesWithAchievements) {
//            Achievement achievement = achievementMapper.selectById(badge.getAchievementId());
//            if (achievement == null) {
//                issues.add("徽章 " + badge.getId() + " 关联的成就 " + badge.getAchievementId() + " 不存在");
//                // 清除无效的成就关联
//                badge.setAchievementId(null);
//                badgeMapper.updateById(badge);
//                fixes.add("清除徽章 " + badge.getId() + " 的无效成就关联");
//            }
//        }
//    }
//
//    /**
//     * 检查用户成就数据完整性
//     */
//    private void checkUserAchievementIntegrity(List<String> issues, List<String> fixes) {
//        log.info("检查用户成就数据完整性");
//
//        // 查找所有用户成就记录
//        List<UserAchievement> userAchievements = userAchievementMapper.selectList(null);
//
//        for (UserAchievement userAchievement : userAchievements) {
//            // 检查成就是否存在
//            Achievement achievement = achievementMapper.selectById(userAchievement.getAchievementId());
//            if (achievement == null) {
//                issues.add("用户成就记录 " + userAchievement.getId() + " 关联的成就 " +
//                          userAchievement.getAchievementId() + " 不存在");
//                // 删除无效的用户成就记录
//                userAchievementMapper.deleteById(userAchievement.getId());
//                fixes.add("删除无效的用户成就记录 " + userAchievement.getId());
//                continue;
//            }
//
//            // 检查进度数据的合理性
//            if (userAchievement.getCurrentProgress() < 0) {
//                issues.add("用户成就 " + userAchievement.getId() + " 的当前进度为负数");
//                userAchievement.setCurrentProgress(0);
//                userAchievementMapper.updateById(userAchievement);
//                fixes.add("修复用户成就 " + userAchievement.getId() + " 的负进度");
//            }
//
//            if (userAchievement.getTargetProgress() != null &&
//                userAchievement.getTargetProgress() != achievement.getTargetValue()) {
//                issues.add("用户成就 " + userAchievement.getId() + " 的目标进度与成就定义不一致");
//                userAchievement.setTargetProgress(achievement.getTargetValue());
//                userAchievementMapper.updateById(userAchievement);
//                fixes.add("修复用户成就 " + userAchievement.getId() + " 的目标进度");
//            }
//
//            // 检查进度百分比
//            if (userAchievement.getTargetProgress() != null && userAchievement.getTargetProgress() > 0) {
//                double expectedPercentage = (double) userAchievement.getCurrentProgress() /
//                                          userAchievement.getTargetProgress() * 100;
//                if (Math.abs(expectedPercentage - userAchievement.getProgressPercentage()) > 0.1) {
//                    issues.add("用户成就 " + userAchievement.getId() + " 的进度百分比计算错误");
//                    userAchievement.setProgressPercentage(expectedPercentage);
//                    userAchievementMapper.updateById(userAchievement);
//                    fixes.add("修复用户成就 " + userAchievement.getId() + " 的进度百分比");
//                }
//            }
//
//            // 检查解锁状态与进度的一致性
//            if (userAchievement.getUnlocked() && userAchievement.getUnlockedAt() == null) {
//                issues.add("用户成就 " + userAchievement.getId() + " 已解锁但缺少解锁时间");
//                userAchievement.setUnlockedAt(LocalDateTime.now());
//                userAchievementMapper.updateById(userAchievement);
//                fixes.add("补充用户成就 " + userAchievement.getId() + " 的解锁时间");
//            }
//        }
//    }
//
//    /**
//     * 检查用户徽章数据完整性
//     */
//    private void checkUserBadgeIntegrity(List<String> issues, List<String> fixes) {
//        log.info("检查用户徽章数据完整性");
//
//        List<UserBadge> userBadges = userBadgeMapper.selectList(null);
//
//        for (UserBadge userBadge : userBadges) {
//            // 检查徽章是否存在
//            Badge badge = badgeMapper.selectById(userBadge.getBadgeId());
//            if (badge == null) {
//                issues.add("用户徽章记录 " + userBadge.getId() + " 关联的徽章 " +
//                          userBadge.getBadgeId() + " 不存在");
//                userBadgeMapper.deleteById(userBadge.getId());
//                fixes.add("删除无效的用户徽章记录 " + userBadge.getId());
//                continue;
//            }
//
//            // 检查解锁状态与解锁时间的一致性
//            if (userBadge.getUnlocked() && userBadge.getUnlockedAt() == null) {
//                issues.add("用户徽章 " + userBadge.getId() + " 已解锁但缺少解锁时间");
//                userBadge.setUnlockedAt(LocalDateTime.now());
//                userBadgeMapper.updateById(userBadge);
//                fixes.add("补充用户徽章 " + userBadge.getId() + " 的解锁时间");
//            }
//
//            // 检查置顶状态与置顶时间的一致性
//            if (userBadge.getIsPinned() && userBadge.getPinnedAt() == null) {
//                issues.add("用户徽章 " + userBadge.getId() + " 已置顶但缺少置顶时间");
//                userBadge.setPinnedAt(LocalDateTime.now());
//                userBadgeMapper.updateById(userBadge);
//                fixes.add("补充用户徽章 " + userBadge.getId() + " 的置顶时间");
//            }
//
//            // 检查未解锁的徽章不应该被置顶
//            if (!userBadge.getUnlocked() && userBadge.getIsPinned()) {
//                issues.add("用户徽章 " + userBadge.getId() + " 未解锁但被置顶");
//                userBadge.setIsPinned(false);
//                userBadge.setPinnedAt(null);
//                userBadgeMapper.updateById(userBadge);
//                fixes.add("取消未解锁徽章 " + userBadge.getId() + " 的置顶状态");
//            }
//        }
//    }
//
//    /**
//     * 检查进度数据的准确性
//     */
//    private void checkProgressAccuracy(List<String> issues, List<String> fixes) {
//        log.info("检查进度数据的准确性");
//
//        // 这里可以根据实际业务逻辑检查进度数据
//        // 例如：检查学习进度是否与实际学习记录一致
//        // 由于具体的业务逻辑可能很复杂，这里只做基本检查
//
//        List<UserAchievement> userAchievements = userAchievementMapper.selectList(null);
//        for (UserAchievement userAchievement : userAchievements) {
//            if (userAchievement.getUnlocked() &&
//                userAchievement.getCurrentProgress() < userAchievement.getTargetProgress()) {
//                issues.add("用户成就 " + userAchievement.getId() + " 已解锁但进度未达到目标");
//                userAchievement.setCurrentProgress(userAchievement.getTargetProgress());
//                userAchievement.setProgressPercentage(100.0);
//                userAchievementMapper.updateById(userAchievement);
//                fixes.add("修复已解锁成就 " + userAchievement.getId() + " 的进度数据");
//            }
//        }
//    }
//
//    /**
//     * 检查置顶徽章数量限制
//     */
//    private void checkPinnedBadgeLimit(List<String> issues, List<String> fixes) {
//        log.info("检查置顶徽章数量限制");
//
//        // 按用户分组检查置顶徽章数量
//        Map<String, List<UserBadge>> userPinnedBadges = new HashMap<>();
//        List<UserBadge> allPinnedBadges = userBadgeMapper.selectList(null)
//            .stream()
//            .filter(ub -> ub.getIsPinned() != null && ub.getIsPinned())
//            .toList();
//
//        for (UserBadge userBadge : allPinnedBadges) {
//            userPinnedBadges.computeIfAbsent(userBadge.getUserId(), k -> new ArrayList<>()).add(userBadge);
//        }
//
//        for (Map.Entry<String, List<UserBadge>> entry : userPinnedBadges.entrySet()) {
//            String userId = entry.getKey();
//            List<UserBadge> pinnedBadges = entry.getValue();
//
//            if (pinnedBadges.size() > 4) {
//                issues.add("用户 " + userId + " 置顶了 " + pinnedBadges.size() + " 个徽章，超过限制");
//
//                // 保留最近置顶的4个，取消其他的置顶状态
//                pinnedBadges.sort((a, b) -> b.getPinnedAt().compareTo(a.getPinnedAt()));
//                for (int i = 4; i < pinnedBadges.size(); i++) {
//                    UserBadge badge = pinnedBadges.get(i);
//                    badge.setIsPinned(false);
//                    badge.setPinnedAt(null);
//                    userBadgeMapper.updateById(badge);
//                }
//                fixes.add("修复用户 " + userId + " 的置顶徽章数量限制");
//            }
//        }
//    }
//
//    /**
//     * 检查孤儿数据
//     */
//    private void checkOrphanedData(List<String> issues, List<String> fixes) {
//        log.info("检查孤儿数据");
//
//        // 检查是否有用户成就记录但没有对应的用户徽章记录（对于有徽章的成就）
//        List<UserAchievement> unlockedAchievements = userAchievementMapper.selectList(null)
//            .stream()
//            .filter(ua -> ua.getUnlocked() != null && ua.getUnlocked())
//            .toList();
//
//        for (UserAchievement userAchievement : unlockedAchievements) {
//            Achievement achievement = achievementMapper.selectById(userAchievement.getAchievementId());
//            if (achievement != null && StrUtil.isNotBlank(achievement.getBadgeId())) {
//                UserBadge userBadge = userBadgeMapper.selectByUserIdAndBadgeId(
//                    userAchievement.getUserId(), achievement.getBadgeId());
//
//                if (userBadge == null || !userBadge.getUnlocked()) {
//                    issues.add("用户 " + userAchievement.getUserId() + " 已解锁成就 " +
//                              userAchievement.getAchievementId() + " 但对应徽章未解锁");
//
//                    // 创建或更新用户徽章记录
//                    if (userBadge == null) {
//                        userBadge = new UserBadge();
//                        userBadge.setUserId(userAchievement.getUserId());
//                        userBadge.setBadgeId(achievement.getBadgeId());
//                        userBadge.setUnlocked(true);
//                        userBadge.setUnlockedAt(userAchievement.getUnlockedAt());
//                        userBadge.setIsPinned(false);
//                        userBadge.setNotificationStatus(0);
//                        userBadge.setIsViewed(false);
//                        userBadge.setUnlockChannel("consistency_fix");
//                        userBadge.setUserAchievementId(userAchievement.getId());
//                        userBadgeMapper.insert(userBadge);
//                    } else {
//                        userBadge.setUnlocked(true);
//                        userBadge.setUnlockedAt(userAchievement.getUnlockedAt());
//                        userBadgeMapper.updateById(userBadge);
//                    }
//                    fixes.add("修复用户 " + userAchievement.getUserId() + " 的徽章解锁状态");
//                }
//            }
//        }
//    }
//
//    /**
//     * 修复特定用户的数据一致性问题
//     *
//     * @param userId 用户ID
//     * @return 修复结果
//     */
//    @Transactional(rollbackFor = Exception.class)
//    public Map<String, Object> fixUserDataConsistency(String userId) {
//        log.info("修复用户 {} 的数据一致性问题", userId);
//
//        Map<String, Object> result = new HashMap<>();
//        List<String> fixes = new ArrayList<>();
//
//        // 重新计算用户成就进度
//        List<UserAchievement> userAchievements = userAchievementMapper.selectList(null)
//            .stream()
//            .filter(ua -> userId.equals(ua.getUserId()))
//            .toList();
//
//        for (UserAchievement userAchievement : userAchievements) {
//            if (userAchievement.getTargetProgress() != null && userAchievement.getTargetProgress() > 0) {
//                double correctPercentage = (double) userAchievement.getCurrentProgress() /
//                                         userAchievement.getTargetProgress() * 100;
//                if (Math.abs(correctPercentage - userAchievement.getProgressPercentage()) > 0.1) {
//                    userAchievement.setProgressPercentage(correctPercentage);
//                    userAchievementMapper.updateById(userAchievement);
//                    fixes.add("修复成就 " + userAchievement.getAchievementId() + " 的进度百分比");
//                }
//            }
//        }
//
//        result.put("userId", userId);
//        result.put("fixTime", LocalDateTime.now());
//        result.put("totalFixes", fixes.size());
//        result.put("fixes", fixes);
//
//        return result;
//    }
//}
