<?xml version="1.0" encoding="UTF-8"?>
<svg width="900" height="750" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .entity { fill: white; stroke: black; stroke-width: 2; }
      .attribute { fill: white; stroke: black; stroke-width: 1; }
      .relationship { fill: white; stroke: black; stroke-width: 2; }
      .text { font-family: Arial, sans-serif; font-size: 12px; text-anchor: middle; dominant-baseline: middle; }
      .line { stroke: black; stroke-width: 1; fill: none; }
      .cardinality { font-family: Arial, sans-serif; font-size: 10px; text-anchor: middle; }
    </style>
  </defs>
  
  <!-- 用户实体 -->
  <rect x="50" y="250" width="100" height="60" class="entity"/>
  <text x="100" y="280" class="text">用户</text>
  
  <!-- 用户属性 -->
  <ellipse cx="100" cy="180" rx="30" ry="20" class="attribute"/>
  <text x="100" y="180" class="text">用户ID</text>
  <line x1="100" y1="200" x2="100" y2="250" class="line"/>
  
  <ellipse cx="30" cy="220" rx="25" ry="20" class="attribute"/>
  <text x="30" y="220" class="text">姓名</text>
  <line x1="45" y1="235" x2="70" y2="250" class="line"/>
  
  <ellipse cx="30" cy="340" rx="25" ry="20" class="attribute"/>
  <text x="30" y="340" class="text">专业</text>
  <line x1="45" y1="325" x2="70" y2="310" class="line"/>
  
  <ellipse cx="100" cy="370" rx="30" ry="20" class="attribute"/>
  <text x="100" y="370" class="text">年级</text>
  <line x1="100" y1="350" x2="100" y2="310" class="line"/>
  
  <!-- 面试记录实体 -->
  <rect x="350" y="250" width="120" height="60" class="entity"/>
  <text x="410" y="280" class="text">面试记录</text>
  
  <!-- 面试记录属性 -->
  <ellipse cx="300" cy="150" rx="30" ry="20" class="attribute"/>
  <text x="300" y="150" class="text">记录ID</text>
  <line x1="315" y1="165" x2="370" y2="250" class="line"/>
  
  <ellipse cx="410" cy="130" rx="30" ry="20" class="attribute"/>
  <text x="410" y="130" class="text">职位名称</text>
  <line x1="410" y1="150" x2="410" y2="250" class="line"/>
  
  <ellipse cx="460" cy="120" rx="25" ry="20" class="attribute"/>
  <text x="460" y="120" class="text">公司</text>
  <line x1="450" y1="135" x2="440" y2="250" class="line"/>
  
  <ellipse cx="220" cy="180" rx="25" ry="20" class="attribute"/>
  <text x="220" y="180" class="text">难度</text>
  <line x1="235" y1="195" x2="350" y2="250" class="line"/>

  <ellipse cx="570" cy="220" rx="30" ry="20" class="attribute"/>
  <text x="570" y="220" class="text">面试时间</text>
  <line x1="555" y1="235" x2="470" y2="260" class="line"/>

  <ellipse cx="220" cy="380" rx="25" ry="20" class="attribute"/>
  <text x="220" y="380" class="text">时长</text>
  <line x1="235" y1="365" x2="350" y2="310" class="line"/>

  <ellipse cx="320" cy="400" rx="30" ry="20" class="attribute"/>
  <text x="320" y="400" class="text">总分</text>
  <line x1="335" y1="385" x2="370" y2="310" class="line"/>

  <ellipse cx="570" cy="380" rx="25" ry="20" class="attribute"/>
  <text x="570" y="380" class="text">状态</text>
  <line x1="555" y1="365" x2="470" y2="310" class="line"/>

  <!-- 面试问题实体 -->
  <rect x="650" y="100" width="120" height="60" class="entity"/>
  <text x="710" y="130" class="text">面试问题</text>

  <!-- 面试问题属性 -->
  <ellipse cx="600" cy="50" rx="30" ry="20" class="attribute"/>
  <text x="600" y="50" class="text">问题ID</text>
  <line x1="615" y1="65" x2="670" y2="100" class="line"/>

  <ellipse cx="710" cy="30" rx="30" ry="20" class="attribute"/>
  <text x="710" y="30" class="text">问题内容</text>
  <line x1="710" y1="50" x2="710" y2="100" class="line"/>

  <ellipse cx="820" cy="50" rx="30" ry="20" class="attribute"/>
  <text x="820" y="50" class="text">问题类型</text>
  <line x1="805" y1="65" x2="750" y2="100" class="line"/>

  <ellipse cx="820" cy="200" rx="30" ry="20" class="attribute"/>
  <text x="820" y="200" class="text">难度等级</text>
  <line x1="805" y1="185" x2="750" y2="160" class="line"/>

  <!-- 评分维度实体 -->
  <rect x="650" y="400" width="120" height="60" class="entity"/>
  <text x="710" y="430" class="text">评分维度</text>

  <!-- 评分维度属性 -->
  <ellipse cx="580" cy="280" rx="30" ry="20" class="attribute"/>
  <text x="580" y="280" class="text">维度ID</text>
  <line x1="595" y1="295" x2="670" y2="400" class="line"/>

  <ellipse cx="710" cy="310" rx="35" ry="20" class="attribute"/>
  <text x="710" y="310" class="text">专业知识</text>
  <line x1="710" y1="330" x2="710" y2="400" class="line"/>

  <ellipse cx="850" cy="350" rx="35" ry="20" class="attribute"/>
  <text x="850" y="350" class="text">沟通能力</text>
  <line x1="835" y1="365" x2="770" y2="400" class="line"/>

  <ellipse cx="580" cy="530" rx="35" ry="20" class="attribute"/>
  <text x="580" y="530" class="text">逻辑思维</text>
  <line x1="595" y1="515" x2="670" y2="460" class="line"/>

  <ellipse cx="710" cy="550" rx="30" ry="20" class="attribute"/>
  <text x="710" y="550" class="text">创新能力</text>
  <line x1="710" y1="530" x2="710" y2="460" class="line"/>

  <ellipse cx="850" cy="530" rx="30" ry="20" class="attribute"/>
  <text x="850" y="530" class="text">分数</text>
  <line x1="835" y1="515" x2="770" y2="460" class="line"/>

  <!-- 职位类别实体 -->
  <rect x="350" y="580" width="120" height="60" class="entity"/>
  <text x="410" y="610" class="text">职位类别</text>

  <!-- 职位类别属性 -->
  <ellipse cx="280" cy="650" rx="30" ry="20" class="attribute"/>
  <text x="280" y="650" class="text">类别ID</text>
  <line x1="295" y1="635" x2="370" y2="610" class="line"/>

  <ellipse cx="410" cy="680" rx="30" ry="20" class="attribute"/>
  <text x="410" y="680" class="text">类别名称</text>
  <line x1="410" y1="660" x2="410" y2="640" class="line"/>

  <ellipse cx="540" cy="650" rx="30" ry="20" class="attribute"/>
  <text x="540" y="650" class="text">行业领域</text>
  <line x1="525" y1="635" x2="450" y2="610" class="line"/>
  
  <!-- 关系：参加面试 -->
  <polygon points="200,250 230,270 200,290 170,270" class="relationship"/>
  <text x="200" y="270" class="text">参加</text>
  <line x1="150" y1="280" x2="170" y2="270" class="line"/>
  <line x1="230" y1="270" x2="350" y2="280" class="line"/>
  <text x="160" y="290" class="cardinality">1</text>
  <text x="330" y="290" class="cardinality">n</text>
  
  <!-- 关系：包含问题 -->
  <polygon points="520,160 550,180 520,200 490,180" class="relationship"/>
  <text x="520" y="180" class="text">包含</text>
  <line x1="470" y1="260" x2="490" y2="180" class="line"/>
  <line x1="550" y1="180" x2="650" y2="140" class="line"/>
  <text x="480" y="220" class="cardinality">1</text>
  <text x="600" y="160" class="cardinality">n</text>
  
  <!-- 关系：生成评分 -->
  <polygon points="530,340 560,360 530,380 500,360" class="relationship"/>
  <text x="530" y="360" class="text">生成</text>
  <line x1="470" y1="300" x2="500" y2="360" class="line"/>
  <line x1="560" y1="360" x2="650" y2="420" class="line"/>
  <text x="480" y="320" class="cardinality">1</text>
  <text x="620" y="395" class="cardinality">n</text>

  <!-- 关系：属于类别 -->
  <polygon points="380,460 410,480 380,500 350,480" class="relationship"/>
  <text x="380" y="480" class="text">属于</text>
  <line x1="410" y1="310" x2="410" y2="460" class="line"/>
  <line x1="380" y1="500" x2="410" y2="580" class="line"/>
  <text x="420" y="430" class="cardinality">n</text>
  <text x="420" y="560" class="cardinality">1</text>
</svg>
