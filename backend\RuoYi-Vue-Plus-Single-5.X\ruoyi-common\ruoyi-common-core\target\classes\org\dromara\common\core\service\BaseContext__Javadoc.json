{"doc": "\n @description: 基于ThreadLocal封装工具类，用户保存和获取当前登录用户Sa-Token token值\r\n @author: yzm\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "getCurrentToken", "paramTypes": [], "doc": "\n @description: 获取值\r\n @author: yzm\r\n"}, {"name": "setCurrentToken", "paramTypes": ["java.lang.String"], "doc": "\n @description: 设置值\r\n @author: yzm\r\n @param: [token] 线程token\r\n"}], "constructors": []}