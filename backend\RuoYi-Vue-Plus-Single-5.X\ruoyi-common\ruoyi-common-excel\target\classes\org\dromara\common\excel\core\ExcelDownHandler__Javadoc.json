{"doc": "\n <h1>Excel表格下拉选操作</h1>\r\n 考虑到下拉选过多可能导致Excel打开缓慢的问题，只校验前1000行\r\n <p>\r\n 即只有前1000行的数据可以用下拉框，超出的自行通过限制数据量的形式，第二次输出\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "EXCEL_COLUMN_NAME", "doc": "\n Excel表格中的列名英文\r\n 仅为了解析列英文，禁止修改\r\n"}, {"name": "OPTIONS_SHEET_NAME", "doc": "\n 单选数据Sheet名\r\n"}, {"name": "LINKED_OPTIONS_SHEET_NAME", "doc": "\n 联动选择数据Sheet名的头\r\n"}, {"name": "dropDownOptions", "doc": "\n 下拉可选项\r\n"}, {"name": "currentOptionsColumnIndex", "doc": "\n 当前单选进度\r\n"}, {"name": "currentLinkedOptionsSheetIndex", "doc": "\n 当前联动选择进度\r\n"}], "enumConstants": [], "methods": [{"name": "afterSheetCreate", "paramTypes": ["cn.idev.excel.write.metadata.holder.WriteWorkbookHolder", "cn.idev.excel.write.metadata.holder.WriteSheetHolder"], "doc": "\n <h2>开始创建下拉数据</h2>\r\n 1.通过解析传入的@ExcelProperty同级是否标注有@DropDown选项\r\n 如果有且设置了value值，则将其直接置为下拉可选项\r\n <p>\r\n 2.或者在调用ExcelUtil时指定了可选项，将依据传入的可选项做下拉\r\n <p>\r\n 3.二者并存，注意调用方式\r\n"}, {"name": "dropDownWithSimple", "paramTypes": ["org.apache.poi.ss.usermodel.DataValidationHelper", "org.apache.poi.ss.usermodel.Sheet", "java.lang.Integer", "java.util.List"], "doc": "\n <h2>简单下拉框</h2>\r\n 直接将可选项拼接为指定列的数据校验值\r\n\r\n @param celIndex 列index\r\n @param value    下拉选可选值\r\n"}, {"name": "dropDownLinkedOptions", "paramTypes": ["org.apache.poi.ss.usermodel.DataValidationHelper", "org.apache.poi.ss.usermodel.Workbook", "org.apache.poi.ss.usermodel.Sheet", "org.dromara.common.excel.core.DropDownOptions"], "doc": "\n <h2>额外表格形式的级联下拉框</h2>\r\n\r\n @param options 额外表格形式存储的下拉可选项\r\n"}, {"name": "dropDownWithSheet", "paramTypes": ["org.apache.poi.ss.usermodel.DataValidationHelper", "org.apache.poi.ss.usermodel.Workbook", "org.apache.poi.ss.usermodel.Sheet", "java.lang.Integer", "java.util.List"], "doc": "\n <h2>额外表格形式的普通下拉框</h2>\r\n 由于下拉框可选值数量过多，为提升Excel打开效率，使用额外表格形式做下拉\r\n\r\n @param celIndex 下拉选\r\n @param value    下拉选可选值\r\n"}, {"name": "markOptionsToSheet", "paramTypes": ["org.apache.poi.ss.usermodel.DataValidationHelper", "org.apache.poi.ss.usermodel.Sheet", "java.lang.Integer", "org.apache.poi.ss.usermodel.DataValidationConstraint"], "doc": "\n 挂载下拉的列，仅限一级选项\r\n"}, {"name": "markLinkedOptionsToSheet", "paramTypes": ["org.apache.poi.ss.usermodel.DataValidationHelper", "org.apache.poi.ss.usermodel.Sheet", "java.lang.Integer", "java.lang.Integer", "org.apache.poi.ss.usermodel.DataValidationConstraint"], "doc": "\n 挂载下拉的列，仅限二级选项\r\n"}, {"name": "markDataValidationToSheet", "paramTypes": ["org.apache.poi.ss.usermodel.DataValidationHelper", "org.apache.poi.ss.usermodel.Sheet", "org.apache.poi.ss.usermodel.DataValidationConstraint", "org.apache.poi.ss.util.CellRangeAddressList"], "doc": "\n 应用数据校验\r\n"}, {"name": "getExcelColumnName", "paramTypes": ["int"], "doc": "\n <h2>依据列index获取列名英文</h2>\r\n 依据列index转换为Excel中的列名英文\r\n <p>例如第1列，index为0，解析出来为A列</p>\r\n 第27列，index为26，解析为AA列\r\n <p>第28列，index为27，解析为AB列</p>\r\n\r\n @param columnIndex 列index\r\n @return 列index所在得英文名\r\n"}], "constructors": []}