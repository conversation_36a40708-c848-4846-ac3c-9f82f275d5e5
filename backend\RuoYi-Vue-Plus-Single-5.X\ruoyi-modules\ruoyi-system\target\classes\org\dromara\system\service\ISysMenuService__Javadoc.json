{"doc": "\n 菜单 业务层\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectMenuList", "paramTypes": ["java.lang.Long"], "doc": "\n 根据用户查询系统菜单列表\r\n\r\n @param userId 用户ID\r\n @return 菜单列表\r\n"}, {"name": "selectMenuList", "paramTypes": ["org.dromara.system.domain.bo.SysMenuBo", "java.lang.Long"], "doc": "\n 根据用户查询系统菜单列表\r\n\r\n @param menu   菜单信息\r\n @param userId 用户ID\r\n @return 菜单列表\r\n"}, {"name": "selectMenuPermsByUserId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据用户ID查询权限\r\n\r\n @param userId 用户ID\r\n @return 权限列表\r\n"}, {"name": "selectMenuPermsByRoleId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据角色ID查询权限\r\n\r\n @param roleId 角色ID\r\n @return 权限列表\r\n"}, {"name": "selectMenuTreeByUserId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据用户ID查询菜单树信息\r\n\r\n @param userId 用户ID\r\n @return 菜单列表\r\n"}, {"name": "selectMenuListByRoleId", "paramTypes": ["java.lang.Long"], "doc": "\n 根据角色ID查询菜单树信息\r\n\r\n @param roleId 角色ID\r\n @return 选中菜单列表\r\n"}, {"name": "buildMenus", "paramTypes": ["java.util.List"], "doc": "\n 构建前端路由所需要的菜单\r\n\r\n @param menus 菜单列表\r\n @return 路由列表\r\n"}, {"name": "buildMenuTreeSelect", "paramTypes": ["java.util.List"], "doc": "\n 构建前端所需要下拉树结构\r\n\r\n @param menus 菜单列表\r\n @return 下拉树结构列表\r\n"}, {"name": "selectMenuById", "paramTypes": ["java.lang.Long"], "doc": "\n 根据菜单ID查询信息\r\n\r\n @param menuId 菜单ID\r\n @return 菜单信息\r\n"}, {"name": "hasChildByMenuId", "paramTypes": ["java.lang.Long"], "doc": "\n 是否存在菜单子节点\r\n\r\n @param menuId 菜单ID\r\n @return 结果 true 存在 false 不存在\r\n"}, {"name": "hasChildByMenuId", "paramTypes": ["java.util.List"], "doc": "\n 是否存在菜单子节点\r\n\r\n @param menuIds 菜单ID串\r\n @return 结果 true 存在 false 不存在\r\n"}, {"name": "checkMenuExistRole", "paramTypes": ["java.lang.Long"], "doc": "\n 查询菜单是否存在角色\r\n\r\n @param menuId 菜单ID\r\n @return 结果 true 存在 false 不存在\r\n"}, {"name": "insertMenu", "paramTypes": ["org.dromara.system.domain.bo.SysMenuBo"], "doc": "\n 新增保存菜单信息\r\n\r\n @param bo 菜单信息\r\n @return 结果\r\n"}, {"name": "updateMenu", "paramTypes": ["org.dromara.system.domain.bo.SysMenuBo"], "doc": "\n 修改保存菜单信息\r\n\r\n @param bo 菜单信息\r\n @return 结果\r\n"}, {"name": "deleteMenuById", "paramTypes": ["java.lang.Long"], "doc": "\n 删除菜单管理信息\r\n\r\n @param menuId 菜单ID\r\n @return 结果\r\n"}, {"name": "deleteMenuById", "paramTypes": ["java.util.List"], "doc": "\n 批量删除菜单管理信息\r\n\r\n @param menuIds 菜单ID串\r\n @return 结果\r\n"}, {"name": "checkMenuNameUnique", "paramTypes": ["org.dromara.system.domain.bo.SysMenuBo"], "doc": "\n 校验菜单名称是否唯一\r\n\r\n @param menu 菜单信息\r\n @return 结果\r\n"}], "constructors": []}