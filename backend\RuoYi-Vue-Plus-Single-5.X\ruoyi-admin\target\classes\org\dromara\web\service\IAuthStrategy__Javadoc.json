{"doc": "\n 授权策略\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "login", "paramTypes": ["java.lang.String", "org.dromara.system.domain.vo.SysClientVo", "java.lang.String"], "doc": "\n 登录\r\n\r\n @param body      登录对象\r\n @param client    授权管理视图对象\r\n @param grantType 授权类型\r\n @return 登录验证信息\r\n"}, {"name": "login", "paramTypes": ["java.lang.String", "org.dromara.system.domain.vo.SysClientVo"], "doc": "\n 登录\r\n\r\n @param body   登录对象\r\n @param client 授权管理视图对象\r\n @return 登录验证信息\r\n"}], "constructors": []}