{"doc": "\n 文件处理工具类\r\n\r\n <AUTHOR> Li\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "setAttachmentResponseHeader", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "java.lang.String"], "doc": "\n 下载文件名重新编码\r\n\r\n @param response     响应对象\r\n @param realFileName 真实文件名\r\n"}, {"name": "percentEncode", "paramTypes": ["java.lang.String"], "doc": "\n 百分号编码工具方法\r\n\r\n @param s 需要百分号编码的字符串\r\n @return 百分号编码后的字符串\r\n"}, {"name": "isValidFileExtention", "paramTypes": ["org.springframework.web.multipart.MultipartFile", "java.lang.String[]"], "doc": "\n 检查文件扩展名是否符合要求\r\n"}, {"name": "getSecureFilePathForUpload", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 获取安全的文件路径\r\n\r\n @param originalFilename 原始文件名\r\n @param secureFilePath   安全路径\r\n @return 安全文件路径\r\n"}], "constructors": []}