package org.dromara.app.service;

import org.dromara.app.domain.InterviewReport;
import org.dromara.app.service.IMultimodalAnalysisService.DimensionScore;

import java.awt.image.BufferedImage;
import java.util.List;
import java.util.Map;

/**
 * 图表生成服务接口
 *
 * <AUTHOR>
 */
public interface IChartGenerationService {

    /**
     * 生成雷达图
     *
     * @param dimensionScores 维度评分列表
     * @param title 图表标题
     * @return 雷达图图像
     */
    BufferedImage generateRadarChart(List<DimensionScore> dimensionScores, String title);

    /**
     * 生成雷达图（带对比数据）
     *
     * @param dimensionScores 维度评分列表
     * @param industryAverages 行业平均分
     * @param title 图表标题
     * @return 雷达图图像
     */
    BufferedImage generateRadarChartWithComparison(List<DimensionScore> dimensionScores, 
                                                  List<Integer> industryAverages, String title);

    /**
     * 生成柱状图
     *
     * @param data 数据
     * @param title 图表标题
     * @param xAxisLabel X轴标签
     * @param yAxisLabel Y轴标签
     * @return 柱状图图像
     */
    BufferedImage generateBarChart(Map<String, Integer> data, String title, 
                                  String xAxisLabel, String yAxisLabel);

    /**
     * 生成折线图
     *
     * @param data 数据
     * @param title 图表标题
     * @param xAxisLabel X轴标签
     * @param yAxisLabel Y轴标签
     * @return 折线图图像
     */
    BufferedImage generateLineChart(Map<String, Integer> data, String title, 
                                   String xAxisLabel, String yAxisLabel);

    /**
     * 生成饼图
     *
     * @param data 数据
     * @param title 图表标题
     * @return 饼图图像
     */
    BufferedImage generatePieChart(Map<String, Integer> data, String title);

    /**
     * 生成能力分布图
     *
     * @param dimensionScores 维度评分列表
     * @return 能力分布图图像
     */
    BufferedImage generateCapabilityDistributionChart(List<DimensionScore> dimensionScores);

    /**
     * 生成趋势分析图
     *
     * @param historicalData 历史数据
     * @param title 图表标题
     * @return 趋势分析图图像
     */
    BufferedImage generateTrendChart(Map<String, List<Integer>> historicalData, String title);

    /**
     * 生成综合评估仪表盘
     *
     * @param report 面试报告
     * @return 仪表盘图像
     */
    BufferedImage generateDashboard(InterviewReport report);

    /**
     * 将图像转换为字节数组
     *
     * @param image 图像
     * @param format 格式（PNG, JPEG等）
     * @return 字节数组
     */
    byte[] imageToByteArray(BufferedImage image, String format);

    /**
     * 保存图像到文件
     *
     * @param image 图像
     * @param filePath 文件路径
     * @param format 格式
     * @return 是否保存成功
     */
    boolean saveImageToFile(BufferedImage image, String filePath, String format);
}