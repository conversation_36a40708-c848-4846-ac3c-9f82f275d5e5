package org.dromara.app.exception;

import org.dromara.common.core.exception.ServiceException;

/**
 * 面试业务异常
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
public class InterviewException extends ServiceException {

    private static final long serialVersionUID = 1L;

    public InterviewException(String message) {
        super(message);
    }

    public InterviewException(int code, String message) {
        super(message, code);
    }

    /**
     * 岗位不存在异常
     */
    public static class JobNotFoundException extends InterviewException {
        public JobNotFoundException(Long jobId) {
            super("岗位不存在: " + jobId);
        }
    }

    /**
     * 面试模式不存在异常
     */
    public static class InterviewModeNotFoundException extends InterviewException {
        public InterviewModeNotFoundException(String modeId) {
            super("面试模式不存在: " + modeId);
        }
    }

    /**
     * 会话已过期异常
     */
    public static class SessionExpiredException extends InterviewException {
        public SessionExpiredException(String sessionId) {
            super("面试会话已过期: " + sessionId);
        }
    }

    /**
     * 设备检测失败异常
     */
    public static class DeviceCheckFailedException extends InterviewException {
        public DeviceCheckFailedException(String message) {
            super("设备检测失败: " + message);
        }
    }

    /**
     * 用户未登录异常
     */
    public static class UserNotLoginException extends InterviewException {
        public UserNotLoginException() {
            super(401, "用户未登录，请先登录");
        }
    }

}
