{"doc": "\n AI Agent上下文\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "sessionId", "doc": "\n 会话ID\r\n"}, {"name": "userId", "doc": "\n 用户ID\r\n"}, {"name": "agentType", "doc": "\n Agent类型\r\n"}, {"name": "parameters", "doc": "\n 上下文参数\r\n"}, {"name": "createTime", "doc": "\n 创建时间\r\n"}, {"name": "updateTime", "doc": "\n 最后更新时间\r\n"}], "enumConstants": [], "methods": [{"name": "setParameter", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": "\n 设置参数\r\n"}, {"name": "getParameter", "paramTypes": ["java.lang.String"], "doc": "\n 获取参数\r\n"}, {"name": "getParameter", "paramTypes": ["java.lang.String", "java.lang.Object"], "doc": "\n 获取参数（带默认值）\r\n"}], "constructors": []}