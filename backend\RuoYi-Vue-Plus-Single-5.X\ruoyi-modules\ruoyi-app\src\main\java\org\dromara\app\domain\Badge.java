package org.dromara.app.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

/**
 * 徽章实体
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_badge")
public class Badge extends BaseEntity {

    /**
     * 徽章ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 徽章图标
     */
    private String icon;

    /**
     * 徽章颜色
     */
    private String color;

    /**
     * 徽章标题
     */
    private String title;

    /**
     * 徽章描述
     */
    private String description;

    /**
     * 徽章类别
     */
    private String category;

    /**
     * 稀有度
     */
    private String rarity;

    /**
     * 关联成就ID
     */
    private String achievementId;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 是否启用
     */
    private Boolean isEnabled;

    /**
     * 解锁条件（单独徽章可能不关联成就）
     */
    private String unlockCriteria;

    /**
     * 徽章特效
     */
    private String effect;

    /**
     * 特殊徽章标志（如限时、活动等）
     */
    private String specialFlag;

    /**
     * 徽章标签（用逗号分隔）
     */
    private String tags;
}
