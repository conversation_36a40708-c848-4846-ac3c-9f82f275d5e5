package org.dromara.app.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.app.domain.enums.ActivityType;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.time.LocalDateTime;

/**
 * 用户活动总览对象 app_activity_summary
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_activity_summary")
public class ActivitySummary extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 总览ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 总活动时长(毫秒)
     */
    private Long totalDuration;

    /**
     * 总会话数
     */
    private Integer totalSessions;

    /**
     * 课程学习时长(毫秒)
     */
    private Long courseDuration;

    /**
     * 面试练习时长(毫秒)
     */
    private Long interviewDuration;

    /**
     * 书籍阅读时长(毫秒)
     */
    private Long bookDuration;

    /**
     * 视频学习时长(毫秒)
     */
    private Long videoDuration;

    /**
     * 习题练习时长(毫秒)
     */
    private Long exerciseDuration;

    /**
     * 文档阅读时长(毫秒)
     */
    private Long documentDuration;

    /**
     * 其他活动时长(毫秒)
     */
    private Long otherDuration;

    /**
     * 最后活动时间
     */
    private LocalDateTime lastActivityTime;

    /**
     * 初始化用户活动总览
     *
     * @param userId 用户ID
     * @return 初始化的总览对象
     */
    public static ActivitySummary initialize(Long userId) {
        ActivitySummary summary = new ActivitySummary();
        summary.setUserId(userId);
        summary.setTotalDuration(0L);
        summary.setTotalSessions(0);
        summary.setCourseDuration(0L);
        summary.setInterviewDuration(0L);
        summary.setBookDuration(0L);
        summary.setVideoDuration(0L);
        summary.setExerciseDuration(0L);
        summary.setDocumentDuration(0L);
        summary.setOtherDuration(0L);
        return summary;
    }

    /**
     * 更新活动时长
     *
     * @param activityType 活动类型
     * @param duration     时长(毫秒)
     */
    public void updateDuration(ActivityType activityType, Long duration) {
        if (duration == null || duration <= 0) {
            return;
        }

        // 更新总时长
        this.totalDuration = (this.totalDuration != null ? this.totalDuration : 0L) + duration;

        // 更新总会话数
        this.totalSessions = (this.totalSessions != null ? this.totalSessions : 0) + 1;

        // 更新最后活动时间
        this.lastActivityTime = LocalDateTime.now();

        // 根据活动类型更新对应的时长
        switch (activityType) {
            case COURSE:
                this.courseDuration = (this.courseDuration != null ? this.courseDuration : 0L) + duration;
                break;
            case INTERVIEW:
                this.interviewDuration = (this.interviewDuration != null ? this.interviewDuration : 0L) + duration;
                break;
            case BOOK:
                this.bookDuration = (this.bookDuration != null ? this.bookDuration : 0L) + duration;
                break;
            case VIDEO:
                this.videoDuration = (this.videoDuration != null ? this.videoDuration : 0L) + duration;
                break;
            case EXERCISE:
                this.exerciseDuration = (this.exerciseDuration != null ? this.exerciseDuration : 0L) + duration;
                break;
            case DOCUMENT:
                this.documentDuration = (this.documentDuration != null ? this.documentDuration : 0L) + duration;
                break;
            case OTHER:
                this.otherDuration = (this.otherDuration != null ? this.otherDuration : 0L) + duration;
                break;
        }
    }

    /**
     * 获取指定类型的活动时长
     *
     * @param activityType 活动类型
     * @return 时长(毫秒)
     */
    public Long getDurationByType(ActivityType activityType) {
        switch (activityType) {
            case COURSE:
                return this.courseDuration != null ? this.courseDuration : 0L;
            case INTERVIEW:
                return this.interviewDuration != null ? this.interviewDuration : 0L;
            case BOOK:
                return this.bookDuration != null ? this.bookDuration : 0L;
            case VIDEO:
                return this.videoDuration != null ? this.videoDuration : 0L;
            case EXERCISE:
                return this.exerciseDuration != null ? this.exerciseDuration : 0L;
            case DOCUMENT:
                return this.documentDuration != null ? this.documentDuration : 0L;
            case OTHER:
                return this.otherDuration != null ? this.otherDuration : 0L;
            default:
                return 0L;
        }
    }
}
