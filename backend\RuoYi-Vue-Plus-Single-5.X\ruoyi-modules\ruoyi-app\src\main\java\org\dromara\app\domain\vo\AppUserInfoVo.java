package org.dromara.app.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 应用用户信息VO
 *
 * <AUTHOR>
 */
@Data
public class AppUserInfoVo {

    /**
     * 用户ID
     */
    private String id;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 用户姓名
     */
    private String name;

    /**
     * 学号
     */
    private String studentId;

    /**
     * 专业
     */
    private String major;

    /**
     * 年级
     */
    private String grade;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 访问令牌
     */
    private String tokenValue;

    /**
     * 令牌名称
     */
    private String tokenName;

    /**
     * 刷新令牌
     */
    private String refreshToken;

    /**
     * 注册时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date registeredAt;
}
