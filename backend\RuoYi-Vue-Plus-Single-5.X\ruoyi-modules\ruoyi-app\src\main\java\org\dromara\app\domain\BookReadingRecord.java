package org.dromara.app.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 用户书籍阅读记录对象 app_book_reading_record
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_book_reading_record")
public class BookReadingRecord extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 记录ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 书籍ID
     */
    private Long bookId;

    /**
     * 当前章节ID（MongoDB中的ID）
     */
    private String currentChapterId;

    /**
     * 当前章节索引
     */
    private Integer currentChapterIndex;

    /**
     * 阅读进度百分比
     */
    private BigDecimal readingProgress;

    /**
     * 总阅读时长（分钟）
     */
    private Integer totalReadingTime;

    /**
     * 最后阅读时间
     */
    private LocalDateTime lastReadingTime;

    /**
     * 阅读设置（字体大小、主题等）
     */
    private String readingSettings;

    /**
     * 已完成章节ID列表，逗号分隔
     */
    private String completedChapters;

    /**
     * 是否读完：0-未读完，1-已读完
     */
    private Boolean isFinished;

    /**
     * 阅读设置对象（转换后的字段，不存储到数据库）
     */
    @TableField(exist = false)
    private Map<String, Object> readingSettingsMap;

    /**
     * 已完成章节列表（转换后的字段，不存储到数据库）
     */
    @TableField(exist = false)
    private List<String> completedChapterList;

    /**
     * 书籍信息（关联查询，不存储到数据库）
     */
    @TableField(exist = false)
    private Book book;
}
