package org.dromara.app.domain;

import com.baomidou.mybatisplus.annotation.OrderBy;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;
import java.util.List;

/**
 * 面试模式对象 app_interview_mode
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_interview_mode")
public class InterviewMode extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 模式ID
     */
    @TableId(value = "id")
    private String id;

    /**
     * 模式名称
     */
    private String name;

    /**
     * 模式描述
     */
    private String description;

    /**
     * 模式图标
     */
    private String icon;

    /**
     * 模式颜色
     */
    private String color;

    /**
     * 默认时长（分钟）
     */
    private Integer duration;

    /**
     * 难度等级（1-5）
     */
    private Integer difficulty;

    /**
     * 模式特性（JSON数组）
     */
    private List<String> features;

    /**
     * 排序号
     */
    @OrderBy(asc = true, sort = 1)
    private Integer sortOrder;

    /**
     * 状态（0正常 1停用）
     */
    private String status;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    private String delFlag;

}
