package org.dromara.app.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.config.XunfeiAvatarConfig;
import org.dromara.app.domain.dto.avatar.AvatarStartDto;
import org.dromara.app.domain.dto.avatar.AvatarTextDto;
import org.dromara.app.domain.vo.avatar.AvatarSessionVo;
import org.dromara.app.service.IXunfeiAvatarService;
import org.dromara.app.service.avatar.XunfeiAvatarAuthService;
import org.dromara.app.service.avatar.XunfeiAvatarProtocolService;
import org.dromara.app.service.avatar.XunfeiAvatarWebSocketService;
import org.dromara.common.core.exception.ServiceException;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

/**
 * 讯飞数字人服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-20
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class XunfeiAvatarServiceImpl implements IXunfeiAvatarService {

    private final XunfeiAvatarConfig config;
    private final XunfeiAvatarAuthService authService;
    private final XunfeiAvatarProtocolService protocolService;

    // 会话管理
    private final Map<String, AvatarSession> sessions = new ConcurrentHashMap<>();
    private final ScheduledExecutorService heartbeatExecutor = Executors.newScheduledThreadPool(5);

    /**
     * 内部会话类
     */
    private static class AvatarSession {
        private final String sessionId;
        private final XunfeiAvatarWebSocketService webSocketService;
        private final AvatarSessionVo sessionInfo;
        private volatile LocalDateTime lastActiveTime;

        public AvatarSession(String sessionId, XunfeiAvatarWebSocketService webSocketService, AvatarSessionVo sessionInfo) {
            this.sessionId = sessionId;
            this.webSocketService = webSocketService;
            this.sessionInfo = sessionInfo;
            this.lastActiveTime = LocalDateTime.now();
        }

        public void updateActiveTime() {
            this.lastActiveTime = LocalDateTime.now();
            this.sessionInfo.setLastActiveTime(this.lastActiveTime);
        }
    }

    @Override
    public AvatarSessionVo startSession(AvatarStartDto startDto) {
        String sessionId = UUID.randomUUID().toString();
        log.info("启动数字人会话: sessionId={}", sessionId);

        try {
            // 创建WebSocket服务实例
            XunfeiAvatarWebSocketService webSocketService = new XunfeiAvatarWebSocketService();

            // 生成认证URL
            String requestUrl = authService.assembleRequestUrl(
                    config.getAvatarUrl(),
                    config.getApiKey(),
                    config.getApiSecret()
            );

            // 建立连接
            long timeout = startDto.getTimeout() != null ? startDto.getTimeout() : config.getConnectTimeout() / 1000;
            boolean connected = webSocketService.connect(requestUrl, timeout);
            if (!connected) {
                throw new ServiceException("WebSocket连接失败");
            }

            // 构建启动请求
            JSONObject startRequest = protocolService.buildStartRequest(
                    startDto.getAvatarId(),
                    startDto.getSceneId(),
                    startDto.getVcn(),
                    startDto.getWidth(),
                    startDto.getHeight(),
                    startDto.getBackground()
            );

            // 发送启动请求并等待响应
            boolean started = webSocketService.startAndWait(startRequest, timeout);
            if (!started) {
                webSocketService.close();
                throw new ServiceException("数字人启动失败");
            }

            // 创建会话信息
            AvatarSessionVo sessionInfo = new AvatarSessionVo();
            sessionInfo.setSessionId(sessionId);
            sessionInfo.setAvatarId(startDto.getAvatarId() != null ? startDto.getAvatarId() : config.getDefaultAvatarId());
            sessionInfo.setSceneId(startDto.getSceneId() != null ? startDto.getSceneId() : config.getDefaultSceneId());
            sessionInfo.setVcn(startDto.getVcn() != null ? startDto.getVcn() : config.getDefaultVcn());
            sessionInfo.setWidth(startDto.getWidth() != null ? startDto.getWidth() : config.getDefaultWidth());
            sessionInfo.setHeight(startDto.getHeight() != null ? startDto.getHeight() : config.getDefaultHeight());
            sessionInfo.setConnected(true);
            sessionInfo.setCreateTime(LocalDateTime.now());
            sessionInfo.setLastActiveTime(LocalDateTime.now());
            sessionInfo.setStatusDesc("已连接");

            // 获取推流地址
            String streamUrl = webSocketService.getStreamUrl();
            if (streamUrl != null && !streamUrl.isEmpty()) {
                sessionInfo.setStreamUrl(streamUrl);
                log.info("会话创建成功，推流地址: {}", streamUrl);
            } else {
                log.info("会话创建成功，推流地址将在后续消息中获取");
            }

            // 创建会话对象
            AvatarSession session = new AvatarSession(sessionId, webSocketService, sessionInfo);
            sessions.put(sessionId, session);

            // 设置消息处理器，用于实时更新推流地址
            webSocketService.setMessageHandler(message -> {
                try {
                    // 检查是否有推流地址更新
                    String currentStreamUrl = webSocketService.getStreamUrl();
                    if (currentStreamUrl != null && !currentStreamUrl.equals(sessionInfo.getStreamUrl())) {
                        sessionInfo.setStreamUrl(currentStreamUrl);
                        log.info("会话 {} 推流地址已更新: {}", sessionId, currentStreamUrl);
                    }
                } catch (Exception e) {
                    log.error("处理WebSocket消息时更新推流地址失败", e);
                }
            });

            // 启动心跳任务
            startHeartbeat(sessionId);

            log.info("数字人会话启动成功: sessionId={}", sessionId);
            return sessionInfo;

        } catch (Exception e) {
            log.error("启动数字人会话失败: sessionId={}", sessionId, e);
            throw new ServiceException("启动数字人会话失败: " + e.getMessage());
        }
    }

    @Override
    public boolean sendTextDriver(String sessionId, AvatarTextDto textDto) {
        AvatarSession session = getSession(sessionId);
        if (session == null) {
            return false;
        }

        try {
            JSONObject textRequest = protocolService.buildTextRequest(
                    textDto.getText(),
                    textDto.getVcn(),
                    textDto.getSpeed(),
                    textDto.getPitch(),
                    textDto.getVolume()
            );

            boolean sent = session.webSocketService.sendMessage(textRequest);
            if (sent) {
                session.updateActiveTime();
                log.info("发送文本驱动成功: sessionId={}, text={}", sessionId, textDto.getText());
            }
            return sent;

        } catch (Exception e) {
            log.error("发送文本驱动失败: sessionId={}", sessionId, e);
            return false;
        }
    }

    @Override
    public boolean sendTextInteract(String sessionId, AvatarTextDto textDto) {
        AvatarSession session = getSession(sessionId);
        if (session == null) {
            return false;
        }

        try {
            JSONObject textRequest = protocolService.buildTextInteractRequest(
                    textDto.getText(),
                    textDto.getVcn(),
                    textDto.getSpeed(),
                    textDto.getPitch(),
                    textDto.getVolume()
            );

            boolean sent = session.webSocketService.sendMessage(textRequest);
            if (sent) {
                session.updateActiveTime();
                log.info("发送文本交互成功: sessionId={}, text={}", sessionId, textDto.getText());
            }
            return sent;

        } catch (Exception e) {
            log.error("发送文本交互失败: sessionId={}", sessionId, e);
            return false;
        }
    }

    @Override
    public boolean sendAudioDriver(String sessionId, String audioData, int status) {
        AvatarSession session = getSession(sessionId);
        if (session == null) {
            return false;
        }

        try {
            String requestId = UUID.randomUUID().toString();
            JSONObject audioRequest = protocolService.buildAudioRequest(requestId, status, audioData);

            boolean sent = session.webSocketService.sendMessage(audioRequest);
            if (sent) {
                session.updateActiveTime();
                log.debug("发送音频驱动成功: sessionId={}, status={}", sessionId, status);
            }
            return sent;

        } catch (Exception e) {
            log.error("发送音频驱动失败: sessionId={}", sessionId, e);
            return false;
        }
    }

    @Override
    public boolean resetAvatar(String sessionId) {
        AvatarSession session = getSession(sessionId);
        if (session == null) {
            return false;
        }

        try {
            JSONObject resetRequest = protocolService.buildResetRequest();
            boolean sent = session.webSocketService.sendMessage(resetRequest);
            if (sent) {
                session.updateActiveTime();
                log.info("重置数字人成功: sessionId={}", sessionId);
            }
            return sent;

        } catch (Exception e) {
            log.error("重置数字人失败: sessionId={}", sessionId, e);
            return false;
        }
    }

    @Override
    public boolean stopSession(String sessionId) {
        AvatarSession session = sessions.remove(sessionId);
        if (session == null) {
            return false;
        }

        try {
            // 发送停止请求
            JSONObject stopRequest = protocolService.buildStopRequest();
            session.webSocketService.sendMessage(stopRequest);

            // 关闭连接
            session.webSocketService.close();

            // 更新会话状态
            session.sessionInfo.setConnected(false);
            session.sessionInfo.setStatusDesc("已断开");

            log.info("停止数字人会话成功: sessionId={}", sessionId);
            return true;

        } catch (Exception e) {
            log.error("停止数字人会话失败: sessionId={}", sessionId, e);
            return false;
        }
    }

    @Override
    public boolean sendHeartbeat(String sessionId) {
        AvatarSession session = getSession(sessionId);
        if (session == null) {
            return false;
        }

        try {
            JSONObject pingRequest = protocolService.buildPingRequest();
            boolean sent = session.webSocketService.sendMessage(pingRequest);
            if (sent) {
                session.updateActiveTime();
                log.debug("发送心跳成功: sessionId={}", sessionId);
            }
            return sent;

        } catch (Exception e) {
            log.error("发送心跳失败: sessionId={}", sessionId, e);
            return false;
        }
    }

    @Override
    public boolean sendAction(String sessionId, String actionType, String actionValue) {
        AvatarSession session = getSession(sessionId);
        if (session == null) {
            return false;
        }

        try {
            JSONObject cmdRequest = protocolService.buildCmdRequest(actionType, actionValue);
            boolean sent = session.webSocketService.sendMessage(cmdRequest);
            if (sent) {
                session.updateActiveTime();
                log.info("发送动作指令成功: sessionId={}, actionType={}, actionValue={}", sessionId, actionType, actionValue);
            }
            return sent;

        } catch (Exception e) {
            log.error("发送动作指令失败: sessionId={}", sessionId, e);
            return false;
        }
    }

    @Override
    public void setMessageHandler(String sessionId, Consumer<JSONObject> messageHandler) {
        AvatarSession session = getSession(sessionId);
        if (session != null) {
            session.webSocketService.setMessageHandler(messageHandler);
        }
    }

    @Override
    public void setErrorHandler(String sessionId, Consumer<String> errorHandler) {
        AvatarSession session = getSession(sessionId);
        if (session != null) {
            session.webSocketService.setErrorHandler(errorHandler);
        }
    }

    @Override
    public boolean isSessionActive(String sessionId) {
        AvatarSession session = getSession(sessionId);
        return session != null && session.webSocketService.isConnected();
    }

    @Override
    public AvatarSessionVo getSessionInfo(String sessionId) {
        AvatarSession session = getSession(sessionId);
        return session != null ? session.sessionInfo : null;
    }

    @Override
    public boolean updateStreamUrl(String sessionId) {
        AvatarSession session = getSession(sessionId);
        if (session == null) {
            return false;
        }

        try {
            String currentStreamUrl = session.webSocketService.getStreamUrl();
            if (currentStreamUrl != null && !currentStreamUrl.equals(session.sessionInfo.getStreamUrl())) {
                session.sessionInfo.setStreamUrl(currentStreamUrl);
                session.updateActiveTime();
                log.info("会话 {} 推流地址已更新: {}", sessionId, currentStreamUrl);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("更新会话推流地址失败: sessionId={}", sessionId, e);
            return false;
        }
    }

    /**
     * 获取会话
     */
    private AvatarSession getSession(String sessionId) {
        if (StrUtil.isBlank(sessionId)) {
            return null;
        }
        return sessions.get(sessionId);
    }

    /**
     * 启动心跳任务
     */
    private void startHeartbeat(String sessionId) {
        heartbeatExecutor.scheduleAtFixedRate(() -> {
            try {
                if (isSessionActive(sessionId)) {
                    sendHeartbeat(sessionId);
                } else {
                    // 会话已断开，清理资源
                    sessions.remove(sessionId);
                }
            } catch (Exception e) {
                log.error("心跳任务执行失败: sessionId={}", sessionId, e);
            }
        }, config.getHeartbeatInterval(), config.getHeartbeatInterval(), TimeUnit.MILLISECONDS);
    }
}
