package org.dromara.app.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;
import java.io.Serializable;

/**
 * 专业实体
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_major")
public class Major extends BaseEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 专业ID
     */
    @TableId(value = "major_id")
    private Long majorId;

    /**
     * 专业编码
     */
    @TableField("major_code")
    private String majorCode;

    /**
     * 专业名称
     */
    @TableField("major_name")
    private String majorName;

    /**
     * 专业图标
     */
    @TableField("icon")
    private String icon;

    /**
     * 专业颜色
     */
    @TableField("color")
    private String color;

    /**
     * 题库数量
     */
    @TableField("question_bank_count")
    private Integer questionBankCount;

    /**
     * 排序
     */
    @TableField("sort")
    private Integer sort;

    /**
     * 状态（0正常 1停用）
     */
    @TableField("status")
    private String status;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;
}
