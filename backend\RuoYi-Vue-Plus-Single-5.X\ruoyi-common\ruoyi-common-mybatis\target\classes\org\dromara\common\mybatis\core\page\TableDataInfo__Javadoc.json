{"doc": "\n 表格分页数据对象\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "total", "doc": "\n 总记录数\r\n"}, {"name": "rows", "doc": "\n 列表数据\r\n"}, {"name": "code", "doc": "\n 消息状态码\r\n"}, {"name": "msg", "doc": "\n 消息内容\r\n"}], "enumConstants": [], "methods": [{"name": "build", "paramTypes": ["com.baomidou.mybatisplus.core.metadata.IPage"], "doc": "\n 根据分页对象构建表格分页数据对象\r\n"}, {"name": "build", "paramTypes": ["java.util.List"], "doc": "\n 根据数据列表构建表格分页数据对象\r\n"}, {"name": "build", "paramTypes": [], "doc": "\n 构建表格分页数据对象\r\n"}, {"name": "build", "paramTypes": ["java.util.List", "com.baomidou.mybatisplus.core.metadata.IPage"], "doc": "\n 根据原始数据列表和分页参数，构建表格分页数据对象（用于假分页）\r\n\r\n @param list 原始数据列表（全部数据）\r\n @param page 分页参数对象（包含当前页码、每页大小等）\r\n @return 构造好的分页结果 TableDataInfo<T>\r\n"}], "constructors": [{"name": "<init>", "paramTypes": ["java.util.List", "long"], "doc": "\n 分页\r\n\r\n @param list  列表数据\r\n @param total 总记录数\r\n"}]}