{"doc": "\n 日期格式与时间格式枚举\r\n", "fields": [{"name": "timeFormat", "doc": "\n 时间格式\r\n"}], "enumConstants": [{"name": "YY", "doc": "\n 例如：2023年表示为\"23\"\r\n"}, {"name": "YYYY", "doc": "\n 例如：2023年表示为\"2023\"\r\n"}, {"name": "YYYY_MM", "doc": "\n 例例如，2023年7月可以表示为 \"2023-07\"\r\n"}, {"name": "YYYY_MM_DD", "doc": "\n 例如，日期 \"2023年7月22日\" 可以表示为 \"2023-07-22\"\r\n"}, {"name": "YYYY_MM_DD_HH_MM", "doc": "\n 例如，当前时间如果是 \"2023年7月22日下午3点30分\"，则可以表示为 \"2023-07-22 15:30\"\r\n"}, {"name": "YYYY_MM_DD_HH_MM_SS", "doc": "\n 例如，当前时间如果是 \"2023年7月22日下午3点30分45秒\"，则可以表示为 \"2023-07-22 15:30:45\"\r\n"}, {"name": "HH_MM_SS", "doc": "\n 例如：下午3点30分45秒，表示为 \"15:30:45\"\r\n"}, {"name": "YYYY_MM_SLASH", "doc": "\n 例例如，2023年7月可以表示为 \"2023/07\"\r\n"}, {"name": "YYYY_MM_DD_SLASH", "doc": "\n 例如，日期 \"2023年7月22日\" 可以表示为 \"2023/07/22\"\r\n"}, {"name": "YYYY_MM_DD_HH_MM_SLASH", "doc": "\n 例如，当前时间如果是 \"2023年7月22日下午3点30分45秒\"，则可以表示为 \"2023/07/22 15:30:45\"\r\n"}, {"name": "YYYY_MM_DD_HH_MM_SS_SLASH", "doc": "\n 例如，当前时间如果是 \"2023年7月22日下午3点30分45秒\"，则可以表示为 \"2023/07/22 15:30:45\"\r\n"}, {"name": "YYYY_MM_DOT", "doc": "\n 例例如，2023年7月可以表示为 \"2023.07\"\r\n"}, {"name": "YYYY_MM_DD_DOT", "doc": "\n 例如，日期 \"2023年7月22日\" 可以表示为 \"2023.07.22\"\r\n"}, {"name": "YYYY_MM_DD_HH_MM_DOT", "doc": "\n 例如，当前时间如果是 \"2023年7月22日下午3点30分\"，则可以表示为 \"2023.07.22 15:30\"\r\n"}, {"name": "YYYY_MM_DD_HH_MM_SS_DOT", "doc": "\n 例如，当前时间如果是 \"2023年7月22日下午3点30分45秒\"，则可以表示为 \"2023.07.22 15:30:45\"\r\n"}, {"name": "YYYYMM", "doc": "\n 例如，2023年7月可以表示为 \"202307\"\r\n"}, {"name": "YYYYMMDD", "doc": "\n 例如，2023年7月22日可以表示为 \"20230722\"\r\n"}, {"name": "YYYYMMDDHH", "doc": "\n 例如，2023年7月22日下午3点可以表示为 \"2023072215\"\r\n"}, {"name": "YYYYMMDDHHMM", "doc": "\n 例如，2023年7月22日下午3点30分可以表示为 \"202307221530\"\r\n"}, {"name": "YYYYMMDDHHMMSS", "doc": "\n 例如，2023年7月22日下午3点30分45秒可以表示为 \"20230722153045\"\r\n"}], "methods": [], "constructors": []}