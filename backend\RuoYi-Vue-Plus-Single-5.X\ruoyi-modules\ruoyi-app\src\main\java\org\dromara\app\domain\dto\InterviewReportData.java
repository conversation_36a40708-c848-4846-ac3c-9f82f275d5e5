package org.dromara.app.domain.dto;

import lombok.Data;
import org.dromara.app.domain.InterviewReport;

import java.util.List;
import java.util.Map;

/**
 * 面试报告数据传输对象
 *
 * <AUTHOR>
 */
@Data
public class InterviewReportData {

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 生成时间
     */
    private Long generateTime;

    /**
     * 基本信息
     */
    private BasicInfo basicInfo;

    /**
     * 总体评分
     */
    private Integer overallScore;

    /**
     * 等级
     */
    private String rank;

    /**
     * 百分位
     */
    private Integer percentile;

    /**
     * 维度评分
     */
    private List<DimensionScore> dimensionScores;

    /**
     * 优势列表
     */
    private List<String> strengths;

    /**
     * 劣势列表
     */
    private List<String> weaknesses;

    /**
     * 总体反馈
     */
    private String overallFeedback;

    /**
     * 雷达图数据
     */
    private RadarChartData radarChartData;

    /**
     * 改进建议
     */
    private List<ImprovementSuggestion> improvementSuggestions;

    /**
     * 学习路径推荐
     */
    private List<LearningPathRecommendation> learningPaths;

    /**
     * 对比数据
     */
    private Map<String, Object> comparisonData;

    /**
     * 基本信息
     */
    @Data
    public static class BasicInfo {
        /**
         * 候选人姓名
         */
        private String candidateName;

        /**
         * 应聘岗位
         */
        private String jobPosition;

        /**
         * 公司名称
         */
        private String company;

        /**
         * 面试日期
         */
        private String interviewDate;

        /**
         * 面试时长
         */
        private String duration;

        /**
         * 总问题数
         */
        private Integer totalQuestions;

        /**
         * 已回答问题数
         */
        private Integer answeredQuestions;
    }

    /**
     * 维度评分
     */
    @Data
    public static class DimensionScore {
        /**
         * 维度名称
         */
        private String dimension;

        /**
         * 得分
         */
        private Integer score;

        /**
         * 最大分数
         */
        private Integer maxScore;

        /**
         * 百分位
         */
        private Integer percentile;

        /**
         * 描述
         */
        private String description;

        /**
         * 优势
         */
        private List<String> strengths;

        /**
         * 劣势
         */
        private List<String> weaknesses;

        /**
         * 建议
         */
        private List<String> recommendations;
    }

    /**
     * 雷达图数据
     */
    @Data
    public static class RadarChartData {
        /**
         * 维度列表
         */
        private List<String> dimensions;

        /**
         * 分数列表
         */
        private List<Integer> scores;

        /**
         * 最大分数列表
         */
        private List<Integer> maxScores;

        /**
         * 行业平均分
         */
        private List<Integer> industryAverages;

        /**
         * 图表配置
         */
        private Map<String, Object> chartConfig;
    }

    /**
     * 改进建议
     */
    @Data
    public static class ImprovementSuggestion {
        /**
         * 标题
         */
        private String title;

        /**
         * 描述
         */
        private String description;

        /**
         * 行动项
         */
        private List<String> actionItems;

        /**
         * 优先级
         */
        private String priority;

        /**
         * 预计时间（天）
         */
        private Integer estimatedTime;

        /**
         * 难度
         */
        private String difficulty;
    }

    /**
     * 学习路径推荐
     */
    @Data
    public static class LearningPathRecommendation {
        /**
         * 标题
         */
        private String title;

        /**
         * 描述
         */
        private String description;

        /**
         * 预计学习时间（小时）
         */
        private Integer estimatedHours;

        /**
         * 难度
         */
        private String difficulty;

        /**
         * 资源列表
         */
        private List<LearningResource> resources;

        /**
         * 里程碑
         */
        private List<String> milestones;

        /**
         * 优先级
         */
        private Integer priority;
    }

    /**
     * 学习资源
     */
    @Data
    public static class LearningResource {
        /**
         * 标题
         */
        private String title;

        /**
         * 类型：video, article, course, practice
         */
        private String type;

        /**
         * 描述
         */
        private String description;

        /**
         * 链接
         */
        private String url;

        /**
         * 时长
         */
        private String duration;

        /**
         * 难度
         */
        private String difficulty;
    }
}