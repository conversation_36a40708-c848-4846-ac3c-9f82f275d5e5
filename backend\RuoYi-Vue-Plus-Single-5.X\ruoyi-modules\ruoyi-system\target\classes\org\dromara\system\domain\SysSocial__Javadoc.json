{"doc": "\n 社会化关系对象 sys_social\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "id", "doc": "\n 主键\r\n"}, {"name": "userId", "doc": "\n 用户ID\r\n"}, {"name": "authId", "doc": "\n 的唯一ID\r\n"}, {"name": "source", "doc": "\n 用户来源\r\n"}, {"name": "accessToken", "doc": "\n 用户的授权令牌\r\n"}, {"name": "expireIn", "doc": "\n 用户的授权令牌的有效期，部分平台可能没有\r\n"}, {"name": "refreshToken", "doc": "\n 刷新令牌，部分平台可能没有\r\n"}, {"name": "openId", "doc": "\n 用户的 open id\r\n"}, {"name": "userName", "doc": "\n 授权的第三方账号\r\n"}, {"name": "nick<PERSON><PERSON>", "doc": "\n 授权的第三方昵称\r\n"}, {"name": "email", "doc": "\n 授权的第三方邮箱\r\n"}, {"name": "avatar", "doc": "\n 授权的第三方头像地址\r\n"}, {"name": "accessCode", "doc": "\n 平台的授权信息，部分平台可能没有\r\n"}, {"name": "unionId", "doc": "\n 用户的 unionid\r\n"}, {"name": "scope", "doc": "\n 授予的权限，部分平台可能没有\r\n"}, {"name": "tokenType", "doc": "\n 个别平台的授权信息，部分平台可能没有\r\n"}, {"name": "idToken", "doc": "\n id token，部分平台可能没有\r\n"}, {"name": "macAlgorithm", "doc": "\n 小米平台用户的附带属性，部分平台可能没有\r\n"}, {"name": "<PERSON><PERSON><PERSON>", "doc": "\n 小米平台用户的附带属性，部分平台可能没有\r\n"}, {"name": "code", "doc": "\n 用户的授权code，部分平台可能没有\r\n"}, {"name": "oauthToken", "doc": "\n Twitter平台用户的附带属性，部分平台可能没有\r\n"}, {"name": "oauthTokenSecret", "doc": "\n Twitter平台用户的附带属性，部分平台可能没有\r\n"}], "enumConstants": [], "methods": [], "constructors": []}