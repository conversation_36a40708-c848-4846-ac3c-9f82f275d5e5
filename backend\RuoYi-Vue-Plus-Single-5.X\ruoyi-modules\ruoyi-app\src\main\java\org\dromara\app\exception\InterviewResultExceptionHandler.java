package org.dromara.app.exception;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.exception.ServiceException;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.stream.Collectors;

/**
 * 面试结果相关异常处理器
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@Slf4j
@RestControllerAdvice(basePackages = "org.dromara.app.controller")
public class InterviewResultExceptionHandler {

    /**
     * 处理业务异常
     */
    @ExceptionHandler(ServiceException.class)
    public R<Void> handleServiceException(ServiceException e) {
        log.error("业务异常: {}", e.getMessage());
        return R.fail(e.getCode(), e.getMessage());
    }

    /**
     * 处理参数验证异常（@Valid）
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public R<Void> handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
        String message = e.getBindingResult().getFieldErrors().stream()
            .map(FieldError::getDefaultMessage)
            .collect(Collectors.joining(", "));
        log.error("参数验证异常: {}", message);
        return R.fail("参数验证失败: " + message);
    }

    /**
     * 处理参数绑定异常
     */
    @ExceptionHandler(BindException.class)
    public R<Void> handleBindException(BindException e) {
        String message = e.getBindingResult().getFieldErrors().stream()
            .map(FieldError::getDefaultMessage)
            .collect(Collectors.joining(", "));
        log.error("参数绑定异常: {}", message);
        return R.fail("参数绑定失败: " + message);
    }

    /**
     * 处理约束验证异常（@Validated）
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public R<Void> handleConstraintViolationException(ConstraintViolationException e) {
        String message = e.getConstraintViolations().stream()
            .map(ConstraintViolation::getMessage)
            .collect(Collectors.joining(", "));
        log.error("约束验证异常: {}", message);
        return R.fail("参数验证失败: " + message);
    }

    /**
     * 处理非法参数异常
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public R<Void> handleIllegalArgumentException(IllegalArgumentException e) {
        log.error("非法参数异常: {}", e.getMessage());
        return R.fail("参数错误: " + e.getMessage());
    }

    /**
     * 处理空指针异常
     */
    @ExceptionHandler(NullPointerException.class)
    public R<Void> handleNullPointerException(NullPointerException e) {
        log.error("空指针异常", e);
        return R.fail("系统内部错误，请联系管理员");
    }

    /**
     * 处理数据库相关异常
     */
    @ExceptionHandler({
        org.springframework.dao.DataAccessException.class,
        com.baomidou.mybatisplus.core.exceptions.MybatisPlusException.class
    })
    public R<Void> handleDataAccessException(Exception e) {
        log.error("数据库操作异常", e);
        return R.fail("数据操作失败，请稍后重试");
    }

    /**
     * 处理其他未知异常
     */
    @ExceptionHandler(Exception.class)
    public R<Void> handleException(Exception e) {
        log.error("系统异常", e);
        return R.fail("系统异常，请稍后重试");
    }

    /**
     * 面试结果不存在异常
     */
    public static class InterviewResultNotFoundException extends ServiceException {
        public InterviewResultNotFoundException(String resultId) {
            super("面试结果不存在: " + resultId);
        }
    }

    /**
     * 面试结果已存在异常
     */
    public static class InterviewResultAlreadyExistsException extends ServiceException {
        public InterviewResultAlreadyExistsException(String sessionId) {
            super("面试结果已存在: " + sessionId);
        }
    }

    /**
     * 面试结果状态异常
     */
    public static class InterviewResultStatusException extends ServiceException {
        public InterviewResultStatusException(String message) {
            super("面试结果状态异常: " + message);
        }
    }

    /**
     * 权限不足异常
     */
    public static class InsufficientPermissionException extends ServiceException {
        public InsufficientPermissionException() {
            super("权限不足，无法访问该面试结果");
        }
    }

    /**
     * 分享链接过期异常
     */
    public static class ShareLinkExpiredException extends ServiceException {
        public ShareLinkExpiredException() {
            super("分享链接已过期");
        }
    }

    /**
     * 学习资源不可用异常
     */
    public static class LearningResourceUnavailableException extends ServiceException {
        public LearningResourceUnavailableException(String resourceId) {
            super("学习资源不可用: " + resourceId);
        }
    }

}
