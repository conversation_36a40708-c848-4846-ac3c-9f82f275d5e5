package org.dromara.app.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.app.domain.KnowledgeBase;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 知识库视图对象 app_knowledge_base
 *
 * <AUTHOR>
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = KnowledgeBase.class)
public class KnowledgeBaseVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 知识库ID
     */
    @ExcelProperty(value = "知识库ID")
    private Long id;

    /**
     * 知识库名称
     */
    @ExcelProperty(value = "知识库名称")
    private String name;

    /**
     * 知识库描述
     */
    @ExcelProperty(value = "知识库描述")
    private String description;

    /**
     * 知识库类型 (general/technical/business/etc.)
     */
    @ExcelProperty(value = "知识库类型")
    private String type;

    /**
     * 知识库状态 (0=禁用 1=启用)
     */
    @ExcelProperty(value = "知识库状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_normal_disable")
    private Integer status;

    /**
     * 向量维度 (默认1024)
     */
    @ExcelProperty(value = "向量维度")
    private Integer vectorDimension;

    /**
     * 文档数量
     */
    @ExcelProperty(value = "文档数量")
    private Long documentCount;

    /**
     * 向量数量
     */
    @ExcelProperty(value = "向量数量")
    private Long vectorCount;

    /**
     * 索引配置 (JSON格式)
     */
    private String indexConfig;

    /**
     * 扩展配置 (JSON格式)
     */
    private String extendConfig;

    /**
     * 最后更新时间
     */
    @ExcelProperty(value = "最后同步时间")
    private LocalDateTime lastSyncTime;

    /**
     * 排序字段
     */
    @ExcelProperty(value = "排序")
    private Integer sortOrder;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 创建部门
     */
    private Long createDept;

    /**
     * 创建者
     */
    @ExcelProperty(value = "创建者")
    private Long createBy;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    @ExcelProperty(value = "更新者")
    private Long updateBy;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    // ========== 扩展字段 ==========

    /**
     * 知识库类型名称（用于显示）
     */
    private String typeName;

    /**
     * 状态名称（用于显示）
     */
    private String statusName;

    /**
     * 创建者名称
     */
    private String createByName;

    /**
     * 更新者名称
     */
    private String updateByName;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 平均向量相似度（统计信息）
     */
    private Double avgSimilarity;

    /**
     * 最近处理文档数量
     */
    private Long recentProcessedCount;

    /**
     * 索引健康状态
     */
    private String indexHealth;
}
