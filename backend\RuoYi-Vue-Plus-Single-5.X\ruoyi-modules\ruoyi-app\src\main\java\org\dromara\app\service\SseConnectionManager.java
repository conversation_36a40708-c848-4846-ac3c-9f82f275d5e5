package org.dromara.app.service;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * SSE连接管理器
 * 负责管理SSE连接的生命周期，防止连接泄露
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SseConnectionManager {

    /**
     * 默认连接超时时间（毫秒）
     */
    private static final long DEFAULT_TIMEOUT = 30 * 60 * 1000L; // 30分钟
    /**
     * 最大连接数
     */
    private static final int MAX_CONNECTIONS = 500;
    /**
     * 清理任务执行间隔（分钟）
     */
    private static final int CLEANUP_INTERVAL_MINUTES = 5;
    /**
     * 连接缓存，存储连接ID到连接包装器的映射
     */
    private final Map<String, ConnectionWrapper> connections = new ConcurrentHashMap<>();
    /**
     * 定时清理任务执行器
     */
    private ScheduledExecutorService scheduler;

    @PostConstruct
    public void init() {
        scheduler = Executors.newScheduledThreadPool(1, r -> {
            Thread thread = new Thread(r, "SseConnectionCleanup");
            thread.setDaemon(true);
            return thread;
        });

        // 定期清理过期连接
        scheduler.scheduleAtFixedRate(
            this::cleanExpiredConnections,
            CLEANUP_INTERVAL_MINUTES,
            CLEANUP_INTERVAL_MINUTES,
            TimeUnit.MINUTES
        );

        log.info("SSE连接管理器初始化完成，清理间隔: {} 分钟", CLEANUP_INTERVAL_MINUTES);
    }

    @PreDestroy
    public void destroy() {
        if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdown();
            try {
                if (!scheduler.awaitTermination(10, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }

        // 关闭所有连接
        connections.values().forEach(wrapper -> {
            try {
                wrapper.getEmitter().complete();
            } catch (Exception e) {
                log.debug("关闭SSE连接时发生异常", e);
            }
        });
        connections.clear();

        log.info("SSE连接管理器已销毁");
    }

    /**
     * 创建SSE连接
     *
     * @param connectionId 连接ID
     * @return SSE发射器
     */
    public SseEmitter createConnection(String connectionId) {
        return createConnection(connectionId, DEFAULT_TIMEOUT);
    }

    /**
     * 创建SSE连接
     *
     * @param connectionId 连接ID
     * @param timeout      超时时间（毫秒）
     * @return SSE发射器
     */
    public SseEmitter createConnection(String connectionId, Long timeout) {
        // 检查连接数是否超限
        if (connections.size() >= MAX_CONNECTIONS) {
            cleanOldestConnection();
        }

        SseEmitter emitter = new SseEmitter(timeout);
        ConnectionWrapper wrapper = new ConnectionWrapper(emitter);

        // 设置连接事件处理器
        emitter.onCompletion(() -> {
            connections.remove(connectionId);
            log.debug("SSE连接完成: connectionId={}", connectionId);
        });

        emitter.onTimeout(() -> {
            connections.remove(connectionId);
            log.debug("SSE连接超时: connectionId={}", connectionId);
        });

        emitter.onError((ex) -> {
            connections.remove(connectionId);
            log.debug("SSE连接错误: connectionId={}, error={}", connectionId, ex.getMessage());
        });

        // 移除旧连接（如果存在）
        ConnectionWrapper oldWrapper = connections.put(connectionId, wrapper);
        if (oldWrapper != null) {
            try {
                oldWrapper.getEmitter().complete();
                log.debug("替换旧的SSE连接: connectionId={}", connectionId);
            } catch (Exception e) {
                log.debug("关闭旧SSE连接时发生异常", e);
            }
        }

        log.debug("创建SSE连接: connectionId={}, timeout={}ms, 当前连接数={}",
            connectionId, timeout, connections.size());

        return emitter;
    }

    /**
     * 获取SSE连接
     *
     * @param connectionId 连接ID
     * @return SSE发射器，如果不存在则返回null
     */
    public SseEmitter getConnection(String connectionId) {
        ConnectionWrapper wrapper = connections.get(connectionId);
        if (wrapper != null) {
            wrapper.updateLastAccess();
            return wrapper.getEmitter();
        }
        return null;
    }

    /**
     * 移除SSE连接
     *
     * @param connectionId 连接ID
     */
    public void removeConnection(String connectionId) {
        ConnectionWrapper wrapper = connections.remove(connectionId);
        if (wrapper != null) {
            try {
                wrapper.getEmitter().complete();
                log.debug("移除SSE连接: connectionId={}", connectionId);
            } catch (Exception e) {
                log.debug("关闭SSE连接时发生异常", e);
            }
        }
    }

    /**
     * 向指定连接发送数据
     *
     * @param connectionId 连接ID
     * @param data         数据
     * @return 是否发送成功
     */
    public boolean sendData(String connectionId, Object data) {
        SseEmitter emitter = getConnection(connectionId);
        if (emitter != null) {
            try {
                emitter.send(data);
                return true;
            } catch (IOException e) {
                log.debug("发送SSE数据失败: connectionId={}, error={}", connectionId, e.getMessage());
                removeConnection(connectionId);
            }
        }
        return false;
    }

    /**
     * 清理过期连接
     */
    private void cleanExpiredConnections() {
        LocalDateTime expireTime = LocalDateTime.now().minusMinutes(60); // 1小时过期
        int cleanedCount = 0;

        var iterator = connections.entrySet().iterator();
        while (iterator.hasNext()) {
            var entry = iterator.next();
            if (entry.getValue().getLastAccess().isBefore(expireTime)) {
                try {
                    entry.getValue().getEmitter().complete();
                } catch (Exception e) {
                    log.debug("关闭过期SSE连接时发生异常", e);
                }
                iterator.remove();
                cleanedCount++;
            }
        }

        if (cleanedCount > 0) {
            log.info("清理过期SSE连接: {} 个，剩余: {} 个", cleanedCount, connections.size());
        }
    }

    /**
     * 清理最旧的连接（当连接数超限时）
     */
    private void cleanOldestConnection() {
        if (connections.isEmpty()) {
            return;
        }

        String oldestConnectionId = null;
        LocalDateTime oldestTime = LocalDateTime.now();

        for (Map.Entry<String, ConnectionWrapper> entry : connections.entrySet()) {
            if (entry.getValue().getLastAccess().isBefore(oldestTime)) {
                oldestTime = entry.getValue().getLastAccess();
                oldestConnectionId = entry.getKey();
            }
        }

        if (oldestConnectionId != null) {
            removeConnection(oldestConnectionId);
            log.debug("清理最旧的SSE连接: connectionId={}", oldestConnectionId);
        }
    }

    /**
     * 获取连接统计信息
     *
     * @return 统计信息
     */
    public ConnectionStats getStats() {
        return new ConnectionStats(
            connections.size(),
            MAX_CONNECTIONS
        );
    }

    /**
     * 连接包装器，包含连接对象和最后访问时间
     */
    private static class ConnectionWrapper {
        private final SseEmitter emitter;
        private LocalDateTime lastAccess;

        public ConnectionWrapper(SseEmitter emitter) {
            this.emitter = emitter;
            this.lastAccess = LocalDateTime.now();
        }

        public SseEmitter getEmitter() {
            return emitter;
        }

        public LocalDateTime getLastAccess() {
            return lastAccess;
        }

        public void updateLastAccess() {
            this.lastAccess = LocalDateTime.now();
        }
    }

    /**
     * 连接统计信息
     */
    public static class ConnectionStats {
        private final int currentCount;
        private final int maxCount;

        public ConnectionStats(int currentCount, int maxCount) {
            this.currentCount = currentCount;
            this.maxCount = maxCount;
        }

        public int getCurrentCount() {
            return currentCount;
        }

        public int getMaxCount() {
            return maxCount;
        }

        @Override
        public String toString() {
            return String.format("ConnectionStats{current=%d, max=%d}", currentCount, maxCount);
        }
    }
}
