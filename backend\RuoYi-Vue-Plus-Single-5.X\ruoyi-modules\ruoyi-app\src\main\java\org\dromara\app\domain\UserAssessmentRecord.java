package org.dromara.app.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 用户评估记录实体
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("user_assessment_record")
public class UserAssessmentRecord extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 记录ID
     */
    @TableId(type = IdType.AUTO)
    private Long recordId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 评估类型：initial-初始评估，periodic-定期评估
     */
    private String assessmentType;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 总题目数
     */
    private Integer totalQuestions;

    /**
     * 已完成题目数
     */
    private Integer completedQuestions;

    /**
     * 状态：in_progress-进行中，completed-已完成，abandoned-已放弃
     */
    private String status;

    /**
     * 总体得分
     */
    private Integer overallScore;

    /**
     * 专业知识得分
     */
    private Integer professionalKnowledge;

    /**
     * 逻辑思维得分
     */
    private Integer logicalThinking;

    /**
     * 语言表达得分
     */
    private Integer languageExpression;

    /**
     * 抗压能力得分
     */
    private Integer stressResistance;

    /**
     * 团队协作得分
     */
    private Integer teamCollaboration;

    /**
     * 创新能力得分
     */
    private Integer innovation;

    /**
     * 评估结果列表（不存储到数据库）
     */
    @TableField(exist = false)
    private List<UserAssessmentResult> results;
}
