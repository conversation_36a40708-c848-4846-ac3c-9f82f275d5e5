package org.dromara.app.domain.vo;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 访问控制状态视图对象
 *
 * <AUTHOR>
 * @date 2025-01-19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccessControlStatusVo {

    /**
     * 访问控制是否启用
     */
    private Boolean accessControlEnabled;

    /**
     * 总用户数
     */
    private Long totalUsers;

    /**
     * 活跃用户数
     */
    private Long activeUsers;

    /**
     * 被禁用用户数
     */
    private Long disabledUsers;

    /**
     * 今日访问总数
     */
    private Long todayAccessCount;

    /**
     * 今日成功访问数
     */
    private Long todaySuccessfulAccess;

    /**
     * 今日失败访问数
     */
    private Long todayFailedAccess;

    /**
     * 访问成功率（百分比）
     */
    private Double accessSuccessRate;

    /**
     * 异常访问数量
     */
    private Long abnormalAccessCount;

    /**
     * 权限检查总数
     */
    private Long totalPermissionChecks;

    /**
     * 权限检查成功数
     */
    private Long successfulPermissionChecks;

    /**
     * 权限检查失败数
     */
    private Long failedPermissionChecks;

    /**
     * 平均响应时间（毫秒）
     */
    private Double averageResponseTime;

    /**
     * 最繁忙的资源
     */
    private Map<String, Long> busiestResources;

    /**
     * 最活跃的用户
     */
    private Map<Long, Long> mostActiveUsers;

    /**
     * 访问来源统计
     */
    private Map<String, Long> accessSourceStats;

    /**
     * 权限分布统计
     */
    private Map<String, Long> permissionDistribution;

    /**
     * 安全事件数量
     */
    private Long securityEventCount;

    /**
     * 最近安全事件
     */
    private List<Map<String, Object>> recentSecurityEvents;

    /**
     * 系统安全等级：low/medium/high/critical
     */
    private String securityLevel;

    /**
     * 安全建议
     */
    private List<String> securityRecommendations;

    /**
     * 访问限制规则数量
     */
    private Integer accessLimitRules;

    /**
     * 临时禁用用户数量
     */
    private Integer temporaryDisabledUsers;

    /**
     * 最后更新时间
     */
    private LocalDateTime lastUpdateTime;

    /**
     * 系统健康状态：healthy/warning/critical
     */
    private String systemHealth;

    /**
     * 性能指标
     */
    private Map<String, Object> performanceMetrics;
}