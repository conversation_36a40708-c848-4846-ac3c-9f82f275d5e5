{"doc": "\n 流式聊天服务\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "streamChat", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String", "java.lang.String"], "doc": "\n 流式聊天\r\n"}, {"name": "streamChat", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String"], "doc": "\n 流式聊天（使用默认提供商）\r\n"}, {"name": "enhanceMessageForAgent", "paramTypes": ["org.dromara.common.chat.agent.AgentType", "java.lang.String"], "doc": "\n 根据Agent类型增强消息\r\n"}, {"name": "selectStreamingModel", "paramTypes": ["java.lang.String"], "doc": "\n 选择流式模型\r\n"}, {"name": "getOrCreateMemory", "paramTypes": ["java.lang.String"], "doc": "\n 获取或创建内存\r\n"}, {"name": "clearSessionMemory", "paramTypes": ["java.lang.String"], "doc": "\n 清理会话内存\r\n"}, {"name": "clearAllMemory", "paramTypes": [], "doc": "\n 清理所有内存\r\n"}, {"name": "getMemoryCacheSize", "paramTypes": [], "doc": "\n 获取会话内存统计\r\n"}, {"name": "hasSession", "paramTypes": ["java.lang.String"], "doc": "\n 检查会话是否存在\r\n"}], "constructors": []}