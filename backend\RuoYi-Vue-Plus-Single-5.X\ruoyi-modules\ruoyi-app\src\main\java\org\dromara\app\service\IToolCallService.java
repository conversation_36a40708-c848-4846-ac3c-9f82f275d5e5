package org.dromara.app.service;


import org.dromara.app.domain.AiTool;
import org.dromara.app.domain.ToolCall;

import java.util.List;
import java.util.Map;

/**
 * 工具调用服务接口
 *
 * <AUTHOR>
 */
public interface IToolCallService {

    /**
     * 获取可用的工具列表
     *
     * @param userId 用户ID
     * @return 工具列表
     */
    List<AiTool> getAvailableTools(Long userId);

    /**
     * 根据ID获取工具
     *
     * @param toolId 工具ID
     * @return 工具信息
     */
    AiTool getToolById(String toolId);

    /**
     * 根据名称获取工具
     *
     * @param toolName 工具名称
     * @return 工具信息
     */
    AiTool getToolByName(String toolName);

    /**
     * 执行工具调用
     *
     * @param toolId     工具ID
     * @param parameters 调用参数
     * @param userId     用户ID
     * @param sessionId  会话ID
     * @param messageId  消息ID
     * @return 调用结果
     */
    ToolCall.ToolCallResult executeToolCall(String toolId, Map<String, Object> parameters,
                                            Long userId, String sessionId, String messageId);

    /**
     * 异步执行工具调用
     *
     * @param toolId     工具ID
     * @param parameters 调用参数
     * @param userId     用户ID
     * @param sessionId  会话ID
     * @param messageId  消息ID
     * @return 调用记录ID
     */
    String executeToolCallAsync(String toolId, Map<String, Object> parameters,
                                Long userId, String sessionId, String messageId);

    /**
     * 获取工具调用记录
     *
     * @param callId 调用记录ID
     * @return 调用记录
     */
    ToolCall getToolCallRecord(String callId);

    /**
     * 获取用户的工具调用历史
     *
     * @param userId   用户ID
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @return 调用历史
     */
    List<ToolCall> getUserToolCallHistory(Long userId, Integer pageNum, Integer pageSize);

    /**
     * 获取会话的工具调用记录
     *
     * @param sessionId 会话ID
     * @return 调用记录列表
     */
    List<ToolCall> getSessionToolCalls(String sessionId);

    /**
     * 验证工具调用权限
     *
     * @param toolId 工具ID
     * @param userId 用户ID
     * @return 是否有权限
     */
    boolean hasToolPermission(String toolId, Long userId);

    /**
     * 验证工具参数
     *
     * @param toolId     工具ID
     * @param parameters 参数
     * @return 验证结果
     */
    ParameterValidationResult validateParameters(String toolId, Map<String, Object> parameters);

    /**
     * 获取工具的函数定义（用于AI模型）
     *
     * @param toolIds 工具ID列表
     * @return 函数定义列表
     */
    List<Map<String, Object>> getToolFunctionDefinitions(List<String> toolIds);

    /**
     * 解析AI模型的工具调用请求
     *
     * @param toolCalls AI模型返回的工具调用信息
     * @param userId    用户ID
     * @param sessionId 会话ID
     * @param messageId 消息ID
     * @return 工具调用结果列表
     */
    List<ToolCall.ToolCallResult> processAiToolCalls(List<Map<String, Object>> toolCalls,
                                                     Long userId, String sessionId, String messageId);

    /**
     * 参数验证结果
     */
    class ParameterValidationResult {
        private final boolean valid;
        private final String errorMessage;
        private final Map<String, String> fieldErrors;

        public ParameterValidationResult(boolean valid, String errorMessage, Map<String, String> fieldErrors) {
            this.valid = valid;
            this.errorMessage = errorMessage;
            this.fieldErrors = fieldErrors;
        }

        public static ParameterValidationResult success() {
            return new ParameterValidationResult(true, null, null);
        }

        public static ParameterValidationResult failure(String errorMessage) {
            return new ParameterValidationResult(false, errorMessage, null);
        }

        public static ParameterValidationResult failure(String errorMessage, Map<String, String> fieldErrors) {
            return new ParameterValidationResult(false, errorMessage, fieldErrors);
        }

        public boolean isValid() {
            return valid;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public Map<String, String> getFieldErrors() {
            return fieldErrors;
        }
    }
}
