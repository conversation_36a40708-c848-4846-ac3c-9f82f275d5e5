package org.dromara.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.dromara.app.domain.QuestionTag;

import java.util.List;

/**
 * 问题标签Mapper接口
 *
 * <AUTHOR> Assistant
 * @date 2025-07-19
 */
@Mapper
public interface QuestionTagMapper extends BaseMapper<QuestionTag> {

    /**
     * 根据分类查询标签
     *
     * @param category 标签分类
     * @return 标签列表
     */
    @Select({
        "<script>",
        "SELECT * FROM app_question_tag",
        "WHERE del_flag = '0' AND status = '0'",
        "<if test='category != null and category != \"\"'>",
        "AND category = #{category}",
        "</if>",
        "ORDER BY sort_order ASC, usage_count DESC",
        "</script>"
    })
    List<QuestionTag> selectByCategory(@Param("category") String category);

    /**
     * 查询热门标签
     *
     * @param limit 限制数量
     * @return 热门标签列表
     */
    @Select({
        "SELECT * FROM app_question_tag",
        "WHERE del_flag = '0' AND status = '0'",
        "ORDER BY usage_count DESC, sort_order ASC",
        "LIMIT #{limit}"
    })
    List<QuestionTag> selectHotTags(@Param("limit") Integer limit);

    /**
     * 增加标签使用次数
     *
     * @param tagName 标签名称
     * @return 影响行数
     */
    @Update("UPDATE app_question_tag SET usage_count = usage_count + 1 WHERE name = #{tagName}")
    int incrementUsageCount(@Param("tagName") String tagName);

    /**
     * 根据名称查询标签
     *
     * @param names 标签名称列表
     * @return 标签列表
     */
    @Select({
        "<script>",
        "SELECT * FROM app_question_tag",
        "WHERE del_flag = '0' AND status = '0'",
        "AND name IN",
        "<foreach collection='names' item='name' open='(' separator=',' close=')'>",
        "#{name}",
        "</foreach>",
        "</script>"
    })
    List<QuestionTag> selectByNames(@Param("names") List<String> names);

}