{"doc": "\n 自定义 Mapper 接口, 实现 自定义扩展\r\n\r\n @param <T> table 泛型\r\n @param <V> vo 泛型\r\n <AUTHOR>\r\n @since 2021-05-13\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "currentVoClass", "paramTypes": [], "doc": "\n 获取当前实例对象关联的泛型类型 V 的 Class 对象\r\n\r\n @return 返回当前实例对象关联的泛型类型 V 的 Class 对象\r\n"}, {"name": "currentModelClass", "paramTypes": [], "doc": "\n 获取当前实例对象关联的泛型类型 T 的 Class 对象\r\n\r\n @return 返回当前实例对象关联的泛型类型 T 的 Class 对象\r\n"}, {"name": "selectList", "paramTypes": [], "doc": "\n 使用默认的查询条件查询并返回结果列表\r\n\r\n @return 返回查询结果的列表\r\n"}, {"name": "insertBatch", "paramTypes": ["java.util.Collection"], "doc": "\n 批量插入实体对象集合\r\n\r\n @param entityList 实体对象集合\r\n @return 插入操作是否成功的布尔值\r\n"}, {"name": "updateBatchById", "paramTypes": ["java.util.Collection"], "doc": "\n 批量根据ID更新实体对象集合\r\n\r\n @param entityList 实体对象集合\r\n @return 更新操作是否成功的布尔值\r\n"}, {"name": "insertOrUpdateBatch", "paramTypes": ["java.util.Collection"], "doc": "\n 批量插入或更新实体对象集合\r\n\r\n @param entityList 实体对象集合\r\n @return 插入或更新操作是否成功的布尔值\r\n"}, {"name": "insertBatch", "paramTypes": ["java.util.Collection", "int"], "doc": "\n 批量插入实体对象集合并指定批处理大小\r\n\r\n @param entityList 实体对象集合\r\n @param batchSize  批处理大小\r\n @return 插入操作是否成功的布尔值\r\n"}, {"name": "updateBatchById", "paramTypes": ["java.util.Collection", "int"], "doc": "\n 批量根据ID更新实体对象集合并指定批处理大小\r\n\r\n @param entityList 实体对象集合\r\n @param batchSize  批处理大小\r\n @return 更新操作是否成功的布尔值\r\n"}, {"name": "insertOrUpdateBatch", "paramTypes": ["java.util.Collection", "int"], "doc": "\n 批量插入或更新实体对象集合并指定批处理大小\r\n\r\n @param entityList 实体对象集合\r\n @param batchSize  批处理大小\r\n @return 插入或更新操作是否成功的布尔值\r\n"}, {"name": "selectVoById", "paramTypes": ["java.io.Serializable"], "doc": "\n 根据ID查询单个VO对象\r\n\r\n @param id 主键ID\r\n @return 查询到的单个VO对象\r\n"}, {"name": "selectVoById", "paramTypes": ["java.io.Serializable", "java.lang.Class"], "doc": "\n 根据ID查询单个VO对象并将其转换为指定的VO类\r\n\r\n @param id      主键ID\r\n @param voClass 要转换的VO类的Class对象\r\n @param <C>     VO类的类型\r\n @return 查询到的单个VO对象，经过转换为指定的VO类后返回\r\n"}, {"name": "selectVoByIds", "paramTypes": ["java.util.Collection"], "doc": "\n 根据ID集合批量查询VO对象列表\r\n\r\n @param idList 主键ID集合\r\n @return 查询到的VO对象列表\r\n"}, {"name": "selectVoByIds", "paramTypes": ["java.util.Collection", "java.lang.Class"], "doc": "\n 根据ID集合批量查询实体对象列表，并将其转换为指定的VO对象列表\r\n\r\n @param idList  主键ID集合\r\n @param voClass 要转换的VO类的Class对象\r\n @param <C>     VO类的类型\r\n @return 查询到的VO对象列表，经过转换为指定的VO类后返回\r\n"}, {"name": "selectVoByMap", "paramTypes": ["java.util.Map"], "doc": "\n 根据查询条件Map查询VO对象列表\r\n\r\n @param map 查询条件Map\r\n @return 查询到的VO对象列表\r\n"}, {"name": "selectVoByMap", "paramTypes": ["java.util.Map", "java.lang.Class"], "doc": "\n 根据查询条件Map查询实体对象列表，并将其转换为指定的VO对象列表\r\n\r\n @param map     查询条件Map\r\n @param voClass 要转换的VO类的Class对象\r\n @param <C>     VO类的类型\r\n @return 查询到的VO对象列表，经过转换为指定的VO类后返回\r\n"}, {"name": "selectVoOne", "paramTypes": ["com.baomidou.mybatisplus.core.conditions.Wrapper"], "doc": "\n 根据条件查询单个VO对象\r\n\r\n @param wrapper 查询条件Wrapper\r\n @return 查询到的单个VO对象\r\n"}, {"name": "selectVoOne", "paramTypes": ["com.baomidou.mybatisplus.core.conditions.Wrapper", "boolean"], "doc": "\n 根据条件查询单个VO对象，并根据需要决定是否抛出异常\r\n\r\n @param wrapper 查询条件Wrapper\r\n @param throwEx 是否抛出异常的标志\r\n @return 查询到的单个VO对象\r\n"}, {"name": "selectVoOne", "paramTypes": ["com.baomidou.mybatisplus.core.conditions.Wrapper", "java.lang.Class"], "doc": "\n 根据条件查询单个VO对象，并指定返回的VO对象的类型\r\n\r\n @param wrapper 查询条件Wrapper\r\n @param voClass 返回的VO对象的Class对象\r\n @param <C>     返回的VO对象的类型\r\n @return 查询到的单个VO对象，经过类型转换为指定的VO类后返回\r\n"}, {"name": "selectVoOne", "paramTypes": ["com.baomidou.mybatisplus.core.conditions.Wrapper", "java.lang.Class", "boolean"], "doc": "\n 根据条件查询单个实体对象，并将其转换为指定的VO对象\r\n\r\n @param wrapper 查询条件Wrapper\r\n @param voClass 要转换的VO类的Class对象\r\n @param throwEx 是否抛出异常的标志\r\n @param <C>     VO类的类型\r\n @return 查询到的单个VO对象，经过转换为指定的VO类后返回\r\n"}, {"name": "selectVoList", "paramTypes": [], "doc": "\n 查询所有VO对象列表\r\n\r\n @return 查询到的VO对象列表\r\n"}, {"name": "selectVoList", "paramTypes": ["com.baomidou.mybatisplus.core.conditions.Wrapper"], "doc": "\n 根据条件查询VO对象列表\r\n\r\n @param wrapper 查询条件Wrapper\r\n @return 查询到的VO对象列表\r\n"}, {"name": "selectVoList", "paramTypes": ["com.baomidou.mybatisplus.core.conditions.Wrapper", "java.lang.Class"], "doc": "\n 根据条件查询实体对象列表，并将其转换为指定的VO对象列表\r\n\r\n @param wrapper 查询条件Wrapper\r\n @param voClass 要转换的VO类的Class对象\r\n @param <C>     VO类的类型\r\n @return 查询到的VO对象列表，经过转换为指定的VO类后返回\r\n"}, {"name": "selectVoPage", "paramTypes": ["com.baomidou.mybatisplus.core.metadata.IPage", "com.baomidou.mybatisplus.core.conditions.Wrapper"], "doc": "\n 根据条件分页查询VO对象列表\r\n\r\n @param page    分页信息\r\n @param wrapper 查询条件Wrapper\r\n @return 查询到的VO对象分页列表\r\n"}, {"name": "selectVoPage", "paramTypes": ["com.baomidou.mybatisplus.core.metadata.IPage", "com.baomidou.mybatisplus.core.conditions.Wrapper", "java.lang.Class"], "doc": "\n 根据条件分页查询实体对象列表，并将其转换为指定的VO对象分页列表\r\n\r\n @param page    分页信息\r\n @param wrapper 查询条件Wrapper\r\n @param voClass 要转换的VO类的Class对象\r\n @param <C>     VO类的类型\r\n @param <P>     VO对象分页列表的类型\r\n @return 查询到的VO对象分页列表，经过转换为指定的VO类后返回\r\n"}, {"name": "selectObjs", "paramTypes": ["com.baomidou.mybatisplus.core.conditions.Wrapper", "java.util.function.Function"], "doc": "\n 根据条件查询符合条件的对象，并将其转换为指定类型的对象列表\r\n\r\n @param wrapper 查询条件Wrapper\r\n @param mapper  转换函数，用于将查询到的对象转换为指定类型的对象\r\n @param <C>     要转换的对象的类型\r\n @return 查询到的符合条件的对象列表，经过转换为指定类型的对象后返回\r\n"}], "constructors": []}