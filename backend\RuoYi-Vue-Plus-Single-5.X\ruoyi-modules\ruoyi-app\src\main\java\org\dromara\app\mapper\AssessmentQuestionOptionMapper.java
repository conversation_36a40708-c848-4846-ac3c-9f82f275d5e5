package org.dromara.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.dromara.app.domain.AssessmentQuestionOption;

import java.util.List;

/**
 * 评估问题选项Mapper接口
 *
 * <AUTHOR>
 */
public interface AssessmentQuestionOptionMapper extends BaseMapper<AssessmentQuestionOption> {

    /**
     * 根据问题ID查询选项列表
     *
     * @param questionId 问题ID
     * @return 选项列表
     */
    List<AssessmentQuestionOption> selectOptionsByQuestionId(@Param("questionId") Long questionId);

    /**
     * 根据问题ID和选项编码查询选项
     *
     * @param questionId 问题ID
     * @param optionCode 选项编码
     * @return 选项
     */
    AssessmentQuestionOption selectOptionByQuestionIdAndCode(@Param("questionId") Long questionId,
                                                             @Param("optionCode") String optionCode);

    /**
     * 批量插入选项
     *
     * @param options 选项列表
     * @return 插入数量
     */
    int insertBatch(@Param("options") List<AssessmentQuestionOption> options);

    /**
     * 根据问题ID删除选项
     *
     * @param questionId 问题ID
     * @return 删除数量
     */
    int deleteByQuestionId(@Param("questionId") Long questionId);
}
