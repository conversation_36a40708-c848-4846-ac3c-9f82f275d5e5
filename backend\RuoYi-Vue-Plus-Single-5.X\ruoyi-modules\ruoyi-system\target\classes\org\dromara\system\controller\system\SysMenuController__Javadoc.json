{"doc": "\n 菜单信息\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "getRouters", "paramTypes": [], "doc": "\n 获取路由信息\r\n\r\n @return 路由信息\r\n"}, {"name": "list", "paramTypes": ["org.dromara.system.domain.bo.SysMenuBo"], "doc": "\n 获取菜单列表\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": "\n 根据菜单编号获取详细信息\r\n\r\n @param menuId 菜单ID\r\n"}, {"name": "treeselect", "paramTypes": ["org.dromara.system.domain.bo.SysMenuBo"], "doc": "\n 获取菜单下拉树列表\r\n"}, {"name": "roleMenuTreeselect", "paramTypes": ["java.lang.Long"], "doc": "\n 加载对应角色菜单列表树\r\n\r\n @param roleId 角色ID\r\n"}, {"name": "add", "paramTypes": ["org.dromara.system.domain.bo.SysMenuBo"], "doc": "\n 新增菜单\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.system.domain.bo.SysMenuBo"], "doc": "\n 修改菜单\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Long"], "doc": "\n 删除菜单\r\n\r\n @param menuId 菜单ID\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": "\n 批量级联删除菜单\r\n\r\n @param menuIds 菜单ID串\r\n"}], "constructors": []}