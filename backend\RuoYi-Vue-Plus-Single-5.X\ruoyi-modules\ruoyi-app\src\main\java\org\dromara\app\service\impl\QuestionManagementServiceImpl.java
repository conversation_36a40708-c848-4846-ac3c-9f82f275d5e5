package org.dromara.app.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.InterviewQuestion;
import org.dromara.app.domain.Job;
import org.dromara.app.mapper.InterviewQuestionMapper;
import org.dromara.app.mapper.JobMapper;
import org.dromara.app.service.IQuestionManagementService;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 问题管理增强Service业务层处理
 *
 * <AUTHOR> Assistant
 * @date 2025-07-19
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class QuestionManagementServiceImpl implements IQuestionManagementService {

    private final InterviewQuestionMapper questionMapper;
    private final JobMapper jobMapper;

    @Override
    public List<InterviewQuestion> recommendQuestions(Long jobId, String technicalDomain, 
                                                    Integer difficulty, Integer questionCount, 
                                                    Boolean includeMultimodal) {
        List<InterviewQuestion> questions = new ArrayList<>();
        
        try {
            // 首先获取岗位专属问题
            if (jobId != null) {
                List<InterviewQuestion> jobQuestions = questionMapper.selectByJobId(jobId, difficulty, questionCount);
                questions.addAll(jobQuestions);
            }
            
            // 如果岗位专属问题不够，补充技术领域通用问题
            if (questions.size() < questionCount && technicalDomain != null) {
                int remainingCount = questionCount - questions.size();
                List<InterviewQuestion> domainQuestions = questionMapper.selectByTechnicalDomain(
                    technicalDomain, null, remainingCount);
                
                // 过滤掉已经存在的问题
                Set<Long> existingIds = questions.stream()
                    .map(InterviewQuestion::getId)
                    .collect(Collectors.toSet());
                
                domainQuestions.stream()
                    .filter(q -> !existingIds.contains(q.getId()))
                    .limit(remainingCount)
                    .forEach(questions::add);
            }
            
            // 如果需要包含多模态问题
            if (includeMultimodal != null && includeMultimodal) {
                List<InterviewQuestion> multimodalQuestions = questionMapper.selectMultimodalQuestions(jobId, 3);
                
                // 替换部分普通问题为多模态问题
                int replaceCount = Math.min(multimodalQuestions.size(), questions.size() / 3);
                for (int i = 0; i < replaceCount && i < multimodalQuestions.size(); i++) {
                    if (i < questions.size()) {
                        questions.set(i, multimodalQuestions.get(i));
                    }
                }
            }
            
            // 随机打乱问题顺序
            Collections.shuffle(questions);
            
        } catch (Exception e) {
            log.error("推荐问题失败，jobId: {}, technicalDomain: {}", jobId, technicalDomain, e);
        }
        
        return questions.stream().limit(questionCount).collect(Collectors.toList());
    }

    @Override
    public List<InterviewQuestion> recommendQuestionsForUser(Long userId, Long jobId, Integer questionCount) {
        // 这里可以根据用户的历史表现、技能水平等进行个性化推荐
        // 暂时使用基础推荐逻辑
        
        Job job = null;
        if (jobId != null) {
            job = jobMapper.selectById(jobId);
        }
        
        String technicalDomain = job != null ? job.getTechnicalDomain() : null;
        Integer targetDifficulty = job != null ? job.getDifficulty() : 3;
        
        return recommendQuestions(jobId, technicalDomain, targetDifficulty, questionCount, true);
    }

    @Override
    public DifficultyGradingSystem getDifficultyGradingSystem(String technicalDomain) {
        DifficultyGradingSystem system = new DifficultyGradingSystem();
        system.setTechnicalDomain(technicalDomain);
        
        Map<Integer, DifficultyLevel> levels = new HashMap<>();
        
        // 定义通用难度级别
        levels.put(1, createDifficultyLevel(1, "入门", "基础概念和简单应用", 
            Arrays.asList("基础概念", "简单操作", "常见问题"), 300, 0.85));
        levels.put(2, createDifficultyLevel(2, "初级", "基本技能和理论理解", 
            Arrays.asList("基本技能", "理论理解", "简单分析"), 450, 0.75));
        levels.put(3, createDifficultyLevel(3, "中级", "综合应用和问题解决", 
            Arrays.asList("综合应用", "问题分析", "方案设计"), 600, 0.65));
        levels.put(4, createDifficultyLevel(4, "高级", "深入理解和复杂应用", 
            Arrays.asList("深入理解", "复杂应用", "优化设计"), 900, 0.45));
        levels.put(5, createDifficultyLevel(5, "专家", "创新思维和架构设计", 
            Arrays.asList("创新思维", "架构设计", "技术前瞻"), 1200, 0.25));
        
        system.setLevels(levels);
        
        // 根据技术领域调整描述
        switch (technicalDomain != null ? technicalDomain : "General") {
            case "AI":
                system.setDescription("人工智能领域问题难度分级，注重算法理论和实践应用");
                break;
            case "BigData":
                system.setDescription("大数据领域问题难度分级，注重数据处理和分析能力");
                break;
            case "IoT":
                system.setDescription("物联网领域问题难度分级，注重硬件软件结合和系统集成");
                break;
            case "SmartSystems":
                system.setDescription("智能系统领域问题难度分级，注重系统架构和运维能力");
                break;
            default:
                system.setDescription("通用技术问题难度分级系统");
                break;
        }
        
        return system;
    }

    @Override
    public QuestionCoverageAnalysis analyzeQuestionCoverage(Long jobId) {
        QuestionCoverageAnalysis analysis = new QuestionCoverageAnalysis();
        analysis.setJobId(jobId);
        
        try {
            // 获取岗位信息
            Job job = jobMapper.selectById(jobId);
            if (job != null) {
                analysis.setJobName(job.getName());
            }
            
            // 获取该岗位的所有问题
            List<InterviewQuestion> questions = questionMapper.selectByJobId(jobId, null, null);
            analysis.setTotalQuestions(questions.size());
            
            // 分析分类分布
            Map<String, Integer> categoryDistribution = questions.stream()
                .collect(Collectors.groupingBy(
                    q -> q.getCategory() != null ? q.getCategory() : "未分类",
                    Collectors.collectingAndThen(Collectors.counting(), Math::toIntExact)
                ));
            analysis.setCategoryDistribution(categoryDistribution);
            
            // 分析难度分布
            Map<Integer, Integer> difficultyDistribution = questions.stream()
                .collect(Collectors.groupingBy(
                    InterviewQuestion::getDifficulty,
                    Collectors.collectingAndThen(Collectors.counting(), Math::toIntExact)
                ));
            analysis.setDifficultyDistribution(difficultyDistribution);
            
            // 分析类型分布
            Map<String, Integer> typeDistribution = questions.stream()
                .collect(Collectors.groupingBy(
                    InterviewQuestion::getQuestionType,
                    Collectors.collectingAndThen(Collectors.counting(), Math::toIntExact)
                ));
            analysis.setTypeDistribution(typeDistribution);
            
            // 识别缺失领域
            List<String> missingAreas = identifyMissingAreas(job, questions);
            analysis.setMissingAreas(missingAreas);
            
            // 计算覆盖度分数
            double coverageScore = calculateCoverageScore(questions, missingAreas);
            analysis.setCoverageScore(coverageScore);
            
        } catch (Exception e) {
            log.error("分析问题覆盖度失败，jobId: {}", jobId, e);
            analysis.setCoverageScore(0.0);
            analysis.setMissingAreas(Arrays.asList("分析失败"));
        }
        
        return analysis;
    }

    @Override
    public MultimodalEvaluationConfig getMultimodalConfig(Long questionId) {
        MultimodalEvaluationConfig config = new MultimodalEvaluationConfig();
        config.setQuestionId(questionId);
        
        try {
            InterviewQuestion question = questionMapper.selectById(questionId);
            if (question != null && "multimodal".equals(question.getQuestionType())) {
                Map<String, Object> requirements = question.getMultimodalRequirements();
                if (requirements != null) {
                    config.setAudioRequired((Boolean) requirements.get("audio"));
                    config.setVideoRequired((Boolean) requirements.get("video"));
                    config.setTextRequired((Boolean) requirements.get("text"));
                    
                    @SuppressWarnings("unchecked")
                    List<String> elements = (List<String>) requirements.get("required_elements");
                    config.setRequiredElements(elements);
                }
                
                Map<String, Object> criteria = question.getEvaluationCriteria();
                if (criteria != null) {
                    Map<String, Integer> weights = new HashMap<>();
                    criteria.forEach((key, value) -> {
                        if (value instanceof Number) {
                            weights.put(key, ((Number) value).intValue());
                        }
                    });
                    config.setEvaluationWeights(weights);
                }
                
                config.setTimeLimit(question.getTimeLimit());
                config.setInstructions("请按照多模态评估要求完成回答，注意语音表达清晰、肢体语言自然、内容逻辑完整。");
            }
        } catch (Exception e) {
            log.error("获取多模态配置失败，questionId: {}", questionId, e);
        }
        
        return config;
    }

    @Override
    public boolean batchUpdateQuestionTags(List<Long> questionIds, List<String> tags) {
        if (questionIds == null || questionIds.isEmpty() || tags == null || tags.isEmpty()) {
            return false;
        }
        
        try {
            for (Long questionId : questionIds) {
                InterviewQuestion question = questionMapper.selectById(questionId);
                if (question != null) {
                    question.setTags(tags);
                    questionMapper.updateById(question);
                }
            }
            return true;
        } catch (Exception e) {
            log.error("批量更新问题标签失败", e);
            return false;
        }
    }

    /**
     * 创建难度级别
     */
    private DifficultyLevel createDifficultyLevel(Integer level, String name, String description, 
                                                List<String> characteristics, Integer timeLimit, Double passRate) {
        DifficultyLevel difficultyLevel = new DifficultyLevel();
        difficultyLevel.setLevel(level);
        difficultyLevel.setName(name);
        difficultyLevel.setDescription(description);
        difficultyLevel.setCharacteristics(characteristics);
        difficultyLevel.setTimeLimit(timeLimit);
        difficultyLevel.setPassRate(passRate);
        return difficultyLevel;
    }

    /**
     * 识别缺失领域
     */
    private List<String> identifyMissingAreas(Job job, List<InterviewQuestion> questions) {
        List<String> missingAreas = new ArrayList<>();
        
        if (job == null) {
            return missingAreas;
        }
        
        // 根据技术领域检查必要的问题类型
        String technicalDomain = job.getTechnicalDomain();
        Set<String> existingCategories = questions.stream()
            .map(InterviewQuestion::getCategory)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());
        
        // 定义各技术领域应该包含的问题类型
        List<String> requiredCategories = getRequiredCategoriesForDomain(technicalDomain);
        
        for (String required : requiredCategories) {
            if (!existingCategories.contains(required)) {
                missingAreas.add(required);
            }
        }
        
        // 检查多模态问题
        boolean hasMultimodal = questions.stream()
            .anyMatch(q -> "multimodal".equals(q.getQuestionType()));
        if (!hasMultimodal) {
            missingAreas.add("多模态评估问题");
        }
        
        return missingAreas;
    }

    /**
     * 获取技术领域必需的问题类型
     */
    private List<String> getRequiredCategoriesForDomain(String technicalDomain) {
        switch (technicalDomain != null ? technicalDomain : "General") {
            case "AI":
                return Arrays.asList("技术问题", "算法设计", "项目经验", "理论基础");
            case "BigData":
                return Arrays.asList("技术问题", "数据处理", "系统设计", "性能优化");
            case "IoT":
                return Arrays.asList("技术问题", "硬件设计", "系统集成", "通信协议");
            case "SmartSystems":
                return Arrays.asList("技术问题", "系统架构", "运维经验", "故障处理");
            default:
                return Arrays.asList("技术问题", "项目经验", "团队协作");
        }
    }

    /**
     * 计算覆盖度分数
     */
    private double calculateCoverageScore(List<InterviewQuestion> questions, List<String> missingAreas) {
        if (questions.isEmpty()) {
            return 0.0;
        }
        
        // 基础分数：问题数量
        double baseScore = Math.min(questions.size() / 20.0, 1.0) * 40;
        
        // 难度分布分数
        Map<Integer, Long> difficultyCount = questions.stream()
            .collect(Collectors.groupingBy(InterviewQuestion::getDifficulty, Collectors.counting()));
        double difficultyScore = difficultyCount.size() >= 3 ? 20 : difficultyCount.size() * 6.67;
        
        // 类型分布分数
        Map<String, Long> typeCount = questions.stream()
            .collect(Collectors.groupingBy(InterviewQuestion::getQuestionType, Collectors.counting()));
        double typeScore = Math.min(typeCount.size() * 5, 20);
        
        // 缺失领域扣分
        double missingPenalty = missingAreas.size() * 5;
        
        return Math.max(0, baseScore + difficultyScore + typeScore - missingPenalty);
    }

}