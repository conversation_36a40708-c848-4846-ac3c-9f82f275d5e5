package org.dromara.app.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.BookReadingRecord;
import org.dromara.app.mapper.BookReadingRecordMapper;
import org.dromara.app.service.IBookReadingRecordService;
import org.dromara.common.core.utils.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 书籍阅读记录Service业务层处理
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BookReadingRecordServiceImpl extends ServiceImpl<BookReadingRecordMapper, BookReadingRecord>
    implements IBookReadingRecordService {

    private final BookReadingRecordMapper bookReadingRecordMapper;
    private final ObjectMapper objectMapper;

    /**
     * 根据用户ID和书籍ID查询阅读记录
     */
    @Override
    public BookReadingRecord queryByUserIdAndBookId(Long userId, Long bookId) {
        if (userId == null || bookId == null) {
            return null;
        }

        BookReadingRecord record = bookReadingRecordMapper.selectByUserIdAndBookId(userId, bookId);
        if (record != null) {
            processReadingRecordData(record);
        }

        return record;
    }

    /**
     * 查询用户的阅读历史（分页）
     */
    @Override
    public Page<BookReadingRecord> queryUserReadingHistory(Integer pageNum, Integer pageSize, Long userId) {
        if (userId == null) {
            return new Page<>();
        }

        Page<BookReadingRecord> page = new Page<>(pageNum, pageSize);
        Page<BookReadingRecord> result = bookReadingRecordMapper.selectUserReadingHistory(page, userId);

        // 处理数据转换
        result.getRecords().forEach(this::processReadingRecordData);

        return result;
    }

    /**
     * 查询用户最近阅读的书籍
     */
    @Override
    public List<BookReadingRecord> queryRecentReading(Long userId, Integer limit) {
        if (userId == null) {
            return List.of();
        }

        if (limit == null || limit <= 0) {
            limit = 5;
        }

        List<BookReadingRecord> records = bookReadingRecordMapper.selectRecentReading(userId, limit);
        records.forEach(this::processReadingRecordData);

        return records;
    }

    /**
     * 统计用户阅读数据
     */
    @Override
    public Map<String, Object> queryUserReadingStats(Long userId) {
        if (userId == null) {
            return new HashMap<>();
        }

        Map<String, Object> stats = new HashMap<>();

        try {
            // 查询基础统计数据
            LambdaQueryWrapper<BookReadingRecord> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(BookReadingRecord::getUserId, userId);

            List<BookReadingRecord> records = list(wrapper);

            // 统计总书籍数
            int totalBooks = records.size();

            // 统计已读完书籍数
            long completedBooks = records.stream()
                .filter(record -> Boolean.TRUE.equals(record.getIsFinished()))
                .count();

            // 统计总阅读时长
            int totalReadingTime = records.stream()
                .mapToInt(record -> record.getTotalReadingTime() != null ? record.getTotalReadingTime() : 0)
                .sum();

            // 统计今日阅读时长（这里简化处理，实际应该查询今日的阅读记录）
            int todayReadingTime = 0;
            LocalDateTime today = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0);
            for (BookReadingRecord record : records) {
                if (record.getLastReadingTime() != null && record.getLastReadingTime().isAfter(today)) {
                    todayReadingTime += 30; // 简化处理，假设每次阅读30分钟
                }
            }

            // 统计连续阅读天数（简化处理）
            int readingStreak = calculateReadingStreak(records);

            stats.put("totalBooks", totalBooks);
            stats.put("completedBooks", completedBooks);
            stats.put("totalReadingTime", totalReadingTime);
            stats.put("todayReadingTime", todayReadingTime);
            stats.put("readingStreak", readingStreak);

        } catch (Exception e) {
            log.error("查询用户阅读统计失败: {}", e.getMessage());
            // 返回默认值
            stats.put("totalBooks", 0);
            stats.put("completedBooks", 0);
            stats.put("totalReadingTime", 0);
            stats.put("todayReadingTime", 0);
            stats.put("readingStreak", 0);
        }

        return stats;
    }

    /**
     * 保存或更新阅读记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdateReadingRecord(Long userId, Long bookId, String currentChapterId,
                                             Integer currentChapterIndex, Integer readingProgress,
                                             Map<String, Object> readingSettings) {
        if (userId == null || bookId == null) {
            return false;
        }

        try {
            BookReadingRecord record = queryByUserIdAndBookId(userId, bookId);

            if (record == null) {
                // 新建记录
                record = new BookReadingRecord();
                record.setUserId(userId);
                record.setBookId(bookId);
                record.setCurrentChapterIndex(0);
                record.setReadingProgress(BigDecimal.ZERO);
                record.setTotalReadingTime(0);
                record.setIsFinished(false);
            }

            // 更新记录
            if (StringUtils.isNotBlank(currentChapterId)) {
                record.setCurrentChapterId(currentChapterId);
            }
            if (currentChapterIndex != null) {
                record.setCurrentChapterIndex(currentChapterIndex);
            }
            if (readingProgress != null) {
                record.setReadingProgress(BigDecimal.valueOf(readingProgress));
            }
            if (readingSettings != null) {
                record.setReadingSettings(convertMapToJson(readingSettings));
            }

            record.setLastReadingTime(LocalDateTime.now());

            return saveOrUpdate(record);

        } catch (Exception e) {
            log.error("保存阅读记录失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 标记章节已完成
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean markChapterCompleted(Long userId, Long bookId, String chapterId) {
        if (userId == null || bookId == null || StringUtils.isBlank(chapterId)) {
            return false;
        }

        try {
            BookReadingRecord record = queryByUserIdAndBookId(userId, bookId);

            if (record == null) {
                // 创建新记录
                record = new BookReadingRecord();
                record.setUserId(userId);
                record.setBookId(bookId);
                record.setCurrentChapterIndex(0);
                record.setReadingProgress(BigDecimal.ZERO);
                record.setTotalReadingTime(0);
                record.setIsFinished(false);
                record.setCompletedChapters(chapterId);
            } else {
                // 更新已完成章节列表
                Set<String> completedChapters = new HashSet<>();
                if (StringUtils.isNotBlank(record.getCompletedChapters())) {
                    completedChapters.addAll(Arrays.asList(record.getCompletedChapters().split(",")));
                }
                completedChapters.add(chapterId);
                record.setCompletedChapters(String.join(",", completedChapters));
            }

            record.setLastReadingTime(LocalDateTime.now());
            return saveOrUpdate(record);

        } catch (Exception e) {
            log.error("标记章节完成失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 标记书籍已读完
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean markBookFinished(Long userId, Long bookId) {
        if (userId == null || bookId == null) {
            return false;
        }

        try {
            BookReadingRecord record = queryByUserIdAndBookId(userId, bookId);

            if (record == null) {
                return false;
            }

            record.setIsFinished(true);
            record.setReadingProgress(BigDecimal.valueOf(100));
            record.setLastReadingTime(LocalDateTime.now());

            return updateById(record);

        } catch (Exception e) {
            log.error("标记书籍已读完失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 增加阅读时长
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addReadingTime(Long userId, Long bookId, Integer readingTime) {
        if (userId == null || bookId == null || readingTime == null || readingTime <= 0) {
            return false;
        }

        try {
            BookReadingRecord record = queryByUserIdAndBookId(userId, bookId);

            if (record == null) {
                // 创建新记录
                record = new BookReadingRecord();
                record.setUserId(userId);
                record.setBookId(bookId);
                record.setCurrentChapterIndex(0);
                record.setReadingProgress(BigDecimal.ZERO);
                record.setTotalReadingTime(readingTime);
                record.setIsFinished(false);
            } else {
                // 增加阅读时长
                int currentTime = record.getTotalReadingTime() != null ? record.getTotalReadingTime() : 0;
                record.setTotalReadingTime(currentTime + readingTime);
            }

            record.setLastReadingTime(LocalDateTime.now());
            return saveOrUpdate(record);

        } catch (Exception e) {
            log.error("增加阅读时长失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 删除阅读记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteReadingRecord(Long userId, Long bookId) {
        if (userId == null || bookId == null) {
            return false;
        }

        try {
            LambdaQueryWrapper<BookReadingRecord> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(BookReadingRecord::getUserId, userId)
                .eq(BookReadingRecord::getBookId, bookId);

            return remove(wrapper);

        } catch (Exception e) {
            log.error("删除阅读记录失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 处理阅读记录数据转换
     */
    private void processReadingRecordData(BookReadingRecord record) {
        if (record == null) {
            return;
        }

        // 转换阅读设置JSON为Map
        if (StringUtils.isNotBlank(record.getReadingSettings())) {
            try {
                Map<String, Object> settingsMap = objectMapper.readValue(
                    record.getReadingSettings(),
                    objectMapper.getTypeFactory().constructMapType(Map.class, String.class, Object.class)
                );
                record.setReadingSettingsMap(settingsMap);
            } catch (JsonProcessingException e) {
                log.warn("解析阅读设置JSON失败: {}", e.getMessage());
                record.setReadingSettingsMap(new HashMap<>());
            }
        } else {
            record.setReadingSettingsMap(new HashMap<>());
        }

        // 转换已完成章节字符串为列表
        if (StringUtils.isNotBlank(record.getCompletedChapters())) {
            List<String> chapterList = Arrays.stream(record.getCompletedChapters().split(","))
                .map(String::trim)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
            record.setCompletedChapterList(chapterList);
        } else {
            record.setCompletedChapterList(new ArrayList<>());
        }
    }

    /**
     * 将Map转换为JSON字符串
     */
    private String convertMapToJson(Map<String, Object> map) {
        if (map == null || map.isEmpty()) {
            return null;
        }

        try {
            return objectMapper.writeValueAsString(map);
        } catch (JsonProcessingException e) {
            log.warn("转换Map为JSON失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 计算连续阅读天数
     */
    private int calculateReadingStreak(List<BookReadingRecord> records) {
        if (records.isEmpty()) {
            return 0;
        }

        // 简化处理：基于最后阅读时间计算
        // 实际应该根据每日阅读记录来计算
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime yesterday = now.minusDays(1);

        long recentReading = records.stream()
            .filter(record -> record.getLastReadingTime() != null)
            .filter(record -> record.getLastReadingTime().isAfter(yesterday))
            .count();

        return recentReading > 0 ? (int) recentReading : 0;
    }
}
