{"doc": "\n RabbitMQ 消息实体\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "messageId", "doc": "\n 消息ID\r\n"}, {"name": "content", "doc": "\n 消息内容\r\n"}, {"name": "messageType", "doc": "\n 消息类型\r\n"}, {"name": "sendTime", "doc": "\n 发送时间\r\n"}, {"name": "retryCount", "doc": "\n 重试次数\r\n"}, {"name": "source", "doc": "\n 消息来源\r\n"}, {"name": "extras", "doc": "\n 附加信息\r\n"}], "enumConstants": [], "methods": [{"name": "create", "paramTypes": ["java.lang.Object"], "doc": "\n 创建消息\r\n\r\n @param content 消息内容\r\n @param <T>     内容类型\r\n @return 消息对象\r\n"}], "constructors": []}