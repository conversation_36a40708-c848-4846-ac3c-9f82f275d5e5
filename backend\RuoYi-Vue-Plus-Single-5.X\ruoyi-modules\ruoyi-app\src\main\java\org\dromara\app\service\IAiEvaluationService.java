package org.dromara.app.service;

import org.dromara.app.domain.vo.InterviewResponseVo;

import java.util.List;

/**
 * AI评估服务接口
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
public interface IAiEvaluationService {

    /**
     * 评估面试回答
     *
     * @param question    问题内容
     * @param answer      回答内容
     * @param audioUrl    音频URL（可选）
     * @param videoUrl    视频URL（可选）
     * @param duration    回答时长
     * @param jobContext  岗位上下文信息
     * @return 评估结果
     */
    InterviewResponseVo.FeedbackInfo evaluateAnswer(String question, String answer, 
                                                    String audioUrl, String videoUrl, 
                                                    Integer duration, String jobContext);

    /**
     * 生成面试问题
     *
     * @param jobId       岗位ID
     * @param difficulty  难度等级
     * @param count       问题数量
     * @param resumeUrl   简历URL（可选）
     * @param customQuestions 自定义问题（可选）
     * @return 生成的问题列表
     */
    List<InterviewResponseVo.InterviewQuestion> generateQuestions(Long jobId, String difficulty, 
                                                                  Integer count, String resumeUrl, 
                                                                  List<String> customQuestions);

    /**
     * 生成面试总结报告
     *
     * @param sessionId 会话ID
     * @return 面试结果
     */
    InterviewResponseVo.InterviewResult generateInterviewReport(String sessionId);

    /**
     * 分析简历内容
     *
     * @param resumeUrl 简历URL
     * @param jobId     岗位ID
     * @return 简历分析结果
     */
    String analyzeResume(String resumeUrl, Long jobId);

    /**
     * 检查回答质量
     *
     * @param answer 回答内容
     * @return 质量评分（0-100）
     */
    Double checkAnswerQuality(String answer);

    /**
     * 生成改进建议
     *
     * @param sessionId 会话ID
     * @return 改进建议列表
     */
    List<String> generateImprovementSuggestions(String sessionId);
}
