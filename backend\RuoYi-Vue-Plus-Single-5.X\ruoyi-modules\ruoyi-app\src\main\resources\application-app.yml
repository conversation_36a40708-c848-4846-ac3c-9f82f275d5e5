# AI智能体应用配置
# 该配置用于ruoyi-app模块

--- # Ollama配置
ollama:
  # Ollama服务器地址
  base-url: http://localhost:11434
  # 默认模型名称
  default-model: llama3.1:8b
  # 连接超时时间（秒）
  connect-timeout: 30
  # 读取超时时间（秒）
  read-timeout: 300
  # 写入超时时间（秒）
  write-timeout: 30
  # 默认温度参数
  default-temperature: 0.7
  # 默认最大Token数
  default-max-tokens: 4096
  # 是否启用流式响应
  enable-streaming: true
  # 重试次数
  retry-count: 3
  # 重试间隔（毫秒）
  retry-interval: 1000
  # 是否启用健康检查
  enable-health-check: true
  # 健康检查间隔（秒）
  health-check-interval: 60

--- # 应用配置
app:
  # 文件上传配置
  file:
    upload:
      # 上传路径
      path: /data/uploads
      # 最大文件大小（字节，10MB）
      max-size: 10485760
      # 允许的文件类型
      allowed-types: jpg,jpeg,png,gif,pdf,doc,docx,txt,mp3,wav,ogg
      # 基础URL
      base-url: http://localhost:8080

  # AI代理配置
  agent:
    # 是否启用默认代理初始化
    enable-default-init: true
    # 缓存配置
    cache:
      # 代理列表缓存时间（小时）
      agents-expire: 1
      # 快速操作缓存时间（小时）
      quick-actions-expire: 1

  # 聊天配置
  chat:
    # 默认上下文消息数量
    default-context-limit: 10
    # 会话超时时间（毫秒，5分钟）
    session-timeout: 300000
    # 是否启用消息历史
    enable-history: true
    # 历史消息保留天数
    history-retention-days: 30

  # 工具配置
  tools:
    # 是否启用工具调用
    enabled: true
    # 工具执行超时时间（秒）
    execution-timeout: 30
    # 最大重试次数
    max-retries: 3
    # 是否启用工具缓存
    enable-cache: true
    # 缓存超时时间（分钟）
    cache-timeout: 10

  # 知识库配置
  knowledge:
    # 是否启用知识库功能
    enabled: false
    # 向量化配置
    vector:
      # 默认嵌入模型
      embedding-model: text-embedding-ada-002
      # 向量维度
      dimension: 1536
      # 距离度量方式
      distance-metric: cosine
      # 分块大小
      chunk-size: 1000
      # 分块重叠
      chunk-overlap: 200
    # 搜索配置
    search:
      # 默认返回结果数
      default-top-k: 5
      # 相似度阈值
      similarity-threshold: 0.7

--- # 任务调度配置（如果需要）
app-jobs:
  # 是否启用定时任务
  enabled: false
  # 清理过期会话任务
  clean-expired-sessions:
    # 执行周期（cron表达式，每天凌晨2点）
    cron: "0 0 2 * * ?"
    # 是否启用
    enabled: true
  # 统计数据更新任务
  update-statistics:
    # 执行周期（每小时）
    cron: "0 0 * * * ?"
    # 是否启用
    enabled: true

--- # 监控配置
app-monitor:
  # 是否启用性能监控
  enabled: true
  # 慢查询阈值（毫秒）
  slow-query-threshold: 1000
  # API调用统计
  api-stats:
    # 是否启用
    enabled: true
    # 统计周期（分钟）
    period: 5
  # 错误率监控
  error-rate:
    # 告警阈值（百分比）
    threshold: 5.0
    # 统计窗口（分钟）
    window: 10

--- # 面试系统配置
app:
  interview:
    # 会话配置
    session:
      # 默认过期时间（小时）
      default-expiration-hours: 2
      # 最大会话数量（每个用户）
      max-sessions-per-user: 5
      # 会话清理间隔（分钟）
      cleanup-interval-minutes: 30
      # 是否启用清理任务
      cleanup-enabled: true
    # 搜索配置
    search:
      # 默认搜索建议数量
      default-suggestion-limit: 5
      # 热门关键词数量
      hot-keyword-limit: 10
      # 最近搜索记录数量
      recent-search-limit: 5
      # 关键词最小长度
      min-keyword-length: 2
      # 关键词最大长度
      max-keyword-length: 50
    # 设备检测配置
    device:
      # 是否启用设备检测
      enabled: true
      # 检测超时时间（秒）
      timeout-seconds: 10
      # 模拟检测失败概率（用于测试）
      mock-failure-rate: 0.1
    # 排序配置
    sort:
      # 默认排序方式
      default-sort-by: smart
      # 默认排序顺序
      default-sort-order: desc
      # 智能排序权重配置
      smart-sort:
        # 热度权重
        hot-weight: 0.3
        # 通过率权重
        pass-rate-weight: 0.25
        # 难度权重
        difficulty-weight: 0.2
        # 时长权重
        duration-weight: 0.15
        # 题目数量权重
        question-count-weight: 0.1
        # 最佳通过率
        optimal-pass-rate: 65.0
        # 最佳难度
        optimal-difficulty: 3
        # 最佳时长
        optimal-duration: 37.5
        # 最佳题目数量
        optimal-question-count: 12.5

--- # 讯飞AI服务配置
xunfei:
  # 讯飞星火大模型配置
  spark:
    # 应用ID（已废弃，保留兼容性）
    app-id: ${XUNFEI_SPARK_APP_ID:your_app_id}
    # API密钥（已废弃，保留兼容性）
    api-key: ${XUNFEI_SPARK_API_KEY:your_api_key}
    # API密钥（已废弃，保留兼容性）
    api-secret: ${XUNFEI_SPARK_API_SECRET:your_api_secret}
    # API密码（HTTP调用认证，从控制台获取）
    api-password: ${XUNFEI_SPARK_API_PASSWORD:your_api_password}
    # 服务地址
    base-url: https://spark-api-open.xf-yun.com/v1/chat/completions
    # 模型版本（generalv3.5=Max, generalv3=Pro, lite=Lite, 4.0Ultra=Ultra）
    model: generalv3.5
    # 默认温度
    temperature: 0.7
    # 最大Token数
    max-tokens: 4096
    # 连接超时时间（秒）
    connect-timeout: 30
    # 读取超时时间（秒）
    read-timeout: 60

  # 语音识别配置
  speech:
    # 应用ID
    app-id: ${XUNFEI_SPEECH_APP_ID:your_app_id}
    # API密钥
    api-key: ${XUNFEI_SPEECH_API_KEY:your_api_key}
    # API密钥
    api-secret: ${XUNFEI_SPEECH_API_SECRET:your_api_secret}
    # 语音识别服务地址
    base-url: https://iat-api.xfyun.cn/v2/iat
    # 语言类型
    language: zh_cn
    # 音频格式
    audio-format: audio/L16;rate=16000
    # 是否启用标点符号
    enable-punctuation: true
    # 是否启用数字转换
    enable-number-convert: true

  # 情感分析配置
  emotion:
    # 应用ID
    app-id: ${XUNFEI_EMOTION_APP_ID:your_app_id}
    # API密钥
    api-key: ${XUNFEI_EMOTION_API_KEY:your_api_key}
    # API密钥
    api-secret: ${XUNFEI_EMOTION_API_SECRET:your_api_secret}
    # 情感分析服务地址
    base-url: https://api.xf-yun.com/v1/private/s9a3d6d6c
    # 分析类型
    analysis-type: emotion
    # 语言类型
    language: zh

  # 语音合成配置
  tts:
    # 应用ID
    app-id: ${XUNFEI_TTS_APP_ID:your_app_id}
    # API密钥
    api-key: ${XUNFEI_TTS_API_KEY:your_api_key}
    # API密钥
    api-secret: ${XUNFEI_TTS_API_SECRET:your_api_secret}
    # 语音合成服务地址
    base-url: https://tts-api.xfyun.cn/v2/tts
    # 发音人
    voice-name: xiaoyan
    # 语速
    speed: 50
    # 音量
    volume: 50
    # 音调
    pitch: 50
    # 音频格式
    audio-format: lame

--- # 多模态分析配置
app:
  multimodal:
    # 是否启用多模态分析
    enabled: true
    # 分析超时时间（秒）
    analysis-timeout: 300
    # 并发分析数量限制
    max-concurrent-analysis: 5
    # 文件大小限制
    file-limits:
      # 音频文件最大大小（MB）
      max-audio-size: 50
      # 视频文件最大大小（MB）
      max-video-size: 200
      # 文本最大长度
      max-text-length: 10000
    # 支持的文件格式
    supported-formats:
      audio: ["mp3", "wav", "ogg", "m4a", "amr"]
      video: ["mp4", "avi", "mov", "wmv", "flv"]
      text: ["txt", "md", "doc", "docx"]
    # 分析结果缓存配置
    cache:
      # 是否启用缓存
      enabled: true
      # 缓存过期时间（小时）
      expire-hours: 24
      # 最大缓存数量
      max-size: 1000
    # 实时分析配置
    realtime:
      # 是否启用实时分析
      enabled: true
      # 音频流分析间隔（毫秒）
      audio-interval: 1000
      # 视频帧分析间隔（毫秒）
      video-interval: 2000
      # 实时分析超时时间（秒）
      timeout: 60

--- # OpenCV配置（用于视频分析）
opencv:
  # 是否启用OpenCV
  enabled: false
  # 模型文件路径
  models:
    # 人脸检测模型
    face-detection: models/opencv_face_detector_uint8.pb
    # 人脸关键点检测模型
    face-landmarks: models/face_landmark_68.dat
    # 表情识别模型
    emotion-recognition: models/emotion_model.pb
  # 分析参数
  analysis:
    # 人脸检测置信度阈值
    face-confidence-threshold: 0.5
    # 表情识别置信度阈值
    emotion-confidence-threshold: 0.6
    # 视频帧采样率（每秒帧数）
    frame-sample-rate: 2
