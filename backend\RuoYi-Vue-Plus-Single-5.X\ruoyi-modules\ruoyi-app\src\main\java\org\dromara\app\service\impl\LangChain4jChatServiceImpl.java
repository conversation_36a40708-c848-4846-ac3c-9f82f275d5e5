package org.dromara.app.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import dev.langchain4j.data.message.AiMessage;
import dev.langchain4j.data.message.SystemMessage;
import dev.langchain4j.data.message.UserMessage;
import dev.langchain4j.memory.chat.MessageWindowChatMemory;
import dev.langchain4j.model.chat.StreamingChatModel;
import dev.langchain4j.model.chat.response.ChatResponse;
import dev.langchain4j.model.chat.response.StreamingChatResponseHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.Agent;
import org.dromara.app.domain.ChatMessage;
import org.dromara.app.domain.ChatSession;
import org.dromara.app.domain.VectorEmbedding;
import org.dromara.app.domain.dto.ChatRequestDto;
import org.dromara.app.domain.dto.ChatResponseDto;
import org.dromara.app.mapper.ChatMessageMapper;
import org.dromara.app.mapper.ChatSessionMapper;
import org.dromara.app.service.*;
import org.dromara.app.service.PromptEngineeringService.ConversationTurn;
import org.dromara.app.service.PromptEngineeringService.LengthAdjustmentStrategy;
import org.dromara.common.chat.agent.AgentContext;
import org.dromara.common.chat.agent.AgentType;
import org.dromara.common.chat.agent.factory.AgentFactory;
import org.dromara.common.chat.agent.service.*;
import org.springframework.boot.configurationprocessor.json.JSONException;
import org.springframework.boot.configurationprocessor.json.JSONObject;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 基于LangChain4j的聊天服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service("langChain4jChatServiceByMyself")
@RequiredArgsConstructor
public class LangChain4jChatServiceImpl implements IChatService {

    private final AgentFactory agentFactory;
    private final IAgentService agentService;
    private final ChatSessionMapper chatSessionMapper;
    private final ChatMessageMapper chatMessageMapper;
    private final ChatMemoryManager chatMemoryManager;
    private final SseConnectionManager sseConnectionManager;
    private final PromptEngineeringService promptEngineeringService;
    private final IXunfeiService xunfeiService;

    @Override
    public ChatResponseDto sendMessage(ChatRequestDto request, Long userId) {
        long startTime = System.currentTimeMillis();
        String agentType = request.getAgentType() != null ? request.getAgentType() : "general";
        String sessionId = request.getSessionId();

        try {

            // 获取或创建会话
            if (StrUtil.isBlank(sessionId)) {
                ChatSession session = createSession(userId, agentType, null);
                sessionId = session.getId();
            }

            // 检查会话权限
            if (!checkSessionPermission(sessionId, userId)) {
                return ChatResponseDto.error("会话不存在或无权限访问");
            }

            // 提取用户消息中的关键信息（如果消息过长）
            String userMessage = request.getMessage();
            if (userMessage.length() > 1000) {  // 如果消息超过1000字符，提取关键信息
                userMessage = promptEngineeringService.extractKeyInformation(userMessage, 1000);
                log.info("用户消息过长，已提取关键信息。原长度: {}, 提取后长度: {}", request.getMessage().length(), userMessage.length());
            }

            // 更新请求消息
            request.setMessage(userMessage);

            // 保存用户消息
            ChatMessage userMessageObj = createUserMessage(sessionId, userId, request);
            saveMessage(userMessageObj);

            // 获取Agent配置
            Agent agent = agentService.getAgentByType(agentType);
            if (agent == null) {
                return ChatResponseDto.error("rjb-sias类型不存在");
            }

            // 创建Agent上下文
            AgentContext context = createAgentContext(sessionId, userId, agentType);

            // 使用RAG增强查询（如果适用）
            String enhancedMessage = enhanceMessageWithRAG(agentType, userMessage);

            // 根据Agent类型调用相应的服务
            String response = invokeAgentService(context, enhancedMessage, agentType, request.getProvider());

            if (StrUtil.isNotBlank(response)) {
                // 保存AI回复
                ChatMessage assistantMessage = createAssistantMessage(sessionId, userId, response,
                    buildMetadata(request, agent));
                saveMessage(assistantMessage);

                // 更新会话信息
                updateSessionAfterMessage(sessionId, response);

                // 增加Agent使用次数
                agentService.incrementUsageCount(agentType);

                return ChatResponseDto.success(response, assistantMessage.getId(), sessionId);
            } else {
                return ChatResponseDto.error("AI响应为空");
            }

        } catch (Exception e) {
            log.error("发送消息失败", e);
            return ChatResponseDto.error("发送消息失败: " + e.getMessage());
        } finally {
            // 记录响应时间
            long duration = System.currentTimeMillis() - startTime;
            log.info("AI响应耗时: {}ms", duration);
        }
    }

    @Override
    public SseEmitter sendMessageStream(ChatRequestDto request, Long userId) {
        SseEmitter emitter = new SseEmitter(300000L); // 5分钟超时

        CompletableFuture.runAsync(() -> {
            try {
                // 获取或创建会话
                String sessionId = request.getSessionId();
                if (StrUtil.isBlank(sessionId)) {
                    ChatSession session = createSession(userId, request.getAgentType(), null);
                    sessionId = session.getId();

                    // 发送会话ID
                    emitter.send(SseEmitter.event()
                        .name("session")
                        .data(Map.of("sessionId", sessionId)));
                }

                // 检查会话权限
                if (!checkSessionPermission(sessionId, userId)) {
                    emitter.send(SseEmitter.event()
                        .name("error")
                        .data("会话不存在或无权限访问"));
                    emitter.complete();
                    return;
                }

                // 提取用户消息中的关键信息（如果消息过长）
                String userMessage = request.getMessage();
                if (userMessage.length() > 1000) {  // 如果消息超过1000字符，提取关键信息
                    userMessage = promptEngineeringService.extractKeyInformation(userMessage, 1000);
                    log.info("流式请求：用户消息过长，已提取关键信息。原长度: {}, 提取后长度: {}",
                        request.getMessage().length(), userMessage.length());

                    // 更新请求消息
                    request.setMessage(userMessage);
                }

                // 保存用户消息
                ChatMessage userMessageObj = createUserMessage(sessionId, userId, request);
                saveMessage(userMessageObj);

                // 发送用户消息确认
                emitter.send(SseEmitter.event()
                    .name("user_message")
                    .data(Map.of("messageId", userMessageObj.getId())));

                // 获取Agent配置
                Agent agent = agentService.getAgentByType(request.getAgentType());
                if (agent == null) {
                    emitter.send(SseEmitter.event()
                        .name("error")
                        .data("rjb-sias类型不存在"));
                    emitter.complete();
                    return;
                }

                // 创建Agent上下文
                AgentContext context = createAgentContext(sessionId, userId, agent.getAgentType());

                // TODO: 对pormpt进行处理 判断用户输入的是符合正确的prompt

                // 使用RAG增强查询（如果适用）
                String enhancedMessage = enhanceMessageWithRAG(agent.getAgentType(), userMessage);

                // 创建临时的AI消息
                String assistantMessageId = IdUtil.fastSimpleUUID();
                AtomicReference<String> fullResponse = new AtomicReference<>("");

                // 发送开始事件
                emitter.send(SseEmitter.event()
                    .name("start")
                    .data(Map.of("messageId", assistantMessageId)));

                // 调用讯飞流式Agent服务
                String finalSessionId = sessionId;
                invokeXunfeiStreamingService(context, enhancedMessage, agent.getAgentType(), new StreamingChatResponseHandler() {

                        @Override
                        public void onPartialResponse(String token) {
                            try {
                                // 发送token
                                JSONObject tokenMessage = new JSONObject();
                                tokenMessage.put("type", "token");
                                tokenMessage.put("messageId", assistantMessageId);
                                tokenMessage.put("token", token);
                                tokenMessage.put("timestamp", System.currentTimeMillis());

                                emitter.send(SseEmitter.event()
                                    .name("token")
                                    .data(tokenMessage.toString()));

                                // 累积完整响应
                                fullResponse.updateAndGet(current -> current + token);

                            } catch (IOException | JSONException e) {
                                log.error("发送流式token失败", e);
                            }
                        }

                        @Override
                        public void onCompleteResponse(ChatResponse response) {
                            try {
                                // 保存完整的AI回复
                                ChatMessage assistantMessage = createAssistantMessage(finalSessionId, userId,
                                    fullResponse.get(), buildMetadata(request, agent));
                                assistantMessage.setId(assistantMessageId);
                                saveMessage(assistantMessage);

                                // 更新会话信息
                                updateSessionAfterMessage(finalSessionId, fullResponse.get());

                                // 增加Agent使用次数
                                agentService.incrementUsageCount(request.getAgentType());

                                // 发送完成事件
                                JSONObject completeMessage = new JSONObject();
                                completeMessage.put("type", "complete");
                                completeMessage.put("messageId", assistantMessageId);
                                completeMessage.put("fullMessage", fullResponse.get());
                                completeMessage.put("timestamp", System.currentTimeMillis());

                                emitter.send(SseEmitter.event()
                                    .name("complete")
                                    .data(completeMessage.toString()));

                                emitter.complete();

                            } catch (IOException | JSONException e) {
                                log.error("发送流式完成事件失败", e);
                                emitter.completeWithError(e);
                            }
                        }

                        @Override
                        public void onError(Throwable error) {
                            log.error("流式响应错误", error);
                            try {
                                emitter.send(SseEmitter.event()
                                    .name("error")
                                    .data("AI响应失败: " + error.getMessage()));
                            } catch (IOException e) {
                                log.error("发送错误事件失败", e);
                            }
                            emitter.completeWithError(error);
                        }
                    });

            } catch (Exception e) {
                log.error("流式发送消息失败", e);
                try {
                    emitter.send(SseEmitter.event()
                        .name("error")
                        .data("发送失败: " + e.getMessage()));
                } catch (IOException ioException) {
                    log.error("发送错误事件失败", ioException);
                }
                emitter.completeWithError(e);
            }
        });

        return emitter;
    }

    /**
     * 调用特定类型的Agent服务（同步）
     *
     * @param agentType Agent类型
     * @param message   用户消息
     * @param params    额外参数
     * @param userId    用户ID
     * @return 处理结果
     */
    public String callAgentService(String agentType, String message, Map<String, String> params, Long userId) {
        // 创建Agent上下文
        AgentContext context = createAgentContext(null, userId, agentType);

        // 获取提供商（如果指定）
        String provider = params != null ? params.getOrDefault("provider", null) : null;

        // 根据Agent类型调用对应的服务
        switch (agentType.toLowerCase()) {
            case "general":
            case "general_chat":
                return agentFactory.createGeneralChatAgent(context, provider).chat(message);

            case "interviewer":
                if (params != null && params.containsKey("action")) {
                    InterviewerAgent agent = agentFactory.createInterviewerAgent(context, provider);
                    String action = params.get("action");

                    switch (action) {
                        case "conduct":
                            return agent.conductInterview(message);
                        case "generate":
                            String position = params.getOrDefault("position", "Software Engineer");
                            int count = Integer.parseInt(params.getOrDefault("count", "3"));
                            return agent.generateQuestions(position, count);
                        case "evaluate":
                            String question = params.getOrDefault("question", "");
                            return agent.evaluateAnswer(question, message);
                        default:
                            return agent.conductInterview(message);
                    }
                } else {
                    return agentFactory.createInterviewerAgent(context, provider).conductInterview(message);
                }

            case "resume":
            case "resume_analyzer":
                if (params != null && params.containsKey("action")) {
                    ResumeAnalyzerAgent agent = agentFactory.createResumeAnalyzerAgent(context, provider);
                    String action = params.get("action");

                    switch (action) {
                        case "analyze":
                            return agent.analyzeResume(message);
                        case "match":
                            String jobRequirements = params.getOrDefault("job_requirements", "");
                            return agent.matchWithJob(message, jobRequirements);
                        case "extract":
                            return agent.extractKeyInfo(message);
                        case "optimize":
                            String targetPosition = params.getOrDefault("target_position", "");
                            return agent.optimizeForPosition(message, targetPosition);
                        default:
                            return agent.analyzeResume(message);
                    }
                } else {
                    return agentFactory.createResumeAnalyzerAgent(context, provider).analyzeResume(message);
                }

            case "skill":
            case "skill_assessor":
                if (params != null && params.containsKey("action")) {
                    SkillAssessorAgent agent = agentFactory.createSkillAssessorAgent(context, provider);
                    String action = params.get("action");

                    switch (action) {
                        case "assess":
                            String skillArea = params.getOrDefault("skill_area", "");
                            return agent.assessSkill(skillArea, message);
                        case "test":
                            skillArea = params.getOrDefault("skill_area", "Programming");
                            String level = params.getOrDefault("level", "Intermediate");
                            int count = Integer.parseInt(params.getOrDefault("count", "3"));
                            return agent.generateSkillTest(skillArea, level, count);
                        case "evaluate":
                            skillArea = params.getOrDefault("skill_area", "");
                            String question = params.getOrDefault("question", "");
                            return agent.evaluateSkillAnswer(skillArea, question, message);
                        case "report":
                            return agent.generateSkillReport(message);
                        case "learning_path":
                            String targetSkill = params.getOrDefault("target_skill", "");
                            String currentLevel = params.getOrDefault("current_level", "Beginner");
                            String timeFrame = params.getOrDefault("time_frame", "3 months");
                            return agent.createLearningPath(targetSkill, currentLevel, timeFrame);
                        default:
                            return agent.assessSkill("General", message);
                    }
                } else {
                    return agentFactory.createSkillAssessorAgent(context, provider).assessSkill("General", message);
                }

            case "career":
            case "career_advisor":
                if (params != null && params.containsKey("action")) {
                    CareerAdvisorAgent agent = agentFactory.createCareerAdvisorAgent(context, provider);
                    String action = params.get("action");

                    switch (action) {
                        case "advice":
                            return agent.provideCareerAdvice(message);
                        case "plan":
                            String background = params.getOrDefault("background", "");
                            String goals = params.getOrDefault("goals", "");
                            String timeFrame = params.getOrDefault("time_frame", "5 years");
                            return agent.createCareerPlan(background, goals, timeFrame);
                        case "trends":
                            String industry = params.getOrDefault("industry", message);
                            return agent.analyzeIndustryTrends(industry);
                        case "transition":
                            String currentCareer = params.getOrDefault("current_career", "");
                            String targetCareer = params.getOrDefault("target_career", "");
                            return agent.guideCareerTransition(currentCareer, targetCareer, message);
                        case "salary":
                            String position = params.getOrDefault("position", "");
                            String experience = params.getOrDefault("experience", "");
                            String location = params.getOrDefault("location", "");
                            return agent.provideSalaryNegotiationAdvice(position, experience, location);
                        default:
                            return agent.provideCareerAdvice(message);
                    }
                } else {
                    return agentFactory.createCareerAdvisorAgent(context, provider).provideCareerAdvice(message);
                }

            case "mock":
            case "mock_interviewer":
                if (params != null && params.containsKey("action")) {
                    MockInterviewerAgent agent = agentFactory.createMockInterviewerAgent(context, provider);
                    String action = params.get("action");

                    switch (action) {
                        case "conduct":
                            return agent.conductMockInterview(message);
                        case "design":
                            String position = params.getOrDefault("position", "");
                            String duration = params.getOrDefault("duration", "30 minutes");
                            String focus = params.getOrDefault("focus", "");
                            return agent.designInterviewFlow(position, duration, focus);
                        case "stress":
                            String scenario = params.getOrDefault("scenario", "");
                            return agent.conductStressInterview(scenario, message);
                        case "feedback":
                            return agent.provideFeedback(message);
                        case "prepare":
                            String questionType = params.getOrDefault("question_type", "");
                            String question = params.getOrDefault("question", "");
                            return agent.prepareQuestionType(questionType, question);
                        default:
                            return agent.conductMockInterview(message);
                    }
                } else {
                    return agentFactory.createMockInterviewerAgent(context, provider).conductMockInterview(message);
                }

            case "learning":
            case "learning_guide":
                if (params != null && params.containsKey("action")) {
                    LearningGuideAgent agent = agentFactory.createLearningGuideAgent(context, provider);
                    String action = params.get("action");

                    switch (action) {
                        case "guidance":
                            return agent.provideLearningGuidance(message);
                        case "plan":
                            String goal = params.getOrDefault("goal", "");
                            String currentLevel = params.getOrDefault("current_level", "Beginner");
                            String timeAvailable = params.getOrDefault("time_available", "10 hours/week");
                            String preference = params.getOrDefault("preference", "");
                            return agent.createLearningPlan(goal, currentLevel, timeAvailable, preference);
                        case "resources":
                            String topic = params.getOrDefault("topic", message);
                            String level = params.getOrDefault("level", "Intermediate");
                            String resourceType = params.getOrDefault("resource_type", "All");
                            return agent.recommendResources(topic, level, resourceType);
                        case "progress":
                            String challenges = params.getOrDefault("challenges", "");
                            return agent.analyzeProgress(message, challenges);
                        case "methods":
                            String content = params.getOrDefault("content", message);
                            String difficulties = params.getOrDefault("difficulties", "");
                            String environment = params.getOrDefault("environment", "");
                            return agent.provideLearningMethods(content, difficulties, environment);
                        case "knowledge_map":
                            String domain = params.getOrDefault("domain", message);
                            String knownConcepts = params.getOrDefault("known_concepts", "");
                            return agent.buildKnowledgeMap(domain, knownConcepts);
                        default:
                            return agent.provideLearningGuidance(message);
                    }
                } else {
                    return agentFactory.createLearningGuideAgent(context, provider).provideLearningGuidance(message);
                }

            default:
                // 默认使用通用聊天
                return agentFactory.createGeneralChatAgent(context, provider).chat(message);
        }
    }

    /**
     * 调用特定类型的Agent服务（流式）
     *
     * @param agentType Agent类型
     * @param message   用户消息
     * @param params    额外参数
     * @param userId    用户ID
     * @return SSE流
     */
    public SseEmitter callAgentServiceStream(String agentType, String message, Map<String, String> params, Long userId) {
        SseEmitter emitter = new SseEmitter(300000L); // 5分钟超时

        CompletableFuture.runAsync(() -> {
            try {
                // 创建临时的AI消息
                String assistantMessageId = IdUtil.fastSimpleUUID();
                AtomicReference<String> fullResponse = new AtomicReference<>("");

                // 发送开始事件
                emitter.send(SseEmitter.event()
                    .name("start")
                    .data(Map.of("messageId", assistantMessageId)));

                // 创建Agent上下文
                AgentContext context = createAgentContext(null, userId, agentType);

                // 获取提供商（如果指定）
                String provider = params != null ? params.getOrDefault("provider", null) : null;

                // 构建流式响应处理器
                StreamingChatResponseHandler handler = new StreamingChatResponseHandler() {
                    @Override
                    public void onPartialResponse(String token) {
                        try {
                            // 发送token
                            JSONObject tokenMessage = new JSONObject();
                            tokenMessage.put("type", "token");
                            tokenMessage.put("messageId", assistantMessageId);
                            tokenMessage.put("token", token);
                            tokenMessage.put("timestamp", System.currentTimeMillis());

                            emitter.send(SseEmitter.event()
                                .name("token")
                                .data(tokenMessage.toString()));

                            // 累积完整响应
                            fullResponse.updateAndGet(current -> current + token);
                        } catch (IOException | JSONException e) {
                            log.error("发送流式token失败", e);
                        }
                    }

                    @Override
                    public void onCompleteResponse(ChatResponse response) {
                        try {
                            // 发送完成事件
                            JSONObject completeMessage = new JSONObject();
                            completeMessage.put("type", "complete");
                            completeMessage.put("messageId", assistantMessageId);
                            completeMessage.put("fullMessage", fullResponse.get());
                            completeMessage.put("timestamp", System.currentTimeMillis());

                            emitter.send(SseEmitter.event()
                                .name("complete")
                                .data(completeMessage.toString()));

                            emitter.complete();
                        } catch (IOException | JSONException e) {
                            log.error("发送流式完成事件失败", e);
                            emitter.completeWithError(e);
                        }
                    }

                    @Override
                    public void onError(Throwable error) {
                        log.error("流式响应错误", error);
                        try {
                            emitter.send(SseEmitter.event()
                                .name("error")
                                .data("AI响应失败: " + error.getMessage()));
                        } catch (IOException e) {
                            log.error("发送错误事件失败", e);
                        }
                        emitter.completeWithError(error);
                    }
                };

                // 根据Agent类型调用对应的流式服务
                switch (agentType.toLowerCase()) {
                    case "general":
                    case "general_chat":
                        // 创建Agent上下文
                        AgentContext generalContext = createAgentContext(null, userId, AgentType.GENERAL_CHAT.getCode());
                        // 调用流式服务
                        invokeStreamingAgentService(generalContext, message, AgentType.GENERAL_CHAT.getCode(), provider, handler);
                        break;

                    case "interviewer":
                        // 创建Agent上下文
                        AgentContext interviewerContext = createAgentContext(null, userId, AgentType.INTERVIEWER.getCode());

                        if (params != null && params.containsKey("action")) {
                            String action = params.get("action");

                            switch (action) {
                                case "conduct":
                                    // 面试对话
                                    invokeStreamingAgentService(interviewerContext, message, AgentType.INTERVIEWER.getCode(), provider, handler);
                                    break;
                                case "generate":
                                    // 生成问题 - 处理特定参数
                                    String position = params.getOrDefault("position", "Software Engineer");
                                    int count = Integer.parseInt(params.getOrDefault("count", "3"));
                                    String enhancedMessage = "请为" + position + "职位生成" + count + "个面试问题：\n" + message;
                                    invokeStreamingAgentService(interviewerContext, enhancedMessage, AgentType.INTERVIEWER.getCode(), provider, handler);
                                    break;
                                case "evaluate":
                                    // 评估回答
                                    String question = params.getOrDefault("question", "");
                                    String enhancedEvalMessage = "面试问题：" + question + "\n候选人回答：" + message + "\n请评估这个回答的质量。";
                                    invokeStreamingAgentService(interviewerContext, enhancedEvalMessage, AgentType.INTERVIEWER.getCode(), provider, handler);
                                    break;
                                default:
                                    invokeStreamingAgentService(interviewerContext, message, AgentType.INTERVIEWER.getCode(), provider, handler);
                                    break;
                            }
                        } else {
                            invokeStreamingAgentService(interviewerContext, message, AgentType.INTERVIEWER.getCode(), provider, handler);
                        }
                        break;

                    case "resume":
                    case "resume_analyzer":
                        // 创建Agent上下文
                        AgentContext resumeContext = createAgentContext(null, userId, AgentType.RESUME_ANALYZER.getCode());

                        if (params != null && params.containsKey("action")) {
                            String action = params.get("action");

                            switch (action) {
                                case "analyze":
                                    // 分析简历
                                    invokeStreamingAgentService(resumeContext, message, AgentType.RESUME_ANALYZER.getCode(), provider, handler);
                                    break;
                                case "match":
                                    // 匹配职位
                                    String jobRequirements = params.getOrDefault("job_requirements", "");
                                    String matchMessage = "简历内容：\n" + message + "\n\n职位要求：\n" + jobRequirements;
                                    invokeStreamingAgentService(resumeContext, matchMessage, AgentType.RESUME_ANALYZER.getCode(), provider, handler);
                                    break;
                                case "extract":
                                    // 提取关键信息
                                    String extractMessage = "请从以下简历中提取关键信息：\n" + message;
                                    invokeStreamingAgentService(resumeContext, extractMessage, AgentType.RESUME_ANALYZER.getCode(), provider, handler);
                                    break;
                                case "optimize":
                                    // 优化简历
                                    String targetPosition = params.getOrDefault("target_position", "");
                                    String optimizeMessage = "简历内容：\n" + message + "\n\n目标职位：" + targetPosition + "\n请提供优化建议。";
                                    invokeStreamingAgentService(resumeContext, optimizeMessage, AgentType.RESUME_ANALYZER.getCode(), provider, handler);
                                    break;
                                default:
                                    invokeStreamingAgentService(resumeContext, message, AgentType.RESUME_ANALYZER.getCode(), provider, handler);
                                    break;
                            }
                        } else {
                            invokeStreamingAgentService(resumeContext, message, AgentType.RESUME_ANALYZER.getCode(), provider, handler);
                        }
                        break;

                    case "skill":
                    case "skill_assessor":
                        // 创建Agent上下文
                        AgentContext skillContext = createAgentContext(null, userId, AgentType.SKILL_ASSESSOR.getCode());

                        if (params != null && params.containsKey("action")) {
                            String action = params.get("action");

                            switch (action) {
                                case "assess":
                                    // 技能评估
                                    String skillArea = params.getOrDefault("skill_area", "");
                                    String assessMessage = "技能领域：" + skillArea + "\n" + message;
                                    invokeStreamingAgentService(skillContext, assessMessage, AgentType.SKILL_ASSESSOR.getCode(), provider, handler);
                                    break;
                                case "test":
                                    // 生成技能测试
                                    String area = params.getOrDefault("skill_area", "Programming");
                                    String level = params.getOrDefault("level", "Intermediate");
                                    int count = Integer.parseInt(params.getOrDefault("count", "3"));
                                    String testMessage = "请为" + area + "领域的" + level + "级别设计" + count + "个测试题目。";
                                    invokeStreamingAgentService(skillContext, testMessage, AgentType.SKILL_ASSESSOR.getCode(), provider, handler);
                                    break;
                                case "evaluate":
                                    // 评估技能答案
                                    String skillArea2 = params.getOrDefault("skill_area", "");
                                    String question = params.getOrDefault("question", "");
                                    String evalMessage = "技能领域：" + skillArea2 + "\n问题：" + question + "\n回答：" + message;
                                    invokeStreamingAgentService(skillContext, evalMessage, AgentType.SKILL_ASSESSOR.getCode(), provider, handler);
                                    break;
                                default:
                                    invokeStreamingAgentService(skillContext, message, AgentType.SKILL_ASSESSOR.getCode(), provider, handler);
                                    break;
                            }
                        } else {
                            invokeStreamingAgentService(skillContext, message, AgentType.SKILL_ASSESSOR.getCode(), provider, handler);
                        }
                        break;

                    // 其他Agent类型（省略其他类型的详细处理，实际应用时可以展开）
                    default:
                        // 默认使用通用聊天
                        AgentContext defaultContext = createAgentContext(null, userId, AgentType.GENERAL_CHAT.getCode());
                        invokeStreamingAgentService(defaultContext, message, AgentType.GENERAL_CHAT.getCode(), provider, handler);
                        break;
                }

            } catch (Exception e) {
                log.error("流式处理Agent消息失败", e);
                try {
                    emitter.send(SseEmitter.event()
                        .name("error")
                        .data("处理失败: " + e.getMessage()));
                } catch (IOException ex) {
                    log.error("发送错误事件失败", ex);
                }
                emitter.completeWithError(e);
            }
        });

        return emitter;
    }

    /**
     * 根据Agent类型调用相应的服务
     */
    private String invokeAgentService(AgentContext context, String enhancedMessage, String agentType, String provider) {
        AgentType type = AgentType.getByCode(agentType);

//        // 增强消息处理：优先使用思维链提示词增强
//        String enhancedMessage = buildChainOfThoughtPrompt(agentType, message);
//
//        // 对长文本进行处理，避免超出模型限制
//        enhancedMessage = adaptPromptLength(enhancedMessage, 4000); // 假设模型最大支持4000 tokens

        // 如果有会话ID，使用会话历史增强
        if (StrUtil.isNotBlank(context.getSessionId())) {
            String conversationalPrompt = buildConversationalPrompt(
                enhancedMessage,
                context.getSessionId(),
                context.getUserId(),
                10 // 最多保留10轮对话
            );

            // 如果构建成功，使用对话增强的提示词
            if (StrUtil.isNotBlank(conversationalPrompt)) {
                enhancedMessage = conversationalPrompt;
            }
        }

        return switch (type) {
            case INTERVIEWER -> {
                InterviewerAgent interviewerAgent = agentFactory.createAgent(AgentType.INTERVIEWER, context, provider);
                yield interviewerAgent.conductInterview(enhancedMessage);
            }
            case RESUME_ANALYZER -> {
                ResumeAnalyzerAgent resumeAgent = agentFactory.createAgent(AgentType.RESUME_ANALYZER, context, provider);
                yield resumeAgent.analyzeResume(enhancedMessage);
            }
            case SKILL_ASSESSOR -> {
                SkillAssessorAgent skillAgent = agentFactory.createAgent(AgentType.SKILL_ASSESSOR, context, provider);
                yield skillAgent.assessSkill("通用技能", enhancedMessage);
            }
            case CAREER_ADVISOR -> {
                CareerAdvisorAgent careerAgent = agentFactory.createAgent(AgentType.CAREER_ADVISOR, context, provider);
                yield careerAgent.provideCareerAdvice(enhancedMessage);
            }
            case MOCK_INTERVIEWER -> {
                MockInterviewerAgent mockAgent = agentFactory.createAgent(AgentType.MOCK_INTERVIEWER, context, provider);
                yield mockAgent.conductMockInterview(enhancedMessage);
            }
            case LEARNING_GUIDE -> {
                LearningGuideAgent learningAgent = agentFactory.createAgent(AgentType.LEARNING_GUIDE, context, provider);
                yield learningAgent.provideLearningGuidance(enhancedMessage);
            }
            default -> {
                GeneralChatAgent generalAgent = agentFactory.createAgent(AgentType.GENERAL_CHAT, context, provider);
                yield generalAgent.chat(enhancedMessage);
            }
        };
    }

    /**
     * 调用讯飞流式Agent服务
     */
    private void invokeXunfeiStreamingService(AgentContext context, String enhancedMessage, String agentType,
                                              StreamingChatResponseHandler handler) {
        try {
            log.info("调用讯飞星火大模型流式服务，Agent类型: {}, 消息长度: {}", agentType, enhancedMessage.length());

            // 构建讯飞大模型需要的上下文信息
            Map<String, Object> xunfeiContext = buildXunfeiContext(context, agentType);

            // 将 StreamingChatResponseHandler 适配为 IXunfeiService.StreamCallback
            IXunfeiService.StreamCallback callback = new IXunfeiService.StreamCallback() {
                @Override
                public void onToken(String token) {
                    handler.onPartialResponse(token);
                }

                @Override
                public void onComplete(String fullResponse) {
                    // 创建一个简单的 ChatResponse 对象
                    ChatResponse build = ChatResponse.builder()
                        .aiMessage(AiMessage.from(fullResponse))
                        .id(UUID.randomUUID().toString(true))
                        .build();
                    handler.onCompleteResponse(build);
                }

                @Override
                public void onError(Throwable throwable) {
                    handler.onError(throwable);
                }
            };

            // 调用讯飞流式聊天服务（通过上下文传递Agent信息）
            xunfeiContext.put("agentType", agentType);
            xunfeiService.sparkChatStream(enhancedMessage, xunfeiContext, callback);

        } catch (Exception e) {
            log.error("调用讯飞流式服务失败: {}", e.getMessage(), e);
            handler.onError(e);
        }
    }

    /**
     * 构建讯飞大模型需要的上下文信息
     */
    private Map<String, Object> buildXunfeiContext(AgentContext context, String agentType) {
        Map<String, Object> xunfeiContext = new HashMap<>();

        // 添加用户ID
        if (context.getUserId() != null) {
            xunfeiContext.put("userId", context.getUserId());
        }

        // 根据Agent类型设置系统提示词
        String systemPrompt = buildSystemPromptForAgent(agentType);
        if (StrUtil.isNotBlank(systemPrompt)) {
            xunfeiContext.put("systemPrompt", systemPrompt);
        }

        // 构建历史对话记录
        if (StrUtil.isNotBlank(context.getSessionId())) {
            List<Map<String, String>> history = buildChatHistoryForXunfei(context.getSessionId(), context.getUserId());
            if (!history.isEmpty()) {
                xunfeiContext.put("history", history);
            }
        }

        return xunfeiContext;
    }

    /**
     * 根据Agent类型构建系统提示词
     */
    private String buildSystemPromptForAgent(String agentType) {
        return switch (agentType.toLowerCase()) {
            case "interview", "interviewer" -> "你是一位专业的面试官，具有多年的招聘和面试经验。\n" +
                "你的任务是：\n" +
                "1. 根据职位要求设计合适的面试问题\n" +
                "2. 评估候选人的回答质量和专业水平\n" +
                "3. 提供建设性的反馈和建议\n" +
                "4. 帮助候选人提升面试表现\n" +
                "请保持专业、客观、友善的态度，提出有针对性的问题。";

            case "resume", "resume_analyzer" -> "你是一位专业的简历分析专家，具有丰富的HR和招聘经验。\n" +
                "你的任务是：\n" +
                "1. 深入分析简历内容的质量和完整性\n" +
                "2. 识别简历中的亮点和不足之处\n" +
                "3. 提供具体的简历优化建议\n" +
                "4. 帮助求职者提升简历的竞争力\n" +
                "请从专业角度给出客观、实用的分析和建议。";

            case "skill_assessor" -> "你是一位专业的技能评估师，具有丰富的教育和培训经验。\n" +
                "你的任务是：\n" +
                "1. 准确评估用户的技能水平\n" +
                "2. 设计针对性的测试和练习\n" +
                "3. 提供学习建议和资源推荐\n" +
                "4. 帮助用户制定技能提升计划\n" +
                "请保持专业、客观，给出具体可行的建议。";

            case "career_advisor" -> "你是一位经验丰富的职业顾问，熟悉各行各业的发展趋势和职业规划。\n" +
                "你的任务是：\n" +
                "1. 提供全面的职业发展建议\n" +
                "2. 帮助用户制定合理的职业规划\n" +
                "3. 分析行业趋势和就业机会\n" +
                "4. 指导求职策略和职业转型\n" +
                "请结合用户的背景和目标，提供个性化的职业指导。";

            case "mock_interviewer" -> "你是一位专业的模拟面试官，致力于帮助候选人提升面试表现。\n" +
                "你的任务是：\n" +
                "1. 模拟真实的面试场景和氛围\n" +
                "2. 提出挑战性和针对性的问题\n" +
                "3. 提供实时反馈和改进建议\n" +
                "4. 帮助候选人克服面试紧张情绪\n" +
                "请营造专业而友好的面试氛围，给出建设性的指导。";

            case "learning_guide" -> "你是一位专业的学习导师，精通各种学习方法和教育资源。\n" +
                "你的任务是：\n" +
                "1. 分析学习需求和目标\n" +
                "2. 制定个性化的学习计划\n" +
                "3. 推荐高质量的学习资源\n" +
                "4. 提供有效的学习策略和方法\n" +
                "请根据学习者的情况，给出专业、实用的学习指导。";

            default -> "你是一个智能助手，具备丰富的知识和经验。\n" +
                "你的任务是帮助用户解决各种问题，提供准确、有用的信息和建议。\n" +
                "请以友好、专业的态度回应用户的问题。\n" +
                "如果遇到不确定的情况，请诚实地告知用户并建议寻求专业帮助。";
        };
    }

    /**
     * 构建讯飞大模型需要的历史对话记录
     */
    private List<Map<String, String>> buildChatHistoryForXunfei(String sessionId, Long userId) {
        List<Map<String, String>> history = new ArrayList<>();
        try {
            // 获取最近的10条历史消息
            List<ChatMessage> messages = chatMessageMapper.selectRecentMessages(sessionId, userId, 10);

            // 按时间顺序排列（最早的在前）
            messages.sort((a, b) -> a.getCreateTime().compareTo(b.getCreateTime()));

            for (ChatMessage msg : messages) {
                Map<String, String> messageMap = new HashMap<>();
                messageMap.put("role", msg.getRole());
                messageMap.put("content", msg.getContent());
                history.add(messageMap);
            }
        } catch (Exception e) {
            log.warn("构建历史对话记录失败: {}", e.getMessage());
        }
        return history;
    }

    /**
     * 调用流式Agent服务
     */
    private void invokeStreamingAgentService(AgentContext context, String enhancedMessage, String agentType,
                                             String provider, StreamingChatResponseHandler handler) {
        // 选择流式模型
        StreamingChatModel streamingModel = agentFactory.selectStreamingModel(provider);

        // 获取或创建内存
        // TODO: 后期改为使用会话ID进行内存管理
        MessageWindowChatMemory memory = getOrCreateMemory(context.getSessionId());

        try {
            // 根据Agent类型进行不同的处理
            switch (agentType) {
                case "interview":
                    // 清空之前的内存以避免系统提示词冲突
                    memory = MessageWindowChatMemory.withMaxMessages(20);
                    // 优化系统提示词
                    String interviewSystemPrompt = "你是一位专业的面试官，具有多年的招聘和面试经验。\n" +
                        "你的任务是：\n" +
                        "1. 根据职位要求设计合适的面试问题\n" +
                        "2. 评估候选人的回答质量和专业水平\n" +
                        "3. 提供建设性的反馈和建议\n" +
                        "4. 帮助候选人提升面试表现\n" +
                        "请保持专业、客观、友善的态度，提出有针对性的问题。";

                    // 使用PromptEngineeringService优化系统提示词
                    interviewSystemPrompt = buildOptimizedSystemPrompt(interviewSystemPrompt, agentType, context.getUserId());

                    // 添加优化后的面试官系统提示词
                    memory.add(SystemMessage.from(interviewSystemPrompt));

                    // 添加用户消息到内存
                    memory.add(UserMessage.from(enhancedMessage));

                    // 使用流式模型和内存中的消息
                    streamingModel.chat(memory.messages(), handler);
                    break;
                case "resume":
                    // 清空之前的内存以避免系统提示词冲突
                    memory = MessageWindowChatMemory.withMaxMessages(20);

                    // 优化系统提示词
                    String resumeSystemPrompt = "你是一位专业的简历分析专家，具有丰富的HR和招聘经验。\n" +
                        "你的任务是：\n" +
                        "1. 深入分析简历内容的质量和完整性\n" +
                        "2. 识别简历中的亮点和不足之处\n" +
                        "3. 提供具体的简历优化建议\n" +
                        "4. 帮助求职者提升简历的竞争力\n" +
                        "请从专业角度给出客观、实用的分析和建议。";

                    // 使用PromptEngineeringService优化系统提示词
                    resumeSystemPrompt = buildOptimizedSystemPrompt(resumeSystemPrompt, agentType, context.getUserId());

                    // 添加简历分析师系统提示词
                    memory.add(SystemMessage.from(resumeSystemPrompt));

                    // 添加用户消息到内存
                    memory.add(UserMessage.from(enhancedMessage));

                    // 使用流式模型和内存中的消息
                    streamingModel.chat(memory.messages(), handler);
                    break;
                case "skill_assessor":
                    // 清空之前的内存以避免系统提示词冲突
                    memory = MessageWindowChatMemory.withMaxMessages(20);

                    // 优化系统提示词
                    String skillSystemPrompt = "你是一位专业的技能评估师，具有丰富的教育和培训经验。\n" +
                        "你的任务是：\n" +
                        "1. 准确评估用户的技能水平\n" +
                        "2. 设计针对性的测试和练习\n" +
                        "3. 提供学习建议和资源推荐\n" +
                        "4. 帮助用户制定技能提升计划\n" +
                        "请保持专业、客观，给出具体可行的建议。";

                    // 使用PromptEngineeringService优化系统提示词
                    skillSystemPrompt = buildOptimizedSystemPrompt(skillSystemPrompt, agentType, context.getUserId());

                    // 添加技能评估师系统提示词
                    memory.add(SystemMessage.from(skillSystemPrompt));

                    // 添加用户消息到内存
                    memory.add(UserMessage.from("通用技能：" + enhancedMessage));

                    // 使用流式模型和内存中的消息
                    streamingModel.chat(memory.messages(), handler);
                    break;
                case "career_advisor":
                    // 清空之前的内存以避免系统提示词冲突
                    memory = MessageWindowChatMemory.withMaxMessages(20);

                    // 优化系统提示词
                    String careerSystemPrompt = "你是一位经验丰富的职业顾问，熟悉各行各业的发展趋势和职业规划。\n" +
                        "你的任务是：\n" +
                        "1. 提供全面的职业发展建议\n" +
                        "2. 帮助用户制定合理的职业规划\n" +
                        "3. 分析行业趋势和就业机会\n" +
                        "4. 指导求职策略和职业转型\n" +
                        "请结合用户的背景和目标，提供个性化的职业指导。";

                    // 使用PromptEngineeringService优化系统提示词
                    careerSystemPrompt = buildOptimizedSystemPrompt(careerSystemPrompt, agentType, context.getUserId());

                    // 添加职业顾问系统提示词
                    memory.add(SystemMessage.from(careerSystemPrompt));

                    // 添加用户消息到内存
                    memory.add(UserMessage.from(enhancedMessage));

                    // 使用流式模型和内存中的消息
                    streamingModel.chat(memory.messages(), handler);
                    break;
                case "mock_interviewer":
                    // 清空之前的内存以避免系统提示词冲突
                    memory = MessageWindowChatMemory.withMaxMessages(20);

                    // 优化系统提示词
                    String mockSystemPrompt = "你是一位专业的模拟面试官，致力于帮助候选人提升面试表现。\n" +
                        "你的任务是：\n" +
                        "1. 模拟真实的面试场景和氛围\n" +
                        "2. 提出挑战性和针对性的问题\n" +
                        "3. 提供实时反馈和改进建议\n" +
                        "4. 帮助候选人克服面试紧张情绪\n" +
                        "请营造专业而友好的面试氛围，给出建设性的指导。";

                    // 使用PromptEngineeringService优化系统提示词
                    mockSystemPrompt = buildOptimizedSystemPrompt(mockSystemPrompt, agentType, context.getUserId());

                    // 添加模拟面试官系统提示词
                    memory.add(SystemMessage.from(mockSystemPrompt));

                    // 添加用户消息到内存
                    memory.add(UserMessage.from(enhancedMessage));

                    // 使用流式模型和内存中的消息
                    streamingModel.chat(memory.messages(), handler);
                    break;
                case "learning_guide":
                    // 清空之前的内存以避免系统提示词冲突
                    memory = MessageWindowChatMemory.withMaxMessages(20);

                    // 优化系统提示词
                    String learningSystemPrompt = "你是一位专业的学习导师，精通各种学习方法和教育资源。\n" +
                        "你的任务是：\n" +
                        "1. 分析学习需求和目标\n" +
                        "2. 制定个性化的学习计划\n" +
                        "3. 推荐高质量的学习资源\n" +
                        "4. 提供有效的学习策略和方法\n" +
                        "请根据学习者的情况，给出专业、实用的学习指导。";

                    // 使用PromptEngineeringService优化系统提示词
                    learningSystemPrompt = buildOptimizedSystemPrompt(learningSystemPrompt, agentType, context.getUserId());

                    // 添加学习导师系统提示词
                    memory.add(SystemMessage.from(learningSystemPrompt));

                    // 添加用户消息到内存
                    memory.add(UserMessage.from(enhancedMessage));

                    // 使用流式模型和内存中的消息
                    streamingModel.chat(memory.messages(), handler);
                    break;
                default:
                    // 清空之前的内存以避免系统提示词冲突
                    memory = MessageWindowChatMemory.withMaxMessages(20);

                    // 优化系统提示词
                    String generalSystemPrompt = "你是一个智能助手，具备丰富的知识和经验。\n" +
                        "你的任务是帮助用户解决各种问题，提供准确、有用的信息和建议。\n" +
                        "请以友好、专业的态度回应用户的问题。\n" +
                        "如果遇到不确定的情况，请诚实地告知用户并建议寻求专业帮助。";

                    // 使用PromptEngineeringService优化系统提示词
                    generalSystemPrompt = buildOptimizedSystemPrompt(generalSystemPrompt, agentType, context.getUserId());

                    // 添加通用助手系统提示词
                    memory.add(SystemMessage.from(generalSystemPrompt));

                    // 添加用户消息到内存
                    memory.add(UserMessage.from(enhancedMessage));

                    // 使用流式模型和内存中的消息
                    streamingModel.chat(memory.messages(), handler);
                    break;
            }
        } catch (Exception e) {
            log.error("流式处理失败: {}", e.getMessage(), e);
            handler.onError(e);
        }
    }

    /**
     * 创建Agent上下文
     */
    private AgentContext createAgentContext(String sessionId, Long userId, String agentType) {
        AgentType type = AgentType.getByCode(agentType);
        AgentContext context = new AgentContext(sessionId, userId, type);

        // 设置额外的上下文参数
        context.setParameter("sessionId", sessionId);
        context.setParameter("userId", userId);
        context.setParameter("agentType", agentType);

        return context;
    }

    /**
     * 获取或创建内存
     */
    private MessageWindowChatMemory getOrCreateMemory(String sessionId) {
        MessageWindowChatMemory memory = chatMemoryManager.getOrCreateMemory(sessionId, 20);

        // 检查是否需要从数据库加载历史消息
        if (memory.messages().isEmpty()) {
            loadHistoryToMemory(sessionId, memory);
        }

        return memory;
    }

    /**
     * 从数据库加载历史消息到内存
     */
    private void loadHistoryToMemory(String sessionId, MessageWindowChatMemory memory) {
        try {
            // 获取会话信息以获取用户ID
            ChatSession session = chatSessionMapper.selectById(sessionId);
            if (session != null) {
                // 获取最近的10条历史消息
                List<ChatMessage> history = chatMessageMapper.selectRecentMessages(sessionId, session.getUserId(), 10);

                // 按时间顺序排列（最早的在前）
                history.sort((a, b) -> a.getCreateTime().compareTo(b.getCreateTime()));

                for (ChatMessage msg : history) {
                    if ("user".equals(msg.getRole())) {
                        memory.add(UserMessage.from(msg.getContent()));
                    } else if ("assistant".equals(msg.getRole())) {
                        memory.add(AiMessage.from(msg.getContent()));
                    }
                }
            }
        } catch (Exception e) {
            log.warn("加载历史消息到内存失败: {}", e.getMessage());
        }
    }

    /**
     * 使用RAG增强消息
     */
    private String enhanceMessageWithRAG(String agentType, String message) {
        try {
            // 获取检索结果
            List<VectorEmbedding> retrievalResults = agentService.retrieveRelevantDocuments(agentType, message);
            log.debug("检索结果: {}", retrievalResults);

            if (retrievalResults != null && !retrievalResults.isEmpty()) {
                // 使用PromptEngineeringService构建RAG增强提示词
                // 默认上下文窗口大小为2000个token，可根据实际情况调整
                return promptEngineeringService.buildRagEnhancedPrompt(message, retrievalResults, agentType, 2000);
            } else {
                log.debug("无RAG检索结果，使用原始消息");
                return message;
            }
        } catch (Exception e) {
            log.warn("RAG增强查询失败，使用原始消息: {}", e.getMessage());
            return message;
        }
    }

    /**
     * 构建消息元数据
     */
    private Map<String, Object> buildMetadata(ChatRequestDto request, Agent agent) {
        Map bean = JSONUtil.toBean(agent.getModelConfig(), Map.class, true);
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("agentType", request.getAgentType());
        metadata.put("provider", request.getProvider());
        metadata.put("modelName", bean.get("modelName"));
        metadata.put("temperature", request.getTemperature());
        metadata.put("timestamp", System.currentTimeMillis());
        return metadata;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public ChatSession createSession(Long userId, String agentType, String title) {
        ChatSession session = new ChatSession();
        session.setId(IdUtil.fastSimpleUUID());
        session.setUserId(userId);
        session.setAgentType(StrUtil.isBlank(agentType) ? "general" : agentType);
        session.setTitle(StrUtil.isBlank(title) ? "新的对话" : title);
        session.setMessageCount(0);
        session.setLastActiveTime(System.currentTimeMillis());
        session.setStatus(1); // 活跃状态
        session.setCreateTime(DateTime.now());
        session.setUpdateTime(DateTime.now());

        // 保存到数据库
        chatSessionMapper.insert(session);

        log.info("创建新会话: {} for user: {}", session.getId(), userId);
        return session;
    }

    @Override
    public Page<ChatSession> getUserSessions(Long userId, Integer pageNum, Integer pageSize) {
        Page<ChatSession> page = new Page<>(pageNum, pageSize);
        return chatSessionMapper.selectUserSessions(page, userId, null);
    }

    @Override
    public ChatSession getSessionDetail(String sessionId, Long userId) {
        if (!checkSessionPermission(sessionId, userId)) {
            return null;
        }

        ChatSession session = chatSessionMapper.selectByIdAndUserId(sessionId, userId);
        if (session != null) {
            // 加载消息列表
            Page<ChatMessage> messagePage = new Page<>(1, 50);
            Page<ChatMessage> messages = chatMessageMapper.selectSessionMessages(messagePage, sessionId, userId);
            session.setMessages(messages.getRecords());
        }

        return session;
    }

    @Override
    public Page<ChatMessage> getSessionMessages(String sessionId, Long userId, Integer pageNum, Integer pageSize) {
        if (!checkSessionPermission(sessionId, userId)) {
            return new Page<>();
        }

        Page<ChatMessage> page = new Page<>(pageNum, pageSize);
        return chatMessageMapper.selectSessionMessages(page, sessionId, userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteSession(String sessionId, Long userId) {
        if (!checkSessionPermission(sessionId, userId)) {
            return false;
        }

        try {
            // 删除会话消息
            chatMessageMapper.deleteSessionMessages(sessionId, userId);

            // 删除会话
            int deleted = chatSessionMapper.deleteByIdAndUserId(sessionId, userId);

            // 清理内存缓存
            agentFactory.clearAgentCache(sessionId);

            return deleted > 0;
        } catch (Exception e) {
            log.error("删除会话失败: sessionId={}, userId={}", sessionId, userId, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean clearSessionMessages(String sessionId, Long userId) {
        if (!checkSessionPermission(sessionId, userId)) {
            return false;
        }

        try {
            // 删除会话消息
            chatMessageMapper.deleteSessionMessages(sessionId, userId);

            // 重置会话统计
            chatSessionMapper.resetSessionStats(sessionId);

            // 清理内存缓存
            agentFactory.clearAgentCache(sessionId);

            return true;
        } catch (Exception e) {
            log.error("清空会话消息失败: sessionId={}, userId={}", sessionId, userId, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSessionTitle(String sessionId, Long userId, String title) {
        if (!checkSessionPermission(sessionId, userId)) {
            return false;
        }

        try {
            int updated = chatSessionMapper.updateTitle(sessionId, userId, title);
            return updated > 0;
        } catch (Exception e) {
            log.error("更新会话标题失败: sessionId={}, userId={}, title={}", sessionId, userId, title, e);
            return false;
        }
    }

    @Override
    public boolean archiveSession(String sessionId, Long userId, boolean archived) {
        if (!checkSessionPermission(sessionId, userId)) {
            return false;
        }

        try {
            int updated = chatSessionMapper.updateArchiveStatus(sessionId, userId, archived);
            return updated > 0;
        } catch (Exception e) {
            log.error("归档会话失败: sessionId={}, userId={}, archived={}", sessionId, userId, archived, e);
            return false;
        }
    }

    @Override
    public Map<String, Object> getUserChatStats(Long userId) {
        try {
            Map<String, Object> stats = new HashMap<>();

            // 总会话数
            Integer totalSessions = chatSessionMapper.countUserSessions(userId);
            stats.put("totalSessions", totalSessions);

            // 总消息数
            Integer totalMessages = chatMessageMapper.countUserMessages(userId);
            stats.put("totalMessages", totalMessages);

            // 今日消息数
            Integer todayMessages = chatMessageMapper.countTodayMessages(userId);
            stats.put("todayMessages", todayMessages);

            // 活跃会话数
            Integer activeSessions = chatSessionMapper.countActiveSessions(userId);
            stats.put("activeSessions", activeSessions);

            // 按Agent类型统计
            List<Map<String, Object>> agentStats = chatSessionMapper.countSessionsByAgent(userId);
            stats.put("agentStats", agentStats);

            return stats;
        } catch (Exception e) {
            log.error("获取用户聊天统计失败: userId={}", userId, e);
            return new HashMap<>();
        }
    }


    @Override
    public boolean checkSessionPermission(String sessionId, Long userId) {
        try {
            ChatSession session = chatSessionMapper.selectByIdAndUserId(sessionId, userId);
            return session != null;
        } catch (Exception e) {
            log.error("检查会话权限失败: sessionId={}, userId={}", sessionId, userId, e);
            return false;
        }
    }

    @Override
    public String generateSessionTitle(String firstMessage) {
        if (StrUtil.isBlank(firstMessage)) {
            return "新的对话";
        }

        String title = firstMessage.length() > 50 ? firstMessage.substring(0, 50) + "..." : firstMessage;
        return StrUtil.format("对话 - {}", title);
    }

    // ========== 私有辅助方法 ==========
    private ChatMessage createUserMessage(String sessionId, Long userId, ChatRequestDto request) {
        ChatMessage message = new ChatMessage();
        message.setId(IdUtil.fastSimpleUUID());
        message.setSessionId(sessionId);
        message.setUserId(userId);
        message.setRole("user");
        message.setContent(request.getMessage());
        message.setMessageType(request.getMessageType());
        message.setStatus(1); // 发送成功
        message.setIsRead(true);
        message.setCreateTime(DateTime.now());
        message.setUpdateTime(DateTime.now());

        if (request.getAttachments() != null && !request.getAttachments().isEmpty()) {
            message.setAttachments(JSONUtil.toJsonStr(request.getAttachments()));
        }

        return message;
    }

    private ChatMessage createAssistantMessage(String sessionId, Long userId, String content, Map<String, Object> metadata) {
        ChatMessage message = new ChatMessage();
        message.setId(IdUtil.fastSimpleUUID());
        message.setSessionId(sessionId);
        message.setUserId(userId);
        message.setRole("assistant");
        message.setContent(content);
        message.setMessageType("text");
        message.setStatus(1); // 发送成功
        message.setIsRead(true);
        message.setCreateTime(DateTime.now());
        message.setUpdateTime(DateTime.now());

        if (metadata != null && !metadata.isEmpty()) {
            message.setMetadata(JSONUtil.toJsonStr(metadata));
        }

        return message;
    }

    private void saveMessage(ChatMessage message) {
        // 保存到数据库
        chatMessageMapper.insert(message);
    }

    private void updateSessionAfterMessage(String sessionId, String lastMessage) {
        // 获取会话信息以获取用户ID
        ChatSession session = chatSessionMapper.selectById(sessionId);
        if (session != null) {
            // 获取当前消息数量
            Integer messageCount = chatMessageMapper.countSessionMessages(sessionId, session.getUserId());

            // 更新会话统计
            String truncatedMessage = lastMessage.length() > 100 ? lastMessage.substring(0, 100) + "..." : lastMessage;
            chatSessionMapper.updateSessionStats(sessionId, messageCount, truncatedMessage, System.currentTimeMillis());
        }
    }

    /**
     * 将聊天历史转换为ConversationTurn对象列表
     *
     * @param sessionId  会话ID
     * @param userId     用户ID
     * @param maxHistory 最大历史消息数量
     * @return 对话历史转换结果
     */
    private List<ConversationTurn> convertHistoryToConversationTurns(String sessionId, Long userId, int maxHistory) {
        List<ConversationTurn> turns = new ArrayList<>();
        try {
            // 获取最近的历史消息
            List<ChatMessage> history = chatMessageMapper.selectRecentMessages(sessionId, userId, maxHistory);

            // 按时间顺序排列（最早的在前）
            history.sort((a, b) -> a.getCreateTime().compareTo(b.getCreateTime()));

            // 转换为ConversationTurn对象
            for (ChatMessage msg : history) {
                String role = msg.getRole(); // user, assistant, system
                String content = msg.getContent();
                long timestamp = msg.getCreateTime().getTime();

                // 提取元数据（如果有）
                Map<String, Object> metadata = new HashMap<>();
                if (StrUtil.isNotBlank(msg.getMetadata())) {
                    metadata = JSONUtil.toBean(msg.getMetadata(), Map.class);
                }

                // 创建ConversationTurn对象
                ConversationTurn turn = new ConversationTurn(role, content, timestamp, metadata);
                turns.add(turn);
            }
        } catch (Exception e) {
            log.warn("转换对话历史失败: {}", e.getMessage());
        }

        return turns;
    }

    /**
     * 构建思维链提示词
     *
     * @param agentType Agent类型
     * @param message   用户消息
     * @return 增强后的提示词
     */
    private String buildChainOfThoughtPrompt(String agentType, String message) {
        try {
            // 根据不同的Agent类型使用不同的任务类型
            String taskType;
            switch (agentType.toLowerCase()) {
                case "interviewer":
                    taskType = "面试评估";
                    break;
                case "resume_analyzer":
                    taskType = "简历分析";
                    break;
                case "skill_assessor":
                    taskType = "技能评估";
                    break;
                case "career_advisor":
                    taskType = "职业规划";
                    break;
                case "learning_guide":
                    taskType = "学习指导";
                    break;
                case "mock_interviewer":
                    taskType = "模拟面试";
                    break;
                default:
                    taskType = "通用";
            }

            // 使用PromptEngineeringService构建思维链提示词
            return promptEngineeringService.buildChainOfThoughtPrompt(message, taskType);
        } catch (Exception e) {
            log.warn("构建思维链提示词失败: {}", e.getMessage());
            return message;
        }
    }

    /**
     * 构建优化的系统提示词
     *
     * @param originalPrompt 原始系统提示词
     * @param agentType      Agent类型
     * @param userId         用户ID
     * @return 优化后的系统提示词
     */
    private String buildOptimizedSystemPrompt(String originalPrompt, String agentType, Long userId) {
        try {
            // 构建用户上下文
            Map<String, Object> userContext = new HashMap<>();

            // 可以从用户设置或其他地方加载用户偏好
            // 这里只是示例，实际应用中可以扩展
            userContext.put("userId", userId);

            // 使用PromptEngineeringService优化系统提示词
            return promptEngineeringService.optimizeSystemPrompt(originalPrompt, agentType, userContext);
        } catch (Exception e) {
            log.warn("优化系统提示词失败: {}", e.getMessage());
            return originalPrompt;
        }
    }

    /**
     * 构建对话提示词
     *
     * @param currentQuery     当前查询
     * @param sessionId        会话ID
     * @param userId           用户ID
     * @param maxHistoryLength 最大历史长度
     * @return 构建的对话提示词
     */
    private String buildConversationalPrompt(String currentQuery, String sessionId, Long userId, int maxHistoryLength) {
        try {
            // 获取对话历史
            List<ConversationTurn> history = convertHistoryToConversationTurns(sessionId, userId, maxHistoryLength);

            // 使用PromptEngineeringService构建对话提示词
            return promptEngineeringService.buildConversationalPrompt(currentQuery, history, maxHistoryLength);
        } catch (Exception e) {
            log.warn("构建对话提示词失败: {}", e.getMessage());
            return currentQuery;
        }
    }

    /**
     * 适应提示词长度，避免超出模型限制
     *
     * @param prompt    提示词
     * @param maxTokens 最大token数量
     * @return 调整后的提示词
     */
    private String adaptPromptLength(String prompt, int maxTokens) {
        try {
            // 默认使用截断末尾的策略，可以根据需要修改
            return promptEngineeringService.adjustPromptLength(prompt, maxTokens, LengthAdjustmentStrategy.PRIORITIZE_RELEVANT);
        } catch (Exception e) {
            log.warn("调整提示词长度失败: {}", e.getMessage());

            // 简单截断处理，确保不会超出限制
            if (prompt.length() > maxTokens * 4) { // 粗略估算token和字符的关系
                return prompt.substring(0, maxTokens * 4) + "...（内容因长度限制被截断）";
            }
            return prompt;
        }
    }
}
