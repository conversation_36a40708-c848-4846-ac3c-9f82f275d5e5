{"doc": "\n 防止重复提交(参考美团GTIS防重系统)\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "doAfterReturning", "paramTypes": ["org.aspectj.lang.JoinPoint", "org.dromara.common.idempotent.annotation.RepeatSubmit", "java.lang.Object"], "doc": "\n 处理完请求后执行\r\n\r\n @param joinPoint 切点\r\n"}, {"name": "doAfterThrowing", "paramTypes": ["org.aspectj.lang.JoinPoint", "org.dromara.common.idempotent.annotation.RepeatSubmit", "java.lang.Exception"], "doc": "\n 拦截异常操作\r\n\r\n @param joinPoint 切点\r\n @param e         异常\r\n"}, {"name": "argsArrayToString", "paramTypes": ["java.lang.Object[]"], "doc": "\n 参数拼装\r\n"}, {"name": "isFilterObject", "paramTypes": ["java.lang.Object"], "doc": "\n 判断是否需要过滤的对象。\r\n\r\n @param o 对象信息。\r\n @return 如果是需要过滤的对象，则返回true；否则返回false。\r\n"}], "constructors": []}