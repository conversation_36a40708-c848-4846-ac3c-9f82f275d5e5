package org.dromara.app.service.impl;


import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.*;
import org.dromara.app.domain.bo.AssessmentResultBo;
import org.dromara.app.domain.vo.*;
import org.dromara.app.mapper.*;
import org.dromara.app.service.IAssessmentService;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 能力评估服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class AssessmentServiceImpl implements IAssessmentService {

    private final AssessmentQuestionMapper assessmentQuestionMapper;
    private final AssessmentQuestionOptionMapper assessmentQuestionOptionMapper;
    private final UserAssessmentRecordMapper userAssessmentRecordMapper;
    private final UserAssessmentResultMapper userAssessmentResultMapper;
    private final UserGrowthProfileMapper userGrowthProfileMapper;

    /**
     * 获取评估问题列表
     */
    @Override
    public List<AssessmentQuestionVo> getAssessmentQuestions() {
        try {
            // 从数据库获取评估问题（包含选项）
            List<AssessmentQuestion> questions = assessmentQuestionMapper.selectQuestionsWithOptionsByStatus("0");

            // 转换为VO对象
            return questions.stream().map(this::convertToQuestionVo).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取评估问题列表失败", e);
            throw new ServiceException("获取评估问题失败");
        }
    }

    /**
     * 转换评估问题实体为VO对象
     */
    private AssessmentQuestionVo convertToQuestionVo(AssessmentQuestion question) {
        AssessmentQuestionVo vo = new AssessmentQuestionVo();
        vo.setId(question.getQuestionCode());
        vo.setType(question.getQuestionType());
        vo.setCategory(question.getCategory());
        vo.setQuestion(question.getQuestionContent());
        vo.setMinValue(question.getMinValue());
        vo.setMaxValue(question.getMaxValue());

        // 转换选项
        if (question.getOptions() != null && !question.getOptions().isEmpty()) {
            List<OptionVo> optionVos = question.getOptions().stream()
                .map(option -> new OptionVo(option.getOptionCode(), option.getOptionText(), option.getOptionScore()))
                .collect(Collectors.toList());
            vo.setOptions(optionVos);
        }

        return vo;
    }

    /**
     * 提交评估结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public InitialAbilityAssessmentVo submitAssessmentResults(List<AssessmentResultBo> results) {
        try {
            Long userId = LoginHelper.getUserId();

            // 创建评估记录
            UserAssessmentRecord record = createAssessmentRecord(userId, results.size());
            userAssessmentRecordMapper.insert(record);

            // 保存评估结果并计算分数
            List<UserAssessmentResult> assessmentResults = new ArrayList<>();
            Map<String, List<Integer>> categoryScores = new HashMap<>();

            for (AssessmentResultBo resultBo : results) {
                UserAssessmentResult result = convertToAssessmentResult(resultBo, record.getRecordId());
                assessmentResults.add(result);

                // 按类别收集分数
                categoryScores.computeIfAbsent(result.getCategory(), k -> new ArrayList<>()).add(result.getScore());
            }

            // 批量插入评估结果
            if (!assessmentResults.isEmpty()) {
                userAssessmentResultMapper.insertBatch(assessmentResults);
            }

            // 计算各维度得分
            InitialAbilityAssessmentVo assessment = calculateAbilityScores(categoryScores);

            // 更新评估记录的得分
            updateRecordScores(record.getRecordId(), assessment);

            // 创建或更新用户成长档案
            createOrUpdateUserGrowthProfile(userId, assessment);

            return assessment;
        } catch (Exception e) {
            log.error("提交评估结果失败", e);
            throw new ServiceException("提交评估结果失败");
        }
    }

    /**
     * 创建评估记录
     */
    private UserAssessmentRecord createAssessmentRecord(Long userId, int totalQuestions) {
        UserAssessmentRecord record = new UserAssessmentRecord();
        record.setUserId(userId);
        record.setAssessmentType("initial");
        record.setStartTime(new Date());
        record.setTotalQuestions(totalQuestions);
        record.setCompletedQuestions(totalQuestions);
        record.setStatus("completed");
        record.setEndTime(new Date());
        record.setCreateBy(userId);
        record.setCreateTime(new Date());
        return record;
    }

    /**
     * 转换评估结果BO为实体对象
     */
    private UserAssessmentResult convertToAssessmentResult(AssessmentResultBo resultBo, Long recordId) {
        UserAssessmentResult result = new UserAssessmentResult();
        result.setRecordId(recordId);

        // 根据问题编码获取问题ID
        AssessmentQuestion question = assessmentQuestionMapper.selectQuestionWithOptionsByCode(resultBo.getQuestionId());
        if (question == null) {
            throw new ServiceException("问题不存在: " + resultBo.getQuestionId());
        }

        result.setQuestionId(question.getQuestionId());
        result.setQuestionType(question.getQuestionType());
        result.setCategory(question.getCategory());
        result.setAnswerTime(new Date());
        result.setCreateBy(LoginHelper.getUserId());
        result.setCreateTime(new Date());

        // 计算得分
        int score = calculateQuestionScore(question, resultBo);
        result.setScore(score);

        if ("single".equals(question.getQuestionType())) {
            // 单选题：根据选项ID获取选项
            AssessmentQuestionOption option = question.getOptions().stream()
                .filter(opt -> opt.getOptionCode().equals(resultBo.getSelectedOptionId()))
                .findFirst()
                .orElseThrow(() -> new ServiceException("选项不存在: " + resultBo.getSelectedOptionId()));
            result.setSelectedOptionId(option.getOptionId());
        } else if ("scale".equals(question.getQuestionType())) {
            // 量表题：直接使用分值
            result.setSelectedValue(resultBo.getSelectedValue());
        }

        return result;
    }

    /**
     * 计算单个问题的得分
     */
    private int calculateQuestionScore(AssessmentQuestion question, AssessmentResultBo resultBo) {
        if ("single".equals(question.getQuestionType())) {
            // 单选题：根据选项获取分数
            return question.getOptions().stream()
                .filter(opt -> opt.getOptionCode().equals(resultBo.getSelectedOptionId()))
                .findFirst()
                .map(AssessmentQuestionOption::getOptionScore)
                .orElse(0);
        } else if ("scale".equals(question.getQuestionType())) {
            // 量表题：直接使用分值
            return resultBo.getSelectedValue() != null ? resultBo.getSelectedValue() : 0;
        }
        return 0;
    }

    /**
     * 计算各维度能力得分
     */
    private InitialAbilityAssessmentVo calculateAbilityScores(Map<String, List<Integer>> categoryScores) {
        InitialAbilityAssessmentVo assessment = new InitialAbilityAssessmentVo();

        // 计算各维度平均分（转换为100分制）
        assessment.setProfessionalKnowledge(calculateCategoryScore(categoryScores.get("professionalKnowledge")));
        assessment.setLogicalThinking(calculateCategoryScore(categoryScores.get("logicalThinking")));
        assessment.setLanguageExpression(calculateCategoryScore(categoryScores.get("languageExpression")));
        assessment.setStressResistance(calculateCategoryScore(categoryScores.get("stressResistance")));
        assessment.setTeamCollaboration(calculateCategoryScore(categoryScores.get("teamCollaboration")));
        assessment.setInnovation(calculateCategoryScore(categoryScores.get("innovation")));

        // 计算总分（加权平均）
        int overallScore = (int) ((assessment.getProfessionalKnowledge() * 0.2) +
            (assessment.getLogicalThinking() * 0.2) +
            (assessment.getLanguageExpression() * 0.2) +
            (assessment.getStressResistance() * 0.1) +
            (assessment.getTeamCollaboration() * 0.15) +
            (assessment.getInnovation() * 0.15));
        assessment.setOverallScore(overallScore);

        return assessment;
    }

    /**
     * 计算单个类别的得分
     */
    private Integer calculateCategoryScore(List<Integer> scores) {
        if (scores == null || scores.isEmpty()) {
            return 0;
        }

        // 计算平均分并转换为100分制
        double average = scores.stream().mapToInt(Integer::intValue).average().orElse(0.0);
        return (int) Math.round(average * 20); // 假设原始分数是1-5分制，转换为100分制
    }

    /**
     * 更新评估记录的得分
     */
    private void updateRecordScores(Long recordId, InitialAbilityAssessmentVo assessment) {
        UserAssessmentRecord record = new UserAssessmentRecord();
        record.setRecordId(recordId);
        record.setOverallScore(assessment.getOverallScore());
        record.setProfessionalKnowledge(assessment.getProfessionalKnowledge());
        record.setLogicalThinking(assessment.getLogicalThinking());
        record.setLanguageExpression(assessment.getLanguageExpression());
        record.setStressResistance(assessment.getStressResistance());
        record.setTeamCollaboration(assessment.getTeamCollaboration());
        record.setInnovation(assessment.getInnovation());
        record.setUpdateBy(LoginHelper.getUserId());
        record.setUpdateTime(new Date());

        userAssessmentRecordMapper.updateById(record);
    }

    /**
     * 创建或更新用户成长档案
     */
    private void createOrUpdateUserGrowthProfile(Long userId, InitialAbilityAssessmentVo assessment) {
        UserGrowthProfile existingProfile = userGrowthProfileMapper.selectProfileByUserId(userId);

        if (existingProfile == null) {
            // 创建新的成长档案
            UserGrowthProfile profile = new UserGrowthProfile();
            profile.setUserId(userId);
            profile.setCurrentStage(determineUserStage(assessment.getOverallScore()));
            profile.setJoinDate(new Date());
            profile.setLastActiveDate(new Date());
            profile.setTotalInterviews(0);

            // 设置初始评估分数
            setInitialAssessmentScores(profile, assessment);
            // 设置当前评估分数
            setCurrentAssessmentScores(profile, assessment);

            profile.setImprovementRate(0);
            profile.setTargetPosition("前端工程师");
            profile.setLearningGoals("[\"掌握React高级特性\",\"提升算法能力\",\"优化表达能力\"]");
            profile.setAchievements("[\"完成初次评估\"]");
            profile.setContinuousLearningDays(1);
            profile.setCompletedCourses(0);
            profile.setCreateBy(userId);
            profile.setCreateTime(new Date());

            userGrowthProfileMapper.insert(profile);
        } else {
            // 更新现有档案
            setCurrentAssessmentScores(existingProfile, assessment);
            existingProfile.setCurrentStage(determineUserStage(assessment.getOverallScore()));
            existingProfile.setLastActiveDate(new Date());

            // 计算进步率
            if (existingProfile.getInitialOverallScore() != null && existingProfile.getInitialOverallScore() > 0) {
                int improvementRate = (int) (((double) (assessment.getOverallScore() - existingProfile.getInitialOverallScore())
                    / existingProfile.getInitialOverallScore()) * 100);
                existingProfile.setImprovementRate(Math.max(0, improvementRate));
            }

            existingProfile.setUpdateBy(userId);
            existingProfile.setUpdateTime(new Date());

            userGrowthProfileMapper.updateById(existingProfile);
        }
    }

    /**
     * 根据总分确定用户阶段
     */
    private String determineUserStage(Integer overallScore) {
        if (overallScore >= 85) {
            return "expert";
        } else if (overallScore >= 75) {
            return "advanced";
        } else if (overallScore >= 65) {
            return "intermediate";
        } else if (overallScore >= 50) {
            return "beginner";
        } else {
            return "new_user";
        }
    }

    /**
     * 设置初始评估分数
     */
    private void setInitialAssessmentScores(UserGrowthProfile profile, InitialAbilityAssessmentVo assessment) {
        profile.setInitialOverallScore(assessment.getOverallScore());
        profile.setInitialProfessionalKnowledge(assessment.getProfessionalKnowledge());
        profile.setInitialLogicalThinking(assessment.getLogicalThinking());
        profile.setInitialLanguageExpression(assessment.getLanguageExpression());
        profile.setInitialStressResistance(assessment.getStressResistance());
        profile.setInitialTeamCollaboration(assessment.getTeamCollaboration());
        profile.setInitialInnovation(assessment.getInnovation());
    }

    /**
     * 设置当前评估分数
     */
    private void setCurrentAssessmentScores(UserGrowthProfile profile, InitialAbilityAssessmentVo assessment) {
        profile.setCurrentOverallScore(assessment.getOverallScore());
        profile.setCurrentProfessionalKnowledge(assessment.getProfessionalKnowledge());
        profile.setCurrentLogicalThinking(assessment.getLogicalThinking());
        profile.setCurrentLanguageExpression(assessment.getLanguageExpression());
        profile.setCurrentStressResistance(assessment.getStressResistance());
        profile.setCurrentTeamCollaboration(assessment.getTeamCollaboration());
        profile.setCurrentInnovation(assessment.getInnovation());
    }

    /**
     * 获取详细能力报告
     */
    @Override
    public DetailedAbilityReportVo getDetailedAbilityReport() {
        try {
            Long userId = LoginHelper.getUserId();

            // 获取用户最新的评估记录
            UserAssessmentRecord latestRecord = userAssessmentRecordMapper.selectLatestRecordByUserId(userId);
            if (latestRecord == null) {
                throw new ServiceException("用户尚未完成评估");
            }

            DetailedAbilityReportVo report = new DetailedAbilityReportVo();

            // 基于评估结果生成优势和劣势分析
            report.setStrengths(generateStrengths(latestRecord));
            report.setWeaknesses(generateWeaknesses(latestRecord));

            // 生成推荐内容
            report.setRecommendations(generateRecommendations(latestRecord));

            return report;
        } catch (Exception e) {
            log.error("获取详细能力报告失败", e);
            throw new ServiceException("获取能力报告失败");
        }
    }

    /**
     * 生成优势分析
     */
    private List<String> generateStrengths(UserAssessmentRecord record) {
        List<String> strengths = new ArrayList<>();

        if (record.getTeamCollaboration() != null && record.getTeamCollaboration() >= 80) {
            strengths.add("团队协作能力出色，善于与他人合作");
        }
        if (record.getLogicalThinking() != null && record.getLogicalThinking() >= 80) {
            strengths.add("逻辑思维能力较强，能够解决复杂问题");
        }
        if (record.getProfessionalKnowledge() != null && record.getProfessionalKnowledge() >= 80) {
            strengths.add("专业知识掌握扎实，对技术有较深理解");
        }
        if (record.getLanguageExpression() != null && record.getLanguageExpression() >= 80) {
            strengths.add("语言表达能力优秀，能够清晰传达想法");
        }
        if (record.getStressResistance() != null && record.getStressResistance() >= 80) {
            strengths.add("抗压能力强，能在高压环境下保持良好表现");
        }
        if (record.getInnovation() != null && record.getInnovation() >= 80) {
            strengths.add("创新思维活跃，能够提出独特的解决方案");
        }

        if (strengths.isEmpty()) {
            strengths.add("具备基础的职业素养，有良好的学习潜力");
        }

        return strengths;
    }

    /**
     * 生成劣势分析
     */
    private List<String> generateWeaknesses(UserAssessmentRecord record) {
        List<String> weaknesses = new ArrayList<>();

        if (record.getStressResistance() != null && record.getStressResistance() < 70) {
            weaknesses.add("抗压能力有待提高，在高压环境下可能表现受影响");
        }
        if (record.getInnovation() != null && record.getInnovation() < 70) {
            weaknesses.add("创新思维可以进一步加强");
        }
        if (record.getLanguageExpression() != null && record.getLanguageExpression() < 70) {
            weaknesses.add("语言表达能力需要持续练习");
        }
        if (record.getProfessionalKnowledge() != null && record.getProfessionalKnowledge() < 70) {
            weaknesses.add("专业知识需要进一步深化和拓展");
        }
        if (record.getLogicalThinking() != null && record.getLogicalThinking() < 70) {
            weaknesses.add("逻辑思维能力需要通过练习来提升");
        }
        if (record.getTeamCollaboration() != null && record.getTeamCollaboration() < 70) {
            weaknesses.add("团队协作技巧需要在实践中进一步提升");
        }

        return weaknesses;
    }

    /**
     * 生成推荐内容
     */
    private RecommendationsVo generateRecommendations(UserAssessmentRecord record) {
        RecommendationsVo recommendations = new RecommendationsVo();

        List<ResourceVo> resources = new ArrayList<>();
        List<PracticeVo> practices = new ArrayList<>();

        // 根据薄弱环节推荐资源和练习
        if (record.getProfessionalKnowledge() != null && record.getProfessionalKnowledge() < 75) {
            resources.add(new ResourceVo("React高级模式与最佳实践", "在线课程",
                "https://example.com/course/react-advanced", "深入学习React高级特性和性能优化"));
            resources.add(new ResourceVo("算法与数据结构精讲", "电子书",
                "https://example.com/book/algorithm", "从基础到高级的算法学习指南"));
        }

        if (record.getLanguageExpression() != null && record.getLanguageExpression() < 75) {
            resources.add(new ResourceVo("技术面试表达技巧", "视频课程",
                "https://example.com/course/interview-expression", "提升面试沟通和表达能力"));
            practices.add(new PracticeVo("演讲练习", "每天练习技术主题演讲，录制并分析", "初级"));
        }

        if (record.getStressResistance() != null && record.getStressResistance() < 75) {
            practices.add(new PracticeVo("模拟高压面试环境", "每周安排2-3次限时模拟面试，提高抗压能力", "中等"));
        }

        if (record.getLogicalThinking() != null && record.getLogicalThinking() < 75) {
            practices.add(new PracticeVo("算法题训练", "每日完成3-5道算法题，提升逻辑思维能力", "中等"));
        }

        if (record.getInnovation() != null && record.getInnovation() < 75) {
            practices.add(new PracticeVo("项目实战训练", "参与开源项目，提升实际问题解决能力", "高级"));
        }

        // 默认推荐
        if (resources.isEmpty()) {
            resources.add(new ResourceVo("全栈开发进阶指南", "综合课程",
                "https://example.com/course/fullstack", "全面提升技术能力"));
        }

        if (practices.isEmpty()) {
            practices.add(new PracticeVo("日常技能练习", "保持持续学习，定期自我评估", "初级"));
        }

        recommendations.setResources(resources);
        recommendations.setPractices(practices);

        return recommendations;
    }

    /**
     * 获取用户成长档案
     */
    @Override
    public UserGrowthProfileVo getUserGrowthProfile() {
        try {
            Long userId = LoginHelper.getUserId();

            // 从数据库获取用户成长档案
            UserGrowthProfile profile = userGrowthProfileMapper.selectProfileByUserId(userId);
            if (profile == null) {
                throw new ServiceException("用户成长档案不存在，请先完成评估");
            }

            // 转换为VO对象
            return convertToGrowthProfileVo(profile);
        } catch (Exception e) {
            log.error("获取用户成长档案失败", e);
            throw new ServiceException("获取用户成长档案失败");
        }
    }

    /**
     * 转换用户成长档案实体为VO对象
     */
    private UserGrowthProfileVo convertToGrowthProfileVo(UserGrowthProfile profile) {
        UserGrowthProfileVo vo = new UserGrowthProfileVo();
        vo.setUserId(profile.getUserId().toString());
        vo.setCurrentStage(profile.getCurrentStage());
        vo.setJoinDate(profile.getJoinDate());
        vo.setLastActiveDate(profile.getLastActiveDate());
        vo.setTotalInterviews(profile.getTotalInterviews());
        vo.setImprovementRate(profile.getImprovementRate());
        vo.setTargetPosition(profile.getTargetPosition());
        vo.setContinuousLearningDays(profile.getContinuousLearningDays());
        vo.setCompletedCourses(profile.getCompletedCourses());

        // 设置初始评估
        InitialAbilityAssessmentVo initialAssessment = new InitialAbilityAssessmentVo();
        initialAssessment.setOverallScore(profile.getInitialOverallScore());
        initialAssessment.setProfessionalKnowledge(profile.getInitialProfessionalKnowledge());
        initialAssessment.setLogicalThinking(profile.getInitialLogicalThinking());
        initialAssessment.setLanguageExpression(profile.getInitialLanguageExpression());
        initialAssessment.setStressResistance(profile.getInitialStressResistance());
        initialAssessment.setTeamCollaboration(profile.getInitialTeamCollaboration());
        initialAssessment.setInnovation(profile.getInitialInnovation());
        vo.setInitialAssessment(initialAssessment);

        // 设置当前评估
        InitialAbilityAssessmentVo currentAssessment = new InitialAbilityAssessmentVo();
        currentAssessment.setOverallScore(profile.getCurrentOverallScore());
        currentAssessment.setProfessionalKnowledge(profile.getCurrentProfessionalKnowledge());
        currentAssessment.setLogicalThinking(profile.getCurrentLogicalThinking());
        currentAssessment.setLanguageExpression(profile.getCurrentLanguageExpression());
        currentAssessment.setStressResistance(profile.getCurrentStressResistance());
        currentAssessment.setTeamCollaboration(profile.getCurrentTeamCollaboration());
        currentAssessment.setInnovation(profile.getCurrentInnovation());
        vo.setCurrentAssessment(currentAssessment);

        // 解析JSON字符串为列表
        vo.setLearningGoals(parseJsonStringToList(profile.getLearningGoals()));
        vo.setAchievements(parseJsonStringToList(profile.getAchievements()));

        return vo;
    }

    /**
     * 解析JSON字符串为字符串列表
     */
    private List<String> parseJsonStringToList(String jsonString) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return new ArrayList<>();
        }

        try {
            // 简单的JSON数组解析（假设格式为 ["item1","item2","item3"]）
            jsonString = jsonString.trim();
            if (jsonString.startsWith("[") && jsonString.endsWith("]")) {
                jsonString = jsonString.substring(1, jsonString.length() - 1);
                if (jsonString.trim().isEmpty()) {
                    return new ArrayList<>();
                }

                List<String> result = new ArrayList<>();
                String[] items = jsonString.split(",");
                for (String item : items) {
                    item = item.trim();
                    if (item.startsWith("\"") && item.endsWith("\"")) {
                        item = item.substring(1, item.length() - 1);
                    }
                    result.add(item);
                }
                return result;
            }
        } catch (Exception e) {
            log.warn("解析JSON字符串失败: {}", jsonString, e);
        }

        return new ArrayList<>();
    }
}
