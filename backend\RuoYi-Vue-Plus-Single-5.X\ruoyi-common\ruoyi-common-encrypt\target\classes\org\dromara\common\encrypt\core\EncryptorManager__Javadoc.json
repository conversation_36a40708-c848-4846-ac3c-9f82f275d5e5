{"doc": "\n 加密管理类\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "encryptorMap", "doc": "\n 缓存加密器\r\n"}, {"name": "fieldCache", "doc": "\n 类加密字段缓存\r\n"}], "enumConstants": [], "methods": [{"name": "getField<PERSON>ache", "paramTypes": ["java.lang.Class"], "doc": "\n 获取类加密字段缓存\r\n"}, {"name": "registAndGetEncryptor", "paramTypes": ["org.dromara.common.encrypt.core.EncryptContext"], "doc": "\n 注册加密执行者到缓存\r\n\r\n @param encryptContext 加密执行者需要的相关配置参数\r\n"}, {"name": "removeEncryptor", "paramTypes": ["org.dromara.common.encrypt.core.EncryptContext"], "doc": "\n 移除缓存中的加密执行者\r\n\r\n @param encryptContext 加密执行者需要的相关配置参数\r\n"}, {"name": "encrypt", "paramTypes": ["java.lang.String", "org.dromara.common.encrypt.core.EncryptContext"], "doc": "\n 根据配置进行加密。会进行本地缓存对应的算法和对应的秘钥信息。\r\n\r\n @param value          待加密的值\r\n @param encryptContext 加密相关的配置信息\r\n"}, {"name": "decrypt", "paramTypes": ["java.lang.String", "org.dromara.common.encrypt.core.EncryptContext"], "doc": "\n 根据配置进行解密\r\n\r\n @param value          待解密的值\r\n @param encryptContext 加密相关的配置信息\r\n"}, {"name": "scanEncryptClasses", "paramTypes": ["java.lang.String"], "doc": "\n 通过 typeAliasesPackage 设置的扫描包 扫描缓存实体\r\n"}, {"name": "getEncryptFieldSetFromClazz", "paramTypes": ["java.lang.Class"], "doc": "\n 获得一个类的加密字段集合\r\n"}], "constructors": [{"name": "<init>", "paramTypes": ["java.lang.String"], "doc": "\n 构造方法传入类加密字段缓存\r\n\r\n @param typeAliasesPackage 实体类包\r\n"}]}