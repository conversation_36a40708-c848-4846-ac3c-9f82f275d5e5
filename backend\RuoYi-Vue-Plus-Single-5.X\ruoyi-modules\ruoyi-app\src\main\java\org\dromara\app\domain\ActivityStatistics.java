package org.dromara.app.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.app.domain.enums.ActivityType;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.time.LocalDate;

/**
 * 用户活动统计对象 app_activity_statistics
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_activity_statistics")
public class ActivityStatistics extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 统计ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 活动类型
     */
    private ActivityType activityType;

    /**
     * 统计日期
     */
    private LocalDate statDate;

    /**
     * 总时长(毫秒)
     */
    private Long totalDuration;

    /**
     * 会话次数
     */
    private Integer sessionCount;

    /**
     * 平均时长(毫秒)
     */
    private Long avgDuration;

    /**
     * 最长时长(毫秒)
     */
    private Long maxDuration;

    /**
     * 初始化统计数据
     *
     * @param userId          用户ID
     * @param activityType    活动类型
     * @param statDate        统计日期
     * @param sessionDuration 首次会话时长
     * @return 初始化的统计对象
     */
    public static ActivityStatistics initialize(Long userId, ActivityType activityType,
                                                LocalDate statDate, Long sessionDuration) {
        ActivityStatistics statistics = new ActivityStatistics();
        statistics.setUserId(userId);
        statistics.setActivityType(activityType);
        statistics.setStatDate(statDate);
        statistics.setTotalDuration(sessionDuration != null ? sessionDuration : 0L);
        statistics.setSessionCount(1);
        statistics.setAvgDuration(sessionDuration != null ? sessionDuration : 0L);
        statistics.setMaxDuration(sessionDuration != null ? sessionDuration : 0L);
        return statistics;
    }

    /**
     * 更新统计数据
     *
     * @param sessionDuration 新增的会话时长
     */
    public void updateStatistics(Long sessionDuration) {
        if (sessionDuration == null || sessionDuration <= 0) {
            return;
        }

        // 更新总时长
        this.totalDuration = (this.totalDuration != null ? this.totalDuration : 0L) + sessionDuration;

        // 更新会话次数
        this.sessionCount = (this.sessionCount != null ? this.sessionCount : 0) + 1;

        // 更新平均时长
        this.avgDuration = this.totalDuration / this.sessionCount;

        // 更新最长时长
        if (this.maxDuration == null || sessionDuration > this.maxDuration) {
            this.maxDuration = sessionDuration;
        }
    }
}
