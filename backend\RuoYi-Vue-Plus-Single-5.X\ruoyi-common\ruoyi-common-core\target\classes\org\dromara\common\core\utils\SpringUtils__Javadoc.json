{"doc": "\n spring工具类\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "containsBean", "paramTypes": ["java.lang.String"], "doc": "\n 如果BeanFactory包含一个与所给名称匹配的bean定义，则返回true\r\n"}, {"name": "isSingleton", "paramTypes": ["java.lang.String"], "doc": "\n 判断以给定名字注册的bean定义是一个singleton还是一个prototype。\r\n 如果与给定名字相应的bean定义没有被找到，将会抛出一个异常（NoSuchBeanDefinitionException）\r\n"}, {"name": "getType", "paramTypes": ["java.lang.String"], "doc": "\n @return Class 注册对象的类型\r\n"}, {"name": "getAliases", "paramTypes": ["java.lang.String"], "doc": "\n 如果给定的bean名字在bean定义中有别名，则返回这些别名\r\n"}, {"name": "getAopProxy", "paramTypes": ["java.lang.Object"], "doc": "\n 获取aop代理对象\r\n"}, {"name": "context", "paramTypes": [], "doc": "\n 获取spring上下文\r\n"}], "constructors": []}