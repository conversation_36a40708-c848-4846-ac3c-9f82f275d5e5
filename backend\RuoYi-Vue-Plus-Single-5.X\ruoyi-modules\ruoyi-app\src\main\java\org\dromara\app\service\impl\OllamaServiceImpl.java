package org.dromara.app.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import dev.langchain4j.model.ollama.OllamaChatModel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.config.OllamaConfig;
import org.dromara.app.service.IOllamaService;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * Ollama服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OllamaServiceImpl implements IOllamaService {

    @Qualifier("ollamaRestTemplate")
    private final RestTemplate restTemplate;

    private final OllamaConfig.OllamaProperties ollamaProperties;


    @Override
    public OllamaResponse chat(String model, List<ChatMessage> messages, Double temperature, Integer maxTokens) {
        try {
            OllamaChatModel model1 = OllamaChatModel.builder()
                .baseUrl(ollamaProperties.getBaseUrl())
                .modelName(StrUtil.isBlank(model) ? ollamaProperties.getDefaultModel() : model)
                .temperature(temperature != null ? temperature : ollamaProperties.getDefaultTemperature())
                .build();
            log.info("发送Ollama聊天请求: {}", JSONUtil.toJsonStr(messages));
            // 发送聊天请求

            return null;
        } catch (Exception e) {
            log.error("Ollama聊天请求失败", e);
            return new OllamaResponse(false, null, "请求异常: " + e.getMessage());
        }
    }

    @Override
    public void chatStream(String model, List<ChatMessage> messages, Double temperature, Integer maxTokens, SseEmitter sseEmitter) {
        CompletableFuture.runAsync(() -> {
            BufferedReader reader = null;
            HttpURLConnection connection = null;

            try {
                String url = ollamaProperties.getBaseUrl() + "/api/chat";

                // 构建请求体
                Map<String, Object> requestBody = new HashMap<>();
                requestBody.put("model", StrUtil.isBlank(model) ? ollamaProperties.getDefaultModel() : model);
                requestBody.put("messages", convertMessages(messages));
                requestBody.put("stream", true);

                // 添加参数配置
                Map<String, Object> options = new HashMap<>();
                options.put("temperature", temperature != null ? temperature : ollamaProperties.getDefaultTemperature());
                if (maxTokens != null) {
                    options.put("num_predict", maxTokens);
                }
                requestBody.put("options", options);

                log.info("发送Ollama流式请求: {}", JSONUtil.toJsonStr(requestBody));

                // 创建连接并发送流式请求
                connection = (HttpURLConnection) new URL(url).openConnection();
                connection.setRequestMethod("POST");
                connection.setRequestProperty("Content-Type", "application/json");
                connection.setRequestProperty("Accept", "application/x-ndjson");
                connection.setConnectTimeout(ollamaProperties.getConnectTimeout() * 1000);
                connection.setReadTimeout(ollamaProperties.getReadTimeout() * 1000);
                connection.setDoOutput(true);

                // 发送请求体
                String jsonBody = JSONUtil.toJsonStr(requestBody);
                connection.getOutputStream().write(jsonBody.getBytes("UTF-8"));
                connection.getOutputStream().flush();

                // 检查响应状态
                int responseCode = connection.getResponseCode();
                if (responseCode != HttpURLConnection.HTTP_OK) {
                    String errorMsg = "Ollama请求失败，状态码: " + responseCode;
                    log.error(errorMsg);
                    sseEmitter.send(SseEmitter.event().name("error").data(errorMsg));
                    sseEmitter.complete();
                    return;
                }

                // 读取流式响应
                reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), "UTF-8"));
                String line;
                StringBuilder fullMessage = new StringBuilder();

                while ((line = reader.readLine()) != null) {
                    if (StrUtil.isNotBlank(line)) {
                        try {
                            JSONObject responseJson = JSONUtil.parseObj(line);

                            if (responseJson.containsKey("message")) {
                                JSONObject messageObj = responseJson.getJSONObject("message");
                                String content = messageObj.getStr("content");

                                if (StrUtil.isNotBlank(content)) {
                                    fullMessage.append(content);
                                    // 判断sseEmitter是否已经断开

                                    // 发送增量内容
                                    sseEmitter.send(SseEmitter.event()
                                        .name("message")
                                        .data(content));
                                }
                            }

                            // 检查是否完成
                            if (responseJson.getBool("done", false)) {
                                // 发送完成事件
                                Map<String, Object> doneData = new HashMap<>();
                                doneData.put("message", fullMessage.toString());
                                doneData.put("done", true);
                                if (responseJson.containsKey("eval_count")) {
                                    doneData.put("eval_count", responseJson.getInt("eval_count"));
                                }
                                if (responseJson.containsKey("eval_duration")) {
                                    doneData.put("eval_duration", responseJson.getLong("eval_duration"));
                                }

                                sseEmitter.send(SseEmitter.event()
                                    .name("done")
                                    .data(JSONUtil.toJsonStr(doneData)));
                                break;
                            }

                        } catch (Exception e) {
                            log.warn("解析流式响应失败: {}", line, e);
                        }
                    }
                }

                sseEmitter.complete();

            } catch (Exception e) {
                log.error("Ollama流式聊天失败", e);
                try {
                    sseEmitter.send(SseEmitter.event()
                        .name("error")
                        .data("流式请求失败: " + e.getMessage()));
                } catch (IOException ioException) {
                    log.error("发送错误事件失败", ioException);
                }
                sseEmitter.completeWithError(e);
            } finally {
                // 确保资源被正确关闭
                if (reader != null) {
                    try {
                        reader.close();
                    } catch (IOException e) {
                        log.warn("关闭Reader失败", e);
                    }
                }
                if (connection != null) {
                    connection.disconnect();
                }
            }
        });
    }

    @Override
    public List<String> getAvailableModels() {
        try {
            String url = ollamaProperties.getBaseUrl() + "/api/tags";

            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);

            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                JSONObject responseJson = JSONUtil.parseObj(response.getBody());
                List<Object> models = responseJson.getJSONArray("models").toList(Object.class);

                List<String> modelNames = new ArrayList<>();
                for (Object model : models) {
                    if (model instanceof Map) {
                        Map<?, ?> modelMap = (Map<?, ?>) model;
                        String name = (String) modelMap.get("name");
                        if (StrUtil.isNotBlank(name)) {
                            modelNames.add(name);
                        }
                    }
                }

                return modelNames;
            }

        } catch (Exception e) {
            log.error("获取Ollama模型列表失败", e);
        }

        return Collections.emptyList();
    }

    @Override
    public boolean isModelAvailable(String modelName) {
        List<String> availableModels = getAvailableModels();
        return availableModels.contains(modelName);
    }

    @Override
    public ModelInfo getModelInfo(String modelName) {
        try {
            String url = ollamaProperties.getBaseUrl() + "/api/show";

            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("name", modelName);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);

            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                JSONObject responseJson = JSONUtil.parseObj(response.getBody());

                ModelInfo modelInfo = new ModelInfo();
                modelInfo.setName(modelName);
                modelInfo.setSize(responseJson.getLong("size"));

                if (responseJson.containsKey("details")) {
                    JSONObject details = responseJson.getJSONObject("details");
                    modelInfo.setDescription(details.getStr("family"));

                    Map<String, Object> parameters = new HashMap<>();
                    parameters.put("parameter_size", details.getStr("parameter_size"));
                    parameters.put("quantization_level", details.getStr("quantization_level"));
                    modelInfo.setParameters(parameters);
                }

                return modelInfo;
            }

        } catch (Exception e) {
            log.error("获取模型信息失败", e);
        }

        return null;
    }

    @Override
    public ServiceStatus checkServiceStatus() {
        ServiceStatus status = new ServiceStatus();

        try {
            String url = ollamaProperties.getBaseUrl() + "/api/tags";

            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                status.setAvailable(true);

                // 获取模型列表
                if (response.getBody() != null) {
                    JSONObject responseJson = JSONUtil.parseObj(response.getBody());
                    List<Object> models = responseJson.getJSONArray("models").toList(Object.class);

                    List<String> modelNames = new ArrayList<>();
                    for (Object model : models) {
                        if (model instanceof Map) {
                            Map<?, ?> modelMap = (Map<?, ?>) model;
                            String name = (String) modelMap.get("name");
                            if (StrUtil.isNotBlank(name)) {
                                modelNames.add(name);
                            }
                        }
                    }
                    status.setModels(modelNames);
                }

                status.setBaseUrl(ollamaProperties.getBaseUrl());

                // 尝试获取版本信息（如果API支持）
                try {
                    String versionUrl = ollamaProperties.getBaseUrl() + "/api/version";
                    ResponseEntity<String> versionResponse = restTemplate.getForEntity(versionUrl, String.class);
                    if (versionResponse.getStatusCode() == HttpStatus.OK && versionResponse.getBody() != null) {
                        JSONObject versionJson = JSONUtil.parseObj(versionResponse.getBody());
                        status.setVersion(versionJson.getStr("version"));
                    }
                } catch (Exception e) {
                    log.debug("获取Ollama版本信息失败，可能不支持该API", e);
                }

            } else {
                status.setAvailable(false);
                status.setErrorMessage("服务不可用，状态码: " + response.getStatusCode());
            }

        } catch (Exception e) {
            status.setAvailable(false);
            status.setErrorMessage("连接失败: " + e.getMessage());
            log.error("检查Ollama服务状态失败", e);
        }

        return status;
    }

    /**
     * 转换消息格式
     */
    private List<Map<String, String>> convertMessages(List<ChatMessage> messages) {
        List<Map<String, String>> ollamaMessages = new ArrayList<>();

        for (ChatMessage message : messages) {
            Map<String, String> ollamaMessage = new HashMap<>();
            ollamaMessage.put("role", message.getRole());
            ollamaMessage.put("content", message.getContent());
            ollamaMessages.add(ollamaMessage);
        }

        return ollamaMessages;
    }
}
