{"doc": "\n ES客户端\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "createIndex", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 创建索引\r\n\r\n @param indexName 索引名称\r\n @param mapping   映射配置\r\n @return 是否成功\r\n"}, {"name": "deleteIndex", "paramTypes": ["java.lang.String"], "doc": "\n 删除索引\r\n\r\n @param indexName 索引名称\r\n @return 是否成功\r\n"}, {"name": "existsIndex", "paramTypes": ["java.lang.String"], "doc": "\n 判断索引是否存在\r\n\r\n @param indexName 索引名称\r\n @return 是否存在\r\n"}, {"name": "addDocument", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Object"], "doc": "\n 添加文档\r\n\r\n @param indexName 索引名称\r\n @param id        文档ID\r\n @param document  文档内容\r\n @return 是否成功\r\n"}, {"name": "batchAddDocuments", "paramTypes": ["java.lang.String", "java.util.List"], "doc": "\n 批量添加文档\r\n\r\n @param indexName 索引名称\r\n @param documents 文档列表\r\n @return 是否成功\r\n"}, {"name": "updateDocument", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Object"], "doc": "\n 更新文档\r\n\r\n @param indexName 索引名称\r\n @param id        文档ID\r\n @param document  更新内容\r\n @return 是否成功\r\n"}, {"name": "deleteDocument", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 删除文档\r\n\r\n @param indexName 索引名称\r\n @param id        文档ID\r\n @return 是否成功\r\n"}, {"name": "getDocument", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 根据ID获取文档\r\n\r\n @param indexName 索引名称\r\n @param id        文档ID\r\n @return 文档内容\r\n"}, {"name": "search", "paramTypes": ["org.dromara.common.es.entity.SearchRequest"], "doc": "\n 搜索文档\r\n\r\n @param searchRequest 搜索请求\r\n @return 搜索结果\r\n"}, {"name": "buildQuery", "paramTypes": ["org.dromara.common.es.entity.SearchRequest"], "doc": "\n 构建查询\r\n"}, {"name": "buildAggregation", "paramTypes": ["org.dromara.common.es.entity.SearchRequest.AggregationConfig"], "doc": "\n 构建聚合\r\n"}, {"name": "buildSearchResult", "paramTypes": ["co.elastic.clients.elasticsearch.core.SearchResponse", "java.lang.Integer", "java.lang.Integer"], "doc": "\n 构建搜索结果\r\n"}, {"name": "parseAggregationResult", "paramTypes": ["co.elastic.clients.elasticsearch._types.aggregations.Aggregate"], "doc": "\n 解析聚合结果\r\n"}], "constructors": []}