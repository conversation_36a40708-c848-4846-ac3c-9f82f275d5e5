package org.dromara.app.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 岗位信息视图对象
 *
 * <AUTHOR> Assistant
 * @date 2025-07-19
 */
@Data
public class JobVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 岗位ID
     */
    private Long id;

    /**
     * 分类ID
     */
    private Long categoryId;

    /**
     * 分类名称
     */
    private String categoryName;

    /**
     * 分类图标
     */
    private String categoryIcon;

    /**
     * 分类颜色
     */
    private String categoryColor;

    /**
     * 岗位名称
     */
    private String name;

    /**
     * 公司名称
     */
    private String company;

    /**
     * 公司Logo
     */
    private String logo;

    /**
     * 难度等级（1-5）
     */
    private Integer difficulty;

    /**
     * 面试时长（分钟）
     */
    private Integer duration;

    /**
     * 题目数量
     */
    private Integer questionCount;

    /**
     * 标签（JSON数组）
     */
    private List<String> tags;

    /**
     * 岗位描述
     */
    private String description;

    /**
     * 面试人数
     */
    private Integer interviewers;

    /**
     * 通过率（百分比）
     */
    private BigDecimal passRate;

    /**
     * 浏览次数
     */
    private Integer viewCount;

    /**
     * 收藏次数
     */
    private Integer favoriteCount;

    /**
     * 排序号
     */
    private Integer sortOrder;

    /**
     * 状态（0正常 1停用）
     */
    private String status;

    /**
     * 技术领域分类
     */
    private String technicalDomain;

    /**
     * 岗位级别（初级/中级/高级/专家）
     */
    private String level;

    /**
     * 工作经验要求（年）
     */
    private Integer experienceYears;

    /**
     * 学历要求
     */
    private String educationRequirement;

    /**
     * 薪资范围
     */
    private String salaryRange;

    /**
     * 工作地点
     */
    private String location;

    /**
     * 核心技能要求（JSON数组）
     */
    private List<String> coreSkills;

    /**
     * 岗位职责
     */
    private String responsibilities;

    /**
     * 任职要求
     */
    private String requirements;

    /**
     * 是否已收藏
     */
    private Boolean isFavorited;

    /**
     * 匹配度分数（推荐时使用）
     */
    private Integer matchScore;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

}