package org.dromara.app.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;
import java.io.Serializable;

/**
 * 题目实体
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_question")
public class Question extends BaseEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 题目ID
     */
    @TableId(value = "question_id")
    private Long questionId;

    /**
     * 题库ID
     */
    @TableField("bank_id")
    private Long bankId;

    /**
     * 题目编码
     */
    @TableField("question_code")
    private String questionCode;

    /**
     * 题目标题
     */
    @TableField("title")
    private String title;

    /**
     * 题目描述
     */
    @TableField("description")
    private String description;

    /**
     * 题目内容
     */
    @TableField("content")
    private String content;

    /**
     * 参考答案
     */
    @TableField("answer")
    private String answer;

    /**
     * 答案解析
     */
    @TableField("analysis")
    private String analysis;

    /**
     * 难度（1-简单 2-中等 3-困难）
     */
    @TableField("difficulty")
    private Integer difficulty;

    /**
     * 分类
     */
    @TableField("category")
    private String category;

    /**
     * 题目类型（1-单选题 2-多选题 3-判断题 4-简答题 5-编程题）
     */
    @TableField("type")
    private Integer type;

    /**
     * 练习次数
     */
    @TableField("practice_count")
    private Integer practiceCount;

    /**
     * 正确率
     */
    @TableField("correct_rate")
    private Integer correctRate;

    /**
     * 通过率（百分比）
     */
    @TableField("acceptance_rate")
    private Double acceptanceRate;

    /**
     * 评论数
     */
    @TableField("comment_count")
    private Integer commentCount;

    /**
     * 标签（JSON格式）
     */
    @TableField("tags")
    private String tags;

    /**
     * 排序
     */
    @TableField("sort")
    private Integer sort;

    /**
     * 状态（0正常 1停用）
     */
    @TableField("status")
    private String status;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;
}
