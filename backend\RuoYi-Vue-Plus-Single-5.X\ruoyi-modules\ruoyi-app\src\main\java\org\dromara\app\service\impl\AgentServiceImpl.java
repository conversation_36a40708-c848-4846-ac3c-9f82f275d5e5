package org.dromara.app.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.config.OllamaConfig;
import org.dromara.app.domain.Agent;
import org.dromara.app.domain.KnowledgeBase;
import org.dromara.app.domain.VectorEmbedding;
import org.dromara.app.mapper.AgentMapper;
import org.dromara.app.service.AdvancedRagService;
import org.dromara.app.service.IAgentService;
import org.dromara.app.service.IRagService;
import org.dromara.common.caffeine.annotation.CaffeineCache;
import org.dromara.common.caffeine.annotation.TimeUnit;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * AI代理服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AgentServiceImpl extends ServiceImpl<AgentMapper, Agent> implements IAgentService {

    private final OllamaConfig.OllamaProperties ollamaProperties;
    private final AgentMapper agentMapper;
    private final IRagService ragService;
    private final AdvancedRagService advancedRagService;

    // 临时存储，实际应该使用数据库
    private final Map<String, Agent> agentStorage = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        // 初始化默认代理
        initDefaultAgents();
    }

    @Override
    @CaffeineCache(
        key = "'enabled-agents'",
        expire = "10",
        timeUnit = TimeUnit.MINUTES
    )
    public List<Agent> getEnabledAgents() {
        try {
            // 从数据库查询
            List<Agent> agents = agentMapper.selectEnabledAgentsOrdered();

            // 处理JSON字段
            agents.forEach(this::processAgentData);

            return agents;
        } catch (Exception e) {
            log.error("查询启用的代理列表失败", e);
            // 降级为临时存储
            List<Agent> agents = new ArrayList<>(agentStorage.values());
            agents.removeIf(agent -> !agent.getEnabled());
            agents.sort(Comparator.comparing(Agent::getSortOrder));
            agents.forEach(this::processAgentData);
            return agents;
        }
    }

    @Override
    @CaffeineCache(
        key = "#agentType",
        expire = "30",
        timeUnit = TimeUnit.MINUTES
    )
    public Agent getAgentByType(String agentTypeId) {
        if (StrUtil.isBlank(agentTypeId)) {
            agentTypeId = "general";
        }

        try {
            // 从数据库查询
            Agent agent = agentMapper.selectEnabledById(agentTypeId);

            if (agent != null) {
                processAgentData(agent);
            }

            return agent;
        } catch (Exception e) {
            log.error("查询代理失败: {}", agentTypeId, e);
            // 降级为临时存储
            Agent agent = agentStorage.get(agentTypeId);
            if (agent != null) {
                processAgentData(agent);
            }
            return agent;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean incrementUsageCount(String agentType) {
        try {
            // TODO: 更新数据库
            // LambdaUpdateWrapper<Agent> wrapper = new LambdaUpdateWrapper<>();
            // wrapper.eq(Agent::getAgentType, agentType)
            //        .setSql("usage_count = usage_count + 1");
            // return update(wrapper);

            // 临时实现
            Agent agent = agentStorage.get(agentType);
            if (agent != null) {
                agent.setUsageCount(agent.getUsageCount() + 1);
                agent.setUpdateTime(DateTime.now());
                return true;
            }

            return false;
        } catch (Exception e) {
            log.error("增加代理使用次数失败: agentType={}", agentType, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateRating(String agentType, Double rating) {
        try {
            // TODO: 实现评分更新逻辑
            // 需要考虑平均评分的计算

            Agent agent = agentStorage.get(agentType);
            if (agent != null) {
                // 简单的评分更新，实际应该计算平均值
                agent.setAverageRating(rating);
                agent.setUpdateTime(DateTime.now());
                return true;
            }

            return false;
        } catch (Exception e) {
            log.error("更新代理评分失败: agentType={}, rating={}", agentType, rating, e);
            return false;
        }
    }


    @Override
    @CaffeineCache(
        key = "#agentType + '-quick-actions'",
        expire = "30",
        timeUnit = TimeUnit.MINUTES
    )
    public List<Agent.QuickAction> getQuickActions(String agentType) {
        Agent agent = getAgentByType(agentType);
        if (agent != null && agent.getQuickActionList() != null) {
            return agent.getQuickActionList();
        }

        return Collections.emptyList();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void initDefaultAgents() {

        try {
            // 检查数据库中是否已有数据
            List<Agent> existingAgents = agentMapper.selectEnabledAgentsOrdered();
            if (!existingAgents.isEmpty()) {
                log.info("数据库中已存在 {} 个代理，跳过初始化", existingAgents.size());
                // 同步到临时存储
                for (Agent agent : existingAgents) {
                    agentStorage.put(agent.getAgentType(), agent);
                }
                return;
            }
        } catch (Exception e) {
            log.warn("检查现有代理失败，继续初始化流程", e);
        }

        // 检查临时存储
        if (!agentStorage.isEmpty()) {
            log.info("临时存储中已存在代理，跳过初始化");
            return;
        }

        List<Agent> defaultAgents = createDefaultAgents();

        try {
            // 批量插入到数据库
            agentMapper.batchInsertDefaultAgents(defaultAgents);
            log.info("批量保存代理到数据库成功");
        } catch (Exception e) {
            log.error("批量保存代理到数据库失败，使用临时存储", e);
        }

        // 保存到临时存储（作为降级方案）
        for (Agent agent : defaultAgents) {
            agentStorage.put(agent.getAgentType(), agent);
            log.info("初始化代理: {} - {}", agent.getAgentType(), agent.getName());
        }

        log.info("默认AI代理初始化完成，共 {} 个", defaultAgents.size());
    }

    // =================  私有方法  =================

    /**
     * 处理Agent数据，解析JSON字段
     */
    private void processAgentData(Agent agent) {
        try {
            // 解析能力列表
            if (StrUtil.isNotBlank(agent.getCapabilities())) {
                List<String> capabilities = JSONUtil.toList(agent.getCapabilities(), String.class);
                agent.setCapabilityList(capabilities);
            }

//            // 解析快速操作
//            if (StrUtil.isNotBlank(agent.getQuickActions())) {
//                List<Agent.QuickAction> quickActions = JSONUtil.toList(agent.getQuickActions(), Agent.QuickAction.class);
//                agent.setQuickActionList(quickActions);
//            }

            // 解析模型配置
            if (StrUtil.isNotBlank(agent.getModelConfig())) {
                Agent.ModelConfig modelConfig = JSONUtil.toBean(agent.getModelConfig(), Agent.ModelConfig.class);
                agent.setModelConfigObject(modelConfig);
            } else {
                // 设置默认模型配置
                Agent.ModelConfig defaultConfig = createDefaultModelConfig();
                agent.setModelConfigObject(defaultConfig);
            }

        } catch (Exception e) {
            log.error("处理Agent数据失败: {}", agent.getAgentType(), e);
        }
    }

    /**
     * 创建默认代理列表
     */
    private List<Agent> createDefaultAgents() {
        List<Agent> agents = new ArrayList<>();

        // 1. 通用助手
        agents.add(createAgent(
            "general",
            "通用助手",
            "专业的rjb-sias，可以回答各种问题并提供有用的建议",
            "i-carbon-bot",
            "#3B82F6",
            ollamaProperties.getSystemPrompts().getGeneral(),
            Arrays.asList("问答咨询", "学习辅导", "信息查询", "创意思考"),
            createGeneralQuickActions(),
            0
        ));

        // 2. 面试官
        agents.add(createAgent(
            "interviewer",
            "面试官",
            "专业的技术面试官，提供真实的面试体验和专业评价",
            "i-carbon-user-speaker",
            "#EF4444",
            ollamaProperties.getSystemPrompts().getInterviewer(),
            Arrays.asList("技术面试", "行为面试", "面试指导", "评价反馈"),
            createInterviewerQuickActions(),
            1
        ));

        // 3. 简历分析师
        agents.add(createAgent(
            "resume_analyzer",
            "简历分析师",
            "专业的简历分析师，提供详细的简历优化建议",
            "i-carbon-document-view",
            "#10B981",
            ollamaProperties.getSystemPrompts().getResumeAnalyzer(),
            Arrays.asList("简历分析", "优化建议", "格式规范", "内容指导"),
            createResumeAnalyzerQuickActions(),
            2
        ));

        // 4. 技能评估师
        agents.add(createAgent(
            "skill_assessor",
            "技能评估师",
            "评估技能水平并提供个性化的学习建议",
            "i-carbon-skill-level-advanced",
            "#F59E0B",
            ollamaProperties.getSystemPrompts().getSkillAssessor(),
            Arrays.asList("技能评估", "能力测试", "学习路径", "提升建议"),
            createSkillAssessorQuickActions(),
            3
        ));

        // 5. 职业顾问
        agents.add(createAgent(
            "career_advisor",
            "职业顾问",
            "提供职业规划和发展建议的专业顾问",
            "i-carbon-partnership",
            "#8B5CF6",
            ollamaProperties.getSystemPrompts().getCareerAdvisor(),
            Arrays.asList("职业规划", "发展建议", "行业分析", "转岗指导"),
            createCareerAdvisorQuickActions(),
            4
        ));

        // 6. 模拟面试官
        agents.add(createAgent(
            "mock_interviewer",
            "模拟面试官",
            "进行逼真的模拟面试，提供详细的表现评估",
            "i-carbon-video-chat",
            "#EC4899",
            ollamaProperties.getSystemPrompts().getMockInterviewer(),
            Arrays.asList("模拟面试", "实时评估", "表现分析", "改进建议"),
            createMockInterviewerQuickActions(),
            5
        ));

        // 7. 学习指导师
        agents.add(createAgent(
            "learning_guide",
            "学习指导师",
            "制定个性化学习计划并提供学习指导",
            "i-carbon-education",
            "#06B6D4",
            ollamaProperties.getSystemPrompts().getLearningGuide(),
            Arrays.asList("学习计划", "知识梳理", "进度跟踪", "方法指导"),
            createLearningGuideQuickActions(),
            6
        ));

        return agents;
    }

    /**
     * 创建Agent对象
     */
    private Agent createAgent(String type, String name, String description, String icon, String color,
                              String systemPrompt, List<String> capabilities, List<Agent.QuickAction> quickActions,
                              Integer sortOrder) {
        Agent agent = new Agent();
        agent.setId(type);
        agent.setName(name);
        agent.setDescription(description);
        agent.setIcon(icon);
        agent.setColor(color);
        agent.setAgentType(type);
        agent.setSystemPrompt(systemPrompt);
        agent.setCapabilities(JSONUtil.toJsonStr(capabilities));
        agent.setQuickActions(JSONUtil.toJsonStr(quickActions));
        agent.setEnabled(true);
        agent.setSortOrder(sortOrder);
        agent.setUsageCount(0L);
        agent.setAverageRating(0.0);
        agent.setModelConfig(JSONUtil.toJsonStr(createDefaultModelConfig()));
        agent.setExtendConfig("{}");
        agent.setCreateBy(1L);
        agent.setUpdateBy(1L);
        agent.setCreateTime(DateTime.now());
        agent.setUpdateTime(DateTime.now());

        return agent;
    }

    /**
     * 创建默认模型配置
     */
    private Agent.ModelConfig createDefaultModelConfig() {
        Agent.ModelConfig config = new Agent.ModelConfig();
        config.setPrimaryModel(ollamaProperties.getDefaultModel());
        config.setFallbackModel("deepseek-r1:1.5b");
        config.setTemperature(ollamaProperties.getDefaultTemperature());
        config.setMaxTokens(ollamaProperties.getDefaultMaxTokens());
        config.setTopP(0.9);
        config.setProvider("ollama");
        config.setEnableStreaming(ollamaProperties.getEnableStreaming());
        config.setTimeoutSeconds(ollamaProperties.getReadTimeout());
        return config;
    }

    // =================  快速操作创建方法  =================

    private List<Agent.QuickAction> createGeneralQuickActions() {
        List<Agent.QuickAction> actions = new ArrayList<>();

        actions.add(createQuickAction(
            "qa_general", "通用问答", "回答任何问题，提供准确和有用的信息",
            "i-carbon-help", "你好，我有一个问题想要咨询", "问答"
        ));

        actions.add(createQuickAction(
            "writing_assist", "写作辅助", "帮助进行各种写作任务，包括文章、邮件、报告等",
            "i-carbon-edit", "请帮我写一篇关于...的文章", "写作"
        ));

        actions.add(createQuickAction(
            "translation", "翻译服务", "提供多语言翻译服务",
            "i-carbon-translate", "请帮我翻译以下内容：", "翻译"
        ));

        actions.add(createQuickAction(
            "code_review", "代码分析", "分析和解释代码，提供优化建议",
            "i-carbon-code", "请分析以下代码：", "编程"
        ));

        return actions;
    }

    private List<Agent.QuickAction> createInterviewerQuickActions() {
        List<Agent.QuickAction> actions = new ArrayList<>();

        actions.add(createQuickAction(
            "tech_interview", "技术面试", "进行技术岗位面试，评估候选人的技术能力",
            "i-carbon-laptop", "我想进行一场Java开发工程师的面试", "技术面试"
        ));

        actions.add(createQuickAction(
            "behavior_interview", "行为面试", "进行行为面试，评估候选人的软技能",
            "i-carbon-user-speaker", "请进行一场关于团队合作的行为面试", "行为面试"
        ));

        actions.add(createQuickAction(
            "interview_feedback", "面试反馈", "对面试表现进行详细分析和反馈",
            "i-carbon-report", "请对我刚才的面试表现给出反馈", "面试评估"
        ));

        actions.add(createQuickAction(
            "interview_prep", "面试准备", "帮助准备常见面试问题和答题技巧",
            "i-carbon-book", "我需要准备前端开发的面试", "面试准备"
        ));

        return actions;
    }

    private List<Agent.QuickAction> createResumeAnalyzerQuickActions() {
        List<Agent.QuickAction> actions = new ArrayList<>();

        actions.add(createQuickAction(
            "resume_analysis", "简历分析", "全面分析简历内容，指出优缺点",
            "i-carbon-document-view", "请分析我的简历", "简历分析"
        ));

        actions.add(createQuickAction(
            "resume_optimization", "简历优化", "提供具体的简历优化建议",
            "i-carbon-edit", "请帮我优化简历", "简历优化"
        ));

        actions.add(createQuickAction(
            "keyword_enhancement", "关键词优化", "优化简历关键词，提升ATS通过率",
            "i-carbon-tag", "请帮我优化简历关键词", "关键词优化"
        ));

        actions.add(createQuickAction(
            "format_check", "格式检查", "检查简历格式和排版，提供改进建议",
            "i-carbon-text-align-left", "请检查我的简历格式", "格式检查"
        ));

        return actions;
    }

    private List<Agent.QuickAction> createSkillAssessorQuickActions() {
        List<Agent.QuickAction> actions = new ArrayList<>();

        actions.add(createQuickAction(
            "skill_test", "技能测试", "进行专业技能测试和评估",
            "i-carbon-skill-level-advanced", "我想测试我的Python技能水平", "技能测试"
        ));

        actions.add(createQuickAction(
            "learning_path", "学习路径", "制定个性化的技能学习计划",
            "i-carbon-education", "请为我制定Java学习路径", "学习规划"
        ));

        actions.add(createQuickAction(
            "skill_gap", "技能差距", "分析技能差距，提供提升建议",
            "i-carbon-report", "分析我与高级工程师的技能差距", "技能分析"
        ));

        actions.add(createQuickAction(
            "certification_guide", "认证指导", "提供技术认证考试指导",
            "i-carbon-certificate", "我想考取AWS认证", "认证指导"
        ));

        return actions;
    }

    private List<Agent.QuickAction> createCareerAdvisorQuickActions() {
        List<Agent.QuickAction> actions = new ArrayList<>();

        actions.add(createQuickAction(
            "career_planning", "职业规划", "制定长期职业发展规划",
            "i-carbon-partnership", "帮我制定5年职业规划", "职业规划"
        ));

        actions.add(createQuickAction(
            "industry_analysis", "行业分析", "分析行业趋势和发展前景",
            "i-carbon-analytics", "分析AI行业的发展前景", "行业分析"
        ));

        actions.add(createQuickAction(
            "career_transition", "转岗指导", "提供转行转岗的专业建议",
            "i-carbon-arrows", "我想从后端转向算法工程师", "转岗指导"
        ));

        actions.add(createQuickAction(
            "salary_negotiation", "薪资谈判", "提供薪资谈判策略和技巧",
            "i-carbon-money", "如何进行薪资谈判？", "薪资谈判"
        ));

        return actions;
    }

    private List<Agent.QuickAction> createMockInterviewerQuickActions() {
        List<Agent.QuickAction> actions = new ArrayList<>();

        actions.add(createQuickAction(
            "coding_interview", "编程面试", "进行真实的编程面试模拟",
            "i-carbon-code", "开始一场算法编程面试", "编程面试"
        ));

        actions.add(createQuickAction(
            "system_design", "系统设计", "进行系统设计面试模拟",
            "i-carbon-network-3", "请出一道系统设计题", "系统设计"
        ));

        actions.add(createQuickAction(
            "mock_scenario", "场景模拟", "模拟特定公司的面试场景",
            "i-carbon-video-chat", "模拟Google的面试", "场景模拟"
        ));

        actions.add(createQuickAction(
            "performance_analysis", "表现分析", "详细分析面试表现，给出评分",
            "i-carbon-report", "分析我在面试中的表现", "表现分析"
        ));

        return actions;
    }

    private List<Agent.QuickAction> createLearningGuideQuickActions() {
        List<Agent.QuickAction> actions = new ArrayList<>();

        actions.add(createQuickAction(
            "study_plan", "学习计划", "制定详细的学习计划",
            "i-carbon-education", "帮我制定React学习计划", "学习计划"
        ));

        actions.add(createQuickAction(
            "knowledge_review", "知识梳理", "梳理和总结知识点",
            "i-carbon-book", "帮我梳理数据结构知识点", "知识梳理"
        ));

        actions.add(createQuickAction(
            "practice_guidance", "练习指导", "提供练习题目和解题指导",
            "i-carbon-task", "给我一些算法练习题", "练习指导"
        ));

        actions.add(createQuickAction(
            "progress_tracking", "进度跟踪", "跟踪学习进度，调整计划",
            "i-carbon-progress-bar", "检查我的学习进度", "进度跟踪"
        ));

        return actions;
    }

    /**
     * 创建通用的快速操作
     */
    private Agent.QuickAction createQuickAction(String id, String title, String description,
                                                String icon, String prompt, String category) {
        Agent.QuickAction action = new Agent.QuickAction();
        action.setId(id);
        action.setTitle(title);
        action.setDescription(description);
        action.setIcon(icon);
        action.setPrompt(prompt);
        action.setCategory(category);
        return action;
    }

    @Override
    public String enhanceQueryWithRAG(String agentType, String userQuery) {
        try {
            if (StrUtil.isBlank(userQuery)) {
                return userQuery;
            }

            // 获取代理相关的知识库ID列表
            List<Long> knowledgeBaseIds = getAgentKnowledgeBaseIds(agentType);
            if (knowledgeBaseIds.isEmpty()) {
                log.debug("代理 {} 没有关联的知识库，跳过RAG增强", agentType);
                return userQuery;
            }

            // 使用高级RAG进行智能检索
            AdvancedRagService.RetrievalOptions options = new AdvancedRagService.RetrievalOptions();
            options.setTopK(5);
            options.setEnableRerank(true);
            options.setEnableQueryExpansion(true);
            options.setMaxContextLength(4000);

            AdvancedRagService.SmartRetrievalResult result = advancedRagService.smartRetrieval(
                userQuery, knowledgeBaseIds, options
            );

            // 获取最相关的知识
            List<VectorEmbedding> relevantKnowledge = result.getResults();
            if (relevantKnowledge.isEmpty()) {
                log.debug("未找到相关知识，返回原始查询");
                return userQuery;
            }

            // 使用高级RAG生成增强提示词
            String promptTemplate = """
                基于以下相关知识回答用户问题：

                {context}

                【用户问题】
                {query}

                请基于上述相关知识回答用户问题。如果知识库中没有相关信息，请明确说明，并基于你的通用知识给出回答。
                """;

            return advancedRagService.generateEnhancedPrompt(userQuery, relevantKnowledge, promptTemplate);

        } catch (Exception e) {
            log.error("RAG增强查询失败: agentType={}, userQuery={}", agentType, userQuery, e);
            return userQuery;
        }
    }

    @Override
    public List<VectorEmbedding> retrieveRelevantDocuments(String agentType, String query) {
        try {
            if (StrUtil.isBlank(query)) {
                log.debug("查询内容为空，返回空结果");
                return new ArrayList<>();
            }

            // 获取代理相关的知识库ID列表
            List<Long> knowledgeBaseIds = getAgentKnowledgeBaseIds(agentType);
            if (knowledgeBaseIds.isEmpty()) {
                log.debug("代理 {} 没有关联的知识库，返回空结果", agentType);
                return new ArrayList<>();
            }

            // 使用高级RAG进行智能检索
            AdvancedRagService.RetrievalOptions options = new AdvancedRagService.RetrievalOptions();
            options.setTopK(10); // 返回前10个最相关的文档
            options.setEnableRerank(true); // 启用重排序
            options.setEnableQueryExpansion(true); // 启用查询扩展
            options.setMaxContextLength(2000); // 设置最大上下文长度
            options.setVectorWeight(0.7); // 向量检索权重
            options.setKeywordWeight(0.3); // 关键词检索权重

            AdvancedRagService.SmartRetrievalResult result = advancedRagService.smartRetrieval(
                query, knowledgeBaseIds, options
            );

            List<VectorEmbedding> relevantDocuments = result.getResults();

            log.debug("为代理 {} 检索到 {} 个相关文档", agentType, relevantDocuments.size());

            return relevantDocuments;

        } catch (Exception e) {
            log.error("检索相关文档失败: agentType={}, query={}", agentType, query, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<Long> getAgentKnowledgeBaseIds(String agentType) {
        try {
            // 根据代理类型获取相关知识库ID
            // 这里可以根据实际需求配置不同代理对应的知识库
            List<Long> knowledgeBaseIds = new ArrayList<>();

            switch (agentType) {
                case "technical":
                case "interviewer":
                case "skill_assessor":
                    // 技术相关代理使用技术知识库
                    knowledgeBaseIds.addAll(getKnowledgeBaseIdsByType("technical"));
                    break;
                case "resume_analyzer":
                case "career_advisor":
                    // 职业相关代理使用职业知识库
                    knowledgeBaseIds.addAll(getKnowledgeBaseIdsByType("career"));
                    break;
                case "general":
                default:
                    // 通用代理使用通用知识库
                    knowledgeBaseIds.addAll(getKnowledgeBaseIdsByType("general"));
                    break;
            }

            return knowledgeBaseIds;

        } catch (Exception e) {
            log.error("获取代理知识库ID失败: agentType={}", agentType, e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据类型获取知识库ID列表
     */
    private List<Long> getKnowledgeBaseIdsByType(String type) {
        try {
            return ragService.getKnowledgeBases().stream()
                .filter(kb -> type.equals(kb.getType()) && kb.getStatus() == 1)
                .map(KnowledgeBase::getId)
                .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("根据类型获取知识库ID失败: type={}", type, e);
            return new ArrayList<>();
        }
    }
}
