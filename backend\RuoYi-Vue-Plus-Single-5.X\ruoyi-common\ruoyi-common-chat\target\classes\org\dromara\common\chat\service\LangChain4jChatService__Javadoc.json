{"doc": "\n 基于LangChain4j的聊天服务\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "general<PERSON><PERSON>", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String"], "doc": "\n 通用聊天\r\n"}, {"name": "conductInterview", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String"], "doc": "\n 面试对话\r\n"}, {"name": "generateInterviewQuestions", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String", "int"], "doc": "\n 生成面试问题\r\n"}, {"name": "evaluateInterviewAnswer", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String", "java.lang.String"], "doc": "\n 评估面试回答\r\n"}, {"name": "analyzeResume", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String"], "doc": "\n 分析简历\r\n"}, {"name": "matchResumeWithJob", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String", "java.lang.String"], "doc": "\n 简历与职位匹配\r\n"}, {"name": "extractResumeKeyInfo", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String"], "doc": "\n 提取简历关键信息\r\n"}, {"name": "optimizeResumeForPosition", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String", "java.lang.String"], "doc": "\n 优化简历\r\n"}, {"name": "assessSkill", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String", "java.lang.String"], "doc": "\n 技能评估\r\n"}, {"name": "generateSkillTest", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String", "java.lang.String", "int"], "doc": "\n 生成技能测试\r\n"}, {"name": "evaluateSkillAnswer", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 评估技能答案\r\n"}, {"name": "generateSkillReport", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String"], "doc": "\n 生成技能报告\r\n"}, {"name": "createLearningPath", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 创建学习路径\r\n"}, {"name": "provideCareerAdvice", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String"], "doc": "\n 职业建议\r\n"}, {"name": "createCareerPlan", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 制定职业规划\r\n"}, {"name": "analyzeIndustryTrends", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String"], "doc": "\n 分析行业趋势\r\n"}, {"name": "guideCareerTransition", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 职业转换指导\r\n"}, {"name": "provideSalaryNegotiationAdvice", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 薪资谈判建议\r\n"}, {"name": "conductMockInterview", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String"], "doc": "\n 模拟面试\r\n"}, {"name": "designInterviewFlow", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 设计面试流程\r\n"}, {"name": "conductStressInterview", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String", "java.lang.String"], "doc": "\n 压力面试\r\n"}, {"name": "provideMockInterviewFeedback", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String"], "doc": "\n 面试反馈\r\n"}, {"name": "prepareQuestionType", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String", "java.lang.String"], "doc": "\n 问题类型准备\r\n"}, {"name": "provideLearningGuidance", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String"], "doc": "\n 学习指导\r\n"}, {"name": "createLearningPlan", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 制定学习计划\r\n"}, {"name": "recommendLearningResources", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 推荐学习资源\r\n"}, {"name": "analyzeLearningProgress", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String", "java.lang.String"], "doc": "\n 分析学习进度\r\n"}, {"name": "provideLearningMethods", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String", "java.lang.String", "java.lang.String"], "doc": "\n 提供学习方法\r\n"}, {"name": "buildKnowledgeMap", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String", "java.lang.String"], "doc": "\n 构建知识图谱\r\n"}, {"name": "clearSessionCache", "paramTypes": ["java.lang.String"], "doc": "\n 清理会话缓存\r\n"}], "constructors": []}