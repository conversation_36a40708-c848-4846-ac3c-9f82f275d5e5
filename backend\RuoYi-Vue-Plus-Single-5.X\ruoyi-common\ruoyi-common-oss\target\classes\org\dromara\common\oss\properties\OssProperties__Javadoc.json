{"doc": "\n OSS对象存储 配置属性\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "endpoint", "doc": "\n 访问站点\r\n"}, {"name": "domain", "doc": "\n 自定义域名\r\n"}, {"name": "prefix", "doc": "\n 前缀\r\n"}, {"name": "accessKey", "doc": "\n ACCESS_KEY\r\n"}, {"name": "secret<PERSON>ey", "doc": "\n SECRET_KEY\r\n"}, {"name": "bucketName", "doc": "\n 存储空间名\r\n"}, {"name": "region", "doc": "\n 存储区域\r\n"}, {"name": "isHttps", "doc": "\n 是否https（Y=是,N=否）\r\n"}, {"name": "accessPolicy", "doc": "\n 桶权限类型(0private 1public 2custom)\r\n"}], "enumConstants": [], "methods": [], "constructors": []}