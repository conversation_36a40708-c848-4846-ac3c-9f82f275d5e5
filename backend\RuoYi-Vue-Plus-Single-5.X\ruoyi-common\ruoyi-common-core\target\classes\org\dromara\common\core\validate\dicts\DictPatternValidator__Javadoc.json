{"doc": "\n 自定义字典值校验器\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "dictType", "doc": "\n 字典类型\r\n"}, {"name": "separator", "doc": "\n 分隔符\r\n"}], "enumConstants": [], "methods": [{"name": "initialize", "paramTypes": ["org.dromara.common.core.validate.dicts.DictPattern"], "doc": "\n 初始化校验器，提取注解上的字典类型\r\n\r\n @param annotation 注解实例\r\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["java.lang.String", "jakarta.validation.ConstraintValidatorContext"], "doc": "\n 校验字段值是否为指定字典类型中的合法值\r\n\r\n @param value   被校验的字段值\r\n @param context 校验上下文（可用于构建错误信息）\r\n @return true 表示校验通过（合法字典值），false 表示不通过\r\n"}], "constructors": []}