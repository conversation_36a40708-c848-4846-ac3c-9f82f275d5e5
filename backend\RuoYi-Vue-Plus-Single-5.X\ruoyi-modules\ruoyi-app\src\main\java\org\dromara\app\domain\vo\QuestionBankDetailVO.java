package org.dromara.app.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 题库详情VO
 *
 * <AUTHOR>
 */
@Data
public class QuestionBankDetailVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 题库ID
     */
    private Long id;

    /**
     * 题库编码
     */
    private String code;

    /**
     * 题库标题
     */
    private String title;

    /**
     * 题库描述
     */
    private String description;

    /**
     * 题库图标
     */
    private String icon;

    /**
     * 题库颜色
     */
    private String color;

    /**
     * 难度（简单/中等/困难）
     */
    private String difficulty;

    /**
     * 题目总数
     */
    private Integer totalQuestions;

    /**
     * 练习次数
     */
    private Integer practiceCount;

    /**
     * 学习进度（百分比）
     */
    private Integer progress;

    /**
     * 分类列表
     */
    private List<String> categories;

    /**
     * 是否已收藏
     */
    private Boolean isBookmarked;
}
