<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.app.mapper.ChatSessionMapper">

    <resultMap type="org.dromara.app.domain.ChatSession" id="ChatSessionResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="title" column="title"/>
        <result property="agentType" column="agent_type"/>
        <result property="messageCount" column="message_count"/>
        <result property="lastMessage" column="last_message"/>
        <result property="lastActiveTime" column="last_active_time"/>
        <result property="status" column="status"/>
        <result property="sessionConfig" column="session_config"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <sql id="selectChatSessionVo">
        select id,
               user_id,
               title,
               agent_type,
               message_count,
               last_message,
               last_active_time,
               status,
               session_config,
               create_by,
               create_time,
               update_by,
               update_time,
               del_flag
        from app_chat_session
    </sql>

    <select id="selectUserSessions" resultMap="ChatSessionResult">
        <include refid="selectChatSessionVo"/>
        where user_id = #{userId} and del_flag = '0'
        <if test="status != null">
            and status = #{status}
        </if>
        order by last_active_time desc
    </select>

    <select id="selectByIdAndUserId" resultMap="ChatSessionResult">
        <include refid="selectChatSessionVo"/>
        where id = #{sessionId} and user_id = #{userId} and del_flag = '0'
    </select>

    <select id="selectActiveSessions" parameterType="Long" resultMap="ChatSessionResult">
        <include refid="selectChatSessionVo"/>
        where user_id = #{userId} and status = 1 and del_flag = '0'
        order by last_active_time desc
        limit #{limit}
    </select>

    <select id="selectUserSessionsByAgent" resultMap="ChatSessionResult">
        <include refid="selectChatSessionVo"/>
        where user_id = #{userId} and agent_type = #{agentType} and del_flag = '0'
        order by last_active_time desc
        limit #{limit}
    </select>


    <select id="selectRecentSessions" parameterType="Long" resultMap="ChatSessionResult">
        <include refid="selectChatSessionVo"/>
        where user_id = #{userId} and last_active_time > #{timestamp} and del_flag = '0'
        order by last_active_time desc
    </select>


    <select id="getPopularAgentTypes" parameterType="int" resultType="java.util.Map">
        select agent_type, count(*) as count
        from app_chat_session
        where del_flag = '0'
        group by agent_type
        order by count desc
        limit #{limit}
    </select>

    <select id="countUserSessions" parameterType="Long" resultType="java.lang.Integer">
        select count(*) from app_chat_session
        where user_id = #{userId} and del_flag = '0'
        <if test="status != null">
            and status = #{status}
        </if>
    </select>

    <select id="selectSessionStats" parameterType="Long" resultType="java.util.Map">
        select agent_type            as agentType,
               count(*)              as sessionCount,
               sum(message_count)    as totalMessages,
               max(last_active_time) as lastActiveTime
        from app_chat_session
        where user_id = #{userId}
          and del_flag = '0'
        group by agent_type
        order by sessionCount desc
    </select>

</mapper>
