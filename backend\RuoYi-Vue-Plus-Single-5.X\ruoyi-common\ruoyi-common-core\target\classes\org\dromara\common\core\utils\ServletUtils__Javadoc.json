{"doc": "\n 客户端工具类，提供获取请求参数、响应处理、头部信息等常用操作\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "getParameter", "paramTypes": ["java.lang.String"], "doc": "\n 获取指定名称的 String 类型的请求参数\r\n\r\n @param name 参数名\r\n @return 参数值\r\n"}, {"name": "getParameter", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 获取指定名称的 String 类型的请求参数，若参数不存在，则返回默认值\r\n\r\n @param name         参数名\r\n @param defaultValue 默认值\r\n @return 参数值或默认值\r\n"}, {"name": "getParameterToInt", "paramTypes": ["java.lang.String"], "doc": "\n 获取指定名称的 Integer 类型的请求参数\r\n\r\n @param name 参数名\r\n @return 参数值\r\n"}, {"name": "getParameterToInt", "paramTypes": ["java.lang.String", "java.lang.Integer"], "doc": "\n 获取指定名称的 Integer 类型的请求参数，若参数不存在，则返回默认值\r\n\r\n @param name         参数名\r\n @param defaultValue 默认值\r\n @return 参数值或默认值\r\n"}, {"name": "getParameterToBool", "paramTypes": ["java.lang.String"], "doc": "\n 获取指定名称的 Boolean 类型的请求参数\r\n\r\n @param name 参数名\r\n @return 参数值\r\n"}, {"name": "getParameterToBool", "paramTypes": ["java.lang.String", "java.lang.Bo<PERSON>an"], "doc": "\n 获取指定名称的 Boolean 类型的请求参数，若参数不存在，则返回默认值\r\n\r\n @param name         参数名\r\n @param defaultValue 默认值\r\n @return 参数值或默认值\r\n"}, {"name": "getParams", "paramTypes": ["jakarta.servlet.ServletRequest"], "doc": "\n 获取所有请求参数（以 Map 的形式返回）\r\n\r\n @param request 请求对象{@link ServletRequest}\r\n @return 请求参数的 Map，键为参数名，值为参数值数组\r\n"}, {"name": "getParamMap", "paramTypes": ["jakarta.servlet.ServletRequest"], "doc": "\n 获取所有请求参数（以 Map 的形式返回，值为字符串形式的拼接）\r\n\r\n @param request 请求对象{@link ServletRequest}\r\n @return 请求参数的 Map，键为参数名，值为拼接后的字符串\r\n"}, {"name": "getRequest", "paramTypes": [], "doc": "\n 获取当前 HTTP 请求对象\r\n\r\n @return 当前 HTTP 请求对象\r\n"}, {"name": "getResponse", "paramTypes": [], "doc": "\n 获取当前 HTTP 响应对象\r\n\r\n @return 当前 HTTP 响应对象\r\n"}, {"name": "getSession", "paramTypes": [], "doc": "\n 获取当前请求的 HttpSession 对象\r\n <p>\r\n 如果当前请求已经关联了一个会话（即已经存在有效的 session ID），\r\n 则返回该会话对象；如果没有关联会话，则会创建一个新的会话对象并返回。\r\n <p>\r\n HttpSession 用于存储会话级别的数据，如用户登录信息、购物车内容等，\r\n 可以在多个请求之间共享会话数据\r\n\r\n @return 当前请求的 HttpSession 对象\r\n"}, {"name": "getRequestAttributes", "paramTypes": [], "doc": "\n 获取当前请求的请求属性\r\n\r\n @return {@link ServletRequestAttributes} 请求属性对象\r\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "paramTypes": ["jakarta.servlet.http.HttpServletRequest", "java.lang.String"], "doc": "\n 获取指定请求头的值，如果头部为空则返回空字符串\r\n\r\n @param request 请求对象\r\n @param name    头部名称\r\n @return 头部值\r\n"}, {"name": "getHeaders", "paramTypes": ["jakarta.servlet.http.HttpServletRequest"], "doc": "\n 获取所有请求头的 Map，键为头部名称，值为头部值\r\n\r\n @param request 请求对象\r\n @return 请求头的 Map\r\n"}, {"name": "renderString", "paramTypes": ["jakarta.servlet.http.HttpServletResponse", "java.lang.String"], "doc": "\n 将字符串渲染到客户端（以 JSON 格式返回）\r\n\r\n @param response 渲染对象\r\n @param string   待渲染的字符串\r\n"}, {"name": "isAjaxRequest", "paramTypes": ["jakarta.servlet.http.HttpServletRequest"], "doc": "\n 判断当前请求是否为 Ajax 异步请求\r\n\r\n @param request 请求对象\r\n @return 是否为 Ajax 请求\r\n"}, {"name": "getClientIP", "paramTypes": [], "doc": "\n 获取客户端 IP 地址\r\n\r\n @return 客户端 IP 地址\r\n"}, {"name": "urlEncode", "paramTypes": ["java.lang.String"], "doc": "\n 对内容进行 URL 编码\r\n\r\n @param str 内容\r\n @return 编码后的内容\r\n"}, {"name": "urlDecode", "paramTypes": ["java.lang.String"], "doc": "\n 对内容进行 URL 解码\r\n\r\n @param str 内容\r\n @return 解码后的内容\r\n"}], "constructors": []}