package org.dromara.app.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.time.LocalDateTime;

/**
 * 用户徽章实体
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_user_badge")
public class UserBadge extends BaseEntity {

    /**
     * 用户徽章ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 徽章ID
     */
    private String badgeId;

    /**
     * 是否解锁
     */
    private Boolean unlocked;

    /**
     * 解锁时间
     */
    private LocalDateTime unlockedAt;

    /**
     * 是否置顶
     */
    private Boolean isPinned;

    /**
     * 置顶时间
     */
    private LocalDateTime pinnedAt;

    /**
     * 置顶排序
     */
    private Integer pinnedSort;

    /**
     * 通知状态（0=未通知，1=已通知）
     */
    private Integer notificationStatus;

    /**
     * 是否查看过
     */
    private Boolean isViewed;

    /**
     * 查看时间
     */
    private LocalDateTime viewedAt;

    /**
     * 解锁渠道
     */
    private String unlockChannel;

    /**
     * 关联用户成就ID
     */
    private String userAchievementId;

    /**
     * 额外数据（JSON格式）
     */
    private String extraData;
}
