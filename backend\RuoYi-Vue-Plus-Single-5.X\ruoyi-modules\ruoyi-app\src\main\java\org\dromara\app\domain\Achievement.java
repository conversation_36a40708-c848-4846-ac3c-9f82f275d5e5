package org.dromara.app.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 成就定义对象 app_achievement
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_achievement")
public class Achievement extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 成就代码(唯一标识)
     */
    private String achievementCode;

    /**
     * 成就名称
     */
    private String achievementName;

    /**
     * 成就描述
     */
    private String achievementDesc;

    /**
     * 成就图标URL
     */
    private String achievementIcon;

    /**
     * 成就类型
     */
    private String achievementType;

    /**
     * 触发条件(JSON格式)
     */
    private String triggerCondition;

    /**
     * 奖励积分
     */
    private Integer rewardPoints;

    /**
     * 是否激活(0否 1是)
     */
    private String isActive;

    /**
     * 排序
     */
    private Integer sortOrder;

}
