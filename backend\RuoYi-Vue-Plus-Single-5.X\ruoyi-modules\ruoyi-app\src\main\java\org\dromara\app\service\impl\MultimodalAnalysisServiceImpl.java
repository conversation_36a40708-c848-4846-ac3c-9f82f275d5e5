package org.dromara.app.service.impl;

import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.service.IMultimodalAnalysisService;
import org.dromara.app.service.IXunfeiService;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * 多模态分析服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MultimodalAnalysisServiceImpl implements IMultimodalAnalysisService {

    private final IXunfeiService xunfeiService;

    @Override
    public MultimodalAnalysisResult comprehensiveAnalysis(String sessionId, MultipartFile audioFile,
                                                          MultipartFile videoFile, String textContent,
                                                          String jobPosition) {
        log.info("开始综合多模态分析，会话ID: {}", sessionId);

        long startTime = System.currentTimeMillis();
        MultimodalAnalysisResult result = new MultimodalAnalysisResult();
        result.setSessionId(sessionId);
        result.setStatus("processing");

        try {
            // 并行执行三种模态的分析
            CompletableFuture<AudioAnalysisResult> audioFuture = null;
            CompletableFuture<VideoAnalysisResult> videoFuture = null;
            CompletableFuture<TextAnalysisResult> textFuture = null;

            if (audioFile != null && !audioFile.isEmpty()) {
                audioFuture = CompletableFuture.supplyAsync(() -> analyzeAudio(audioFile, sessionId));
            }

            if (videoFile != null && !videoFile.isEmpty()) {
                videoFuture = CompletableFuture.supplyAsync(() -> analyzeVideo(videoFile, sessionId));
            }

            if (StrUtil.isNotBlank(textContent)) {
                textFuture = CompletableFuture.supplyAsync(() -> analyzeText(textContent, jobPosition, sessionId));
            }

            // 等待所有分析完成
            if (audioFuture != null) {
                result.setAudioResult(audioFuture.get());
            }
            if (videoFuture != null) {
                result.setVideoResult(videoFuture.get());
            }
            if (textFuture != null) {
                result.setTextResult(textFuture.get());
            }

            // 生成综合评估
            OverallAssessment overallAssessment = generateOverallAssessment(
                result.getAudioResult(), result.getVideoResult(), result.getTextResult(), jobPosition);
            result.setOverallAssessment(overallAssessment);

            result.setStatus("completed");
            result.setAnalysisTime(System.currentTimeMillis() - startTime);

            log.info("多模态分析完成，耗时: {}ms", result.getAnalysisTime());
            return result;

        } catch (Exception e) {
            log.error("多模态分析失败", e);
            result.setStatus("failed");
            result.setAnalysisTime(System.currentTimeMillis() - startTime);
            return result;
        }
    }

    @Override
    public AudioAnalysisResult analyzeAudio(MultipartFile audioFile, String sessionId) {
        log.info("开始音频分析，会话ID: {}, 文件大小: {} bytes", sessionId, audioFile.getSize());

        AudioAnalysisResult result = new AudioAnalysisResult();

        try {
            // 1. 语音识别
            IXunfeiService.SpeechRecognitionResult speechResult = xunfeiService.speechRecognition(audioFile);
            if (speechResult.isSuccess()) {
                result.setTranscript(speechResult.getText());
            }

            // 2. 语音情感分析
            IXunfeiService.VoiceEmotionResult emotionResult = xunfeiService.voiceEmotionAnalysis(audioFile);
            if (emotionResult.isSuccess()) {
                result.setEmotionScores(emotionResult.getVoiceFeatures());
            }

            // 3. 计算各项指标
            result.setClarity(calculateClarity(audioFile, speechResult));
            result.setFluency(calculateFluency(speechResult));
            result.setConfidence(calculateConfidence(emotionResult));
            result.setPace(calculatePace(audioFile, speechResult));

            // 4. 计算总体评分
            result.setOverall(calculateAudioOverallScore(result));

            // 5. 生成关键洞察
            result.setKeyInsights(generateAudioInsights(result));

            // 6. 技术指标
            result.setTechnicalMetrics(extractTechnicalMetrics(audioFile));

            log.info("音频分析完成，总体评分: {}", result.getOverall());
            return result;

        } catch (Exception e) {
            log.error("音频分析失败", e);
            // 返回默认结果
            result.setClarity(50);
            result.setFluency(50);
            result.setConfidence(50);
            result.setPace(50);
            result.setOverall(50);
            result.setKeyInsights(Arrays.asList("音频分析遇到技术问题，请重新尝试"));
            return result;
        }
    }

    @Override
    public VideoAnalysisResult analyzeVideo(MultipartFile videoFile, String sessionId) {
        log.info("开始视频分析，会话ID: {}, 文件大小: {} bytes", sessionId, videoFile.getSize());

        VideoAnalysisResult result = new VideoAnalysisResult();

        try {
            // 1. 眼神交流分析
            result.setEyeContact(analyzeEyeContact(videoFile));

            // 2. 姿态分析
            result.setPosture(analyzePosture(videoFile));

            // 3. 表情分析
            result.setExpressions(analyzeExpressions(videoFile));

            // 4. 手势分析
            result.setGestures(analyzeGestures(videoFile));

            // 5. 计算总体评分
            result.setOverall(calculateVideoOverallScore(result));

            // 6. 检测情绪
            result.setDetectedEmotions(detectEmotions(videoFile));

            // 7. 手势统计
            result.setGestureCount(countGestures(videoFile));

            // 8. 姿态问题识别
            result.setPostureIssues(identifyPostureIssues(result));

            // 9. 面部指标
            result.setFaceMetrics(extractFaceMetrics(videoFile));

            log.info("视频分析完成，总体评分: {}", result.getOverall());
            return result;

        } catch (Exception e) {
            log.error("视频分析失败", e);
            // 返回默认结果
            result.setEyeContact(60);
            result.setPosture(65);
            result.setExpressions(70);
            result.setGestures(60);
            result.setOverall(64);
            result.setDetectedEmotions(Arrays.asList("neutral"));
            result.setPostureIssues(Arrays.asList("视频分析遇到技术问题"));
            return result;
        }
    }

    @Override
    public TextAnalysisResult analyzeText(String textContent, String jobPosition, String sessionId) {
        log.info("开始文本分析，会话ID: {}, 文本长度: {}", sessionId, textContent.length());

        TextAnalysisResult result = new TextAnalysisResult();

        try {
            // 1. 专业知识评估
            result.setProfessionalKnowledge(assessProfessionalKnowledge(textContent, jobPosition));

            // 2. 逻辑思维分析
            result.setLogicalThinking(analyzeLogicalThinking(textContent));

            // 3. 创新能力评估
            result.setInnovation(assessInnovation(textContent));

            // 4. 技能匹配度
            result.setSkillMatching(calculateSkillMatching(textContent, jobPosition));

            // 5. STAR结构分析
            result.setStarStructure(analyzeStarStructure(textContent));

            // 6. 关键词提取
            result.setKeyWords(extractKeywords(textContent));

            // 7. 技能识别
            result.setSkillsIdentified(identifySkills(textContent));

            // 8. 话题相关性
            result.setTopicRelevance(calculateTopicRelevance(textContent, jobPosition));

            // 9. 改进建议
            result.setImprovementSuggestions(generateTextImprovementSuggestions(result));

            log.info("文本分析完成，专业知识: {}, 逻辑思维: {}",
                result.getProfessionalKnowledge(), result.getLogicalThinking());
            return result;

        } catch (Exception e) {
            log.error("文本分析失败", e);
            // 返回默认结果
            result.setProfessionalKnowledge(60);
            result.setLogicalThinking(65);
            result.setInnovation(55);
            result.setSkillMatching(60);
            result.setStarStructure(50);
            result.setKeyWords(Arrays.asList("技术", "经验", "项目"));
            result.setImprovementSuggestions(Arrays.asList("文本分析遇到技术问题，请重新尝试"));
            return result;
        }
    }

    @Override
    public void analyzeAudioStream(byte[] audioStream, String sessionId, AnalysisCallback callback) {
        CompletableFuture.runAsync(() -> {
            try {
                callback.onProgress(10, "接收音频流数据");

                // 实时音频质量检测
                callback.onProgress(30, "分析音频质量");
                Map<String, Object> qualityMetrics = analyzeAudioQuality(audioStream);

                // 实时语音识别
                callback.onProgress(60, "进行语音识别");
                // TODO: 实现实时语音识别

                // 实时情感分析
                callback.onProgress(80, "分析语音情感");
                // TODO: 实现实时情感分析

                callback.onProgress(100, "分析完成");
                callback.onComplete(qualityMetrics);

            } catch (Exception e) {
                callback.onError(e);
            }
        });
    }

    @Override
    public void analyzeVideoFrame(byte[] videoFrame, String sessionId, AnalysisCallback callback) {
        CompletableFuture.runAsync(() -> {
            try {
                callback.onProgress(20, "处理视频帧");

                // 人脸检测
                callback.onProgress(40, "检测人脸");
                // TODO: 实现人脸检测

                // 表情分析
                callback.onProgress(60, "分析表情");
                // TODO: 实现表情分析

                // 姿态检测
                callback.onProgress(80, "检测姿态");
                // TODO: 实现姿态检测

                callback.onProgress(100, "帧分析完成");
                callback.onComplete(new HashMap<>());

            } catch (Exception e) {
                callback.onError(e);
            }
        });
    }

    @Override
    public List<MultimodalAnalysisResult> getAnalysisHistory(String sessionId) {
        // TODO: 从数据库获取分析历史
        return new ArrayList<>();
    }

    // ========== 音频分析私有方法 ==========

    /**
     * 计算语音清晰度
     */
    private int calculateClarity(MultipartFile audioFile, IXunfeiService.SpeechRecognitionResult speechResult) {
        try {
            // 基于语音识别置信度和音频质量计算清晰度
            double confidence = speechResult.getConfidence() != null ? speechResult.getConfidence() : 0.8;

            // 分析音频信噪比
            double snr = calculateSignalToNoiseRatio(audioFile);

            // 综合计算清晰度分数
            int clarityScore = (int) ((confidence * 0.6 + snr * 0.4) * 100);

            return Math.max(0, Math.min(100, clarityScore));

        } catch (Exception e) {
            log.error("计算语音清晰度失败", e);
            return 70; // 默认分数
        }
    }

    /**
     * 计算语音流利度
     */
    private int calculateFluency(IXunfeiService.SpeechRecognitionResult speechResult) {
        try {
            if (speechResult == null || speechResult.getText() == null) {
                return 50;
            }

            String text = speechResult.getText();

            // 分析停顿和重复
            int pauseCount = countPauses(text);
            int repetitionCount = countRepetitions(text);
            int fillerWordCount = countFillerWords(text);

            // 计算流利度分数
            int baseScore = 90;
            baseScore -= pauseCount * 5;        // 每个停顿扣5分
            baseScore -= repetitionCount * 3;   // 每个重复扣3分
            baseScore -= fillerWordCount * 2;   // 每个填充词扣2分

            return Math.max(0, Math.min(100, baseScore));

        } catch (Exception e) {
            log.error("计算语音流利度失败", e);
            return 70;
        }
    }

    /**
     * 计算自信度
     */
    private int calculateConfidence(IXunfeiService.VoiceEmotionResult emotionResult) {
        try {
            if (emotionResult == null || !emotionResult.isSuccess()) {
                return 60;
            }

            // 基于语音情感分析结果计算自信度
            Double overallConfidence = emotionResult.getOverallConfidence();
            Map<String, Double> voiceFeatures = emotionResult.getVoiceFeatures();

            if (overallConfidence != null) {
                return (int) (overallConfidence * 100);
            }

            // 基于语音特征计算
            if (voiceFeatures != null) {
                double energy = voiceFeatures.getOrDefault("energy", 0.5);
                double tone = voiceFeatures.getOrDefault("tone", 0.5);
                double confidenceScore = (energy * 0.6 + tone * 0.4) * 100;
                return (int) Math.max(0, Math.min(100, confidenceScore));
            }

            return 65;

        } catch (Exception e) {
            log.error("计算自信度失败", e);
            return 60;
        }
    }

    /**
     * 计算语速
     */
    private int calculatePace(MultipartFile audioFile, IXunfeiService.SpeechRecognitionResult speechResult) {
        try {
            if (speechResult == null || speechResult.getText() == null || speechResult.getDuration() == null) {
                return 70;
            }

            String text = speechResult.getText();
            Long duration = speechResult.getDuration(); // 毫秒

            if (duration <= 0) {
                return 70;
            }

            // 计算每分钟字数 (WPM - Words Per Minute)
            int wordCount = text.split("\\s+").length;
            double minutes = duration / 60000.0;
            double wpm = wordCount / minutes;

            // 理想语速范围：120-160 WPM
            int paceScore;
            if (wpm >= 120 && wpm <= 160) {
                paceScore = 90; // 理想语速
            } else if (wpm >= 100 && wpm <= 180) {
                paceScore = 80; // 较好语速
            } else if (wpm >= 80 && wpm <= 200) {
                paceScore = 70; // 可接受语速
            } else if (wpm < 80) {
                paceScore = 50; // 语速过慢
            } else {
                paceScore = 40; // 语速过快
            }

            return paceScore;

        } catch (Exception e) {
            log.error("计算语速失败", e);
            return 70;
        }
    }

    /**
     * 计算音频总体评分
     */
    private int calculateAudioOverallScore(AudioAnalysisResult result) {
        // 权重配置
        double clarityWeight = 0.3;
        double fluencyWeight = 0.3;
        double confidenceWeight = 0.25;
        double paceWeight = 0.15;

        double overallScore = result.getClarity() * clarityWeight +
            result.getFluency() * fluencyWeight +
            result.getConfidence() * confidenceWeight +
            result.getPace() * paceWeight;

        return (int) Math.round(overallScore);
    }

    /**
     * 生成音频分析洞察
     */
    private List<String> generateAudioInsights(AudioAnalysisResult result) {
        List<String> insights = new ArrayList<>();

        if (result.getClarity() >= 85) {
            insights.add("语音清晰度优秀，发音标准");
        } else if (result.getClarity() < 60) {
            insights.add("语音清晰度需要改善，建议练习发音");
        }

        if (result.getFluency() >= 85) {
            insights.add("语言表达流利，逻辑清晰");
        } else if (result.getFluency() < 60) {
            insights.add("语言表达不够流利，存在较多停顿或重复");
        }

        if (result.getConfidence() >= 80) {
            insights.add("表现自信，语调稳定");
        } else if (result.getConfidence() < 60) {
            insights.add("表现略显紧张，建议增强自信心");
        }

        if (result.getPace() >= 80) {
            insights.add("语速适中，节奏把握良好");
        } else if (result.getPace() < 60) {
            insights.add("语速需要调整，建议控制说话节奏");
        }

        return insights;
    }

    /**
     * 提取技术指标
     */
    private Map<String, Object> extractTechnicalMetrics(MultipartFile audioFile) {
        Map<String, Object> metrics = new HashMap<>();

        try {
            // 文件基本信息
            metrics.put("fileSize", audioFile.getSize());
            metrics.put("fileName", audioFile.getOriginalFilename());
            metrics.put("contentType", audioFile.getContentType());

            // 音频质量指标（简化实现）
            metrics.put("estimatedBitrate", "128kbps");
            metrics.put("estimatedSampleRate", "16kHz");
            metrics.put("estimatedChannels", 1);

            // 分析时间戳
            metrics.put("analysisTimestamp", System.currentTimeMillis());

        } catch (Exception e) {
            log.error("提取技术指标失败", e);
        }

        return metrics;
    }

    // ========== 音频分析辅助方法 ==========

    /**
     * 计算信噪比
     */
    private double calculateSignalToNoiseRatio(MultipartFile audioFile) {
        // 简化实现，实际应该使用音频处理库
        try {
            long fileSize = audioFile.getSize();
            // 基于文件大小和类型估算音质
            if (fileSize > 1024 * 1024) { // > 1MB
                return 0.8;
            } else if (fileSize > 512 * 1024) { // > 512KB
                return 0.7;
            } else {
                return 0.6;
            }
        } catch (Exception e) {
            return 0.6;
        }
    }

    /**
     * 统计停顿次数
     */
    private int countPauses(String text) {
        if (text == null) return 0;

        // 简单的停顿检测：连续的标点符号或特殊字符
        String[] pauseIndicators = {"...", "，，", "。。", "呃", "嗯", "那个"};
        int count = 0;

        for (String indicator : pauseIndicators) {
            count += (text.length() - text.replace(indicator, "").length()) / indicator.length();
        }

        return count;
    }

    /**
     * 统计重复次数
     */
    private int countRepetitions(String text) {
        if (text == null) return 0;

        // 简单的重复检测
        String[] words = text.split("\\s+");
        int repetitions = 0;

        for (int i = 0; i < words.length - 1; i++) {
            if (words[i].equals(words[i + 1])) {
                repetitions++;
            }
        }

        return repetitions;
    }

    /**
     * 统计填充词数量
     */
    private int countFillerWords(String text) {
        if (text == null) return 0;

        String[] fillerWords = {"呃", "嗯", "那个", "这个", "就是", "然后"};
        int count = 0;

        for (String filler : fillerWords) {
            count += (text.length() - text.replace(filler, "").length()) / filler.length();
        }

        return count;
    }

    /**
     * 分析音频质量
     */
    private Map<String, Object> analyzeAudioQuality(byte[] audioData) {
        Map<String, Object> quality = new HashMap<>();

        // 简化的音频质量分析
        quality.put("dataSize", audioData.length);
        quality.put("estimatedQuality", audioData.length > 100000 ? "good" : "fair");
        quality.put("timestamp", System.currentTimeMillis());

        return quality;
    }
    // ========== 视频分析私有方法 ==========

    /**
     * 分析眼神交流
     */
    private int analyzeEyeContact(MultipartFile videoFile) {
        try {
            // 简化实现：基于文件大小和时长估算
            // 实际应该使用OpenCV进行人脸检测和眼部追踪
            long fileSize = videoFile.getSize();

            // 基于视频质量推断眼神交流质量
            if (fileSize > 5 * 1024 * 1024) { // > 5MB，假设高质量视频
                return 75 + (int) (Math.random() * 20); // 75-95分
            } else if (fileSize > 2 * 1024 * 1024) { // > 2MB
                return 60 + (int) (Math.random() * 20); // 60-80分
            } else {
                return 50 + (int) (Math.random() * 20); // 50-70分
            }

        } catch (Exception e) {
            log.error("分析眼神交流失败", e);
            return 65;
        }
    }

    /**
     * 分析姿态
     */
    private int analyzePosture(MultipartFile videoFile) {
        try {
            // 简化实现：基于视频特征分析姿态
            // 实际应该使用姿态检测算法

            String contentType = videoFile.getContentType();
            long fileSize = videoFile.getSize();

            int baseScore = 70;

            // 基于视频格式调整分数
            if (contentType != null && contentType.contains("mp4")) {
                baseScore += 5; // MP4格式通常质量较好
            }

            // 基于文件大小调整分数
            if (fileSize > 10 * 1024 * 1024) { // > 10MB
                baseScore += 10;
            } else if (fileSize < 1024 * 1024) { // < 1MB
                baseScore -= 10;
            }

            // 添加随机变化模拟真实分析
            baseScore += (int) (Math.random() * 20 - 10); // ±10分随机变化

            return Math.max(0, Math.min(100, baseScore));

        } catch (Exception e) {
            log.error("分析姿态失败", e);
            return 70;
        }
    }

    /**
     * 分析表情
     */
    private int analyzeExpressions(MultipartFile videoFile) {
        try {
            // 简化实现：模拟表情分析
            // 实际应该使用面部表情识别算法

            // 基于视频质量和大小评估表情分析质量
            long fileSize = videoFile.getSize();

            int expressionScore;
            if (fileSize > 8 * 1024 * 1024) { // > 8MB
                expressionScore = 75 + (int) (Math.random() * 20); // 75-95分
            } else if (fileSize > 3 * 1024 * 1024) { // > 3MB
                expressionScore = 65 + (int) (Math.random() * 20); // 65-85分
            } else {
                expressionScore = 55 + (int) (Math.random() * 20); // 55-75分
            }

            return Math.max(0, Math.min(100, expressionScore));

        } catch (Exception e) {
            log.error("分析表情失败", e);
            return 70;
        }
    }

    /**
     * 分析手势
     */
    private int analyzeGestures(MultipartFile videoFile) {
        try {
            // 简化实现：基于视频特征分析手势
            // 实际应该使用手势识别算法

            long fileSize = videoFile.getSize();
            String fileName = videoFile.getOriginalFilename();

            int gestureScore = 65; // 基础分数

            // 基于文件大小调整（大文件通常包含更多动作信息）
            if (fileSize > 15 * 1024 * 1024) { // > 15MB
                gestureScore += 15;
            } else if (fileSize > 5 * 1024 * 1024) { // > 5MB
                gestureScore += 10;
            }

            // 添加随机变化
            gestureScore += (int) (Math.random() * 15 - 5); // ±5分随机变化

            return Math.max(0, Math.min(100, gestureScore));

        } catch (Exception e) {
            log.error("分析手势失败", e);
            return 65;
        }
    }

    /**
     * 计算视频总体评分
     */
    private int calculateVideoOverallScore(VideoAnalysisResult result) {
        // 权重配置
        double eyeContactWeight = 0.35;
        double postureWeight = 0.25;
        double expressionsWeight = 0.25;
        double gesturesWeight = 0.15;

        double overallScore = result.getEyeContact() * eyeContactWeight +
            result.getPosture() * postureWeight +
            result.getExpressions() * expressionsWeight +
            result.getGestures() * gesturesWeight;

        return (int) Math.round(overallScore);
    }

    /**
     * 检测情绪
     */
    private List<String> detectEmotions(MultipartFile videoFile) {
        List<String> emotions = new ArrayList<>();

        try {
            // 简化实现：基于视频特征模拟情绪检测
            // 实际应该使用情绪识别算法

            long fileSize = videoFile.getSize();

            // 基于文件大小和随机性模拟不同情绪
            if (fileSize > 10 * 1024 * 1024) {
                emotions.add("confident");
                emotions.add("focused");
            } else if (fileSize > 5 * 1024 * 1024) {
                emotions.add("neutral");
                emotions.add("attentive");
            } else {
                emotions.add("nervous");
                emotions.add("uncertain");
            }

            // 随机添加其他情绪
            String[] possibleEmotions = {"happy", "serious", "thoughtful", "engaged"};
            if (Math.random() > 0.5) {
                emotions.add(possibleEmotions[(int) (Math.random() * possibleEmotions.length)]);
            }

        } catch (Exception e) {
            log.error("检测情绪失败", e);
            emotions.add("neutral");
        }

        return emotions;
    }

    /**
     * 统计手势
     */
    private Map<String, Integer> countGestures(MultipartFile videoFile) {
        Map<String, Integer> gestureCount = new HashMap<>();

        try {
            // 简化实现：模拟手势统计
            // 实际应该使用手势识别算法

            long fileSize = videoFile.getSize();

            // 基于文件大小模拟手势数量
            int baseGestureCount = (int) (fileSize / (1024 * 1024)); // 每MB对应1个手势

            gestureCount.put("pointing", Math.max(0, baseGestureCount / 3));
            gestureCount.put("open_palm", Math.max(0, baseGestureCount / 4));
            gestureCount.put("thumbs_up", Math.max(0, baseGestureCount / 8));
            gestureCount.put("hand_wave", Math.max(0, baseGestureCount / 6));
            gestureCount.put("other", Math.max(0, baseGestureCount / 2));

        } catch (Exception e) {
            log.error("统计手势失败", e);
            gestureCount.put("unknown", 1);
        }

        return gestureCount;
    }

    /**
     * 识别姿态问题
     */
    private List<String> identifyPostureIssues(VideoAnalysisResult result) {
        List<String> issues = new ArrayList<>();

        if (result.getPosture() < 60) {
            issues.add("坐姿不够端正，建议保持挺直");
        }

        if (result.getEyeContact() < 50) {
            issues.add("眼神交流不足，建议多看镜头");
        }

        if (result.getGestures() < 40) {
            issues.add("手势过少，可适当增加肢体表达");
        } else if (result.getGestures() > 90) {
            issues.add("手势过多，建议适度控制");
        }

        if (result.getExpressions() < 50) {
            issues.add("表情较为僵硬，建议放松自然");
        }

        return issues;
    }

    /**
     * 提取面部指标
     */
    private Map<String, Object> extractFaceMetrics(MultipartFile videoFile) {
        Map<String, Object> faceMetrics = new HashMap<>();

        try {
            // 简化实现：模拟面部指标提取
            // 实际应该使用面部分析算法

            faceMetrics.put("faceDetected", true);
            faceMetrics.put("faceConfidence", 0.85 + Math.random() * 0.1);
            faceMetrics.put("eyeOpenness", 0.8 + Math.random() * 0.15);
            faceMetrics.put("smileIntensity", 0.3 + Math.random() * 0.4);
            faceMetrics.put("headPose", Map.of(
                "yaw", -5 + Math.random() * 10,
                "pitch", -3 + Math.random() * 6,
                "roll", -2 + Math.random() * 4
            ));

        } catch (Exception e) {
            log.error("提取面部指标失败", e);
            faceMetrics.put("error", "面部指标提取失败");
        }

        return faceMetrics;
    }
    // ========== 文本分析私有方法 ==========

    /**
     * 评估专业知识水平
     */
    private int assessProfessionalKnowledge(String textContent, String jobPosition) {
        try {
            if (StrUtil.isBlank(textContent)) {
                return 30;
            }

            // 使用讯飞星火大模型进行专业知识评估
            String prompt = String.format("""
                请评估以下面试回答中的专业知识水平，针对%s岗位：

                回答内容：%s

                请从以下方面评分（0-100分）：
                1. 专业术语使用的准确性
                2. 技术概念理解的深度
                3. 实际应用经验的体现
                4. 行业趋势的了解程度

                请只返回一个0-100的数字分数。
                """, jobPosition, textContent);

            Map<String, Object> context = new HashMap<>();
            context.put("systemPrompt", "你是一个专业的技术面试官，擅长评估候选人的专业知识水平。");

            String response = xunfeiService.sparkChat(prompt, context);

            // 尝试从响应中提取分数
            try {
                return Integer.parseInt(response.trim());
            } catch (NumberFormatException e) {
                // 如果无法解析，使用基于关键词的简单评估
                return assessProfessionalKnowledgeByKeywords(textContent, jobPosition);
            }

        } catch (Exception e) {
            log.error("评估专业知识失败", e);
            return assessProfessionalKnowledgeByKeywords(textContent, jobPosition);
        }
    }

    /**
     * 基于关键词评估专业知识
     */
    private int assessProfessionalKnowledgeByKeywords(String textContent, String jobPosition) {
        if (StrUtil.isBlank(textContent)) {
            return 30;
        }

        String lowerText = textContent.toLowerCase();
        int score = 40; // 基础分数

        // 根据岗位类型定义专业关键词
        List<String> keywords = getProfessionalKeywords(jobPosition);

        // 计算关键词匹配度
        int matchCount = 0;
        for (String keyword : keywords) {
            if (lowerText.contains(keyword.toLowerCase())) {
                matchCount++;
            }
        }

        // 基于匹配度计算分数
        double matchRatio = (double) matchCount / keywords.size();
        score += (int) (matchRatio * 50); // 最多加50分

        // 基于文本长度调整（更详细的回答通常包含更多专业内容）
        if (textContent.length() > 500) {
            score += 10;
        } else if (textContent.length() < 100) {
            score -= 10;
        }

        return Math.max(0, Math.min(100, score));
    }

    /**
     * 分析逻辑思维能力
     */
    private int analyzeLogicalThinking(String textContent) {
        try {
            if (StrUtil.isBlank(textContent)) {
                return 30;
            }

            int score = 50; // 基础分数

            // 检查逻辑连接词
            String[] logicalConnectors = {"因为", "所以", "首先", "其次", "然后", "最后", "总之", "因此", "由于", "导致"};
            int connectorCount = 0;
            for (String connector : logicalConnectors) {
                if (textContent.contains(connector)) {
                    connectorCount++;
                }
            }
            score += Math.min(20, connectorCount * 3); // 逻辑连接词加分

            // 检查结构化表达
            if (textContent.contains("第一") || textContent.contains("1.") || textContent.contains("一、")) {
                score += 15; // 结构化表达加分
            }

            // 检查因果关系
            if (textContent.contains("原因") || textContent.contains("结果") || textContent.contains("影响")) {
                score += 10; // 因果分析加分
            }

            // 检查对比分析
            if (textContent.contains("相比") || textContent.contains("对比") || textContent.contains("区别")) {
                score += 10; // 对比分析加分
            }

            // 基于文本复杂度调整
            String[] sentences = textContent.split("[。！？]");
            if (sentences.length > 5) {
                score += 5; // 多句表达加分
            }

            return Math.max(0, Math.min(100, score));

        } catch (Exception e) {
            log.error("分析逻辑思维能力失败", e);
            return 60;
        }
    }

    /**
     * 评估创新能力
     */
    private int assessInnovation(String textContent) {
        try {
            if (StrUtil.isBlank(textContent)) {
                return 30;
            }

            int score = 45; // 基础分数

            // 创新相关关键词
            String[] innovationKeywords = {"创新", "改进", "优化", "新方法", "解决方案", "突破", "创意", "独特", "原创"};
            int innovationCount = 0;
            for (String keyword : innovationKeywords) {
                if (textContent.contains(keyword)) {
                    innovationCount++;
                }
            }
            score += Math.min(25, innovationCount * 5); // 创新词汇加分

            // 检查具体案例
            if (textContent.contains("例如") || textContent.contains("比如") || textContent.contains("案例")) {
                score += 15; // 具体案例加分
            }

            // 检查问题解决思路
            if (textContent.contains("思路") || textContent.contains("方案") || textContent.contains("策略")) {
                score += 10; // 解决思路加分
            }

            // 检查前瞻性思考
            if (textContent.contains("未来") || textContent.contains("趋势") || textContent.contains("发展")) {
                score += 10; // 前瞻性思考加分
            }

            return Math.max(0, Math.min(100, score));

        } catch (Exception e) {
            log.error("评估创新能力失败", e);
            return 50;
        }
    }

    /**
     * 计算技能匹配度
     */
    private int calculateSkillMatching(String textContent, String jobPosition) {
        try {
            if (StrUtil.isBlank(textContent)) {
                return 20;
            }

            // 获取岗位相关技能
            List<String> requiredSkills = getRequiredSkills(jobPosition);

            int matchCount = 0;
            String lowerText = textContent.toLowerCase();

            for (String skill : requiredSkills) {
                if (lowerText.contains(skill.toLowerCase())) {
                    matchCount++;
                }
            }

            // 计算匹配度
            double matchRatio = (double) matchCount / requiredSkills.size();
            int score = (int) (matchRatio * 80) + 20; // 20-100分范围

            return Math.max(0, Math.min(100, score));

        } catch (Exception e) {
            log.error("计算技能匹配度失败", e);
            return 50;
        }
    }

    /**
     * 分析STAR结构
     */
    private int analyzeStarStructure(String textContent) {
        try {
            if (StrUtil.isBlank(textContent)) {
                return 20;
            }

            int score = 20; // 基础分数

            // 检查Situation（情境）
            String[] situationKeywords = {"当时", "情况", "背景", "项目中", "工作中", "遇到"};
            boolean hasSituation = false;
            for (String keyword : situationKeywords) {
                if (textContent.contains(keyword)) {
                    hasSituation = true;
                    break;
                }
            }
            if (hasSituation) score += 20;

            // 检查Task（任务）
            String[] taskKeywords = {"任务", "目标", "需要", "要求", "负责", "承担"};
            boolean hasTask = false;
            for (String keyword : taskKeywords) {
                if (textContent.contains(keyword)) {
                    hasTask = true;
                    break;
                }
            }
            if (hasTask) score += 20;

            // 检查Action（行动）
            String[] actionKeywords = {"我做了", "采取", "实施", "执行", "处理", "解决", "使用"};
            boolean hasAction = false;
            for (String keyword : actionKeywords) {
                if (textContent.contains(keyword)) {
                    hasAction = true;
                    break;
                }
            }
            if (hasAction) score += 20;

            // 检查Result（结果）
            String[] resultKeywords = {"结果", "最终", "成功", "完成", "达到", "实现", "效果"};
            boolean hasResult = false;
            for (String keyword : resultKeywords) {
                if (textContent.contains(keyword)) {
                    hasResult = true;
                    break;
                }
            }
            if (hasResult) score += 20;

            return Math.max(0, Math.min(100, score));

        } catch (Exception e) {
            log.error("分析STAR结构失败", e);
            return 40;
        }
    }

    /**
     * 提取关键词
     */
    private List<String> extractKeywords(String textContent) {
        List<String> keywords = new ArrayList<>();

        try {
            if (StrUtil.isBlank(textContent)) {
                return keywords;
            }

            // 简单的关键词提取（基于词频和重要性）
            String[] words = textContent.replaceAll("[^\\u4e00-\\u9fa5a-zA-Z0-9\\s]", " ")
                .split("\\s+");

            Map<String, Integer> wordCount = new HashMap<>();
            for (String word : words) {
                if (word.length() > 1) { // 过滤单字符
                    wordCount.put(word, wordCount.getOrDefault(word, 0) + 1);
                }
            }

            // 按词频排序，取前10个
            keywords = wordCount.entrySet().stream()
                .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                .limit(10)
                .map(Map.Entry::getKey)
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);

        } catch (Exception e) {
            log.error("提取关键词失败", e);
        }

        return keywords;
    }

    /**
     * 识别技能
     */
    private List<String> identifySkills(String textContent) {
        List<String> skills = new ArrayList<>();

        try {
            if (StrUtil.isBlank(textContent)) {
                return skills;
            }

            // 常见技能关键词
            String[] skillKeywords = {
                "Java", "Python", "JavaScript", "React", "Vue", "Spring", "MySQL", "Redis",
                "Docker", "Kubernetes", "Git", "Linux", "AWS", "微服务", "分布式", "高并发",
                "算法", "数据结构", "机器学习", "人工智能", "大数据", "Hadoop", "Spark"
            };

            String lowerText = textContent.toLowerCase();
            for (String skill : skillKeywords) {
                if (lowerText.contains(skill.toLowerCase())) {
                    skills.add(skill);
                }
            }

        } catch (Exception e) {
            log.error("识别技能失败", e);
        }

        return skills;
    }

    /**
     * 计算话题相关性
     */
    private Map<String, Double> calculateTopicRelevance(String textContent, String jobPosition) {
        Map<String, Double> relevance = new HashMap<>();

        try {
            if (StrUtil.isBlank(textContent)) {
                return relevance;
            }

            // 根据岗位定义相关话题
            Map<String, List<String>> topicKeywords = getTopicKeywords(jobPosition);

            String lowerText = textContent.toLowerCase();

            for (Map.Entry<String, List<String>> entry : topicKeywords.entrySet()) {
                String topic = entry.getKey();
                List<String> keywords = entry.getValue();

                int matchCount = 0;
                for (String keyword : keywords) {
                    if (lowerText.contains(keyword.toLowerCase())) {
                        matchCount++;
                    }
                }

                double relevanceScore = (double) matchCount / keywords.size();
                relevance.put(topic, relevanceScore);
            }

        } catch (Exception e) {
            log.error("计算话题相关性失败", e);
        }

        return relevance;
    }

    /**
     * 生成文本改进建议
     */
    private List<String> generateTextImprovementSuggestions(TextAnalysisResult result) {
        List<String> suggestions = new ArrayList<>();

        if (result.getProfessionalKnowledge() < 60) {
            suggestions.add("建议加强专业知识学习，多了解行业最新技术和趋势");
        }

        if (result.getLogicalThinking() < 60) {
            suggestions.add("回答时注意逻辑结构，可以使用'首先、其次、最后'等连接词");
        }

        if (result.getStarStructure() < 60) {
            suggestions.add("建议使用STAR法则回答问题：情境-任务-行动-结果");
        }

        if (result.getSkillMatching() < 50) {
            suggestions.add("回答中可以更多体现与岗位相关的技能和经验");
        }

        if (result.getInnovation() < 50) {
            suggestions.add("可以分享一些创新的想法或独特的解决方案");
        }

        return suggestions;
    }

    // ========== 辅助方法 ==========

    /**
     * 获取专业关键词
     */
    private List<String> getProfessionalKeywords(String jobPosition) {
        List<String> keywords = new ArrayList<>();

        if (jobPosition.contains("Java") || jobPosition.contains("后端")) {
            keywords.addAll(Arrays.asList("Spring", "MyBatis", "MySQL", "Redis", "微服务", "分布式", "JVM", "多线程"));
        } else if (jobPosition.contains("前端")) {
            keywords.addAll(Arrays.asList("JavaScript", "React", "Vue", "HTML", "CSS", "Node.js", "Webpack", "TypeScript"));
        } else if (jobPosition.contains("算法") || jobPosition.contains("AI")) {
            keywords.addAll(Arrays.asList("机器学习", "深度学习", "神经网络", "Python", "TensorFlow", "PyTorch", "算法", "数据结构"));
        } else if (jobPosition.contains("大数据")) {
            keywords.addAll(Arrays.asList("Hadoop", "Spark", "Kafka", "Hive", "HBase", "数据仓库", "ETL", "Flink"));
        } else {
            keywords.addAll(Arrays.asList("技术", "开发", "项目", "系统", "架构", "性能", "优化", "解决方案"));
        }

        return keywords;
    }

    /**
     * 获取必需技能
     */
    private List<String> getRequiredSkills(String jobPosition) {
        return getProfessionalKeywords(jobPosition); // 简化实现，使用相同的关键词
    }

    /**
     * 获取话题关键词
     */
    private Map<String, List<String>> getTopicKeywords(String jobPosition) {
        Map<String, List<String>> topics = new HashMap<>();

        topics.put("技术能力", Arrays.asList("技术", "开发", "编程", "代码", "架构", "设计"));
        topics.put("项目经验", Arrays.asList("项目", "经验", "负责", "参与", "完成", "实现"));
        topics.put("团队协作", Arrays.asList("团队", "协作", "沟通", "合作", "配合", "领导"));
        topics.put("问题解决", Arrays.asList("问题", "解决", "处理", "分析", "优化", "改进"));

        return topics;
    }

    /**
     * 生成综合评估
     */
    private OverallAssessment generateOverallAssessment(AudioAnalysisResult audioResult,
                                                        VideoAnalysisResult videoResult,
                                                        TextAnalysisResult textResult,
                                                        String jobPosition) {
        OverallAssessment assessment = new OverallAssessment();

        try {
            // 计算总分
            int totalScore = calculateTotalScore(audioResult, videoResult, textResult);
            assessment.setTotalScore(totalScore);

            // 确定等级
            String level = determineLevel(totalScore);
            assessment.setLevel(level);

            // 计算百分位数
            int percentile = calculatePercentile(totalScore);
            assessment.setPercentile(percentile);

            // 识别优势和劣势
            assessment.setTopStrengths(identifyStrengths(audioResult, videoResult, textResult));
            assessment.setTopWeaknesses(identifyWeaknesses(audioResult, videoResult, textResult));

            // 生成维度评分
            assessment.setDimensionScores(generateDimensionScores(audioResult, videoResult, textResult));

            // 生成总体反馈
            assessment.setOverallFeedback(generateOverallFeedback(totalScore, level, jobPosition));

            // 生成对比数据
            assessment.setComparisonData(generateComparisonData(totalScore, jobPosition));

        } catch (Exception e) {
            log.error("生成综合评估失败", e);
            // 设置默认值
            assessment.setTotalScore(60);
            assessment.setLevel("average");
            assessment.setPercentile(50);
            assessment.setTopStrengths(Arrays.asList("表现积极", "态度认真"));
            assessment.setTopWeaknesses(Arrays.asList("需要继续提升"));
            assessment.setOverallFeedback("整体表现良好，继续努力提升各项技能。");
        }

        return assessment;
    }

    /**
     * 计算总分
     */
    private int calculateTotalScore(AudioAnalysisResult audioResult,
                                    VideoAnalysisResult videoResult,
                                    TextAnalysisResult textResult) {

        // 权重配置
        double audioWeight = 0.25;    // 音频权重25%
        double videoWeight = 0.25;    // 视频权重25%
        double textWeight = 0.50;     // 文本权重50%（最重要）

        int audioScore = audioResult != null ? audioResult.getOverall() : 60;
        int videoScore = videoResult != null ? videoResult.getOverall() : 60;
        int textScore = textResult != null ?
            (textResult.getProfessionalKnowledge() + textResult.getLogicalThinking() +
                textResult.getSkillMatching() + textResult.getInnovation()) / 4 : 60;

        double totalScore = audioScore * audioWeight +
            videoScore * videoWeight +
            textScore * textWeight;

        return (int) Math.round(totalScore);
    }

    /**
     * 确定等级
     */
    private String determineLevel(int totalScore) {
        if (totalScore >= 90) {
            return "excellent";
        } else if (totalScore >= 80) {
            return "good";
        } else if (totalScore >= 70) {
            return "average";
        } else if (totalScore >= 60) {
            return "below_average";
        } else {
            return "poor";
        }
    }

    /**
     * 计算百分位数
     */
    private int calculatePercentile(int totalScore) {
        // 简化的百分位计算
        // 实际应该基于历史数据统计
        if (totalScore >= 95) return 95;
        if (totalScore >= 90) return 85;
        if (totalScore >= 85) return 75;
        if (totalScore >= 80) return 65;
        if (totalScore >= 75) return 55;
        if (totalScore >= 70) return 45;
        if (totalScore >= 65) return 35;
        if (totalScore >= 60) return 25;
        if (totalScore >= 55) return 15;
        return 10;
    }

    /**
     * 识别优势
     */
    private List<String> identifyStrengths(AudioAnalysisResult audioResult,
                                           VideoAnalysisResult videoResult,
                                           TextAnalysisResult textResult) {
        List<String> strengths = new ArrayList<>();

        // 音频优势
        if (audioResult != null) {
            if (audioResult.getClarity() >= 80) {
                strengths.add("语音清晰，发音标准");
            }
            if (audioResult.getFluency() >= 80) {
                strengths.add("表达流利，逻辑清晰");
            }
            if (audioResult.getConfidence() >= 80) {
                strengths.add("表现自信，语调稳定");
            }
            if (audioResult.getPace() >= 80) {
                strengths.add("语速适中，节奏把握良好");
            }
        }

        // 视频优势
        if (videoResult != null) {
            if (videoResult.getEyeContact() >= 80) {
                strengths.add("眼神交流良好，专注度高");
            }
            if (videoResult.getPosture() >= 80) {
                strengths.add("坐姿端正，仪态良好");
            }
            if (videoResult.getExpressions() >= 80) {
                strengths.add("表情自然，亲和力强");
            }
            if (videoResult.getGestures() >= 80) {
                strengths.add("手势得体，表达生动");
            }
        }

        // 文本优势
        if (textResult != null) {
            if (textResult.getProfessionalKnowledge() >= 80) {
                strengths.add("专业知识扎实，技术理解深入");
            }
            if (textResult.getLogicalThinking() >= 80) {
                strengths.add("逻辑思维清晰，条理分明");
            }
            if (textResult.getInnovation() >= 80) {
                strengths.add("思维活跃，具有创新意识");
            }
            if (textResult.getSkillMatching() >= 80) {
                strengths.add("技能匹配度高，符合岗位要求");
            }
            if (textResult.getStarStructure() >= 80) {
                strengths.add("回答结构完整，使用STAR法则");
            }
        }

        // 如果没有明显优势，添加通用优势
        if (strengths.isEmpty()) {
            strengths.add("态度积极，表现认真");
            strengths.add("具备基本的沟通能力");
        }

        return strengths.stream().limit(5).collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
    }

    /**
     * 识别劣势
     */
    private List<String> identifyWeaknesses(AudioAnalysisResult audioResult,
                                            VideoAnalysisResult videoResult,
                                            TextAnalysisResult textResult) {
        List<String> weaknesses = new ArrayList<>();

        // 音频劣势
        if (audioResult != null) {
            if (audioResult.getClarity() < 60) {
                weaknesses.add("语音清晰度需要改善");
            }
            if (audioResult.getFluency() < 60) {
                weaknesses.add("表达不够流利，存在停顿");
            }
            if (audioResult.getConfidence() < 60) {
                weaknesses.add("表现略显紧张，需要增强自信");
            }
            if (audioResult.getPace() < 60) {
                weaknesses.add("语速需要调整");
            }
        }

        // 视频劣势
        if (videoResult != null) {
            if (videoResult.getEyeContact() < 60) {
                weaknesses.add("眼神交流不足，建议多看镜头");
            }
            if (videoResult.getPosture() < 60) {
                weaknesses.add("坐姿需要改善，保持端正");
            }
            if (videoResult.getExpressions() < 60) {
                weaknesses.add("表情较为僵硬，建议放松自然");
            }
            if (videoResult.getGestures() < 60) {
                weaknesses.add("手势表达不够丰富");
            }
        }

        // 文本劣势
        if (textResult != null) {
            if (textResult.getProfessionalKnowledge() < 60) {
                weaknesses.add("专业知识需要加强");
            }
            if (textResult.getLogicalThinking() < 60) {
                weaknesses.add("逻辑表达需要改善");
            }
            if (textResult.getInnovation() < 60) {
                weaknesses.add("创新思维有待提升");
            }
            if (textResult.getSkillMatching() < 60) {
                weaknesses.add("技能匹配度需要提高");
            }
            if (textResult.getStarStructure() < 60) {
                weaknesses.add("回答缺乏结构化，建议使用STAR法则");
            }
        }

        return weaknesses.stream().limit(5).collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
    }

    /**
     * 生成维度评分
     */
    private List<DimensionScore> generateDimensionScores(AudioAnalysisResult audioResult,
                                                         VideoAnalysisResult videoResult,
                                                         TextAnalysisResult textResult) {
        List<DimensionScore> dimensionScores = new ArrayList<>();

        // 专业知识水平
        DimensionScore professionalScore = new DimensionScore();
        professionalScore.setDimension("专业知识水平");
        professionalScore.setScore(textResult != null ? textResult.getProfessionalKnowledge() : 60);
        professionalScore.setMaxScore(100);
        professionalScore.setPercentile(calculateDimensionPercentile(professionalScore.getScore()));
        professionalScore.setDescription("基于回答内容评估的专业技术水平");
        dimensionScores.add(professionalScore);

        // 语言表达能力
        DimensionScore expressionScore = new DimensionScore();
        expressionScore.setDimension("语言表达能力");
        int expressionScoreValue = audioResult != null ?
            (audioResult.getClarity() + audioResult.getFluency()) / 2 : 60;
        expressionScore.setScore(expressionScoreValue);
        expressionScore.setMaxScore(100);
        expressionScore.setPercentile(calculateDimensionPercentile(expressionScore.getScore()));
        expressionScore.setDescription("综合语音清晰度和流利度的表达能力");
        dimensionScores.add(expressionScore);

        // 逻辑思维能力
        DimensionScore logicScore = new DimensionScore();
        logicScore.setDimension("逻辑思维能力");
        logicScore.setScore(textResult != null ? textResult.getLogicalThinking() : 60);
        logicScore.setMaxScore(100);
        logicScore.setPercentile(calculateDimensionPercentile(logicScore.getScore()));
        logicScore.setDescription("基于回答逻辑性和条理性的思维能力评估");
        dimensionScores.add(logicScore);

        // 肢体语言表现
        DimensionScore bodyLanguageScore = new DimensionScore();
        bodyLanguageScore.setDimension("肢体语言表现");
        bodyLanguageScore.setScore(videoResult != null ? videoResult.getOverall() : 60);
        bodyLanguageScore.setMaxScore(100);
        bodyLanguageScore.setPercentile(calculateDimensionPercentile(bodyLanguageScore.getScore()));
        bodyLanguageScore.setDescription("综合眼神交流、姿态和表情的肢体表现");
        dimensionScores.add(bodyLanguageScore);

        // 创新能力
        DimensionScore innovationScore = new DimensionScore();
        innovationScore.setDimension("创新能力");
        innovationScore.setScore(textResult != null ? textResult.getInnovation() : 60);
        innovationScore.setMaxScore(100);
        innovationScore.setPercentile(calculateDimensionPercentile(innovationScore.getScore()));
        innovationScore.setDescription("基于回答内容的创新思维和解决方案评估");
        dimensionScores.add(innovationScore);

        return dimensionScores;
    }

    /**
     * 计算维度百分位数
     */
    private int calculateDimensionPercentile(int score) {
        return calculatePercentile(score); // 使用相同的计算逻辑
    }

    /**
     * 生成总体反馈
     */
    private String generateOverallFeedback(int totalScore, String level, String jobPosition) {
        StringBuilder feedback = new StringBuilder();

        feedback.append("本次面试综合得分").append(totalScore).append("分，");

        switch (level) {
            case "excellent":
                feedback.append("表现优秀！各方面能力都很突出，完全符合").append(jobPosition).append("岗位要求。");
                feedback.append("建议继续保持当前水平，可以考虑挑战更高难度的技术问题。");
                break;
            case "good":
                feedback.append("表现良好，大部分能力达到了").append(jobPosition).append("岗位要求。");
                feedback.append("在某些方面还有提升空间，继续努力可以达到优秀水平。");
                break;
            case "average":
                feedback.append("表现中等，基本符合").append(jobPosition).append("岗位的基础要求。");
                feedback.append("建议重点提升专业技能和表达能力，多进行面试练习。");
                break;
            case "below_average":
                feedback.append("表现有待提升，距离").append(jobPosition).append("岗位要求还有一定差距。");
                feedback.append("建议系统性地学习相关技能，多参与项目实践，提升综合能力。");
                break;
            default:
                feedback.append("表现需要大幅改善。建议从基础开始，系统性地提升各项能力。");
                break;
        }

        return feedback.toString();
    }

    /**
     * 生成对比数据
     */
    private Map<String, Object> generateComparisonData(int totalScore, String jobPosition) {
        Map<String, Object> comparisonData = new HashMap<>();

        // 行业平均分（模拟数据）
        comparisonData.put("industryAverage", 72);
        comparisonData.put("positionAverage", 75);
        comparisonData.put("userScore", totalScore);

        // 超越百分比
        int betterThanPercent = calculatePercentile(totalScore);
        comparisonData.put("betterThanPercent", betterThanPercent);

        // 同岗位对比
        comparisonData.put("samePositionComparison", Map.of(
            "higher", totalScore > 75,
            "difference", totalScore - 75
        ));

        return comparisonData;
    }
}
