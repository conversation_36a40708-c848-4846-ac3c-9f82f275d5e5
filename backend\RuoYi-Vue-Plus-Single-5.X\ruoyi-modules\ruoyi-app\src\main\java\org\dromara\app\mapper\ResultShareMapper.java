package org.dromara.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.dromara.app.domain.ResultShare;

import java.util.List;

/**
 * 结果分享记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@Mapper
public interface ResultShareMapper extends BaseMapper<ResultShare> {

    /**
     * 根据结果ID查询分享记录列表
     *
     * @param resultId 结果ID
     * @return 分享记录列表
     */
    List<ResultShare> selectByResultId(@Param("resultId") String resultId);

    /**
     * 根据用户ID查询分享记录列表
     *
     * @param userId 用户ID
     * @param limit  限制数量
     * @return 分享记录列表
     */
    List<ResultShare> selectByUserId(@Param("userId") Long userId, @Param("limit") Integer limit);

    /**
     * 根据分享链接查询分享记录
     *
     * @param shareUrl 分享链接
     * @return 分享记录
     */
    ResultShare selectByShareUrl(@Param("shareUrl") String shareUrl);

    /**
     * 根据平台查询分享记录
     *
     * @param platform 平台
     * @param limit    限制数量
     * @return 分享记录列表
     */
    List<ResultShare> selectByPlatform(@Param("platform") String platform, @Param("limit") Integer limit);

    /**
     * 更新查看次数
     *
     * @param id 分享记录ID
     * @return 更新数量
     */
    int updateViewCount(@Param("id") Long id);

    /**
     * 根据状态查询分享记录
     *
     * @param status 状态
     * @return 分享记录列表
     */
    List<ResultShare> selectByStatus(@Param("status") String status);

    /**
     * 查询过期的分享记录
     *
     * @return 分享记录列表
     */
    List<ResultShare> selectExpiredShares();

    /**
     * 批量更新过期状态
     *
     * @param ids 分享记录ID列表
     * @return 更新数量
     */
    int batchUpdateExpiredStatus(@Param("ids") List<Long> ids);

    /**
     * 根据结果ID删除分享记录
     *
     * @param resultId 结果ID
     * @return 删除数量
     */
    int deleteByResultId(@Param("resultId") String resultId);

}
