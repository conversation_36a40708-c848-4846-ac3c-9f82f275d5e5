{"doc": "\n 系统访问记录表 sys_logininfor\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "infoId", "doc": "\n ID\r\n"}, {"name": "userName", "doc": "\n 用户账号\r\n"}, {"name": "client<PERSON>ey", "doc": "\n 客户端\r\n"}, {"name": "deviceType", "doc": "\n 设备类型\r\n"}, {"name": "status", "doc": "\n 登录状态 0成功 1失败\r\n"}, {"name": "ipaddr", "doc": "\n 登录IP地址\r\n"}, {"name": "loginLocation", "doc": "\n 登录地点\r\n"}, {"name": "browser", "doc": "\n 浏览器类型\r\n"}, {"name": "os", "doc": "\n 操作系统\r\n"}, {"name": "msg", "doc": "\n 提示消息\r\n"}, {"name": "loginTime", "doc": "\n 访问时间\r\n"}], "enumConstants": [], "methods": [], "constructors": []}