package org.dromara.app.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.app.domain.Feedback;
import org.dromara.app.domain.bo.FeedbackBo;
import org.dromara.app.domain.vo.FeedbackVo;
import org.dromara.app.mapper.FeedbackMapper;
import org.dromara.app.service.IFeedbackService;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 意见反馈Service业务层处理
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
public class FeedbackServiceImpl implements IFeedbackService {

    private final FeedbackMapper baseMapper;

    /**
     * 查询意见反馈
     *
     * @param id 意见反馈主键
     * @return 意见反馈
     */
    @Override
    public FeedbackVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询意见反馈列表
     *
     * @param bo        意见反馈
     * @param pageQuery 分页查询条件
     * @return 意见反馈分页列表
     */
    @Override
    public TableDataInfo<FeedbackVo> queryPageList(FeedbackBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<Feedback> lqw = buildQueryWrapper(bo);
        Page<FeedbackVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询用户反馈列表
     *
     * @param userId 用户ID
     * @return 反馈列表
     */
    @Override
    public List<FeedbackVo> selectUserFeedbackList(Long userId) {
        LambdaQueryWrapper<Feedback> lqw = Wrappers.lambdaQuery();
        lqw.eq(Feedback::getUserId, userId);
        lqw.orderByDesc(Feedback::getCreateTime);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<Feedback> buildQueryWrapper(FeedbackBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Feedback> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId() != null, Feedback::getUserId, bo.getUserId());
        lqw.like(StringUtils.isNotBlank(bo.getType()), Feedback::getType, bo.getType());
        lqw.like(StringUtils.isNotBlank(bo.getContent()), Feedback::getContent, bo.getContent());
        lqw.like(StringUtils.isNotBlank(bo.getContactInfo()), Feedback::getContactInfo, bo.getContactInfo());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), Feedback::getStatus, bo.getStatus());
        lqw.like(StringUtils.isNotBlank(bo.getPlatform()), Feedback::getPlatform, bo.getPlatform());
        lqw.orderByDesc(Feedback::getCreateTime);
        return lqw;
    }

    /**
     * 新增意见反馈
     *
     * @param bo 意见反馈
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(FeedbackBo bo) {
        Feedback add = MapstructUtils.convert(bo, Feedback.class);
        if (add != null) {
            validEntityBeforeSave(add);
        }
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            if (add != null) {
                bo.setId(add.getId());
            }
        }
        return flag;
    }

    /**
     * 修改意见反馈
     *
     * @param bo 意见反馈
     * @return 结果
     */
    @Override
    public Boolean updateByBo(FeedbackBo bo) {
        Feedback update = MapstructUtils.convert(bo, Feedback.class);
        if (update != null) {
            validEntityBeforeSave(update);
        }
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(Feedback entity) {
        // 校验反馈类型
        if (StringUtils.isNotBlank(entity.getType())) {
            List<String> allowedTypes = Arrays.asList("功能建议", "内容问题", "使用问题", "其他");
            if (!allowedTypes.contains(entity.getType())) {
                throw new RuntimeException("不支持的反馈类型: " + entity.getType());
            }
        }

        // 校验状态
        if (StringUtils.isNotBlank(entity.getStatus())) {
            List<String> allowedStatuses = Arrays.asList("PENDING", "PROCESSING", "RESOLVED", "CLOSED");
            if (!allowedStatuses.contains(entity.getStatus())) {
                throw new RuntimeException("不支持的反馈状态: " + entity.getStatus());
            }
        }
    }

    /**
     * 批量删除意见反馈
     *
     * @param ids     需要删除的意见反馈主键集合
     * @param isValid 是否校验,true-删除前校验,false-不校验
     * @return 结果
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 获取用户反馈统计信息
     *
     * @param userId 用户ID
     * @return 统计信息
     */
    @Override
    public Map<String, Object> getUserFeedbackStats(Long userId) {
        LambdaQueryWrapper<Feedback> lqw = Wrappers.lambdaQuery();
        lqw.eq(Feedback::getUserId, userId);

        List<Feedback> feedbackList = baseMapper.selectList(lqw);

        Map<String, Object> stats = new HashMap<>();
        stats.put("totalCount", feedbackList.size());

        // 统计各状态数量
        long pendingCount = feedbackList.stream().filter(f -> "PENDING".equals(f.getStatus())).count();
        long processingCount = feedbackList.stream().filter(f -> "PROCESSING".equals(f.getStatus())).count();
        long resolvedCount = feedbackList.stream().filter(f -> "RESOLVED".equals(f.getStatus())).count();
        long closedCount = feedbackList.stream().filter(f -> "CLOSED".equals(f.getStatus())).count();

        stats.put("pendingCount", pendingCount);
        stats.put("processingCount", processingCount);
        stats.put("resolvedCount", resolvedCount);
        stats.put("closedCount", closedCount);

        // 统计各类型数量
        Map<String, Long> typeStats = new HashMap<>();
        typeStats.put("功能建议", feedbackList.stream().filter(f -> "功能建议".equals(f.getType())).count());
        typeStats.put("内容问题", feedbackList.stream().filter(f -> "内容问题".equals(f.getType())).count());
        typeStats.put("使用问题", feedbackList.stream().filter(f -> "使用问题".equals(f.getType())).count());
        typeStats.put("其他", feedbackList.stream().filter(f -> "其他".equals(f.getType())).count());

        stats.put("typeStats", typeStats);

        return stats;
    }
}
