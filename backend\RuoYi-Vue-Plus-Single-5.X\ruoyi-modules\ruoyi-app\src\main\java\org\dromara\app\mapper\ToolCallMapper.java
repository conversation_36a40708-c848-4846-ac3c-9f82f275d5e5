package org.dromara.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.dromara.app.domain.ToolCall;

import java.util.List;
import java.util.Map;

/**
 * 工具调用Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface ToolCallMapper extends BaseMapper<ToolCall> {

    /**
     * 分页查询工具调用历史
     *
     * @param page      分页对象
     * @param sessionId 会话ID（可选）
     * @param userId    用户ID
     * @return 调用历史分页结果
     */
    Page<ToolCall> selectCallHistory(Page<ToolCall> page,
                                     @Param("sessionId") String sessionId,
                                     @Param("userId") Long userId);

    /**
     * 查询用户的工具调用统计
     *
     * @param userId 用户ID
     * @return 统计信息
     */
    @Select("SELECT " +
        "COUNT(*) as totalCalls, " +
        "SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as successCalls, " +
        "SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as failedCalls, " +
        "AVG(execution_time) as avgExecutionTime " +
        "FROM app_tool_call WHERE user_id = #{userId}")
    Map<String, Object> getUserToolStats(@Param("userId") Long userId);

    /**
     * 查询工具的使用统计
     *
     * @param toolId 工具ID
     * @return 统计信息
     */
    @Select("SELECT " +
        "COUNT(*) as totalCalls, " +
        "SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as successCalls, " +
        "SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as failedCalls, " +
        "AVG(execution_time) as avgExecutionTime, " +
        "MAX(start_time) as lastUsed " +
        "FROM app_tool_call WHERE tool_id = #{toolId}")
    Map<String, Object> getToolUsageStats(@Param("toolId") String toolId);

    /**
     * 查询热门工具（按调用次数排序）
     *
     * @param limit 返回数量限制
     * @return 热门工具列表
     */
    @Select("SELECT tool_id, tool_name, COUNT(*) as call_count " +
        "FROM app_tool_call GROUP BY tool_id, tool_name " +
        "ORDER BY call_count DESC LIMIT #{limit}")
    List<Map<String, Object>> getPopularTools(@Param("limit") Integer limit);

    /**
     * 查询用户最近的工具调用
     *
     * @param userId 用户ID
     * @param limit  返回数量限制
     * @return 最近调用列表
     */
    @Select("SELECT * FROM app_tool_call WHERE user_id = #{userId} " +
        "ORDER BY create_time DESC LIMIT #{limit}")
    List<ToolCall> getRecentCalls(@Param("userId") Long userId, @Param("limit") Integer limit);

    /**
     * 查询会话中的工具调用
     *
     * @param sessionId 会话ID
     * @param userId    用户ID
     * @return 工具调用列表
     */
    @Select("SELECT * FROM app_tool_call WHERE session_id = #{sessionId} AND user_id = #{userId} " +
        "ORDER BY create_time ASC")
    List<ToolCall> getSessionToolCalls(@Param("sessionId") String sessionId, @Param("userId") Long userId);

    /**
     * 统计工具调用错误类型
     *
     * @return 错误统计
     */
    @Select("SELECT error_message, COUNT(*) as count FROM app_tool_call " +
        "WHERE status = 2 AND error_message IS NOT NULL " +
        "GROUP BY error_message ORDER BY count DESC LIMIT 10")
    List<Map<String, Object>> getErrorStats();

    /**
     * 查询执行时间超过阈值的工具调用
     *
     * @param threshold 时间阈值（毫秒）
     * @return 慢调用列表
     */
    @Select("SELECT * FROM app_tool_call WHERE execution_time > #{threshold} " +
        "ORDER BY execution_time DESC LIMIT 100")
    List<ToolCall> getSlowCalls(@Param("threshold") Long threshold);

    /**
     * 批量插入工具调用记录
     *
     * @param toolCalls 调用记录列表
     * @return 影响行数
     */
    int batchInsertToolCalls(@Param("toolCalls") List<ToolCall> toolCalls);

    /**
     * 查询指定用户的历史调用记录
     *
     * @param userId   用户ID
     * @param offset   偏移量
     * @param pageSize 每页大小
     * @return 工具调用记录列表
     */
    List<ToolCall> selectUserHistory(@Param("userId") Long userId, @Param("offset") int offset, @Param("pageSize") Integer pageSize);

    /**
     * 根据会话ID查询工具调用记录
     *
     * @param sessionId 会话ID
     * @return 工具调用记录列表
     */
    List<ToolCall> selectBySessionId(@Param("sessionId") String sessionId);
}
