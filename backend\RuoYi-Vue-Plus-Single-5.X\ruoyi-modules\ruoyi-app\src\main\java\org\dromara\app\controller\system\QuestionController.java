package org.dromara.app.controller.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.app.domain.bo.QuestionBo;
import org.dromara.app.domain.vo.QuestionVo;
import org.dromara.app.service.IQuestionService;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 题目管理
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/question")
public class QuestionController extends BaseController {

    private final IQuestionService questionService;

    /**
     * 查询题目列表
     */
    @SaCheckPermission("system:question:list")
    @GetMapping("/list")
    public TableDataInfo<QuestionVo> list(QuestionBo bo, PageQuery pageQuery) {
        return questionService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出题目列表
     */
    @SaCheckPermission("system:question:export")
    @Log(title = "题目", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(QuestionBo bo, HttpServletResponse response) {
        List<QuestionVo> list = questionService.exportQuestionList(bo);
        ExcelUtil.exportExcel(list, "题目", QuestionVo.class, response);
    }

    /**
     * 获取题目详细信息
     */
    @SaCheckPermission("system:question:query")
    @GetMapping("/{questionId}")
    public R<QuestionVo> getInfo(@NotNull(message = "主键不能为空")
                                 @PathVariable Long questionId) {
        return R.ok(questionService.queryById(questionId));
    }

    /**
     * 新增题目
     */
    @SaCheckPermission("system:question:add")
    @Log(title = "题目", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping
    public R<Void> add(@Validated(AddGroup.class) @RequestBody QuestionBo bo) {
        return toAjax(questionService.insertByBo(bo));
    }

    /**
     * 修改题目
     */
    @SaCheckPermission("system:question:edit")
    @Log(title = "题目", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody QuestionBo bo) {
        return toAjax(questionService.updateByBo(bo));
    }

    /**
     * 删除题目
     */
    @SaCheckPermission("system:question:remove")
    @Log(title = "题目", businessType = BusinessType.DELETE)
    @DeleteMapping("/{questionIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] questionIds) {
        return toAjax(questionService.deleteWithValidByIds(List.of(questionIds), true));
    }

    /**
     * 根据题库ID查询题目列表
     */
    @SaCheckPermission("system:question:list")
    @GetMapping("/listByBank/{bankId}")
    public TableDataInfo<QuestionVo> listByBank(@PathVariable Long bankId, PageQuery pageQuery) {
        QuestionBo bo = new QuestionBo();
        bo.setBankId(bankId);
        return questionService.queryPageList(bo, pageQuery);
    }

    /**
     * 更新题目状态
     */
    @SaCheckPermission("system:question:edit")
    @Log(title = "题目", businessType = BusinessType.UPDATE)
    @PutMapping("/updateStatus")
    public R<Void> updateStatus(@RequestBody Map<String, Object> params) {
        Long questionId = Long.valueOf(params.get("questionId").toString());
        String status = params.get("status").toString();
        return toAjax(questionService.updateStatus(questionId, status));
    }

    /**
     * 批量更新题目状态
     */
    @SaCheckPermission("system:question:edit")
    @Log(title = "题目", businessType = BusinessType.UPDATE)
    @PutMapping("/batchUpdateStatus")
    public R<Void> batchUpdateStatus(@RequestBody Map<String, Object> params) {
        @SuppressWarnings("unchecked")
        List<Long> questionIds = (List<Long>) params.get("questionIds");
        String status = params.get("status").toString();
        return toAjax(questionService.batchUpdateStatus(questionIds, status));
    }

    /**
     * 更新题目难度
     */
    @SaCheckPermission("system:question:edit")
    @Log(title = "题目", businessType = BusinessType.UPDATE)
    @PutMapping("/updateDifficulty")
    public R<Void> updateDifficulty(@RequestBody Map<String, Object> params) {
        Long questionId = Long.valueOf(params.get("questionId").toString());
        String difficulty = params.get("difficulty").toString();
        return toAjax(questionService.updateDifficulty(questionId, difficulty));
    }

    /**
     * 批量更新题目难度
     */
    @SaCheckPermission("system:question:edit")
    @Log(title = "题目", businessType = BusinessType.UPDATE)
    @PutMapping("/batchUpdateDifficulty")
    public R<Void> batchUpdateDifficulty(@RequestBody Map<String, Object> params) {
        @SuppressWarnings("unchecked")
        List<Long> questionIds = (List<Long>) params.get("questionIds");
        String difficulty = params.get("difficulty").toString();
        return toAjax(questionService.batchUpdateDifficulty(questionIds, difficulty));
    }

    /**
     * 复制题目
     */
    @SaCheckPermission("system:question:add")
    @Log(title = "题目", businessType = BusinessType.INSERT)
    @PostMapping("/copy")
    public R<Void> copyQuestion(@RequestBody Map<String, Object> params) {
        Long questionId = Long.valueOf(params.get("questionId").toString());
        Long targetBankId = Long.valueOf(params.get("targetBankId").toString());
        return toAjax(questionService.copyQuestion(questionId, targetBankId));
    }

    /**
     * 批量复制题目
     */
    @SaCheckPermission("system:question:add")
    @Log(title = "题目", businessType = BusinessType.INSERT)
    @PostMapping("/batchCopy")
    public R<Void> batchCopyQuestions(@RequestBody Map<String, Object> params) {
        @SuppressWarnings("unchecked")
        List<Long> questionIds = (List<Long>) params.get("questionIds");
        Long targetBankId = Long.valueOf(params.get("targetBankId").toString());
        return toAjax(questionService.batchCopyQuestions(questionIds, targetBankId));
    }

    /**
     * 移动题目到其他题库
     */
    @SaCheckPermission("system:question:edit")
    @Log(title = "题目", businessType = BusinessType.UPDATE)
    @PutMapping("/move")
    public R<Void> moveQuestion(@RequestBody Map<String, Object> params) {
        Long questionId = Long.valueOf(params.get("questionId").toString());
        Long targetBankId = Long.valueOf(params.get("targetBankId").toString());
        return toAjax(questionService.moveQuestion(questionId, targetBankId));
    }

    /**
     * 批量移动题目到其他题库
     */
    @SaCheckPermission("system:question:edit")
    @Log(title = "题目", businessType = BusinessType.UPDATE)
    @PutMapping("/batchMove")
    public R<Void> batchMoveQuestions(@RequestBody Map<String, Object> params) {
        @SuppressWarnings("unchecked")
        List<Long> questionIds = (List<Long>) params.get("questionIds");
        Long targetBankId = Long.valueOf(params.get("targetBankId").toString());
        return toAjax(questionService.batchMoveQuestions(questionIds, targetBankId));
    }

    /**
     * 批量设置题目排序
     */
    @SaCheckPermission("system:question:edit")
    @Log(title = "题目", businessType = BusinessType.UPDATE)
    @PutMapping("/batchSetSort")
    public R<Void> batchSetSort(@RequestBody Map<String, Object> params) {
        @SuppressWarnings("unchecked")
        List<Long> questionIds = (List<Long>) params.get("questionIds");
        @SuppressWarnings("unchecked")
        List<Integer> sorts = (List<Integer>) params.get("sorts");
        return toAjax(questionService.batchSetSort(questionIds, sorts));
    }

    /**
     * 导入题目数据
     */
    @SaCheckPermission("system:question:import")
    @Log(title = "题目", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public R<Void> importData(MultipartFile file, Long bankId, boolean updateSupport) throws Exception {
        List<QuestionVo> questionList = ExcelUtil.importExcel(file.getInputStream(), QuestionVo.class);
        String operName = "admin"; // 暂时使用固定值
        String message = questionService.importQuestion(questionList, bankId, updateSupport, operName);
        return R.ok(message);
    }

    /**
     * 获取导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil.exportExcel(List.of(), "题目数据", QuestionVo.class, response);
    }

    /**
     * 获取题目类型统计
     */
    @SaCheckPermission("system:question:list")
    @GetMapping("/typeStats")
    public R<Map<String, Long>> getTypeStats(@RequestParam(required = false) Long bankId) {
        return R.ok(questionService.getTypeStats(bankId));
    }

    /**
     * 获取题目难度统计
     */
    @SaCheckPermission("system:question:list")
    @GetMapping("/difficultyStats")
    public R<Map<String, Long>> getDifficultyStats(@RequestParam(required = false) Long bankId) {
        return R.ok(questionService.getDifficultyStats(bankId));
    }

    /**
     * 随机抽题
     */
    @SaCheckPermission("system:question:list")
    @PostMapping("/random")
    public R<List<QuestionVo>> getRandomQuestions(@RequestBody Map<String, Object> params) {
        Long bankId = Long.valueOf(params.get("bankId").toString());
        Integer count = Integer.valueOf(params.get("count").toString());
        String questionType = (String) params.get("questionType");
        String difficulty = (String) params.get("difficulty");

        List<QuestionVo> questions = questionService.getRandomQuestions(bankId, count, questionType, difficulty);
        return R.ok(questions);
    }

    /**
     * 根据标签搜索题目
     */
    @SaCheckPermission("system:question:list")
    @GetMapping("/searchByTags")
    public TableDataInfo<QuestionVo> searchByTags(@RequestParam String tags, PageQuery pageQuery) {
        QuestionBo bo = new QuestionBo();
        bo.setTags(tags);
        return questionService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取题目标签统计
     */
    @SaCheckPermission("system:question:list")
    @GetMapping("/tagStats")
    public R<Map<String, Long>> getTagStats(@RequestParam(required = false) Long bankId) {
        return R.ok(questionService.getTagStats(bankId));
    }

    /**
     * 题目AI评分
     */
    @SaCheckPermission("system:question:edit")
    @Log(title = "题目", businessType = BusinessType.UPDATE)
    @PostMapping("/aiScore")
    public R<Void> aiScoreQuestion(@RequestBody Map<String, Object> params) {
        Long questionId = Long.valueOf(params.get("questionId").toString());
        return toAjax(questionService.aiScoreQuestion(questionId));
    }

    /**
     * 批量AI评分
     */
    @SaCheckPermission("system:question:edit")
    @Log(title = "题目", businessType = BusinessType.UPDATE)
    @PostMapping("/batchAiScore")
    public R<Void> batchAiScoreQuestions(@RequestBody Map<String, Object> params) {
        @SuppressWarnings("unchecked")
        List<Long> questionIds = (List<Long>) params.get("questionIds");
        return toAjax(questionService.batchAiScoreQuestions(questionIds));
    }
}
