package org.dromara.app.service;

import org.dromara.app.domain.bo.AssessmentResultBo;
import org.dromara.app.domain.vo.AssessmentQuestionVo;
import org.dromara.app.domain.vo.DetailedAbilityReportVo;
import org.dromara.app.domain.vo.InitialAbilityAssessmentVo;
import org.dromara.app.domain.vo.UserGrowthProfileVo;

import java.util.List;

/**
 * 能力评估服务接口
 *
 * <AUTHOR>
 */
public interface IAssessmentService {

    /**
     * 获取评估问题列表
     *
     * @return 评估问题列表
     */
    List<AssessmentQuestionVo> getAssessmentQuestions();

    /**
     * 提交评估结果
     *
     * @param results 评估结果
     * @return 能力评估结果
     */
    InitialAbilityAssessmentVo submitAssessmentResults(List<AssessmentResultBo> results);

    /**
     * 获取详细能力报告
     *
     * @return 详细能力报告
     */
    DetailedAbilityReportVo getDetailedAbilityReport();

    /**
     * 获取用户成长档案
     *
     * @return 用户成长档案
     */
    UserGrowthProfileVo getUserGrowthProfile();
}
