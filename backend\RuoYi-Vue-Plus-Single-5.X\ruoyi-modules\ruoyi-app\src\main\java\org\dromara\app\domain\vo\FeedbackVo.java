package org.dromara.app.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.app.domain.Feedback;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 意见反馈视图对象 feedback
 *
 * <AUTHOR>
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = Feedback.class)
public class FeedbackVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 反馈ID
     */
    @ExcelProperty(value = "反馈ID")
    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 反馈类型
     */
    @ExcelProperty(value = "反馈类型")
    private String type;

    /**
     * 反馈内容
     */
    @ExcelProperty(value = "反馈内容")
    private String content;

    /**
     * 联系方式
     */
    @ExcelProperty(value = "联系方式")
    private String contactInfo;

    /**
     * 设备信息
     */
    @ExcelProperty(value = "设备信息")
    private String deviceInfo;

    /**
     * 应用版本
     */
    @ExcelProperty(value = "应用版本")
    private String appVersion;

    /**
     * 平台信息
     */
    @ExcelProperty(value = "平台信息")
    private String platform;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态")
    private String status;

    /**
     * 处理回复
     */
    @ExcelProperty(value = "处理回复")
    private String reply;

    /**
     * 处理人
     */
    @ExcelProperty(value = "处理人")
    private String handler;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间")
    private Date updateTime;
}
