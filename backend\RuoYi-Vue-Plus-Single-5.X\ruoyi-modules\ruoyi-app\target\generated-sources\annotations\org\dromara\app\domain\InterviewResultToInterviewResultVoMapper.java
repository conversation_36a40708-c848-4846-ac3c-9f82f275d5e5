package org.dromara.app.domain;

import io.github.linpeilie.AutoMapperConfig__61;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.bo.InterviewResultBoToInterviewResultMapper;
import org.dromara.app.domain.vo.InterviewResultVo;
import org.dromara.app.domain.vo.InterviewResultVoToInterviewResultMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__61.class,
    uses = {InterviewResultVoToInterviewResultMapper.class,InterviewResultBoToInterviewResultMapper.class},
    imports = {}
)
public interface InterviewResultToInterviewResultVoMapper extends BaseMapper<InterviewResult, InterviewResultVo> {
}
