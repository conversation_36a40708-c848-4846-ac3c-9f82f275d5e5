package org.dromara.app.event;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;

import java.time.LocalDateTime;

/**
 * 面试相关事件定义
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
public class InterviewEvents {

    /**
     * 面试开始事件
     */
    @Getter
    @Setter
    @EqualsAndHashCode(callSuper = true)
    public static class InterviewStartedEvent extends ApplicationEvent {
        private String sessionId;
        private Long userId;
        private Long jobId;
        private String mode;
        private Integer questionCount;
        private LocalDateTime startTime;

        public InterviewStartedEvent(Object source, String sessionId, Long userId, Long jobId,
                                     String mode, Integer questionCount) {
            super(source);
            this.sessionId = sessionId;
            this.userId = userId;
            this.jobId = jobId;
            this.mode = mode;
            this.questionCount = questionCount;
            this.startTime = LocalDateTime.now();
        }
    }

    /**
     * 问题回答事件
     */
    @Getter
    @Setter
    @EqualsAndHashCode(callSuper = true)
    public static class QuestionAnsweredEvent extends ApplicationEvent {
        private String sessionId;
        private String questionId;
        private String questionType;
        private Double score;
        private Integer duration;
        private Boolean hasAudio;
        private Boolean hasVideo;
        private LocalDateTime answerTime;

        public QuestionAnsweredEvent(Object source, String sessionId, String questionId,
                                     String questionType, Double score, Integer duration,
                                     Boolean hasAudio, Boolean hasVideo) {
            super(source);
            this.sessionId = sessionId;
            this.questionId = questionId;
            this.questionType = questionType;
            this.score = score;
            this.duration = duration;
            this.hasAudio = hasAudio;
            this.hasVideo = hasVideo;
            this.answerTime = LocalDateTime.now();
        }
    }

    /**
     * 问题跳过事件
     */
    @Getter
    @Setter
    @EqualsAndHashCode(callSuper = true)
    public static class QuestionSkippedEvent extends ApplicationEvent {
        private String sessionId;
        private String questionId;
        private String questionType;
        private String reason;
        private LocalDateTime skipTime;

        public QuestionSkippedEvent(Object source, String sessionId, String questionId,
                                    String questionType, String reason) {
            super(source);
            this.sessionId = sessionId;
            this.questionId = questionId;
            this.questionType = questionType;
            this.reason = reason;
            this.skipTime = LocalDateTime.now();
        }
    }

    /**
     * 面试完成事件
     */
    @Getter
    @Setter
    @EqualsAndHashCode(callSuper = true)
    public static class InterviewCompletedEvent extends ApplicationEvent {

        private String sessionId;
        private Double totalScore;
        private Double completionRate;
        private Integer answeredCount;
        private Integer skippedCount;
        private Integer totalDuration;
        private String endReason;
        private LocalDateTime endTime;

        public InterviewCompletedEvent(Object source, String sessionId, Double totalScore,
                                       Double completionRate, Integer answeredCount,
                                       Integer skippedCount, Integer totalDuration, String endReason) {
            super(source);
            this.sessionId = sessionId;
            this.totalScore = totalScore;
            this.completionRate = completionRate;
            this.answeredCount = answeredCount;
            this.skippedCount = skippedCount;
            this.totalDuration = totalDuration;
            this.endReason = endReason;
            this.endTime = LocalDateTime.now();
        }
    }

    /**
     * AI评估事件
     */
    @Getter
    @Setter
    @EqualsAndHashCode(callSuper = true)
    public static class AiEvaluationEvent extends ApplicationEvent {
        private String sessionId;
        private String questionId;
        private Long evaluationTime;
        private Double score;
        private String model;
        private Boolean success;
        private String errorMessage;
        private LocalDateTime eventTimestamp;

        public AiEvaluationEvent(Object source, String sessionId, String questionId,
                                 Long evaluationTime, Double score, String model,
                                 Boolean success, String errorMessage) {
            super(source);
            this.sessionId = sessionId;
            this.questionId = questionId;
            this.evaluationTime = evaluationTime;
            this.score = score;
            this.model = model;
            this.success = success;
            this.errorMessage = errorMessage;
            this.eventTimestamp = LocalDateTime.now();
        }
    }

    /**
     * 系统错误事件
     */
    @Getter
    @Setter
    @EqualsAndHashCode(callSuper = true)
    public static class SystemErrorEvent extends ApplicationEvent {
        private String sessionId;
        private Long userId;
        private String errorType;
        private String message;
        private String stackTrace;
        private LocalDateTime eventTimestamp;

        public SystemErrorEvent(Object source, String sessionId, Long userId,
                                String errorType, String message, String stackTrace) {
            super(source);
            this.sessionId = sessionId;
            this.userId = userId;
            this.errorType = errorType;
            this.message = message;
            this.stackTrace = stackTrace;
            this.eventTimestamp = LocalDateTime.now();
        }
    }
}
