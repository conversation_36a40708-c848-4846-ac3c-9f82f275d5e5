package org.dromara.app.service.impl;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alipay.api.AlipayClient;
import com.alipay.api.domain.AlipayTradePagePayModel;
import com.alipay.api.internal.util.AlipaySignature;
import com.alipay.api.request.AlipayTradePagePayRequest;
import com.alipay.api.response.AlipayTradePagePayResponse;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.config.PaymentRabbitMqConfig;
import org.dromara.app.config.PaymentTimeoutConfig;
import org.dromara.app.domain.PaymentOrder;
import org.dromara.app.domain.dto.PaymentOrderDto;
import org.dromara.app.domain.vo.PaymentOrderVo;
import org.dromara.app.event.PaymentEvent;
import org.dromara.app.mapper.PaymentOrderMapper;
import org.dromara.app.service.IPaymentService;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.pay.enums.PaymentStatus;
import org.dromara.common.pay.exception.PaymentException;
import org.dromara.common.pay.properties.AlipayProperties;
import org.dromara.common.rabbitmq.core.RabbitMqTemplate;
import org.dromara.common.rabbitmq.entity.RabbitMessage;
import org.dromara.common.redis.utils.QueueUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import static org.dromara.common.core.enums.FormatsType.YYYYMMDDHHMMSS;
import static org.dromara.common.pay.constant.PayConstants.Alipay.*;
import static org.dromara.common.pay.constant.PayConstants.Common.ORDER_NO_PREFIX;

/**
 * 支付服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PaymentServiceImpl implements IPaymentService {

    private final PaymentOrderMapper paymentOrderMapper;
    private final AlipayClient alipayClient;
    private final AlipayProperties alipayProperties;
    private final ApplicationEventPublisher applicationEventPublisher;
    private final Optional<RabbitMqTemplate> rabbitMqTemplate;

    @Value("${spring.rabbitmq.enabled:false}")
    private boolean rabbitmqEnabled;

    @NotNull
    private static Map<String, String> getStringStringMap(HttpServletRequest request) {
        Map<String, String> params = new HashMap<>();
        Map<String, String[]> requestParams = request.getParameterMap();

        for (String name : requestParams.keySet()) {
            String[] values = requestParams.get(name);
            String valueStr = "";
            for (int i = 0; i < values.length; i++) {
                valueStr = (i == values.length - 1) ? valueStr + values[i] : valueStr + values[i] + ",";
            }
            params.put(name, valueStr);
        }
        return params;
    }

    /**
     * 创建支付订单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public PaymentOrderVo createPaymentOrder(PaymentOrderDto paymentOrderDto) {
        try {
            // 生成订单号
            String orderNo = ORDER_NO_PREFIX +
                DateUtil.format(LocalDateTime.now(), YYYYMMDDHHMMSS.getTimeFormat()) +
                IdUtil.fastSimpleUUID().substring(0, 6);

            // 生成支付token（32位随机字符串）
            String payToken = IdUtil.fastSimpleUUID();

            // 创建支付订单
            PaymentOrder paymentOrder = new PaymentOrder();
            BeanUtils.copyProperties(paymentOrderDto, paymentOrder);
            paymentOrder.setOrderNo(orderNo);
            paymentOrder.setStatus(PaymentStatus.PENDING.getCode());
            paymentOrder.setNotifyCount(0);

            // 设置订单过期时间（30分钟）
            paymentOrder.setExpireTime(LocalDateTime.now().plusMinutes(30));

            // 设置支付token信息
            paymentOrder.setPayToken(payToken);
            paymentOrder.setPayTokenExpireTime(LocalDateTime.now().plusMinutes(30));
            paymentOrder.setPayTokenUsed(false);

            // 保存订单
            paymentOrderMapper.insert(paymentOrder);

            // 如果RabbitMQ已启用，发送延迟消息进行超时处理
            boolean useRedisQueue = false;

            if (rabbitmqEnabled && rabbitMqTemplate.isPresent()) {
                try {
                    RabbitMessage<String> rabbitMessage = RabbitMessage.create(orderNo);
                    rabbitMessage.setMessageId(IdUtil.randomUUID());
                    rabbitMessage.setMessageType("payment_timeout");
                    rabbitMessage.setSource("payment_service");

                    rabbitMqTemplate.get().send(
                        PaymentRabbitMqConfig.PAYMENT_DELAY_EXCHANGE,
                        PaymentRabbitMqConfig.PAYMENT_DELAY_ROUTING_KEY,
                        rabbitMessage
                    );
                    log.info("订单创建成功并发送到RabbitMQ延迟队列，订单号：{}", orderNo);
                } catch (Exception e) {
                    log.warn("发送RabbitMQ延迟消息失败，将使用Redis延迟队列处理订单超时，订单号：{}，错误：{}", orderNo, e.getMessage());
                    useRedisQueue = true;
                }
            } else {
                log.info("RabbitMQ未启用或不可用，使用Redis延迟队列处理订单超时，订单号：{}", orderNo);
                useRedisQueue = true;
            }

            // 使用Redis延迟队列作为备选方案
            if (useRedisQueue) {
                try {
                    // 添加到Redis延迟队列，30分钟后超时
                    QueueUtils.addDelayedQueueObject(
                        PaymentTimeoutConfig.PAYMENT_TIMEOUT_QUEUE,
                        orderNo,
                        30,
                        TimeUnit.MINUTES
                    );
                    log.info("订单创建成功并发送到Redis延迟队列，订单号：{}", orderNo);
                } catch (Exception e) {
                    // 即使延迟队列失败，订单依然创建成功，只是没有自动超时处理
                    log.error("发送Redis延迟消息失败，订单号：{}，错误：{}", orderNo, e.getMessage());
                }
            }

            // 返回结果
            PaymentOrderVo result = new PaymentOrderVo();
            BeanUtils.copyProperties(paymentOrder, result);

            return result;

        } catch (Exception e) {
            log.error("创建支付订单失败：{}", e.getMessage(), e);
            throw new ServiceException("创建支付订单失败");
        }
    }

    /**
     * 支付宝支付
     */
    @Override
    public String alipayPay(String orderNo, String payToken) {
        try {
            // 验证支付token
            if (!validatePayToken(orderNo, payToken)) {
                throw new ServiceException("支付token无效或已过期");
            }

            // 查询订单信息
            PaymentOrder paymentOrder = getPaymentOrderByOrderNo(orderNo);
            if (paymentOrder == null) {
                throw new ServiceException("订单不存在");
            }

            if (!PaymentStatus.PENDING.getCode().equals(paymentOrder.getStatus())) {
                throw new ServiceException("订单状态异常，无法支付");
            }

            // 检查订单是否过期
            if (paymentOrder.getExpireTime().isBefore(LocalDateTime.now())) {
                // 更新订单状态为过期
                paymentOrder.setStatus(PaymentStatus.EXPIRED.getCode());
                paymentOrderMapper.updateById(paymentOrder);
                throw new ServiceException("订单已过期");
            }

            // 创建支付宝支付请求
            AlipayTradePagePayRequest request = new AlipayTradePagePayRequest();
            request.setReturnUrl(alipayProperties.getReturnUrl());
            request.setNotifyUrl(alipayProperties.getNotifyUrl());

            // 设置支付参数
            AlipayTradePagePayModel model = new AlipayTradePagePayModel();
            model.setOutTradeNo(orderNo);
            model.setTotalAmount(paymentOrder.getAmount().toString());
            model.setSubject(paymentOrder.getProductTitle());
            model.setBody("商品类型：" + paymentOrder.getProductType());
            model.setProductCode(PRODUCT_CODE_FAST_INSTANT_TRADE_PAY);
            // 设置超时时间为30分钟
            model.setTimeoutExpress("30m");

            request.setBizModel(model);

            // 调用支付宝API
            AlipayTradePagePayResponse response = alipayClient.pageExecute(request);

            if (response.isSuccess()) {
                log.info("支付宝支付请求成功，订单号：{}", orderNo);
                return response.getBody();
            } else {
                log.error("支付宝支付请求失败，订单号：{}，错误信息：{}", orderNo, response.getMsg());
                throw new ServiceException("支付请求失败：" + response.getMsg());
            }

        } catch (Exception e) {
            log.error("支付宝支付失败，订单号：{}，错误信息：{}", orderNo, e.getMessage(), e);
            throw new ServiceException("支付失败：" + e.getMessage());
        }
    }

    /**
     * 支付宝异步通知处理
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String alipayNotify(HttpServletRequest request) {
        try {
            // 获取支付宝回调参数
            Map<String, String> params = getStringStringMap(request);

            log.info("支付宝异步通知参数：{}", params);

            // 验证签名
            boolean signVerified = AlipaySignature.rsaCheckV1(
                params,
                alipayProperties.getAlipayPublicKey(),
                alipayProperties.getCharset(),
                alipayProperties.getSignType()
            );

            if (!signVerified) {
                log.error("支付宝异步通知签名验证失败");
                return NOTIFY_VERIFY_FAILURE;
            }

            // 获取通知参数
            String tradeStatus = params.get("trade_status");
            String outTradeNo = params.get("out_trade_no");
            String tradeNo = params.get("trade_no");
            String totalAmount = params.get("total_amount");

            log.info("支付宝异步通知：订单号={}，交易号={}，状态={}，金额={}", outTradeNo, tradeNo, tradeStatus, totalAmount);

            // 查询订单
            PaymentOrder paymentOrder = getPaymentOrderByOrderNo(outTradeNo);
            if (paymentOrder == null) {
                log.error("支付宝异步通知：订单不存在，订单号：{}", outTradeNo);
                return NOTIFY_VERIFY_FAILURE;
            }

            // 验证金额
            if (!paymentOrder.getAmount().toString().equals(totalAmount)) {
                log.error("支付宝异步通知：金额不匹配，订单号：{}，期望金额：{}，实际金额：{}",
                    outTradeNo, paymentOrder.getAmount(), totalAmount);
                return NOTIFY_VERIFY_FAILURE;
            }

            // 更新通知次数
            paymentOrder.setNotifyCount(paymentOrder.getNotifyCount() + 1);
            paymentOrder.setLastNotifyTime(LocalDateTime.now());
            paymentOrder.setNotifyResult(tradeStatus);

            // 处理不同的交易状态
            if (TRADE_STATUS_TRADE_SUCCESS.equals(tradeStatus) || TRADE_STATUS_TRADE_FINISHED.equals(tradeStatus)) {
                // 支付成功
                if (!PaymentStatus.PAID.getDescription().equals(paymentOrder.getStatus())) {
                    paymentOrder.setStatus(PaymentStatus.PAID.getDescription());
                    paymentOrder.setAlipayTradeNo(tradeNo);
                    paymentOrder.setPayTime(LocalDateTime.now());
                    log.info("订单支付成功，订单号：{}，支付宝交易号：{}", outTradeNo, tradeNo);

                    // 更新订单状态
                    paymentOrderMapper.updateById(paymentOrder);

                    // 标记支付token为已使用
                    markPayTokenAsUsed(outTradeNo);

                    // 发布支付成功事件
                    applicationEventPublisher.publishEvent(new PaymentEvent.PaymentSuccessEvent(this, outTradeNo));
                    log.info("发布支付成功事件，订单号：{}", outTradeNo);

                    // 从Redis延迟队列移除超时任务
                    removeFromTimeoutQueue(outTradeNo);

                    return NOTIFY_VERIFY_SUCCESS;
                }
            }

            // 更新订单信息
            paymentOrderMapper.updateById(paymentOrder);

            return NOTIFY_VERIFY_SUCCESS;

        } catch (Exception e) {
            log.error("支付宝异步通知处理失败：{}", e.getMessage(), e);
            return NOTIFY_VERIFY_FAILURE;
        }
    }

    /**
     * 支付宝同步回调处理
     */
    @Override
    public String alipayReturn(HttpServletRequest request) {
        try {
            // 获取支付宝回调参数
            Map<String, String> params = new HashMap<>();
            Map<String, String[]> requestParams = request.getParameterMap();

            for (String name : requestParams.keySet()) {
                String[] values = requestParams.get(name);
                String valueStr = "";
                for (int i = 0; i < values.length; i++) {
                    valueStr = (i == values.length - 1) ? valueStr + values[i] : valueStr + values[i] + ",";
                }
                params.put(name, valueStr);
            }

            log.info("支付宝同步回调参数：{}", params);

            // 验证签名
            boolean signVerified = AlipaySignature.rsaCheckV1(
                params,
                alipayProperties.getAlipayPublicKey(),
                alipayProperties.getCharset(),
                alipayProperties.getSignType()
            );

            if (!signVerified) {
                log.error("支付宝同步回调签名验证失败");
                return "signature_error";
            }

            String outTradeNo = params.get("out_trade_no");
            String tradeNo = params.get("trade_no");

            log.info("支付宝同步回调：订单号={}，交易号={}", outTradeNo, tradeNo);
            PaymentOrder paymentOrder = getPaymentOrderByOrderNo(outTradeNo);
            if (paymentOrder == null) {
                log.error("支付宝同步回调：订单不存在，订单号：{}", outTradeNo);
                throw new PaymentException("ORDER_NOT_EXISTS", "订单不存在");
            }
            if (PaymentStatus.PAID.getDescription().equals(paymentOrder.getStatus())) {
                log.info("订单已支付，订单号：{}", outTradeNo);
                return NOTIFY_VERIFY_SUCCESS;
            }

            if (PaymentStatus.PENDING.getCode().equals(paymentOrder.getStatus())) {
                paymentOrder.setStatus(PaymentStatus.PAID.getCode());
                paymentOrder.setAlipayTradeNo(tradeNo);
                paymentOrder.setPayTime(LocalDateTime.now());
                paymentOrderMapper.updateById(paymentOrder);
                log.info("订单支付成功，订单号：{}", outTradeNo);
                markPayTokenAsUsed(outTradeNo);

                // 发布支付成功事件
                applicationEventPublisher.publishEvent(new PaymentEvent.PaymentSuccessEvent(this, outTradeNo));
                log.info("发布支付成功事件，订单号：{}", outTradeNo);

                // 从Redis延迟队列移除超时任务
                removeFromTimeoutQueue(outTradeNo);

                return NOTIFY_VERIFY_SUCCESS;
            }
            return NOTIFY_VERIFY_SUCCESS;
        } catch (Exception e) {
            log.error("支付宝同步回调处理失败：{}", e.getMessage(), e);
            return "error";
        }
    }

    /**
     * 查询订单状态
     */
    @Override
    public PaymentOrderVo queryOrderStatus(String orderNo) {
        PaymentOrder paymentOrder = getPaymentOrderByOrderNo(orderNo);
        if (paymentOrder == null) {
            throw new ServiceException("订单不存在");
        }

        PaymentOrderVo result = new PaymentOrderVo();
        BeanUtils.copyProperties(paymentOrder, result);
        return result;
    }

    /**
     * 取消订单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelOrder(String orderNo) {
        PaymentOrder paymentOrder = getPaymentOrderByOrderNo(orderNo);
        if (paymentOrder == null) {
            throw new ServiceException("订单不存在");
        }

        if (!PaymentStatus.PENDING.getCode().equals(paymentOrder.getStatus())) {
            throw new ServiceException("订单状态异常，无法取消");
        }

        paymentOrder.setStatus(PaymentStatus.CANCELLED.getCode());
        boolean success = paymentOrderMapper.updateById(paymentOrder) > 0;

        if (success) {
            // 发布支付取消事件
            applicationEventPublisher.publishEvent(new PaymentEvent.PaymentCancelledEvent(this, orderNo));
            log.info("发布支付取消事件，订单号：{}", orderNo);

            // 从Redis延迟队列移除超时任务
            removeFromTimeoutQueue(orderNo);
        }

        return success;
    }

    /**
     * 根据订单号查询支付订单
     */
    private PaymentOrder getPaymentOrderByOrderNo(String orderNo) {
        if (StrUtil.isBlank(orderNo)) {
            return null;
        }

        LambdaQueryWrapper<PaymentOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PaymentOrder::getOrderNo, orderNo);
        return paymentOrderMapper.selectOne(wrapper);
    }

    /**
     * 验证支付token
     *
     * @param orderNo  订单号
     * @param payToken 支付token
     * @return 验证结果
     */
    @Override
    public boolean validatePayToken(String orderNo, String payToken) {
        if (StrUtil.isBlank(orderNo) || StrUtil.isBlank(payToken)) {
            return false;
        }

        PaymentOrder paymentOrder = getPaymentOrderByOrderNo(orderNo);
        if (paymentOrder == null) {
            return false;
        }

        // 检查token是否匹配
        if (!payToken.equals(paymentOrder.getPayToken())) {
            return false;
        }

        // 检查token是否已使用
        if (Boolean.TRUE.equals(paymentOrder.getPayTokenUsed())) {
            return false;
        }

        // 检查token是否过期
        return paymentOrder.getPayTokenExpireTime() != null &&
            !paymentOrder.getPayTokenExpireTime().isBefore(LocalDateTime.now());
    }

    /**
     * 标记支付token为已使用
     *
     * @param orderNo 订单号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void markPayTokenAsUsed(String orderNo) {
        PaymentOrder paymentOrder = getPaymentOrderByOrderNo(orderNo);
        if (paymentOrder != null) {
            paymentOrder.setPayTokenUsed(true);
            paymentOrderMapper.updateById(paymentOrder);
            log.info("支付token已标记为已使用，订单号：{}", orderNo);
        }
    }

    /**
     * 处理支付超时
     *
     * @param orderNo 订单号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handlePaymentTimeout(String orderNo) {
        try {
            log.info("开始处理支付超时，订单号：{}", orderNo);

            PaymentOrder paymentOrder = getPaymentOrderByOrderNo(orderNo);
            if (paymentOrder == null) {
                log.warn("处理支付超时时订单不存在，订单号：{}", orderNo);
                return;
            }

            // 只有待支付状态的订单才需要处理超时
            if (!PaymentStatus.PENDING.getCode().equals(paymentOrder.getStatus())) {
                log.info("订单状态非待支付，无需处理超时，订单号：{}，当前状态：{}", orderNo, paymentOrder.getStatus());
                return;
            }

            // 检查订单是否确实已过期
            if (paymentOrder.getExpireTime().isAfter(LocalDateTime.now())) {
                log.info("订单尚未过期，无需处理超时，订单号：{}，过期时间：{}", orderNo, paymentOrder.getExpireTime());
                return;
            }

            // 更新订单状态为已过期
            paymentOrder.setStatus(PaymentStatus.EXPIRED.getCode());
            paymentOrderMapper.updateById(paymentOrder);

            // 发布支付超时事件
            applicationEventPublisher.publishEvent(new PaymentEvent.PaymentTimeoutEvent(this, orderNo));
            log.info("订单支付超时处理完成，订单号：{}", orderNo);

        } catch (Exception e) {
            log.error("处理支付超时失败，订单号：{}，错误：{}", orderNo, e.getMessage(), e);
            throw new ServiceException("处理支付超时失败");
        }
    }

    /**
     * 从Redis延迟队列移除超时任务
     *
     * @param orderNo 订单号
     */
    private void removeFromTimeoutQueue(String orderNo) {
        try {
            boolean removed = QueueUtils.removeDelayedQueueObject(
                PaymentTimeoutConfig.PAYMENT_TIMEOUT_QUEUE,
                orderNo
            );
            if (removed) {
                log.info("已从Redis延迟队列移除超时任务，订单号：{}", orderNo);
            } else {
                log.info("Redis延迟队列中未找到对应的超时任务，订单号：{}", orderNo);
            }
        } catch (Exception e) {
            log.warn("从Redis延迟队列移除超时任务失败，订单号：{}，错误：{}", orderNo, e.getMessage());
            // 不抛出异常，避免影响主要业务流程
        }
    }
}
