{"doc": "\n 支付方式枚举\r\n\r\n <AUTHOR>\r\n", "fields": [{"name": "code", "doc": "\n 支付方式代码\r\n"}, {"name": "name", "doc": "\n 支付方式名称\r\n"}], "enumConstants": [{"name": "ALIPAY", "doc": "\n 支付宝支付\r\n"}, {"name": "WECHAT", "doc": "\n 微信支付\r\n"}, {"name": "UNIONPAY", "doc": "\n 银联支付\r\n"}, {"name": "BALANCE", "doc": "\n 余额支付\r\n"}], "methods": [{"name": "fromCode", "paramTypes": ["java.lang.String"], "doc": "\n 根据代码获取支付方式\r\n\r\n @param code 支付方式代码\r\n @return PaymentMethod\r\n"}], "constructors": []}