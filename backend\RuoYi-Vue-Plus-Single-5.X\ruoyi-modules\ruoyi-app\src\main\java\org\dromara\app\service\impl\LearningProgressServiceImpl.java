package org.dromara.app.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.LearningProgress;
import org.dromara.app.mapper.LearningProgressMapper;
import org.dromara.app.service.ILearningProgressService;
import org.dromara.common.core.exception.ServiceException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 学习进度服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LearningProgressServiceImpl implements ILearningProgressService {

    private final LearningProgressMapper progressMapper;

    @Override
    @Transactional
    public LearningProgress startLearning(Long userId, String learningPathId, Long resourceId) {
        try {
            log.info("用户开始学习，用户ID: {}, 路径ID: {}, 资源ID: {}", userId, learningPathId, resourceId);

            // 检查是否已存在进度记录
            LearningProgress existingProgress = null;
            if (learningPathId != null) {
                existingProgress = progressMapper.selectByLearningPathAndUser(learningPathId, userId);
            } else if (resourceId != null) {
                existingProgress = progressMapper.selectByResourceAndUser(resourceId, userId);
            }

            if (existingProgress != null) {
                // 如果已存在，更新状态和开始时间
                existingProgress.setStatus("in_progress");
                existingProgress.setLastStudyTime(LocalDateTime.now());
                progressMapper.updateById(existingProgress);
                return existingProgress;
            }

            // 创建新的学习进度记录
            LearningProgress progress = new LearningProgress();
            progress.setUserId(userId);
            progress.setLearningPathId(learningPathId);
            progress.setResourceId(resourceId);
            progress.setStatus("in_progress");
            progress.setCompletionPercentage(0);
            progress.setStartTime(LocalDateTime.now());
            progress.setLastStudyTime(LocalDateTime.now());
            progress.setActualStudyMinutes(0);

            // 初始化学习统计
            LearningProgress.LearningStatistics stats = new LearningProgress.LearningStatistics();
            stats.setTotalStudyDays(0);
            stats.setConsecutiveStudyDays(0);
            stats.setAverageDailyMinutes(0);
            stats.setLongestSessionMinutes(0);
            stats.setStudyFrequency(0.0);
            stats.setEfficiencyScore(0.0);
            stats.setLastUpdated(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            progress.setLearningStatistics(stats);

            progressMapper.insert(progress);
            log.info("学习进度创建成功，进度ID: {}", progress.getId());

            return progress;

        } catch (Exception e) {
            log.error("开始学习失败", e);
            throw new ServiceException("开始学习失败: " + e.getMessage());
        }
    }

    @
        Override
    @Transactional
    public boolean updateProgress(Long progressId, Integer completionPercentage, Integer studyMinutes) {
        try {
            log.info("更新学习进度，进度ID: {}, 完成度: {}%, 学习时长: {}分钟", progressId, completionPercentage, studyMinutes);

            LearningProgress progress = progressMapper.selectById(progressId);
            if (progress == null) {
                throw new ServiceException("学习进度不存在");
            }

            // 更新基本信息
            progress.setCompletionPercentage(completionPercentage);
            progress.setLastStudyTime(LocalDateTime.now());

            // 累加学习时长
            int currentMinutes = progress.getActualStudyMinutes() != null ? progress.getActualStudyMinutes() : 0;
            progress.setActualStudyMinutes(currentMinutes + studyMinutes);

            // 更新学习统计
            updateLearningStatistics(progress, studyMinutes);

            // 如果完成度达到100%，自动设置为完成状态
            if (completionPercentage >= 100) {
                progress.setStatus("completed");
                progress.setCompletionTime(LocalDateTime.now());
            }

            int result = progressMapper.updateById(progress);
            log.info("学习进度更新成功");
            return result > 0;

        } catch (Exception e) {
            log.error("更新学习进度失败", e);
            throw new ServiceException("更新学习进度失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean completeLearning(Long progressId, Double effectivenessRating, Double satisfactionRating, String notes) {
        try {
            log.info("完成学习，进度ID: {}", progressId);

            LearningProgress progress = progressMapper.selectById(progressId);
            if (progress == null) {
                throw new ServiceException("学习进度不存在");
            }

            // 更新完成信息
            progress.setStatus("completed");
            progress.setCompletionPercentage(100);
            progress.setCompletionTime(LocalDateTime.now());
            progress.setLastStudyTime(LocalDateTime.now());
            progress.setEffectivenessRating(effectivenessRating);
            progress.setSatisfactionRating(satisfactionRating);
            progress.setNotes(notes);

            // 更新学习统计
            updateLearningStatisticsOnCompletion(progress);

            int result = progressMapper.updateById(progress);
            log.info("学习完成记录更新成功");
            return result > 0;

        } catch (Exception e) {
            log.error("完成学习失败", e);
            throw new ServiceException("完成学习失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean pauseLearning(Long progressId, String reason) {
        try {
            log.info("暂停学习，进度ID: {}, 原因: {}", progressId, reason);

            LearningProgress progress = progressMapper.selectById(progressId);
            if (progress == null) {
                throw new ServiceException("学习进度不存在");
            }

            progress.setStatus("paused");

            // 记录暂停原因到扩展数据
            Map<String, Object> extendedData = progress.getExtendedData();
            if (extendedData == null) {
                extendedData = new HashMap<>();
            }
            extendedData.put("pauseReason", reason);
            extendedData.put("pauseTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            progress.setExtendedData(extendedData);

            int result = progressMapper.updateById(progress);
            log.info("学习暂停成功");
            return result > 0;

        } catch (Exception e) {
            log.error("暂停学习失败", e);
            throw new ServiceException("暂停学习失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean resumeLearning(Long progressId) {
        try {
            log.info("恢复学习，进度ID: {}", progressId);

            LearningProgress progress = progressMapper.selectById(progressId);
            if (progress == null) {
                throw new ServiceException("学习进度不存在");
            }

            progress.setStatus("in_progress");
            progress.setLastStudyTime(LocalDateTime.now());

            // 记录恢复时间到扩展数据
            Map<String, Object> extendedData = progress.getExtendedData();
            if (extendedData == null) {
                extendedData = new HashMap<>();
            }
            extendedData.put("resumeTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            progress.setExtendedData(extendedData);

            int result = progressMapper.updateById(progress);
            log.info("学习恢复成功");
            return result > 0;

        } catch (Exception e) {
            log.error("恢复学习失败", e);
            throw new ServiceException("恢复学习失败: " + e.getMessage());
        }
    }

    @Override
    public List<LearningProgress> getUserLearningProgress(Long userId, String status) {
        try {
            if (status != null && !status.isEmpty()) {
                return progressMapper.selectByUserIdAndStatus(userId, status);
            } else {
                return progressMapper.selectByUserId(userId);
            }
        } catch (Exception e) {
            log.error("获取用户学习进度失败", e);
            throw new ServiceException("获取用户学习进度失败: " + e.getMessage());
        }
    }

    @Override
    public Page<LearningProgress> getLearningProgressPage(Long userId, Integer pageNum, Integer pageSize, String status) {
        try {
            Page<LearningProgress> page = new Page<>(pageNum, pageSize);
            LambdaQueryWrapper<LearningProgress> wrapper = new LambdaQueryWrapper<LearningProgress>()
                .eq(LearningProgress::getUserId, userId)
                .eq(status != null && !status.isEmpty(), LearningProgress::getStatus, status)
                .orderByDesc(LearningProgress::getLastStudyTime);

            return progressMapper.selectPage(page, wrapper);
        } catch (Exception e) {
            log.error("分页查询学习进度失败", e);
            throw new ServiceException("分页查询学习进度失败: " + e.getMessage());
        }
    }

    @Override
    public LearningProgress getLearningProgressDetail(Long progressId, Long userId) {
        try {
            LearningProgress progress = progressMapper.selectById(progressId);
            if (progress == null || !progress.getUserId().equals(userId)) {
                return null;
            }
            return progress;
        } catch (Exception e) {
            log.error("获取学习进度详情失败", e);
            throw new ServiceException("获取学习进度详情失败: " + e.getMessage());
        }
    }

    @Override
    public LearningProgress getProgressByLearningPath(String learningPathId, Long userId) {
        try {
            return progressMapper.selectByLearningPathAndUser(learningPathId, userId);
        } catch (Exception e) {
            log.error("根据学习路径获取进度失败", e);
            throw new ServiceException("根据学习路径获取进度失败: " + e.getMessage());
        }
    }

    @Override
    public LearningProgress getProgressByResource(Long resourceId, Long userId) {
        try {
            return progressMapper.selectByResourceAndUser(resourceId, userId);
        } catch (Exception e) {
            log.error("根据资源获取进度失败", e);
            throw new ServiceException("根据资源获取进度失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getUserLearningStatistics(Long userId) {
        try {
            return progressMapper.selectUserLearningStatistics(userId);
        } catch (Exception e) {
            log.error("获取用户学习统计失败", e);
            throw new ServiceException("获取用户学习统计失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getLearningEffectivenessAssessment(Long userId) {
        try {
            return progressMapper.selectLearningEffectivenessStats(userId);
        } catch (Exception e) {
            log.error("获取学习效果评估失败", e);
            throw new ServiceException("获取学习效果评估失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getStudyTimeStatistics(Long userId) {
        try {
            return progressMapper.selectStudyTimeStatistics(userId);
        } catch (Exception e) {
            log.error("获取学习时间统计失败", e);
            throw new ServiceException("获取学习时间统计失败: " + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getLearningTrend(Long userId, Integer days) {
        try {
            if (days == null || days <= 0) {
                days = 30; // 默认30天
            }
            return progressMapper.selectLearningTrend(userId, days);
        } catch (Exception e) {
            log.error("获取学习趋势失败", e);
            throw new ServiceException("获取学习趋势失败: " + e.getMessage());
        }
    }

    @Override
    public List<LearningProgress> getRecentLearningRecords(Long userId, Integer limit) {
        try {
            if (limit == null || limit <= 0) {
                limit = 10;
            }
            return progressMapper.selectRecentLearningProgress(userId, limit);
        } catch (Exception e) {
            log.error("获取最近学习记录失败", e);
            throw new ServiceException("获取最近学习记录失败: " + e.getMessage());
        }
    }

    @Override
    public List<LearningProgress> getOngoingLearning(Long userId) {
        try {
            return progressMapper.selectOngoingLearning(userId);
        } catch (Exception e) {
            log.error("获取正在进行的学习失败", e);
            throw new ServiceException("获取正在进行的学习失败: " + e.getMessage());
        }
    }

    @Override
    public List<LearningProgress> getCompletedLearning(Long userId, Integer limit) {
        try {
            if (limit == null || limit <= 0) {
                limit = 20;
            }
            return progressMapper.selectCompletedLearning(userId, limit);
        } catch (Exception e) {
            log.error("获取已完成学习失败", e);
            throw new ServiceException("获取已完成学习失败: " + e.getMessage());
        }
    }

    @Override
    public List<LearningProgress> getOverdueLearning(Long userId) {
        try {
            return progressMapper.selectOverdueLearning(userId);
        } catch (Exception e) {
            log.error("获取超期学习项目失败", e);
            throw new ServiceException("获取超期学习项目失败: " + e.getMessage());
        }
    }

    // ========== 私有辅助方法 ==========

    /**
     * 更新学习统计
     */
    private void updateLearningStatistics(LearningProgress progress, Integer studyMinutes) {
        LearningProgress.LearningStatistics stats = progress.getLearningStatistics();
        if (stats == null) {
            stats = new LearningProgress.LearningStatistics();
        }

        // 更新最长单次学习时长
        if (studyMinutes > (stats.getLongestSessionMinutes() != null ? stats.getLongestSessionMinutes() : 0)) {
            stats.setLongestSessionMinutes(studyMinutes);
        }

        // 更新总学习时长和平均时长
        int totalMinutes = progress.getActualStudyMinutes() != null ? progress.getActualStudyMinutes() : 0;
        int totalDays = stats.getTotalStudyDays() != null ? stats.getTotalStudyDays() : 1;
        stats.setAverageDailyMinutes(totalMinutes / Math.max(totalDays, 1));

        // 计算学习效率评分
        double efficiencyScore = calculateEfficiencyScore(progress);
        stats.setEfficiencyScore(efficiencyScore);

        stats.setLastUpdated(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        progress.setLearningStatistics(stats);
    }

    /**
     * 完成时更新学习统计
     */
    private void updateLearningStatisticsOnCompletion(LearningProgress progress) {
        LearningProgress.LearningStatistics stats = progress.getLearningStatistics();
        if (stats == null) {
            stats = new LearningProgress.LearningStatistics();
        }

        // 计算总学习天数
        if (progress.getStartTime() != null && progress.getCompletionTime() != null) {
            long daysBetween = java.time.temporal.ChronoUnit.DAYS.between(
                progress.getStartTime().toLocalDate(),
                progress.getCompletionTime().toLocalDate()) + 1;
            stats.setTotalStudyDays((int) daysBetween);
        }

        // 更新完成率趋势
        List<Double> completionTrend = stats.getCompletionTrend();
        if (completionTrend == null) {
            completionTrend = new ArrayList<>();
        }
        completionTrend.add(100.0);
        stats.setCompletionTrend(completionTrend);

        // 计算最终效率评分
        double finalEfficiencyScore = calculateFinalEfficiencyScore(progress);
        stats.setEfficiencyScore(finalEfficiencyScore);

        stats.setLastUpdated(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        progress.setLearningStatistics(stats);
    }

    /**
     * 计算学习效率评分
     */
    private double calculateEfficiencyScore(LearningProgress progress) {
        // 基于完成度和学习时长计算效率
        int completion = progress.getCompletionPercentage() != null ? progress.getCompletionPercentage() : 0;
        int actualMinutes = progress.getActualStudyMinutes() != null ? progress.getActualStudyMinutes() : 1;
        int estimatedMinutes = progress.getEstimatedStudyMinutes() != null ? progress.getEstimatedStudyMinutes() : actualMinutes;

        // 效率 = 完成度 / (实际时间 / 预计时间)
        double timeRatio = (double) actualMinutes / estimatedMinutes;
        double efficiency = completion / Math.max(timeRatio * 100, 1.0);

        return Math.min(5.0, Math.max(0.0, efficiency * 5)); // 转换为5分制
    }

    /**
     * 计算最终效率评分
     */
    private double calculateFinalEfficiencyScore(LearningProgress progress) {
        double baseScore = calculateEfficiencyScore(progress);

        // 考虑用户评分
        if (progress.getEffectivenessRating() != null) {
            baseScore = (baseScore + progress.getEffectivenessRating()) / 2;
        }

        return baseScore;
    }

    @Override
    @Transactional
    public boolean createLearningPlan(Long progressId, LearningProgress.LearningPlan learningPlan) {
        try {
            log.info("创建学习计划，进度ID: {}", progressId);

            LearningProgress progress = progressMapper.selectById(progressId);
            if (progress == null) {
                throw new ServiceException("学习进度不存在");
            }

            progress.setLearningPlan(learningPlan);

            int result = progressMapper.updateById(progress);
            log.info("学习计划创建成功");
            return result > 0;

        } catch (Exception e) {
            log.error("创建学习计划失败", e);
            throw new ServiceException("创建学习计划失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean updateLearningPlan(Long progressId, LearningProgress.LearningPlan learningPlan) {
        try {
            log.info("更新学习计划，进度ID: {}", progressId);

            LearningProgress progress = progressMapper.selectById(progressId);
            if (progress == null) {
                throw new ServiceException("学习进度不存在");
            }

            // 记录计划调整
            LearningProgress.LearningPlan currentPlan = progress.getLearningPlan();
            if (currentPlan != null) {
                LearningProgress.PlanAdjustment adjustment = new LearningProgress.PlanAdjustment();
                adjustment.setAdjustmentDate(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                adjustment.setReason("用户主动调整");
                adjustment.setAdjustmentType("plan_update");
                adjustment.setAdjustmentDetails("更新学习计划");

                List<LearningProgress.PlanAdjustment> adjustments = currentPlan.getAdjustments();
                if (adjustments == null) {
                    adjustments = new ArrayList<>();
                }
                adjustments.add(adjustment);
                learningPlan.setAdjustments(adjustments);
            }

            progress.setLearningPlan(learningPlan);

            int result = progressMapper.updateById(progress);
            log.info("学习计划更新成功");
            return result > 0;

        } catch (Exception e) {
            log.error("更新学习计划失败", e);
            throw new ServiceException("更新学习计划失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean addLearningFeedback(Long progressId, LearningProgress.LearningFeedback feedback) {
        try {
            log.info("添加学习反馈，进度ID: {}", progressId);

            LearningProgress progress = progressMapper.selectById(progressId);
            if (progress == null) {
                throw new ServiceException("学习进度不存在");
            }

            List<LearningProgress.LearningFeedback> feedbackList = progress.getFeedbackList();
            if (feedbackList == null) {
                feedbackList = new ArrayList<>();
            }

            feedback.setFeedbackDate(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            feedbackList.add(feedback);

            progress.setFeedbackList(feedbackList);

            int result = progressMapper.updateById(progress);
            log.info("学习反馈添加成功");
            return result > 0;

        } catch (Exception e) {
            log.error("添加学习反馈失败", e);
            throw new ServiceException("添加学习反馈失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean updateMilestoneStatus(Long progressId, String milestone, boolean completed) {
        try {
            log.info("更新里程碑状态，进度ID: {}, 里程碑: {}, 完成: {}", progressId, milestone, completed);

            LearningProgress progress = progressMapper.selectById(progressId);
            if (progress == null) {
                throw new ServiceException("学习进度不存在");
            }

            List<String> completedMilestones = progress.getCompletedMilestones();
            if (completedMilestones == null) {
                completedMilestones = new ArrayList<>();
            }

            if (completed && !completedMilestones.contains(milestone)) {
                completedMilestones.add(milestone);
            } else if (!completed) {
                completedMilestones.remove(milestone);
            }

            progress.setCompletedMilestones(completedMilestones);

            int result = progressMapper.updateById(progress);
            log.info("里程碑状态更新成功");
            return result > 0;

        } catch (Exception e) {
            log.error("更新里程碑状态失败", e);
            throw new ServiceException("更新里程碑状态失败: " + e.getMessage());
        }
    }

    @Override
    public Double calculateLearningEfficiency(Long progressId) {
        try {
            LearningProgress progress = progressMapper.selectById(progressId);
            if (progress == null) {
                return 0.0;
            }

            return calculateEfficiencyScore(progress);

        } catch (Exception e) {
            log.error("计算学习效率失败", e);
            return 0.0;
        }
    }

    @Override
    public Map<String, Object> generateLearningReport(Long userId, String startDate, String endDate) {
        try {
            log.info("生成学习报告，用户ID: {}, 开始日期: {}, 结束日期: {}", userId, startDate, endDate);

            Map<String, Object> report = new HashMap<>();

            // 基础统计
            Map<String, Object> basicStats = getUserLearningStatistics(userId);
            report.put("basicStatistics", basicStats);

            // 学习效果评估
            Map<String, Object> effectiveness = getLearningEffectivenessAssessment(userId);
            report.put("effectiveness", effectiveness);

            // 时间统计
            Map<String, Object> timeStats = getStudyTimeStatistics(userId);
            report.put("timeStatistics", timeStats);

            // 学习趋势
            List<Map<String, Object>> trend = getLearningTrend(userId, 30);
            report.put("learningTrend", trend);

            // 最近完成的学习
            List<LearningProgress> completed = getCompletedLearning(userId, 10);
            report.put("recentCompletions", completed);

            // 正在进行的学习
            List<LearningProgress> ongoing = getOngoingLearning(userId);
            report.put("ongoingLearning", ongoing);

            // 超期项目
            List<LearningProgress> overdue = getOverdueLearning(userId);
            report.put("overdueLearning", overdue);

            // 生成时间
            report.put("generatedAt", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

            return report;

        } catch (Exception e) {
            log.error("生成学习报告失败", e);
            throw new ServiceException("生成学习报告失败: " + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> recommendLearningAdjustments(Long userId) {
        try {
            log.info("推荐学习内容调整，用户ID: {}", userId);

            List<Map<String, Object>> recommendations = new ArrayList<>();

            // 获取用户学习数据
            List<LearningProgress> ongoingLearning = getOngoingLearning(userId);
            List<LearningProgress> overdueLearning = getOverdueLearning(userId);
            Map<String, Object> effectiveness = getLearningEffectivenessAssessment(userId);

            // 基于超期学习推荐调整
            if (!overdueLearning.isEmpty()) {
                Map<String, Object> recommendation = new HashMap<>();
                recommendation.put("type", "overdue_adjustment");
                recommendation.put("title", "调整超期学习项目");
                recommendation.put("description", "您有" + overdueLearning.size() + "个学习项目已超期，建议重新安排学习计划");
                recommendation.put("priority", "high");
                recommendation.put("actionItems", Arrays.asList(
                    "重新评估学习目标的现实性",
                    "调整每日学习时间安排",
                    "考虑暂停部分低优先级项目"
                ));
                recommendations.add(recommendation);
            }

            // 基于学习效果推荐调整
            Double avgEffectiveness = (Double) effectiveness.get("avg_effectiveness");
            if (avgEffectiveness != null && avgEffectiveness < 3.0) {
                Map<String, Object> recommendation = new HashMap<>();
                recommendation.put("type", "effectiveness_improvement");
                recommendation.put("title", "提升学习效果");
                recommendation.put("description", "您的平均学习效果评分较低，建议调整学习方法");
                recommendation.put("priority", "medium");
                recommendation.put("actionItems", Arrays.asList(
                    "尝试不同的学习方法",
                    "增加实践练习时间",
                    "寻求学习伙伴或导师指导"
                ));
                recommendations.add(recommendation);
            }

            // 基于学习负荷推荐调整
            if (ongoingLearning.size() > 5) {
                Map<String, Object> recommendation = new HashMap<>();
                recommendation.put("type", "workload_adjustment");
                recommendation.put("title", "优化学习负荷");
                recommendation.put("description", "您同时进行的学习项目较多，建议优化学习负荷");
                recommendation.put("priority", "medium");
                recommendation.put("actionItems", Arrays.asList(
                    "专注于2-3个核心学习项目",
                    "暂停部分非紧急项目",
                    "制定更清晰的优先级排序"
                ));
                recommendations.add(recommendation);
            }

            return recommendations;

        } catch (Exception e) {
            log.error("推荐学习内容调整失败", e);
            throw new ServiceException("推荐学习内容调整失败: " + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getLearningReminders(Long userId) {
        try {
            List<Map<String, Object>> reminders = new ArrayList<>();

            // 获取需要提醒的学习项目
            List<LearningProgress> needReminder = progressMapper.selectItemsNeedingReminder();

            for (LearningProgress progress : needReminder) {
                if (progress.getUserId().equals(userId)) {
                    Map<String, Object> reminder = new HashMap<>();
                    reminder.put("progressId", progress.getId());
                    reminder.put("title", "学习提醒");
                    reminder.put("message", "您有学习项目需要继续");
                    reminder.put("lastStudyTime", progress.getLastStudyTime());
                    reminder.put("completionPercentage", progress.getCompletionPercentage());
                    reminders.add(reminder);
                }
            }

            return reminders;

        } catch (Exception e) {
            log.error("获取学习提醒失败", e);
            throw new ServiceException("获取学习提醒失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean setLearningReminder(Long progressId, LearningProgress.ReminderSettings reminderSettings) {
        try {
            log.info("设置学习提醒，进度ID: {}", progressId);

            LearningProgress progress = progressMapper.selectById(progressId);
            if (progress == null) {
                throw new ServiceException("学习进度不存在");
            }

            LearningProgress.LearningPlan learningPlan = progress.getLearningPlan();
            if (learningPlan == null) {
                learningPlan = new LearningProgress.LearningPlan();
            }

            learningPlan.setReminderSettings(reminderSettings);
            progress.setLearningPlan(learningPlan);

            int result = progressMapper.updateById(progress);
            log.info("学习提醒设置成功");
            return result > 0;

        } catch (Exception e) {
            log.error("设置学习提醒失败", e);
            throw new ServiceException("设置学习提醒失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public int batchUpdateLearningStatistics(Long userId) {
        try {
            log.info("批量更新学习统计，用户ID: {}", userId);

            List<LearningProgress> progressList = progressMapper.selectByUserId(userId);
            int updateCount = 0;

            for (LearningProgress progress : progressList) {
                updateLearningStatistics(progress, 0);
                progressMapper.updateById(progress);
                updateCount++;
            }

            log.info("批量更新学习统计完成，更新数量: {}", updateCount);
            return updateCount;

        } catch (Exception e) {
            log.error("批量更新学习统计失败", e);
            throw new ServiceException("批量更新学习统计失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean deleteLearningProgress(Long progressId, Long userId) {
        try {
            log.info("删除学习进度，进度ID: {}, 用户ID: {}", progressId, userId);

            LearningProgress progress = progressMapper.selectById(progressId);
            if (progress == null || !progress.getUserId().equals(userId)) {
                throw new ServiceException("学习进度不存在或无权限");
            }

            int result = progressMapper.deleteById(progressId);
            log.info("学习进度删除成功");
            return result > 0;

        } catch (Exception e) {
            log.error("删除学习进度失败", e);
            throw new ServiceException("删除学习进度失败: " + e.getMessage());
        }
    }

    @Override
    public String exportLearningData(Long userId, String format) {
        try {
            log.info("导出学习数据，用户ID: {}, 格式: {}", userId, format);

            // 获取用户所有学习数据
            List<LearningProgress> progressList = progressMapper.selectByUserId(userId);
            Map<String, Object> statistics = getUserLearningStatistics(userId);

            // 生成导出文件名
            String fileName = String.format("learning_data_%s_%s.%s",
                userId,
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")),
                format.toLowerCase());

            String filePath = "/data/exports/" + fileName;

            // 根据格式生成文件内容
            String content;
            if ("json".equalsIgnoreCase(format)) {
                Map<String, Object> exportData = new HashMap<>();
                exportData.put("statistics", statistics);
                exportData.put("progressList", progressList);
                exportData.put("exportTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

                // 这里应该使用JSON库序列化，简化处理
                content = exportData.toString();
            } else {
                // CSV格式
                StringBuilder csv = new StringBuilder();
                csv.append("ID,学习路径ID,资源ID,状态,完成度,开始时间,完成时间,学习时长\n");

                for (LearningProgress progress : progressList) {
                    csv.append(String.format("%d,%s,%s,%s,%d,%s,%s,%d\n",
                        progress.getId(),
                        progress.getLearningPathId(),
                        progress.getResourceId(),
                        progress.getStatus(),
                        progress.getCompletionPercentage(),
                        progress.getStartTime(),
                        progress.getCompletionTime(),
                        progress.getActualStudyMinutes()));
                }

                content = csv.toString();
            }

            // 创建目录并写入文件
            java.nio.file.Files.createDirectories(java.nio.file.Paths.get("/data/exports"));
            java.nio.file.Files.write(java.nio.file.Paths.get(filePath), content.getBytes("UTF-8"));

            log.info("学习数据导出成功，文件路径: {}", filePath);
            return filePath;

        } catch (Exception e) {
            log.error("导出学习数据失败", e);
            throw new ServiceException("导出学习数据失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> analyzeLearningData(Long userId) {
        try {
            log.info("分析学习数据，用户ID: {}", userId);

            Map<String, Object> analysis = new HashMap<>();

            // 基础统计
            Map<String, Object> basicStats = getUserLearningStatistics(userId);
            analysis.put("basicStatistics", basicStats);

            // 学习模式分析
            List<LearningProgress> progressList = progressMapper.selectByUserId(userId);
            Map<String, Object> patterns = analyzeLearningPatterns(progressList);
            analysis.put("learningPatterns", patterns);

            // 效率分析
            Map<String, Object> efficiency = analyzeEfficiency(progressList);
            analysis.put("efficiencyAnalysis", efficiency);

            // 时间分布分析
            Map<String, Object> timeDistribution = analyzeTimeDistribution(progressList);
            analysis.put("timeDistribution", timeDistribution);

            // 完成率分析
            Map<String, Object> completionAnalysis = analyzeCompletionRates(progressList);
            analysis.put("completionAnalysis", completionAnalysis);

            return analysis;

        } catch (Exception e) {
            log.error("分析学习数据失败", e);
            throw new ServiceException("分析学习数据失败: " + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getLearningAchievements(Long userId) {
        try {
            log.info("获取学习成就，用户ID: {}", userId);

            List<Map<String, Object>> achievements = new ArrayList<>();

            // 获取用户学习数据
            Map<String, Object> stats = getUserLearningStatistics(userId);
            List<LearningProgress> completed = getCompletedLearning(userId, 100);

            // 完成数量成就
            Integer completedCount = (Integer) stats.get("completed_items");
            if (completedCount != null) {
                if (completedCount >= 1) {
                    achievements.add(createAchievement("first_completion", "初学者", "完成第一个学习项目", "bronze"));
                }
                if (completedCount >= 10) {
                    achievements.add(createAchievement("ten_completions", "学习达人", "完成10个学习项目", "silver"));
                }
                if (completedCount >= 50) {
                    achievements.add(createAchievement("fifty_completions", "学习专家", "完成50个学习项目", "gold"));
                }
            }

            // 学习时长成就
            Integer totalMinutes = (Integer) stats.get("total_study_minutes");
            if (totalMinutes != null) {
                if (totalMinutes >= 600) { // 10小时
                    achievements.add(createAchievement("ten_hours", "时间投资者", "累计学习10小时", "bronze"));
                }
                if (totalMinutes >= 6000) { // 100小时
                    achievements.add(createAchievement("hundred_hours", "学习马拉松", "累计学习100小时", "silver"));
                }
                if (totalMinutes >= 36000) { // 600小时
                    achievements.add(createAchievement("six_hundred_hours", "学习大师", "累计学习600小时", "gold"));
                }
            }

            // 连续学习成就
            int maxConsecutiveDays = calculateMaxConsecutiveDays(completed);
            if (maxConsecutiveDays >= 7) {
                achievements.add(createAchievement("seven_days", "坚持不懈", "连续学习7天", "bronze"));
            }
            if (maxConsecutiveDays >= 30) {
                achievements.add(createAchievement("thirty_days", "习惯养成", "连续学习30天", "silver"));
            }
            if (maxConsecutiveDays >= 100) {
                achievements.add(createAchievement("hundred_days", "学习传奇", "连续学习100天", "gold"));
            }

            return achievements;

        } catch (Exception e) {
            log.error("获取学习成就失败", e);
            throw new ServiceException("获取学习成就失败: " + e.getMessage());
        }
    }

    // ========== 私有分析方法 ==========

    private Map<String, Object> analyzeLearningPatterns(List<LearningProgress> progressList) {
        Map<String, Object> patterns = new HashMap<>();

        // 学习时间偏好分析
        Map<String, Integer> timePreferences = new HashMap<>();
        for (LearningProgress progress : progressList) {
            if (progress.getLastStudyTime() != null) {
                int hour = progress.getLastStudyTime().getHour();
                String timeSlot;
                if (hour < 6) timeSlot = "深夜";
                else if (hour < 12) timeSlot = "上午";
                else if (hour < 18) timeSlot = "下午";
                else timeSlot = "晚上";

                timePreferences.put(timeSlot, timePreferences.getOrDefault(timeSlot, 0) + 1);
            }
        }
        patterns.put("timePreferences", timePreferences);

        // 学习持续时间分析
        List<Integer> sessionDurations = progressList.stream()
            .filter(p -> p.getActualStudyMinutes() != null && p.getActualStudyMinutes() > 0)
            .map(LearningProgress::getActualStudyMinutes)
            .collect(Collectors.toList());

        if (!sessionDurations.isEmpty()) {
            double avgDuration = sessionDurations.stream().mapToInt(Integer::intValue).average().orElse(0);
            patterns.put("averageSessionDuration", avgDuration);
            patterns.put("maxSessionDuration", Collections.max(sessionDurations));
            patterns.put("minSessionDuration", Collections.min(sessionDurations));
        }

        return patterns;
    }

    private Map<String, Object> analyzeEfficiency(List<LearningProgress> progressList) {
        Map<String, Object> efficiency = new HashMap<>();

        List<Double> efficiencyScores = progressList.stream()
            .filter(p -> p.getLearningStatistics() != null && p.getLearningStatistics().getEfficiencyScore() != null)
            .map(p -> p.getLearningStatistics().getEfficiencyScore())
            .collect(Collectors.toList());

        if (!efficiencyScores.isEmpty()) {
            double avgEfficiency = efficiencyScores.stream().mapToDouble(Double::doubleValue).average().orElse(0);
            efficiency.put("averageEfficiency", avgEfficiency);
            efficiency.put("maxEfficiency", Collections.max(efficiencyScores));
            efficiency.put("minEfficiency", Collections.min(efficiencyScores));

            // 效率趋势
            efficiency.put("efficiencyTrend", efficiencyScores);
        }

        return efficiency;
    }

    private Map<String, Object> analyzeTimeDistribution(List<LearningProgress> progressList) {
        Map<String, Object> distribution = new HashMap<>();

        // 按状态分布学习时间
        Map<String, Integer> timeByStatus = new HashMap<>();
        for (LearningProgress progress : progressList) {
            String status = progress.getStatus();
            Integer minutes = progress.getActualStudyMinutes();
            if (minutes != null) {
                timeByStatus.put(status, timeByStatus.getOrDefault(status, 0) + minutes);
            }
        }
        distribution.put("timeByStatus", timeByStatus);

        return distribution;
    }

    private Map<String, Object> analyzeCompletionRates(List<LearningProgress> progressList) {
        Map<String, Object> completion = new HashMap<>();

        long totalItems = progressList.size();
        long completedItems = progressList.stream()
            .filter(p -> "completed".equals(p.getStatus()))
            .count();

        double completionRate = totalItems > 0 ? (double) completedItems / totalItems * 100 : 0;
        completion.put("overallCompletionRate", completionRate);
        completion.put("totalItems", totalItems);
        completion.put("completedItems", completedItems);

        return completion;
    }

    private Map<String, Object> createAchievement(String id, String name, String description, String level) {
        Map<String, Object> achievement = new HashMap<>();
        achievement.put("id", id);
        achievement.put("name", name);
        achievement.put("description", description);
        achievement.put("level", level);
        achievement.put("unlockedAt", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        return achievement;
    }

    private int calculateMaxConsecutiveDays(List<LearningProgress> completed) {
        if (completed.isEmpty()) return 0;

        // 简化实现：基于完成时间计算连续天数
        List<LocalDateTime> completionDates = completed.stream()
            .filter(p -> p.getCompletionTime() != null)
            .map(LearningProgress::getCompletionTime)
            .sorted()
            .collect(Collectors.toList());

        if (completionDates.isEmpty()) return 0;

        int maxConsecutive = 1;
        int currentConsecutive = 1;

        for (int i = 1; i < completionDates.size(); i++) {
            LocalDateTime prev = completionDates.get(i - 1);
            LocalDateTime curr = completionDates.get(i);

            long daysBetween = java.time.temporal.ChronoUnit.DAYS.between(prev.toLocalDate(), curr.toLocalDate());

            if (daysBetween == 1) {
                currentConsecutive++;
                maxConsecutive = Math.max(maxConsecutive, currentConsecutive);
            } else {
                currentConsecutive = 1;
            }
        }

        return maxConsecutive;
    }

    @Override
    public List<Map<String, Object>> getPersonalizedRecommendations(Long userId) {
        try {
            log.info("获取个性化推荐，用户ID: {}", userId);

            List<Map<String, Object>> recommendations = new ArrayList<>();

            // 获取用户学习数据
            Map<String, Object> userStats = getUserLearningStatistics(userId);
            List<LearningProgress> completedLearning = getCompletedLearning(userId, 20);
            List<LearningProgress> ongoingLearning = getOngoingLearning(userId);

            // 基于完成的学习推荐相关内容
            if (!completedLearning.isEmpty()) {
                Map<String, Object> recommendation = new HashMap<>();
                recommendation.put("type", "related_content");
                recommendation.put("title", "相关学习内容推荐");
                recommendation.put("description", "基于您已完成的学习内容，为您推荐相关的进阶课程");
                recommendation.put("priority", "medium");
                recommendations.add(recommendation);
            }

            // 基于学习习惯推荐最佳学习时间
            Map<String, Object> timeRecommendation = new HashMap<>();
            timeRecommendation.put("type", "optimal_time");
            timeRecommendation.put("title", "最佳学习时间推荐");
            timeRecommendation.put("description", "根据您的学习数据分析，建议在特定时间段进行学习");
            timeRecommendation.put("priority", "low");
            recommendations.add(timeRecommendation);

            // 基于学习进度推荐调整
            if (!ongoingLearning.isEmpty()) {
                double avgCompletion = ongoingLearning.stream()
                    .mapToInt(p -> p.getCompletionPercentage() != null ? p.getCompletionPercentage() : 0)
                    .average()
                    .orElse(0.0);

                if (avgCompletion < 30) {
                    Map<String, Object> recommendation = new HashMap<>();
                    recommendation.put("type", "progress_boost");
                    recommendation.put("title", "学习进度提升建议");
                    recommendation.put("description", "您的学习进度较慢，建议调整学习策略");
                    recommendation.put("priority", "high");
                    recommendations.add(recommendation);
                }
            }

            return recommendations;

        } catch (Exception e) {
            log.error("获取个性化推荐失败", e);
            throw new ServiceException("获取个性化推荐失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean batchUpdateProgress(List<Long> progressIds, Map<String, Object> updateData) {
        try {
            log.info("批量更新学习进度，进度数量: {}", progressIds.size());

            for (Long progressId : progressIds) {
                LearningProgress progress = progressMapper.selectById(progressId);
                if (progress != null) {
                    // 根据更新数据更新进度
                    if (updateData.containsKey("status")) {
                        progress.setStatus((String) updateData.get("status"));
                    }
                    if (updateData.containsKey("completionPercentage")) {
                        progress.setCompletionPercentage((Integer) updateData.get("completionPercentage"));
                    }
                    if (updateData.containsKey("notes")) {
                        progress.setNotes((String) updateData.get("notes"));
                    }

                    progress.setLastStudyTime(LocalDateTime.now());
                    progressMapper.updateById(progress);
                }
            }

            log.info("批量更新学习进度成功");
            return true;

        } catch (Exception e) {
            log.error("批量更新学习进度失败", e);
            throw new ServiceException("批量更新学习进度失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean importLearningData(Long userId, Map<String, Object> importData) {
        try {
            log.info("导入学习数据，用户ID: {}", userId);

            @SuppressWarnings("unchecked")
            List<Map<String, Object>> progressData = (List<Map<String, Object>>) importData.get("learningProgress");

            if (progressData != null) {
                for (Map<String, Object> data : progressData) {
                    // 创建新的学习进度记录
                    LearningProgress progress = new LearningProgress();
                    progress.setUserId(userId);
                    // 设置其他字段...

                    progressMapper.insert(progress);
                }
            }

            log.info("学习数据导入成功");
            return true;

        } catch (Exception e) {
            log.error("导入学习数据失败", e);
            throw new ServiceException("导入学习数据失败: " + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getProgressComparison(Long userId, List<Long> compareUserIds) {
        try {
            log.info("获取学习进度对比，用户ID: {}, 对比用户: {}", userId, compareUserIds);

            List<Map<String, Object>> comparison = new ArrayList<>();

            // 获取当前用户统计
            Map<String, Object> userStats = getUserLearningStatistics(userId);
            userStats.put("userId", userId);
            userStats.put("isCurrentUser", true);
            comparison.add(userStats);

            // 获取对比用户统计
            for (Long compareUserId : compareUserIds) {
                Map<String, Object> compareStats = getUserLearningStatistics(compareUserId);
                compareStats.put("userId", compareUserId);
                compareStats.put("isCurrentUser", false);
                comparison.add(compareStats);
            }

            return comparison;

        } catch (Exception e) {
            log.error("获取学习进度对比失败", e);
            throw new ServiceException("获取学习进度对比失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getLearningInsights(Long userId) {
        try {
            log.info("获取学习洞察，用户ID: {}", userId);

            Map<String, Object> insights = new HashMap<>();

            // 学习模式分析
            Map<String, Object> patterns = analyzeLearningPatterns(userId);
            insights.put("learningPatterns", patterns);

            // 效率分析
            Map<String, Object> efficiency = analyzeEfficiency(userId);
            insights.put("efficiency", efficiency);

            // 改进建议
            List<Map<String, Object>> suggestions = generateImprovementSuggestions(userId);
            insights.put("improvementSuggestions", suggestions);

            // 成就分析
            Map<String, Object> achievements = analyzeAchievements(userId);
            insights.put("achievements", achievements);

            return insights;

        } catch (Exception e) {
            log.error("获取学习洞察失败", e);
            throw new ServiceException("获取学习洞察失败: " + e.getMessage());
        }
    }

// ========== 私有分析方法 ==========

    /**
     * 分析学习模式
     */
    private Map<String, Object> analyzeLearningPatterns(Long userId) {
        Map<String, Object> patterns = new HashMap<>();

        try {
            // 获取学习趋势数据
            List<Map<String, Object>> trend = getLearningTrend(userId, 30);

            // 分析学习频率
            double avgDailyMinutes = trend.stream()
                .mapToDouble(day -> ((Number) day.getOrDefault("daily_minutes", 0)).doubleValue())
                .average()
                .orElse(0.0);

            patterns.put("averageDailyMinutes", avgDailyMinutes);
            patterns.put("learningFrequency", trend.size() > 20 ? "高频" : trend.size() > 10 ? "中频" : "低频");

            // 分析学习时间偏好
            // 这里可以添加更复杂的时间模式分析
            patterns.put("preferredTimePattern", "待分析");

        } catch (Exception e) {
            log.warn("分析学习模式失败", e);
        }

        return patterns;
    }

    /**
     * 分析学习效率
     */
    private Map<String, Object> analyzeEfficiency(Long userId) {
        Map<String, Object> efficiency = new HashMap<>();

        try {
            List<LearningProgress> completedLearning = getCompletedLearning(userId, 50);

            if (!completedLearning.isEmpty()) {
                double avgEfficiency = completedLearning.stream()
                    .filter(p -> p.getLearningStatistics() != null && p.getLearningStatistics().getEfficiencyScore() != null)
                    .mapToDouble(p -> p.getLearningStatistics().getEfficiencyScore())
                    .average()
                    .orElse(0.0);

                efficiency.put("averageEfficiencyScore", avgEfficiency);
                efficiency.put("efficiencyLevel", avgEfficiency > 4.0 ? "优秀" : avgEfficiency > 3.0 ? "良好" : "需改进");
            }

        } catch (Exception e) {
            log.warn("分析学习效率失败", e);
        }

        return efficiency;
    }

    /**
     * 生成改进建议
     */
    private List<Map<String, Object>> generateImprovementSuggestions(Long userId) {
        List<Map<String, Object>> suggestions = new ArrayList<>();

        try {
            // 基于学习数据生成个性化建议
            Map<String, Object> userStats = getUserLearningStatistics(userId);

            // 示例建议
            Map<String, Object> suggestion1 = new HashMap<>();
            suggestion1.put("category", "时间管理");
            suggestion1.put("title", "优化学习时间安排");
            suggestion1.put("description", "建议制定固定的学习时间表，提高学习效率");
            suggestion1.put("priority", "medium");
            suggestions.add(suggestion1);

        } catch (Exception e) {
            log.warn("生成改进建议失败", e);
        }

        return suggestions;
    }

    /**
     * 分析学习成就
     */
    private Map<String, Object> analyzeAchievements(Long userId) {
        Map<String, Object> achievements = new HashMap<>();

        try {
            List<LearningProgress> completedLearning = getCompletedLearning(userId, 100);
            Map<String, Object> userStats = getUserLearningStatistics(userId);

            achievements.put("totalCompletedCourses", completedLearning.size());
            achievements.put("totalStudyTime", userStats.get("total_study_minutes"));
            achievements.put("averageRating", userStats.get("avg_satisfaction"));

            // 计算成就等级
            int completedCount = completedLearning.size();
            String achievementLevel = completedCount > 50 ? "专家" :
                completedCount > 20 ? "进阶" :
                    completedCount > 5 ? "入门" : "新手";
            achievements.put("achievementLevel", achievementLevel);

        } catch (Exception e) {
            log.warn("分析学习成就失败", e);
        }

        return achievements;
    }
}
