package org.dromara.app.engine;

import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.Achievement;
import org.dromara.app.domain.dto.TrackEventDto;
import org.dromara.app.service.IUserBehaviorService;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 成就规则引擎
 * 负责解析和执行成就的触发条件
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AchievementRuleEngine {

    private final IUserBehaviorService userBehaviorService;

    /**
     * 检查成就条件是否满足
     *
     * @param userId        用户ID
     * @param achievement   成就信息
     * @param trackEventDto 用户行为事件
     * @return 是否满足条件
     */
    public boolean checkAchievementCondition(Long userId, Achievement achievement, TrackEventDto trackEventDto) {
        try {
            // 解析触发条件
            Map<String, Object> triggerCondition = JSONUtil.toBean(achievement.getTriggerCondition(), Map.class);
            String behaviorType = (String) triggerCondition.get("behaviorType");
            
            // 检查是否匹配当前事件类型
            if (!trackEventDto.getEventType().equals(behaviorType)) {
                return false;
            }
            
            // 根据成就类型执行不同的检查逻辑
            return executeRule(userId, achievement.getAchievementType(), triggerCondition, trackEventDto);
            
        } catch (Exception e) {
            log.error("检查成就条件失败: userId={}, achievementId={}", userId, achievement.getId(), e);
            return false;
        }
    }

    /**
     * 计算成就进度
     *
     * @param userId        用户ID
     * @param achievement   成就信息
     * @param trackEventDto 用户行为事件
     * @return 进度信息 [当前值, 目标值, 进度百分比]
     */
    public AchievementProgress calculateProgress(Long userId, Achievement achievement, TrackEventDto trackEventDto) {
        try {
            Map<String, Object> triggerCondition = JSONUtil.toBean(achievement.getTriggerCondition(), Map.class);
            
            long currentValue = getCurrentValue(userId, triggerCondition);
            long targetValue = getTargetValue(triggerCondition);
            double progressPercentage = Math.min(100.0, (double) currentValue / targetValue * 100);
            
            return new AchievementProgress(currentValue, targetValue, progressPercentage);
            
        } catch (Exception e) {
            log.error("计算成就进度失败: userId={}, achievementId={}", userId, achievement.getId(), e);
            return new AchievementProgress(0, 1, 0.0);
        }
    }

    /**
     * 执行具体的规则检查
     */
    private boolean executeRule(Long userId, String achievementType, Map<String, Object> triggerCondition, TrackEventDto trackEventDto) {
        switch (achievementType) {
            case "LOGIN":
                return checkLoginRule(userId, triggerCondition);
            case "LEARNING":
                return checkLearningRule(userId, triggerCondition);
            case "SOCIAL":
                return checkSocialRule(userId, triggerCondition);
            case "TIME":
                return checkTimeRule(userId, triggerCondition);
            case "CUSTOM":
                return checkCustomRule(userId, triggerCondition, trackEventDto);
            default:
                log.warn("未知的成就类型: {}", achievementType);
                return false;
        }
    }

    /**
     * 检查登录类成就规则
     */
    private boolean checkLoginRule(Long userId, Map<String, Object> triggerCondition) {
        String behaviorType = (String) triggerCondition.get("behaviorType");
        
        if ("LOGIN".equals(behaviorType)) {
            if (triggerCondition.containsKey("count")) {
                Integer requiredCount = (Integer) triggerCondition.get("count");
                Long actualCount = userBehaviorService.countByUserIdAndBehaviorType(userId, "LOGIN");
                return actualCount >= requiredCount;
            }
            
            if (triggerCondition.containsKey("consecutiveDays")) {
                Integer requiredDays = (Integer) triggerCondition.get("consecutiveDays");
                Integer actualDays = userBehaviorService.getConsecutiveLoginDays(userId);
                return actualDays >= requiredDays;
            }
        }
        
        return false;
    }

    /**
     * 检查学习类成就规则
     */
    private boolean checkLearningRule(Long userId, Map<String, Object> triggerCondition) {
        String behaviorType = (String) triggerCondition.get("behaviorType");
        
        if ("VIDEO_WATCH".equals(behaviorType)) {
            Integer requiredCount = (Integer) triggerCondition.get("count");
            Long actualCount = userBehaviorService.countByUserIdAndBehaviorType(userId, "VIDEO_WATCH");
            return actualCount >= requiredCount;
        }
        
        return false;
    }

    /**
     * 检查社交类成就规则
     */
    private boolean checkSocialRule(Long userId, Map<String, Object> triggerCondition) {
        String behaviorType = (String) triggerCondition.get("behaviorType");
        Integer requiredCount = (Integer) triggerCondition.get("count");
        
        if ("COMMENT".equals(behaviorType)) {
            Long actualCount = userBehaviorService.countByUserIdAndBehaviorType(userId, "COMMENT");
            return actualCount >= requiredCount;
        }
        
        if ("LIKE".equals(behaviorType)) {
            Long actualCount = userBehaviorService.countByUserIdAndBehaviorType(userId, "LIKE");
            return actualCount >= requiredCount;
        }
        
        return false;
    }

    /**
     * 检查时间类成就规则
     */
    private boolean checkTimeRule(Long userId, Map<String, Object> triggerCondition) {
        String behaviorType = (String) triggerCondition.get("behaviorType");
        
        if ("STUDY_TIME".equals(behaviorType)) {
            Integer requiredMinutes = (Integer) triggerCondition.get("totalMinutes");
            Long actualMinutes = userBehaviorService.getTotalStudyMinutes(userId);
            return actualMinutes >= requiredMinutes;
        }
        
        return false;
    }

    /**
     * 检查自定义成就规则
     */
    private boolean checkCustomRule(Long userId, Map<String, Object> triggerCondition, TrackEventDto trackEventDto) {
        // 这里可以实现更复杂的自定义规则
        // 例如：组合条件、时间范围限制、特定数据条件等
        
        // 示例：检查特定时间范围内的行为
        if (triggerCondition.containsKey("timeRange")) {
            // 实现时间范围检查逻辑
        }
        
        // 示例：检查组合条件
        if (triggerCondition.containsKey("combinedConditions")) {
            // 实现组合条件检查逻辑
        }
        
        return false;
    }

    /**
     * 获取当前数值
     */
    private long getCurrentValue(Long userId, Map<String, Object> triggerCondition) {
        String behaviorType = (String) triggerCondition.get("behaviorType");
        
        switch (behaviorType) {
            case "LOGIN":
                if (triggerCondition.containsKey("consecutiveDays")) {
                    return userBehaviorService.getConsecutiveLoginDays(userId);
                } else {
                    return userBehaviorService.countByUserIdAndBehaviorType(userId, "LOGIN");
                }
            case "STUDY_TIME":
                return userBehaviorService.getTotalStudyMinutes(userId);
            default:
                return userBehaviorService.countByUserIdAndBehaviorType(userId, behaviorType);
        }
    }

    /**
     * 获取目标数值
     */
    private long getTargetValue(Map<String, Object> triggerCondition) {
        if (triggerCondition.containsKey("count")) {
            return ((Integer) triggerCondition.get("count")).longValue();
        }
        if (triggerCondition.containsKey("consecutiveDays")) {
            return ((Integer) triggerCondition.get("consecutiveDays")).longValue();
        }
        if (triggerCondition.containsKey("totalMinutes")) {
            return ((Integer) triggerCondition.get("totalMinutes")).longValue();
        }
        return 1L;
    }

    /**
     * 成就进度信息
     */
    public static class AchievementProgress {
        private final long currentValue;
        private final long targetValue;
        private final double progressPercentage;

        public AchievementProgress(long currentValue, long targetValue, double progressPercentage) {
            this.currentValue = currentValue;
            this.targetValue = targetValue;
            this.progressPercentage = progressPercentage;
        }

        public long getCurrentValue() { return currentValue; }
        public long getTargetValue() { return targetValue; }
        public double getProgressPercentage() { return progressPercentage; }
        public boolean isCompleted() { return progressPercentage >= 100.0; }
    }

}
