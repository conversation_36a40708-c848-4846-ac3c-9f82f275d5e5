package org.dromara.app.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.app.domain.*;
import org.dromara.app.domain.bo.JobQueryBo;

import org.dromara.app.domain.vo.InterviewResponseVo;
import org.dromara.app.domain.vo.JobCategoryVo;
import org.dromara.app.domain.vo.JobVo;
import org.dromara.app.mapper.*;
import org.dromara.app.service.IInterviewService;
import org.dromara.app.service.IAiEvaluationService;
import org.dromara.common.core.exception.ServiceException;

import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 面试服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class InterviewServiceImpl implements IInterviewService {

    private final JobMapper jobMapper;
    private final JobCategoryMapper jobCategoryMapper;
    private final InterviewModeMapper interviewModeMapper;
    private final InterviewSessionMapper interviewSessionMapper;
    private final JobFavoriteMapper jobFavoriteMapper;
    private final SearchKeywordMapper searchKeywordMapper;
    private final InterviewAnswerMapper interviewAnswerMapper;
    private final SessionQuestionMapper sessionQuestionMapper;
    private final InterviewResultMapper interviewResultMapper;
    private final InterviewQuestionMapper interviewQuestionMapper;
    private final IAiEvaluationService aiEvaluationService;

    @Override
    public TableDataInfo<InterviewResponseVo.JobListResponse> getJobList(JobQueryBo queryBo) {
        try {
            // 设置当前用户ID
            if (StpUtil.isLogin()) {
                queryBo.setUserId(StpUtil.getLoginIdAsLong());
            }

            // 记录搜索关键词
            if (StrUtil.isNotBlank(queryBo.getKeyword())) {
                recordSearchKeyword(queryBo.getKeyword());
            }
            // 校验分页参数
            if (queryBo.getPageNum() == null || queryBo.getPageNum() <= 0) {
                queryBo.setPageNum(1);
            }
            if (queryBo.getPageSize() == null || queryBo.getPageSize() <= 0) {
                queryBo.setPageSize(10); // 默认每页10条
            }

            // 处理排序参数
            if (StrUtil.isBlank(queryBo.getSortBy())) {
                queryBo.setSortBy("smart");
            }
            if (StrUtil.isBlank(queryBo.getSortOrder())) {
                queryBo.setSortOrder("desc");
            }

            // 分页查询岗位
            Page<JobVo> page = new Page<>(queryBo.getPageNum(), queryBo.getPageSize());
            IPage<JobVo> jobPage = jobMapper.selectJobPageWithFavorite(page, queryBo);

            // 查询分类信息
            List<JobCategoryVo> categories = jobCategoryMapper.selectCategoriesWithJobCount(true);

            // 查询推荐岗位
            List<JobVo> recommendedJobs = jobMapper.selectRecommendedJobs(3, queryBo.getUserId());

            // 构建响应数据
            InterviewResponseVo.JobListResponse response = new InterviewResponseVo.JobListResponse();

            // 转换分类信息
            List<InterviewResponseVo.CategoryInfo> categoryInfos = new ArrayList<>();
            for (JobCategoryVo categoryVo : categories) {
                InterviewResponseVo.CategoryInfo categoryInfo = new InterviewResponseVo.CategoryInfo();
                categoryInfo.setCategoryId(categoryVo.getId());
                categoryInfo.setName(categoryVo.getName());
                categoryInfo.setIcon(categoryVo.getIcon());
                categoryInfo.setJobCount(categoryVo.getJobCount());
                categoryInfo.setDescription(categoryVo.getDescription());
                categoryInfos.add(categoryInfo);
            }

            response.setJobs(jobPage.getRecords());
            response.setTotal(jobPage.getTotal());
            response.setPage((int) jobPage.getCurrent());
            response.setPageSize((int) jobPage.getSize());
            response.setHasMore(jobPage.getCurrent() < jobPage.getPages());
            response.setCategories(categoryInfos);
            response.setRecommendedJobs(recommendedJobs);

            return TableDataInfo.build(List.of(response));
        } catch (Exception e) {
            log.error("获取岗位列表失败", e);
            throw new ServiceException("获取岗位列表失败");
        }
    }

    @Override
    public InterviewResponseVo.CategoriesResponse getCategories(Boolean includeJobCount) {
        try {
            List<JobCategoryVo> categories = jobCategoryMapper.selectCategoriesWithJobCount(
                includeJobCount != null ? includeJobCount : false
            );

            // 转换分类信息
            List<InterviewResponseVo.CategoryInfo> categoryInfos = new ArrayList<>();
            for (JobCategoryVo categoryVo : categories) {
                InterviewResponseVo.CategoryInfo categoryInfo = new InterviewResponseVo.CategoryInfo();
                categoryInfo.setCategoryId(categoryVo.getId());
                categoryInfo.setName(categoryVo.getName());
                categoryInfo.setIcon(categoryVo.getIcon());
                categoryInfo.setJobCount(categoryVo.getJobCount());
                categoryInfo.setDescription(categoryVo.getDescription());
                categoryInfos.add(categoryInfo);
            }

            InterviewResponseVo.CategoriesResponse response = new InterviewResponseVo.CategoriesResponse();
            response.setCategories(categoryInfos);
            response.setTotalCategories(categories.size());

            return response;
        } catch (Exception e) {
            log.error("获取岗位分类失败", e);
            throw new ServiceException("获取岗位分类失败");
        }
    }

    @Override
    public InterviewResponseVo.InterviewModesResponse getInterviewModes() {
        try {
            LambdaQueryWrapper<InterviewMode> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(InterviewMode::getStatus, "0")
                .eq(InterviewMode::getDelFlag, "0")
                .orderByAsc(InterviewMode::getSortOrder)
                .orderByAsc(InterviewMode::getCreateTime);

            List<InterviewMode> modes = interviewModeMapper.selectList(wrapper);

            // 转换为ModeInfo
            List<InterviewResponseVo.ModeInfo> modeInfos = new ArrayList<>();
            for (InterviewMode mode : modes) {
                InterviewResponseVo.ModeInfo modeInfo = new InterviewResponseVo.ModeInfo();
                modeInfo.setModeId(mode.getId());
                modeInfo.setName(mode.getName());
                modeInfo.setDescription(mode.getDescription());
                modeInfo.setIcon(mode.getIcon());
                modeInfo.setQuestionCount(15); // 默认问题数量
                modeInfo.setDuration(mode.getDuration());
                modeInfo.setIsPremium(false); // 默认非付费
                modeInfo.setIsAvailable(true);
                modeInfos.add(modeInfo);
            }

            InterviewResponseVo.InterviewModesResponse response = new InterviewResponseVo.InterviewModesResponse();
            response.setModes(modeInfos);

            return response;
        } catch (Exception e) {
            log.error("获取面试模式失败", e);
            throw new ServiceException("获取面试模式失败");
        }
    }

    @Override
    public InterviewResponseVo.SearchSuggestionsResponse getSearchSuggestions(String keyword, Integer limit) {
        try {
            int searchLimit = limit != null ? limit : 5;

            // 获取搜索建议
            List<String> suggestionTexts = new ArrayList<>();
            if (StrUtil.isNotBlank(keyword)) {
                suggestionTexts = searchKeywordMapper.selectSuggestionsByKeyword(keyword, searchLimit);
            }

            // 转换为SuggestionItem
            List<InterviewResponseVo.SuggestionItem> suggestions = new ArrayList<>();
            for (String text : suggestionTexts) {
                InterviewResponseVo.SuggestionItem item = new InterviewResponseVo.SuggestionItem();
                item.setText(text);
                item.setType("keyword");
                item.setEntityId(null);
                suggestions.add(item);
            }

            InterviewResponseVo.SearchSuggestionsResponse response = new InterviewResponseVo.SearchSuggestionsResponse();
            response.setSuggestions(suggestions);
            response.setOriginalQuery(keyword);

            return response;
        } catch (Exception e) {
            log.error("获取搜索建议失败", e);
            throw new ServiceException("获取搜索建议失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public InterviewResponseVo.CreateSessionResponse createInterviewSession(Long jobId, String mode, String resumeUrl, List<String> customizedQuestions) {
        try {
            // 检查用户是否登录
            if (!StpUtil.isLogin()) {
                throw new ServiceException("请先登录");
            }

            Long userId = StpUtil.getLoginIdAsLong();

            // 检查岗位是否存在
            Job job = jobMapper.selectById(jobId);
            if (job == null || !"0".equals(job.getStatus()) || !"0".equals(job.getDelFlag())) {
                throw new ServiceException("岗位不存在或已下线");
            }

            // 检查面试模式是否存在
            InterviewMode interviewMode = interviewModeMapper.selectById(mode);
            if (interviewMode == null || !"0".equals(interviewMode.getStatus()) || !"0".equals(interviewMode.getDelFlag())) {
                throw new ServiceException("面试模式不存在或已停用");
            }

            // 生成会话ID和令牌
            String sessionId = "session_" + System.currentTimeMillis() + "_" + IdUtil.fastSimpleUUID();
            String sessionToken = "token_" + sessionId;

            // 创建面试会话
            InterviewSession session = new InterviewSession();
            session.setId(sessionId);
            session.setUserId(userId);
            session.setJobId(jobId);
            session.setModeId(mode);
            session.setSessionToken(sessionToken);
            session.setEstimatedDuration(interviewMode.getDuration());
            session.setQuestionCount(job.getQuestionCount());
            session.setResumeUrl(resumeUrl);
            session.setCustomizedQuestions(JSONUtil.toJsonStr(customizedQuestions));
            session.setStatus("created");
            session.setExpiresAt(LocalDateTime.now().plusHours(2)); // 2小时后过期

            interviewSessionMapper.insert(session);

            // 构建响应
            InterviewResponseVo.CreateSessionResponse response = new InterviewResponseVo.CreateSessionResponse();
            response.setSessionId(sessionId);
            response.setJobId(jobId);
            response.setMode(mode);
            response.setCreatedTime(LocalDateTime.now());
            response.setExpiryTime(session.getExpiresAt());
            response.setStatus(session.getStatus());

            return response;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("创建面试会话失败", e);
            throw new ServiceException("创建面试会话失败");
        } finally {
            log.info("创建面试会话结束");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void favoriteJob(Long jobId, Boolean isFavorited) {
        try {
            // 检查用户是否登录
            if (!StpUtil.isLogin()) {
                throw new ServiceException("请先登录");
            }

            Long userId = StpUtil.getLoginIdAsLong();

            // 检查岗位是否存在
            Job job = jobMapper.selectById(jobId);
            if (job == null || !"0".equals(job.getStatus()) || !"0".equals(job.getDelFlag())) {
                throw new ServiceException("岗位不存在或已下线");
            }

            LambdaQueryWrapper<JobFavorite> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(JobFavorite::getUserId, userId)
                .eq(JobFavorite::getJobId, jobId);

            JobFavorite existingFavorite = jobFavoriteMapper.selectOne(wrapper);

            if (isFavorited) {
                // 收藏
                if (existingFavorite == null) {
                    JobFavorite favorite = new JobFavorite();
                    favorite.setUserId(userId);
                    favorite.setJobId(jobId);
                    favorite.setCreateTime(LocalDateTime.now());
                    jobFavoriteMapper.insert(favorite);

                    // 更新岗位收藏数
                    jobMapper.updateFavoriteCount(jobId, 1);
                }
            } else {
                // 取消收藏
                if (existingFavorite != null) {
                    jobFavoriteMapper.deleteById(existingFavorite.getId());

                    // 更新岗位收藏数
                    jobMapper.updateFavoriteCount(jobId, -1);
                }
            }

            // 操作成功，无需返回值
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("收藏岗位操作失败", e);
            throw new ServiceException("收藏岗位操作失败");
        }
    }

    @Override
    public InterviewResponseVo.DeviceCheckResult checkDevice() {
        try {
            // 模拟设备检测
            InterviewResponseVo.DeviceCheckResult result = new InterviewResponseVo.DeviceCheckResult();
            result.setHasMicrophone(true);
            result.setHasCamera(true);
            result.setHasNetworkConnection(true);
            result.setNetworkStrength(5);
            result.setDeviceInfo(new HashMap<>());

            // 随机模拟一些检测失败的情况（用于测试）
            if (Math.random() < 0.1) {
                result.setHasCamera(false);
                result.setNetworkStrength(2);
            }

            return result;
        } catch (Exception e) {
            log.error("设备检测失败", e);
            throw new ServiceException("设备检测失败");
        }
    }

    @Override
    public InterviewResponseVo.StatisticsResponse getStatistics() {
        try {
            // 模拟统计数据
            Map<String, Integer> categoryDistribution = new HashMap<>();
            categoryDistribution.put("技术开发", 150);
            categoryDistribution.put("产品运营", 80);
            categoryDistribution.put("设计创意", 60);

            Map<String, Integer> difficultyDistribution = new HashMap<>();
            difficultyDistribution.put("初级", 120);
            difficultyDistribution.put("中级", 100);
            difficultyDistribution.put("高级", 70);

            // 构建响应
            InterviewResponseVo.StatisticsResponse response = new InterviewResponseVo.StatisticsResponse();
            response.setTotalJobs(290);
            response.setTotalQuestions(1500);
            response.setTotalUsers(5000);
            response.setTotalInterviews(8000);
            response.setCategoryDistribution(categoryDistribution);
            response.setDifficultyDistribution(difficultyDistribution);
            response.setWeeklyTrend(new ArrayList<>());

            return response;
        } catch (Exception e) {
            log.error("获取统计信息失败", e);
            throw new ServiceException("获取统计信息失败");
        }
    }

    @Override
    public InterviewResponseVo.HistoryListResponse getInterviewHistory(Integer page, Integer pageSize, String category, String status) {
        try {
            // 检查用户是否登录
            if (!StpUtil.isLogin()) {
                throw new ServiceException("请先登录");
            }

            Long userId = StpUtil.getLoginIdAsLong();

            // 使用优化的自定义查询方法，一次性完成关联查询
            Page<InterviewResult> pageQuery = new Page<>(page, pageSize);
            IPage<InterviewResult> pageResult = interviewResultMapper.selectHistoryPageWithJobInfo(
                pageQuery, userId, category, status);

            // 批量转换为响应对象，减少数据库查询次数
            List<InterviewResponseVo.InterviewRecord> records = batchConvertToInterviewRecords(pageResult.getRecords());

            InterviewResponseVo.HistoryListResponse response = new InterviewResponseVo.HistoryListResponse();
            response.setRecords(records);
            response.setTotal(pageResult.getTotal());
            response.setHasMore(pageResult.getCurrent() < pageResult.getPages());
            response.setPage(page);
            response.setPageSize(pageSize);

            return response;
        } catch (Exception e) {
            log.error("获取面试历史记录失败", e);
            throw new ServiceException("获取面试历史记录失败");
        }
    }

    @Override
    public InterviewResponseVo.Statistics getUserStatistics() {
        log.info("获取用户统计数据");

        try {
            // 检查用户是否登录
            if (!StpUtil.isLogin()) {
                throw new ServiceException("请先登录");
            }

            Long userId = StpUtil.getLoginIdAsLong();

            // 查询用户的面试记录
            List<InterviewResult> userResults = interviewResultMapper.selectList(
                new LambdaQueryWrapper<InterviewResult>()
                    .eq(InterviewResult::getUserId, userId)
                    .eq(InterviewResult::getDelFlag, "0")
            );

            // 计算统计数据
            InterviewResponseVo.Statistics statistics = new InterviewResponseVo.Statistics();
            statistics.setTotalInterviews(userResults.size());

            if (!userResults.isEmpty()) {
                // 计算平均分
                double averageScore = userResults.stream()
                    .mapToInt(InterviewResult::getTotalScore)
                    .average()
                    .orElse(0.0);
                statistics.setAverageScore(averageScore);

                // 计算月度提升（模拟数据）
                statistics.setMonthlyImprovement(5);
                statistics.setImprovementPercent(12.5);

                // 根据平均分确定等级
                String currentLevel;
                if (averageScore >= 90) {
                    currentLevel = "专家级";
                } else if (averageScore >= 80) {
                    currentLevel = "高级";
                } else if (averageScore >= 70) {
                    currentLevel = "中级";
                } else if (averageScore >= 60) {
                    currentLevel = "初级";
                } else {
                    currentLevel = "入门级";
                }
                statistics.setCurrentLevel(currentLevel);

                // 计算下一等级进度（模拟）
                int nextLevelProgress = (int) ((averageScore % 10) * 10);
                statistics.setNextLevelProgress(nextLevelProgress);
            } else {
                statistics.setAverageScore(0.0);
                statistics.setMonthlyImprovement(0);
                statistics.setImprovementPercent(0.0);
                statistics.setCurrentLevel("入门级");
                statistics.setNextLevelProgress(0);
            }

            return statistics;
        } catch (Exception e) {
            log.error("获取用户统计数据失败", e);
            throw new ServiceException("获取用户统计数据失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public InterviewResponseVo.JobDetailResponse getJobDetail(Long jobId) {
        log.info("获取岗位详情，jobId: {}", jobId);

        // 查询岗位基本信息
        Job job = jobMapper.selectById(jobId);
        if (job == null) {
            throw new ServiceException("岗位不存在");
        }

        // 查询岗位分类
        JobCategory category = jobCategoryMapper.selectById(job.getCategoryId());

        // 构建响应对象
        InterviewResponseVo.JobDetailResponse response = new InterviewResponseVo.JobDetailResponse();
        response.setId(job.getId());  // 改为setId
        response.setName(job.getName());  // 改为setName
        response.setCompany(job.getCompany());
        response.setLogo(job.getLogo());  // 改为setLogo
        response.setDescription(job.getDescription());
        response.setDifficulty(job.getDifficulty() != null ? job.getDifficulty() : 1);  // 改为Integer类型
        response.setQuestionCount(job.getQuestionCount());
        response.setInterviewCount(job.getInterviewers() != null ? job.getInterviewers() : 0); // 使用数据库中的面试人数
        response.setRating(4.5);
        response.setIsFavorited(false);
        response.setCreatedTime(job.getCreateTime() != null ?
            DateUtil.toLocalDateTime(job.getCreateTime()) : LocalDateTime.now());
        response.setUpdatedTime(job.getUpdateTime() != null ?
            DateUtil.toLocalDateTime(job.getUpdateTime()) : LocalDateTime.now());
        response.setCoreSkills(job.getCoreSkills());

        // 设置分类信息
        if (category != null) {
            response.setCategoryId(category.getId());
            response.setCategoryName(category.getName());
        }

        // 设置基本字段，优先使用数据库数据
        // 岗位要求：优先使用数据库中的requirements字段
        if (job.getRequirements() != null && !job.getRequirements().trim().isEmpty()) {
            // 如果requirements是文本格式，按换行符分割
            response.setRequirements(List.of(job.getRequirements().split("\n")));
        } else {
            response.setRequirements(List.of("熟悉相关技术栈", "具备良好的代码规范", "有团队协作能力"));
        }

        // 技能列表：优先使用数据库中的coreSkills，其次使用tags
        if (job.getCoreSkills() != null && !job.getCoreSkills().isEmpty()) {
            response.setSkills(job.getCoreSkills());
        } else if (job.getTags() != null && !job.getTags().isEmpty()) {
            response.setSkills(job.getTags());
        } else {
            response.setSkills(List.of("相关技术栈"));
        }

        // 岗位职责：优先使用数据库中的responsibilities字段
        if (job.getResponsibilities() != null && !job.getResponsibilities().trim().isEmpty()) {
            response.setResponsibilities(List.of(job.getResponsibilities().split("\n")));
        } else {
            response.setResponsibilities(List.of("负责系统开发", "参与需求分析", "代码审查"));
        }

        // 设置从数据库直接获取的字段
        response.setDuration(job.getDuration() != null ? job.getDuration() : 30);  // 面试时长
        response.setInterviewers(job.getInterviewers() != null ? job.getInterviewers() : 0);  // 已练习人数

        // 通过率：将数据库中的decimal转换为百分比字符串
        if (job.getPassRate() != null) {
            response.setPassRate(job.getPassRate().intValue() + "%");
        } else {
            response.setPassRate("0%");
        }

        // 技能标签：使用数据库中的tags字段
//        response.setTags(job.getTags() != null ? job.getTags() : List.of());

        // 设置面试流程
        List<InterviewResponseVo.InterviewStep> interviewProcess = List.of(
            createInterviewStep(1, "自我介绍", 3, "简单介绍个人背景和项目经验"),
            createInterviewStep(2, "技术基础", 10, "考察Java基础知识和编程能力"),
            createInterviewStep(3, "项目经验", 12, "深入了解项目经验和技术栈"),
            createInterviewStep(4, "场景题目", 5, "解决实际工作中的技术问题")
        );
        response.setInterviewProcess(interviewProcess);

        // 设置技能考查重点
        List<InterviewResponseVo.SkillPoint> skillPoints = List.of(
            createSkillPoint("Java基础", 25, "high"),
            createSkillPoint("Spring框架", 30, "high"),
            createSkillPoint("数据库", 20, "medium"),
            createSkillPoint("系统设计", 15, "medium"),
            createSkillPoint("项目经验", 10, "low")
        );
        response.setSkillPoints(skillPoints);

        // 设置练习收益
        response.setBenefits(List.of(
            "提升Java技术面试能力",
            "了解企业面试流程",
            "获得详细的能力评估报告",
            "针对性的学习建议"
        ));

        return response;
    }

    @Override
    public InterviewResponseVo.SampleQuestionsResponse getSampleQuestions(Long jobId, Integer count) {
        log.info("获取示例问题，jobId: {}, count: {}", jobId, count);

        try {
            // 验证岗位是否存在
            Job job = jobMapper.selectById(jobId);
            if (job == null) {
                throw new ServiceException("岗位不存在");
            }

            // 设置默认数量
            int questionCount = count != null ? count : 5;

            // 从数据库查询示例问题
            List<InterviewQuestion> dbQuestions = interviewQuestionMapper.selectByJobId(jobId, null, questionCount);

            // 转换为响应对象
            List<InterviewResponseVo.QuestionInfo> questions = new ArrayList<>();
            for (InterviewQuestion dbQuestion : dbQuestions) {
                InterviewResponseVo.QuestionInfo questionInfo = new InterviewResponseVo.QuestionInfo();
                questionInfo.setQuestionId(String.valueOf(dbQuestion.getId()));
                questionInfo.setContent(dbQuestion.getQuestion());
                questionInfo.setCategoryName(dbQuestion.getCategory());
                questionInfo.setDifficulty(String.valueOf(dbQuestion.getDifficulty()));
                questionInfo.setType(dbQuestion.getQuestionType());
                questionInfo.setTimeLimit(dbQuestion.getTimeLimit());
                questionInfo.setTags(dbQuestion.getTags() != null ? dbQuestion.getTags() : new ArrayList<>());
                questions.add(questionInfo);
            }

            // 构建响应
            InterviewResponseVo.SampleQuestionsResponse response = new InterviewResponseVo.SampleQuestionsResponse();
            response.setQuestions(questions);
            response.setTotalCount(questions.size());
            response.setJobId(jobId);

            log.info("成功获取示例问题，jobId: {}, 实际返回数量: {}", jobId, questions.size());
            return response;

        } catch (Exception e) {
            log.error("获取示例问题失败，jobId: {}, count: {}", jobId, count, e);
            throw new ServiceException("获取示例问题失败：" + e.getMessage());
        }
    }

    @Override
    public InterviewResponseVo.RelatedJobsResponse getRelatedJobs(Long jobId, Integer limit) {
        log.info("获取相关岗位，jobId: {}, limit: {}", jobId, limit);

        // 验证岗位是否存在
        Job job = jobMapper.selectById(jobId);
        if (job == null) {
            throw new ServiceException("岗位不存在");
        }

        // 查询相关岗位（基于相同分类）
        LambdaQueryWrapper<Job> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Job::getCategoryId, job.getCategoryId())
               .ne(Job::getId, jobId)
               .eq(Job::getStatus, "0")
               .orderByDesc(Job::getCreateTime);

        if (limit != null && limit > 0) {
            wrapper.last("LIMIT " + limit);
        } else {
            wrapper.last("LIMIT 10");
        }

        List<Job> relatedJobs = jobMapper.selectList(wrapper);
        List<JobVo> collect = relatedJobs
            .stream()
            .map(relatedJob -> BeanUtil.copyProperties(relatedJob, JobVo.class))
            .collect(Collectors.toList());

        InterviewResponseVo.RelatedJobsResponse response = new InterviewResponseVo.RelatedJobsResponse();
        response.setRelatedJobs(collect);
        response.setSourceJobId(jobId);

        return response;
    }

    public void recordSearchKeyword(String keyword) {
        try {
            if (StrUtil.isBlank(keyword)) {
                return;
            }

            LambdaQueryWrapper<SearchKeyword> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(SearchKeyword::getKeyword, keyword);

            SearchKeyword existingKeyword = searchKeywordMapper.selectOne(wrapper);

            if (existingKeyword != null) {
                // 更新搜索次数
                LambdaUpdateWrapper<SearchKeyword> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(SearchKeyword::getId, existingKeyword.getId())
                    .set(SearchKeyword::getSearchCount, existingKeyword.getSearchCount() + 1)
                    .set(SearchKeyword::getLastSearchTime, LocalDateTime.now());
                searchKeywordMapper.update(null, updateWrapper);
            } else {
                // 新增搜索关键词
                SearchKeyword newKeyword = new SearchKeyword();
                newKeyword.setKeyword(keyword);
                newKeyword.setSearchCount(1);
                newKeyword.setLastSearchTime(LocalDateTime.now());
                newKeyword.setCreateTime(LocalDateTime.now());
                searchKeywordMapper.insert(newKeyword);
            }
        } catch (Exception e) {
            log.error("记录搜索关键词失败: {}", keyword, e);
            // 不抛出异常，避免影响主业务流程
        }
    }

    @Override
    public InterviewResponseVo.JobStatisticsResponse getJobStatistics(Long jobId) {
        log.info("获取岗位统计数据，jobId: {}", jobId);

        // 验证岗位是否存在
        Job job = jobMapper.selectById(jobId);
        if (job == null) {
            throw new ServiceException("岗位不存在");
        }

        // 构建统计数据（模拟数据）
        InterviewResponseVo.JobStatisticsResponse response = new InterviewResponseVo.JobStatisticsResponse();
        response.setJobId(jobId);
        response.setTotalInterviews(1250);
        response.setAvgScore(75.5);
        response.setTotalQuestions(job.getQuestionCount());

        // 难度分布
        Map<String, Integer> difficultyDistribution = new HashMap<>();
        difficultyDistribution.put("简单", 30);
        difficultyDistribution.put("中等", 50);
        difficultyDistribution.put("困难", 20);
        response.setDifficultyDistribution(difficultyDistribution);

        // 技能分布
        Map<String, Double> skillDistribution = new HashMap<>();
        skillDistribution.put("Java", 0.85);
        skillDistribution.put("Spring", 0.75);
        skillDistribution.put("MySQL", 0.65);
        response.setSkillDistribution(skillDistribution);

        return response;
    }

    @Override
    public InterviewResponseVo.JobInterviewModesResponse getJobInterviewModes(Long jobId) {
        log.info("获取岗位面试模式列表，jobId: {}", jobId);

        // 验证岗位是否存在
        Job job = jobMapper.selectById(jobId);
        if (job == null) {
            throw new ServiceException("岗位不存在");
        }

        // 构建面试模式列表（模拟数据）
        List<InterviewResponseVo.ModeInfo> modes = new ArrayList<>();

        InterviewResponseVo.ModeInfo mode1 = new InterviewResponseVo.ModeInfo();
        mode1.setModeId("standard");
        mode1.setName("标准模式");
        mode1.setDescription("标准的面试流程，包含技术和行为问题");
        mode1.setIcon("standard-icon");
        mode1.setDuration(45);
        mode1.setQuestionCount(15);
        mode1.setIsPremium(false);
        mode1.setIsAvailable(true);
        modes.add(mode1);

        InterviewResponseVo.ModeInfo mode2 = new InterviewResponseVo.ModeInfo();
        mode2.setModeId("quick");
        mode2.setName("快速模式");
        mode2.setDescription("快速面试，主要考察核心技能");
        mode2.setIcon("quick-icon");
        mode2.setDuration(20);
        mode2.setQuestionCount(8);
        mode2.setIsPremium(false);
        mode2.setIsAvailable(true);
        modes.add(mode2);

        InterviewResponseVo.JobInterviewModesResponse response = new InterviewResponseVo.JobInterviewModesResponse();
        response.setAvailableModes(modes);
        response.setJobId(jobId);

        return response;
    }

    @Override
    public InterviewResponseVo.UserReadinessResponse checkUserReadiness(Long jobId, String userId) {
        log.info("检查用户准备度，jobId: {}, userId: {}", jobId, userId);

        // 验证岗位是否存在
        Job job = jobMapper.selectById(jobId);
        if (job == null) {
            throw new ServiceException("岗位不存在");
        }

        // 构建用户准备度数据（模拟数据）
        InterviewResponseVo.UserReadinessResponse response = new InterviewResponseVo.UserReadinessResponse();
        response.setReadinessScore(75.0);
        response.setStrongAreas(List.of("Java", "MySQL", "Spring"));
        response.setWeakAreas(List.of("微服务架构", "Redis"));

        // 推荐准备
        List<InterviewResponseVo.RecommendationItem> recommendations = new ArrayList<>();
        InterviewResponseVo.RecommendationItem rec1 = new InterviewResponseVo.RecommendationItem();
        rec1.setType("skill");
        rec1.setTitle("微服务架构学习");
        rec1.setDescription("建议加强微服务架构相关知识");
        rec1.setUrl("/learning/microservices");
        recommendations.add(rec1);

        response.setRecommendations(recommendations);

        return response;
    }

    @Override
    public InterviewResponseVo.ShareJobResponse shareJob(Long jobId, String platform) {
        log.info("分享岗位信息，jobId: {}, platform: {}", jobId, platform);

        // 验证岗位是否存在
        Job job = jobMapper.selectById(jobId);
        if (job == null) {
            throw new ServiceException("岗位不存在");
        }

        // 验证分享平台
        if (!List.of("wechat", "qq", "weibo", "link").contains(platform)) {
            throw new ServiceException("不支持的分享平台");
        }

        // 生成分享链接
        String shareUrl = "https://interview.example.com/job/" + jobId + "?from=" + platform;

        InterviewResponseVo.ShareJobResponse response = new InterviewResponseVo.ShareJobResponse();
        response.setSuccess(true);
        response.setShareUrl(shareUrl);

        return response;
    }

    @Override
    public InterviewResponseVo.SessionInfo getSessionInfo(String sessionId) {
        log.info("获取会话信息，sessionId: {}", sessionId);

        try {
            // 查询会话信息
            InterviewSession session = interviewSessionMapper.selectById(sessionId);
            if (session == null) {
                throw new ServiceException("面试会话不存在");
            }

            // 验证会话是否属于当前用户
            Long currentUserId = StpUtil.getLoginIdAsLong();
            if (!session.getUserId().equals(currentUserId)) {
                throw new ServiceException("无权访问该面试会话");
            }

            // 查询岗位信息
            Job job = jobMapper.selectById(session.getJobId());
            if (job == null) {
                throw new ServiceException("关联岗位不存在");
            }

            // 查询岗位分类信息
            JobCategory category = jobCategoryMapper.selectById(job.getCategoryId());

            // 查询已完成的问题数量
            Integer completedQuestions = interviewAnswerMapper.countAnsweredBySessionId(sessionId);

            // 查询当前问题索引
            SessionQuestion currentQuestion = sessionQuestionMapper.selectCurrentQuestion(sessionId);
            Integer currentQuestionIndex = currentQuestion != null ? currentQuestion.getQuestionOrder() - 1 : 0;

            // 构建响应对象
            InterviewResponseVo.SessionInfo response = new InterviewResponseVo.SessionInfo();
            response.setSessionId(sessionId);
            response.setJobId(session.getJobId());
            response.setJobTitle(job.getName());
            response.setCompany(job.getCompany());
            response.setCategoryName(category != null ? category.getName() : "未分类");
            response.setMode(session.getModeId());
            response.setStatus(session.getStatus());
            response.setStartTime(session.getStartTime());
            response.setEndTime(session.getEndTime());
            response.setTotalQuestions(session.getQuestionCount());
            response.setCompletedQuestions(completedQuestions != null ? completedQuestions : 0);
            response.setCurrentQuestionIndex(currentQuestionIndex);
            response.setInterviewerId("ai_interviewer_001");
            response.setInterviewerName("AI面试官");
            response.setInterviewerAvatar("/images/ai-interviewer.png");
            response.setCreatedTime(session.getCreateTime() != null ?
                DateUtil.toLocalDateTime(session.getCreateTime()) : LocalDateTime.now());
            response.setExpiryTime(session.getExpiresAt());

            return response;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取会话信息失败", e);
            throw new ServiceException("获取会话信息失败");
        }
    }

    @Override
    public InterviewResponseVo.QuestionListResponse getSessionQuestions(String sessionId) {
        log.info("获取面试问题列表，sessionId: {}", sessionId);

        try {
            // 验证会话是否存在
            InterviewSession session = interviewSessionMapper.selectById(sessionId);
            if (session == null) {
                throw new ServiceException("面试会话不存在");
            }

            // 验证会话是否属于当前用户
            Long currentUserId = StpUtil.getLoginIdAsLong();
            if (!session.getUserId().equals(currentUserId)) {
                throw new ServiceException("无权访问该面试会话");
            }

            // 查询会话的所有问题
            List<SessionQuestion> sessionQuestions = sessionQuestionMapper.selectBySessionIdOrderByOrder(sessionId);

            // 如果没有问题，则生成问题
            if (sessionQuestions.isEmpty()) {
                sessionQuestions = generateAndSaveSessionQuestions(session);
            }

            // 查询已回答的问题
            List<InterviewAnswer> answers = interviewAnswerMapper.selectBySessionId(sessionId);
            Map<String, InterviewAnswer> answerMap = new HashMap<>();
            for (InterviewAnswer answer : answers) {
                answerMap.put(answer.getQuestionId(), answer);
            }

            // 构建问题列表
            List<InterviewResponseVo.InterviewQuestion> questions = new ArrayList<>();
            for (SessionQuestion sq : sessionQuestions) {
                InterviewResponseVo.InterviewQuestion question = new InterviewResponseVo.InterviewQuestion();
                question.setQuestionId(sq.getQuestionId());
                question.setContent(sq.getContent());
                question.setDifficulty(sq.getDifficulty());
                question.setType(sq.getType());
                question.setTimeLimit(sq.getTimeLimit());
                question.setOrder(sq.getQuestionOrder());
                question.setTags(sq.getTags() != null ? sq.getTags() : List.of("通用"));
                question.setStatus(sq.getStatus());

                // 设置答案信息
                InterviewAnswer answer = answerMap.get(sq.getQuestionId());
                if (answer != null) {
                    InterviewResponseVo.AnswerInfo answerInfo = new InterviewResponseVo.AnswerInfo();
                    answerInfo.setAnswerId(answer.getId());
                    answerInfo.setContent(answer.getContent());
                    answerInfo.setAudioUrl(answer.getAudioUrl());
                    answerInfo.setDuration(answer.getDuration());
                    answerInfo.setSubmittedTime(answer.getSubmittedTime());
                    question.setAnswer(answerInfo);

                    // 设置反馈信息
                    if (answer.getFeedback() != null) {
                        InterviewResponseVo.FeedbackInfo feedbackInfo = new InterviewResponseVo.FeedbackInfo();
                        feedbackInfo.setScore(answer.getScore());
                        feedbackInfo.setComments(answer.getFeedback());
                        question.setFeedback(feedbackInfo);
                    }
                }

                questions.add(question);
            }

            InterviewResponseVo.QuestionListResponse response = new InterviewResponseVo.QuestionListResponse();
            response.setQuestions(questions);
            response.setSessionId(sessionId);
            response.setTotalCount(sessionQuestions.size());

            return response;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取面试问题列表失败", e);
            throw new ServiceException("获取面试问题列表失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public InterviewResponseVo.AnswerResponse submitAnswer(String sessionId, String questionId, String answer, String audioUrl, Integer duration) {
        log.info("提交面试回答，sessionId: {}, questionId: {}, duration: {}", sessionId, questionId, duration);

        try {
            // 验证会话是否存在
            InterviewSession session = interviewSessionMapper.selectById(sessionId);
            if (session == null) {
                throw new ServiceException("面试会话不存在");
            }

            // 验证会话是否属于当前用户
            Long currentUserId = StpUtil.getLoginIdAsLong();
            if (!session.getUserId().equals(currentUserId)) {
                throw new ServiceException("无权访问该面试会话");
            }

            // 验证会话状态
            if (!"started".equals(session.getStatus()) && !"in_progress".equals(session.getStatus())) {
                throw new ServiceException("面试会话状态不正确，无法提交答案");
            }

            // 验证问题是否存在
            SessionQuestion sessionQuestion = sessionQuestionMapper.selectBySessionIdAndQuestionId(sessionId, questionId);
            if (sessionQuestion == null) {
                throw new ServiceException("问题不存在");
            }

            // 检查是否已经回答过
            InterviewAnswer existingAnswer = interviewAnswerMapper.selectBySessionIdAndQuestionId(sessionId, questionId);
            if (existingAnswer != null) {
                throw new ServiceException("该问题已经回答过");
            }

            // 查询岗位信息用于AI评估
            Job job = jobMapper.selectById(session.getJobId());
            String jobContext = job != null ? job.getName() + " - " + job.getDescription() : "";

            // 使用AI服务评估答案
            InterviewResponseVo.FeedbackInfo feedback = aiEvaluationService.evaluateAnswer(
                sessionQuestion.getContent(),
                answer,
                audioUrl,
                null, // videoUrl
                duration,
                jobContext
            );

            // 保存答案到数据库
            InterviewAnswer interviewAnswer = new InterviewAnswer();
            interviewAnswer.setId(IdUtil.fastSimpleUUID());
            interviewAnswer.setSessionId(sessionId);
            interviewAnswer.setQuestionId(questionId);
            interviewAnswer.setUserId(currentUserId);
            interviewAnswer.setContent(answer);
            interviewAnswer.setAudioUrl(audioUrl);
            interviewAnswer.setDuration(duration);
            interviewAnswer.setSubmittedTime(LocalDateTime.now());
            interviewAnswer.setScore(feedback.getScore());
            interviewAnswer.setFeedback(feedback.getComments());
            interviewAnswer.setEvaluationDetails(JSONUtil.toJsonStr(feedback));
            interviewAnswer.setStatus("evaluated");
            interviewAnswer.setSkipped(false);
            interviewAnswer.setCreateBy(currentUserId);
            interviewAnswer.setCreateTime(new Date());

            interviewAnswerMapper.insert(interviewAnswer);

            // 更新当前问题状态为已回答
            sessionQuestionMapper.updateStatusBySessionIdAndQuestionId(sessionId, questionId, "answered");

            // 查找下一个问题并设置为当前问题
            SessionQuestion nextQuestion = sessionQuestionMapper.selectNextQuestion(sessionId, sessionQuestion.getQuestionOrder());
            String nextQuestionId = null;
            if (nextQuestion != null) {
                sessionQuestionMapper.updateStatusBySessionIdAndQuestionId(sessionId, nextQuestion.getQuestionId(), "current");
                nextQuestionId = nextQuestion.getQuestionId();
            }

            // 更新会话状态
            InterviewSession updateSession = new InterviewSession();
            updateSession.setId(sessionId);
            updateSession.setStatus("in_progress");
            updateSession.setUpdateTime(new Date());
            interviewSessionMapper.updateById(updateSession);

            // 构建响应
            InterviewResponseVo.AnswerResponse response = new InterviewResponseVo.AnswerResponse();
            response.setAnswerId(interviewAnswer.getId());
            response.setQuestionId(questionId);
            response.setSessionId(sessionId);
            response.setSuccess(true);
            response.setFeedback(feedback);
            response.setNextQuestionId(nextQuestionId);

            return response;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("提交面试回答失败", e);
            throw new ServiceException("提交面试回答失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public InterviewResponseVo.EndSessionResponse endSession(String sessionId, String reason) {
        log.info("结束面试会话，sessionId: {}, reason: {}", sessionId, reason);

        try {
            // 验证会话是否存在
            InterviewSession session = interviewSessionMapper.selectById(sessionId);
            if (session == null) {
                throw new ServiceException("面试会话不存在");
            }

            // 验证会话是否属于当前用户
            Long currentUserId = StpUtil.getLoginIdAsLong();
            if (!session.getUserId().equals(currentUserId)) {
                throw new ServiceException("无权访问该面试会话");
            }

            // 检查会话是否已经结束
            if ("completed".equals(session.getStatus()) || "cancelled".equals(session.getStatus())) {
                throw new ServiceException("面试会话已经结束");
            }

            // 更新所有未回答问题的状态为跳过
            List<SessionQuestion> pendingQuestions = sessionQuestionMapper.selectBySessionIdOrderByOrder(sessionId)
                .stream()
                .filter(q -> "pending".equals(q.getStatus()) || "current".equals(q.getStatus()))
                .toList();

            for (SessionQuestion question : pendingQuestions) {
                // 为跳过的问题创建答案记录
                InterviewAnswer skipAnswer = new InterviewAnswer();
                skipAnswer.setId(IdUtil.fastSimpleUUID());
                skipAnswer.setSessionId(sessionId);
                skipAnswer.setQuestionId(question.getQuestionId());
                skipAnswer.setUserId(currentUserId);
                skipAnswer.setContent("");
                skipAnswer.setSubmittedTime(LocalDateTime.now());
                skipAnswer.setStatus("skipped");
                skipAnswer.setSkipped(true);
                skipAnswer.setSkipReason(reason != null ? reason : "面试结束");
                skipAnswer.setCreateBy(currentUserId);
                skipAnswer.setCreateTime(new Date());

                interviewAnswerMapper.insert(skipAnswer);

                // 更新问题状态
                sessionQuestionMapper.updateStatusBySessionIdAndQuestionId(sessionId, question.getQuestionId(), "skipped");
            }

            // 更新会话状态
            InterviewSession updateSession = new InterviewSession();
            updateSession.setId(sessionId);
            updateSession.setStatus("completed");
            updateSession.setEndTime(LocalDateTime.now());
            updateSession.setUpdateBy(currentUserId);
            updateSession.setUpdateTime(new Date());
            interviewSessionMapper.updateById(updateSession);

            // 生成面试结果报告（异步处理）
            try {
                aiEvaluationService.generateInterviewReport(sessionId);
            } catch (Exception e) {
                log.warn("生成面试报告失败，但不影响会话结束: {}", e.getMessage());
            }

            // 构建响应
            InterviewResponseVo.EndSessionResponse response = new InterviewResponseVo.EndSessionResponse();
            response.setSessionId(sessionId);
            response.setStatus("completed");
            response.setResultAvailable(true);
            response.setCanRestart(false);
            response.setSummaryUrl("/interview/result?sessionId=" + sessionId);
            response.setEndTime(LocalDateTime.now());

            return response;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("结束面试会话失败", e);
            throw new ServiceException("结束面试会话失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public InterviewResponseVo.FeedbackResponse submitFeedback(String sessionId, Integer rating, String comments, List<String> tags) {
        log.info("提交面试反馈，sessionId: {}, rating: {}", sessionId, rating);

        try {
            // 验证会话是否存在
            InterviewSession session = interviewSessionMapper.selectById(sessionId);
            if (session == null) {
                throw new ServiceException("面试会话不存在");
            }

            // 验证评分范围
            if (rating < 1 || rating > 5) {
                throw new ServiceException("评分必须在1-5之间");
            }

            // 这里可以将反馈信息保存到数据库
            // 暂时只返回成功响应

            InterviewResponseVo.FeedbackResponse response = new InterviewResponseVo.FeedbackResponse();
            response.setSuccess(true);
            response.setMessage("反馈提交成功，感谢您的宝贵意见！");

            return response;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("提交面试反馈失败", e);
            throw new ServiceException("提交面试反馈失败");
        }
    }

    @Override
    public InterviewResponseVo.InterviewResult getInterviewResult(String sessionId) {
        log.info("获取面试结果，sessionId: {}", sessionId);

        try {
            // 验证会话是否存在
            InterviewSession session = interviewSessionMapper.selectById(sessionId);
            if (session == null) {
                throw new ServiceException("面试会话不存在");
            }

            // 验证会话是否属于当前用户
            Long currentUserId = StpUtil.getLoginIdAsLong();
            if (!session.getUserId().equals(currentUserId)) {
                throw new ServiceException("无权访问该面试会话");
            }

            // 检查会话是否已完成
            if (!"completed".equals(session.getStatus())) {
                throw new ServiceException("面试尚未完成，无法查看结果");
            }

            // 使用AI服务生成详细的面试结果
            InterviewResponseVo.InterviewResult result = aiEvaluationService.generateInterviewReport(sessionId);

            return result;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取面试结果失败", e);
            throw new ServiceException("获取面试结果失败");
        }
    }

    @Override
    public Map<String, Boolean> checkDevices() {
        log.info("检查设备状态");

        // 模拟设备检查结果
        Map<String, Boolean> deviceStatus = new HashMap<>();
        deviceStatus.put("camera", true);
        deviceStatus.put("microphone", true);
        deviceStatus.put("network", true);

        return deviceStatus;
    }

    @Override
    public InterviewResponseVo.SessionStatus getSessionStatus(String sessionId) {
        log.info("获取会话状态，sessionId: {}", sessionId);

        try {
            // 验证会话是否存在
            InterviewSession session = interviewSessionMapper.selectById(sessionId);
            if (session == null) {
                throw new ServiceException("面试会话不存在");
            }

            // 验证会话是否属于当前用户
            Long currentUserId = StpUtil.getLoginIdAsLong();
            if (!session.getUserId().equals(currentUserId)) {
                throw new ServiceException("无权访问该面试会话");
            }

            // 查询当前问题
            SessionQuestion currentQuestion = sessionQuestionMapper.selectCurrentQuestion(sessionId);
            Integer currentQuestionIndex = currentQuestion != null ? currentQuestion.getQuestionOrder() - 1 : 0;

            // 计算剩余时间
            Integer remainingTime = calculateRemainingTime(session);

            // 查询已回答问题数量
            Integer answeredCount = interviewAnswerMapper.countAnsweredBySessionId(sessionId);

            InterviewResponseVo.SessionStatus status = new InterviewResponseVo.SessionStatus();
            status.setSessionId(sessionId);
            status.setStatus(session.getStatus());
            status.setCurrentQuestionIndex(currentQuestionIndex);
            status.setTotalQuestions(session.getQuestionCount());
            status.setIsActive("started".equals(session.getStatus()) || "in_progress".equals(session.getStatus()));
            status.setLastActivity(session.getUpdateTime() != null ?
                DateUtil.toLocalDateTime(session.getUpdateTime()) : LocalDateTime.now());
            status.setRemainingTime(remainingTime);

            return status;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取会话状态失败", e);
            throw new ServiceException("获取会话状态失败");
        }
    }

    @Override
    public InterviewResponseVo.InterviewerInfo getInterviewerInfo(String interviewerId) {
        log.info("获取AI面试官信息，interviewerId: {}", interviewerId);

        // 构建AI面试官信息（模拟数据）
        InterviewResponseVo.InterviewerInfo info = new InterviewResponseVo.InterviewerInfo();
        info.setInterviewerId(StrUtil.isNotBlank(interviewerId) ? interviewerId : "ai_interviewer_001");
        info.setName("AI面试官");
        info.setAvatar("/images/ai-interviewer.png");
        info.setTitle("高级面试官");
        info.setCompany("智能面试系统");
        info.setBackground("拥有丰富的技术面试经验，精通多种编程语言和框架");
        info.setSpecialties(List.of("技术面试", "行为面试", "项目经验评估"));
        info.setInterviewStyle("专业、友好、有建设性");
        info.setVoiceType("标准普通话");

        return info;
    }

    /**
     * 计算剩余时间（秒）
     */
    private Integer calculateRemainingTime(InterviewSession session) {
        if (session.getExpiresAt() == null) {
            return 1800; // 默认30分钟
        }

        LocalDateTime now = LocalDateTime.now();
        LocalDateTime expiryTime = session.getExpiresAt();

        if (now.isAfter(expiryTime)) {
            return 0; // 已过期
        }

        return (int) java.time.Duration.between(now, expiryTime).getSeconds();
    }

    /**
     * 根据分数获取等级
     */
    private String getScoreLevel(Double score) {
        if (score >= 90) {
            return "excellent";
        } else if (score >= 80) {
            return "good";
        } else if (score >= 70) {
            return "average";
        } else if (score >= 60) {
            return "below_average";
        } else {
            return "poor";
        }
    }

    /**
     * 生成并保存会话问题
     */
    @Transactional(rollbackFor = Exception.class)
    protected List<SessionQuestion> generateAndSaveSessionQuestions(InterviewSession session) {
        log.info("为会话生成问题，sessionId: {}", session.getId());

        try {
            // 查询岗位信息
            Job job = jobMapper.selectById(session.getJobId());
            if (job == null) {
                throw new ServiceException("关联岗位不存在");
            }

            // 使用AI服务生成问题
            List<InterviewResponseVo.InterviewQuestion> aiQuestions = aiEvaluationService.generateQuestions(
                session.getJobId(),
                "medium", // 默认中等难度
                session.getQuestionCount() != null ? session.getQuestionCount() : 10,
                null, // 简历URL
                null  // 自定义问题
            );

            // 转换为SessionQuestion实体并保存
            List<SessionQuestion> sessionQuestions = new ArrayList<>();
            for (int i = 0; i < aiQuestions.size(); i++) {
                InterviewResponseVo.InterviewQuestion aiQ = aiQuestions.get(i);

                SessionQuestion sq = new SessionQuestion();
                sq.setId(IdUtil.fastSimpleUUID());
                sq.setSessionId(session.getId());
                sq.setQuestionId(aiQ.getQuestionId());
                sq.setContent(aiQ.getContent());
                sq.setType(aiQ.getType());
                sq.setDifficulty(aiQ.getDifficulty());
                sq.setCategory(job.getName()); // 使用岗位名称作为分类
                sq.setTimeLimit(aiQ.getTimeLimit());
                sq.setQuestionOrder(i + 1);
                sq.setTags(aiQ.getTags());
                sq.setStatus(i == 0 ? "current" : "pending"); // 第一个问题设为当前问题
                sq.setIsCustom(false);
                sq.setSource("ai_generated");
                sq.setCreateBy(session.getUserId());
                sq.setCreateTime(new Date());

                sessionQuestions.add(sq);
            }

            // 批量插入问题
            if (!sessionQuestions.isEmpty()) {
                sessionQuestionMapper.batchInsert(sessionQuestions);
            }

            return sessionQuestions;

        } catch (Exception e) {
            log.error("生成会话问题失败", e);
            throw new ServiceException("生成面试问题失败: " + e.getMessage());
        }
    }

    /**
     * 创建空的历史记录响应
     */
    private InterviewResponseVo.HistoryListResponse createEmptyHistoryResponse(Integer page, Integer pageSize) {
        InterviewResponseVo.HistoryListResponse response = new InterviewResponseVo.HistoryListResponse();
        response.setRecords(new ArrayList<>());
        response.setTotal(0L);
        response.setHasMore(false);
        response.setPage(page);
        response.setPageSize(pageSize);
        return response;
    }

    /**
     * 批量转换InterviewResult为InterviewRecord，优化数据库查询
     */
    private List<InterviewResponseVo.InterviewRecord> batchConvertToInterviewRecords(List<InterviewResult> results) {
        if (results.isEmpty()) {
            return new ArrayList<>();
        }

        // 批量获取岗位信息，避免N+1查询
        Set<Long> jobIds = results.stream()
            .map(InterviewResult::getJobId)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());

        Map<Long, Job> jobMap = new HashMap<>();
        Map<Long, JobCategory> categoryMap = new HashMap<>();

        if (!jobIds.isEmpty()) {
            // 批量查询岗位信息
            List<Job> jobs = jobMapper.selectBatchIds(jobIds);
            jobMap = jobs.stream().collect(Collectors.toMap(Job::getId, job -> job));

            // 批量查询分类信息
            Set<Long> categoryIds = jobs.stream()
                .map(Job::getCategoryId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

            if (!categoryIds.isEmpty()) {
                List<JobCategory> categories = jobCategoryMapper.selectBatchIds(categoryIds);
                categoryMap = categories.stream().collect(Collectors.toMap(JobCategory::getId, category -> category));
            }
        }

        // 批量转换
        final Map<Long, Job> finalJobMap = jobMap;
        final Map<Long, JobCategory> finalCategoryMap = categoryMap;

        return results.stream()
            .map(result -> convertToInterviewRecordOptimized(result, finalJobMap, finalCategoryMap))
            .collect(Collectors.toList());
    }

    /**
     * 转换InterviewResult为InterviewRecord（优化版本）
     */
    private InterviewResponseVo.InterviewRecord convertToInterviewRecordOptimized(InterviewResult result,
                                                                                 Map<Long, Job> jobMap,
                                                                                 Map<Long, JobCategory> categoryMap) {
        InterviewResponseVo.InterviewRecord record = new InterviewResponseVo.InterviewRecord();

        record.setId(Long.parseLong(result.getId()));
        record.setJobName(result.getJobName());
        record.setCompany(result.getCompany());
        record.setDifficulty(getDifficultyText(result.getTotalScore()));
        record.setQuestionCount(result.getTotalQuestions());
        record.setDuration(result.getDuration());
        record.setTotalScore(result.getTotalScore());
        record.setStatus(convertStatus(result.getStatus(), result.getTotalScore()));

        // 格式化日期和时间
        if (result.getDate() != null) {
            record.setDate(DateUtil.formatLocalDateTime(result.getDate()));
            record.setTime(DateUtil.format(result.getDate(), "HH:mm"));
            record.setTimeAgo(getTimeAgo(result.getDate()));
        }

        // 从缓存的Map中获取岗位和分类信息，避免重复查询
        Job job = jobMap.get(result.getJobId());
        if (job != null) {
            record.setIcon(getJobIconByCategory(job.getCategoryId(), categoryMap));
            JobCategory category = categoryMap.get(job.getCategoryId());
            record.setCategory(category != null ? category.getName() : "未知分类");
        } else {
            record.setIcon("default-icon");
            record.setCategory("未知分类");
        }

        // 设置维度分数（模拟数据，实际应该从数据库查询）
        InterviewResponseVo.DimensionScores dimensions = new InterviewResponseVo.DimensionScores();
        int baseScore = result.getTotalScore();
        dimensions.setProfessional(Math.max(0, Math.min(100, baseScore + (int)(Math.random() * 10 - 5))));
        dimensions.setCommunication(Math.max(0, Math.min(100, baseScore + (int)(Math.random() * 10 - 5))));
        dimensions.setLogic(Math.max(0, Math.min(100, baseScore + (int)(Math.random() * 10 - 5))));
        dimensions.setInnovation(Math.max(0, Math.min(100, baseScore + (int)(Math.random() * 10 - 5))));
        record.setDimensions(dimensions);

        return record;
    }

    /**
     * 根据分类ID获取岗位图标（优化版本）
     */
    private String getJobIconByCategory(Long categoryId, Map<Long, JobCategory> categoryMap) {
        if (categoryId == null) {
            return "default-icon";
        }

        JobCategory category = categoryMap.get(categoryId);
        if (category != null && StrUtil.isNotBlank(category.getIcon())) {
            return category.getIcon();
        }

        // 根据分类名称返回默认图标
        if (category != null) {
            String categoryName = category.getName();
            if (categoryName.contains("前端")) {
                return "frontend-icon";
            } else if (categoryName.contains("后端")) {
                return "backend-icon";
            } else if (categoryName.contains("移动")) {
                return "mobile-icon";
            } else if (categoryName.contains("数据")) {
                return "data-icon";
            }
        }

        return "default-icon";
    }

    /**
     * 转换InterviewResult为InterviewRecord（兼容旧版本）
     */
    private InterviewResponseVo.InterviewRecord convertToInterviewRecord(InterviewResult result) {
        InterviewResponseVo.InterviewRecord record = new InterviewResponseVo.InterviewRecord();

        record.setId(Long.parseLong(result.getId()));
        record.setJobName(result.getJobName());
        record.setCompany(result.getCompany());
        record.setDifficulty(getDifficultyText(result.getTotalScore()));
        record.setQuestionCount(result.getTotalQuestions());
        record.setDuration(result.getDuration());
        record.setTotalScore(result.getTotalScore());
        record.setStatus(convertStatus(result.getStatus(), result.getTotalScore()));

        // 格式化日期和时间
        if (result.getDate() != null) {
            record.setDate(DateUtil.formatLocalDateTime(result.getDate()));
            record.setTime(DateUtil.format(result.getDate(), "HH:mm"));
            record.setTimeAgo(getTimeAgo(result.getDate()));
        }

        // 设置图标（根据岗位分类）
        record.setIcon(getJobIcon(result.getJobId()));

        // 设置分类
        record.setCategory(getJobCategory(result.getJobId()));

        // 设置维度分数（模拟数据，实际应该从数据库查询）
        InterviewResponseVo.DimensionScores dimensions = new InterviewResponseVo.DimensionScores();
        int baseScore = result.getTotalScore();
        dimensions.setProfessional(Math.max(0, Math.min(100, baseScore + (int)(Math.random() * 10 - 5))));
        dimensions.setCommunication(Math.max(0, Math.min(100, baseScore + (int)(Math.random() * 10 - 5))));
        dimensions.setLogic(Math.max(0, Math.min(100, baseScore + (int)(Math.random() * 10 - 5))));
        dimensions.setInnovation(Math.max(0, Math.min(100, baseScore + (int)(Math.random() * 10 - 5))));
        record.setDimensions(dimensions);

        return record;
    }

    /**
     * 根据分数获取难度文本
     */
    private String getDifficultyText(Integer score) {
        if (score >= 90) {
            return "高级难度";
        }
        if (score >= 70) {
            return "中级难度";
        }
        return "初级难度";
    }

    /**
     * 转换状态
     */
    private String convertStatus(String originalStatus, Integer score) {
        if ("completed".equals(originalStatus)) {
            if (score >= 90) {
                return "excellent";
            }
            if (score >= 80) {
                return "good";
            }
            if (score >= 60) {
                return "fair";
            }
            return "poor";
        }
        return originalStatus;
    }

    /**
     * 获取时间差描述
     */
    private String getTimeAgo(LocalDateTime dateTime) {
        if (dateTime == null) {
            return "";
        }

        LocalDateTime now = LocalDateTime.now();
        long days = java.time.Duration.between(dateTime, now).toDays();
        long hours = java.time.Duration.between(dateTime, now).toHours();
        long minutes = java.time.Duration.between(dateTime, now).toMinutes();

        if (days > 0) {
            return days + "天前";
        }
        if (hours > 0) {
            return hours + "小时前";
        }
        if (minutes > 0) {
            return minutes + "分钟前";
        }
        return "刚刚";
    }

    /**
     * 获取岗位图标
     */
    private String getJobIcon(Long jobId) {
        try {
            Job job = jobMapper.selectById(jobId);
            if (job != null) {
                JobCategory category = jobCategoryMapper.selectById(job.getCategoryId());
                if (category != null) {
                    return category.getIcon() != null ? category.getIcon() : "i-mdi-briefcase";
                }
            }
        } catch (Exception e) {
            log.warn("获取岗位图标失败，jobId: {}", jobId, e);
        }
        return "i-mdi-briefcase";
    }

    /**
     * 获取岗位分类
     */
    private String getJobCategory(Long jobId) {
        try {
            Job job = jobMapper.selectById(jobId);
            if (job != null) {
                JobCategory category = jobCategoryMapper.selectById(job.getCategoryId());
                if (category != null) {
                    return category.getName();
                }
            }
        } catch (Exception e) {
            log.warn("获取岗位分类失败，jobId: {}", jobId, e);
        }
        return "其他";
    }

    /**
     * 创建面试流程步骤
     */
    private InterviewResponseVo.InterviewStep createInterviewStep(int step, String name, int duration, String description) {
        InterviewResponseVo.InterviewStep interviewStep = new InterviewResponseVo.InterviewStep();
        interviewStep.setStep(step);
        interviewStep.setName(name);
        interviewStep.setDuration(duration);
        interviewStep.setDescription(description);
        return interviewStep;
    }

    /**
     * 创建技能考查点
     */
    private InterviewResponseVo.SkillPoint createSkillPoint(String name, int weight, String level) {
        InterviewResponseVo.SkillPoint skillPoint = new InterviewResponseVo.SkillPoint();
        skillPoint.setName(name);
        skillPoint.setWeight(weight);
        skillPoint.setLevel(level);
        return skillPoint;
    }

}
