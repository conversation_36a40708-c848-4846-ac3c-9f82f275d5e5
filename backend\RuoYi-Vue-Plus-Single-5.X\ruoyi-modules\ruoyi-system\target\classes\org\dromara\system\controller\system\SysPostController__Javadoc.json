{"doc": "\n 岗位信息操作处理\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.system.domain.bo.SysPostBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 获取岗位列表\r\n"}, {"name": "export", "paramTypes": ["org.dromara.system.domain.bo.SysPostBo", "jakarta.servlet.http.HttpServletResponse"], "doc": "\n 导出岗位列表\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": "\n 根据岗位编号获取详细信息\r\n\r\n @param postId 岗位ID\r\n"}, {"name": "add", "paramTypes": ["org.dromara.system.domain.bo.SysPostBo"], "doc": "\n 新增岗位\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.system.domain.bo.SysPostBo"], "doc": "\n 修改岗位\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": "\n 删除岗位\r\n\r\n @param postIds 岗位ID串\r\n"}, {"name": "optionselect", "paramTypes": ["java.lang.Long[]", "java.lang.Long"], "doc": "\n 获取岗位选择框列表\r\n\r\n @param postIds 岗位ID串\r\n @param deptId  部门id\r\n"}], "constructors": []}