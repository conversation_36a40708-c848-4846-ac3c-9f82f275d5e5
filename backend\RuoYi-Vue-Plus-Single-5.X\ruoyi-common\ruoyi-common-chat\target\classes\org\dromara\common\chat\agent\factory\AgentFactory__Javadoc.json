{"doc": "\n AI Agent工厂类\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "createAgent", "paramTypes": ["org.dromara.common.chat.agent.AgentType", "org.dromara.common.chat.agent.AgentContext", "java.lang.String"], "doc": "\n 创建Agent\r\n"}, {"name": "createAgent", "paramTypes": ["org.dromara.common.chat.agent.AgentType", "org.dromara.common.chat.agent.AgentContext"], "doc": "\n 创建Agent（使用默认提供商）\r\n"}, {"name": "createInterviewerAgent", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String"], "doc": "\n 创建面试官Agent\r\n\r\n @param context  上下文\r\n @param provider 模型提供商（可选）\r\n @return 面试官Agent实例\r\n"}, {"name": "createResumeAnalyzerAgent", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String"], "doc": "\n 创建简历分析Agent\r\n\r\n @param context  上下文\r\n @param provider 模型提供商（可选）\r\n @return 简历分析Agent实例\r\n"}, {"name": "createSkillAssessorAgent", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String"], "doc": "\n 创建技能评估Agent\r\n\r\n @param context  上下文\r\n @param provider 模型提供商（可选）\r\n @return 技能评估Agent实例\r\n"}, {"name": "createCareerAdvisorAgent", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String"], "doc": "\n 创建职业顾问Agent\r\n\r\n @param context  上下文\r\n @param provider 模型提供商（可选）\r\n @return 职业顾问Agent实例\r\n"}, {"name": "createMockInterviewerAgent", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String"], "doc": "\n 创建模拟面试Agent\r\n\r\n @param context  上下文\r\n @param provider 模型提供商（可选）\r\n @return 模拟面试Agent实例\r\n"}, {"name": "createLearningGuideAgent", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String"], "doc": "\n 创建学习导师Agent\r\n\r\n @param context  上下文\r\n @param provider 模型提供商（可选）\r\n @return 学习导师Agent实例\r\n"}, {"name": "createGeneralChatAgent", "paramTypes": ["org.dromara.common.chat.agent.AgentContext", "java.lang.String"], "doc": "\n 创建通用聊天Agent\r\n\r\n @param context  上下文\r\n @param provider 模型提供商（可选）\r\n @return 通用聊天Agent实例\r\n"}, {"name": "selectModel", "paramTypes": ["java.lang.String"], "doc": "\n 选择模型\r\n"}, {"name": "selectStreamingModel", "paramTypes": ["java.lang.String"], "doc": "\n 选择流式模型\r\n"}, {"name": "getDefaultStreamingChatModel", "paramTypes": [], "doc": "\n 获取默认流式聊天模型\r\n"}, {"name": "getStreamingChatModel", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": "\n 根据Agent类型和模型名称获取流式聊天模型\r\n\r\n @param agentType Agent类型\r\n @param modelName 模型名称（可选）\r\n @return 流式聊天模型\r\n"}, {"name": "buildCacheKey", "paramTypes": ["org.dromara.common.chat.agent.AgentType", "java.lang.String", "java.lang.String"], "doc": "\n 构建缓存键\r\n"}, {"name": "clearAgentCache", "paramTypes": ["java.lang.String"], "doc": "\n 清理Agent缓存\r\n"}, {"name": "clearAllAgentCache", "paramTypes": [], "doc": "\n 清理所有Agent缓存\r\n"}], "constructors": []}