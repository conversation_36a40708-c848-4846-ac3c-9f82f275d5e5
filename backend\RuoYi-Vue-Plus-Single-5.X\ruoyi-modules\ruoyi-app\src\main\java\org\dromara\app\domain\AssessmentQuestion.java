package org.dromara.app.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serializable;
import java.util.List;

/**
 * 评估问题实体
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("assessment_question")
public class AssessmentQuestion extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 问题ID
     */
    @TableId(type = IdType.AUTO)
    private Long questionId;

    /**
     * 问题编码
     */
    private String questionCode;

    /**
     * 问题类型：single-单选题，scale-量表题
     */
    private String questionType;

    /**
     * 问题类别
     */
    private String category;

    /**
     * 问题内容
     */
    private String questionContent;

    /**
     * 量表最小值（量表题）
     */
    private Integer minValue;

    /**
     * 量表最大值（量表题）
     */
    private Integer maxValue;

    /**
     * 排序顺序
     */
    private Integer sortOrder;

    /**
     * 状态（0正常 1停用）
     */
    private String status;

    /**
     * 问题选项列表（不存储到数据库）
     */
    @TableField(exist = false)
    private List<AssessmentQuestionOption> options;
}
