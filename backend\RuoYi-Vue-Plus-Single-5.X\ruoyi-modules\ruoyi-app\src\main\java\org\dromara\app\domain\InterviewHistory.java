package org.dromara.app.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 面试历史记录对象 app_interview_history
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@Data
@TableName("app_interview_history")
public class InterviewHistory implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 历史记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 结果ID
     */
    private String resultId;

    /**
     * 自定义标题
     */
    private String title;

    /**
     * 是否收藏（0否 1是）
     */
    private Boolean isFavorite;

    /**
     * 标签（JSON数组）
     */
    private List<String> tags;

    /**
     * 用户备注
     */
    private String notes;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

}
