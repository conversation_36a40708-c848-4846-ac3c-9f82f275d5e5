{"doc": "\n 反射工具类. 提供调用getter/setter方法, 访问私有变量, 调用私有方法, 获取泛型类型Class, 被AOP过的真实类等工具函数.\r\n\r\n <AUTHOR> Li\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "invokeGetter", "paramTypes": ["java.lang.Object", "java.lang.String"], "doc": "\n 调用Getter方法.\r\n 支持多级，如：对象名.对象名.方法\r\n"}, {"name": "invokeSetter", "paramTypes": ["java.lang.Object", "java.lang.String", "java.lang.Object"], "doc": "\n 调用Setter方法, 仅匹配方法名。\r\n 支持多级，如：对象名.对象名.方法\r\n"}], "constructors": []}