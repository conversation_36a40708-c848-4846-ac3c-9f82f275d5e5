{"doc": "\n ES字段类型枚举\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [{"name": "TEXT", "doc": "\n 文本类型\r\n"}, {"name": "KEYWORD", "doc": "\n 关键字类型\r\n"}, {"name": "INTEGER", "doc": "\n 整数类型\r\n"}, {"name": "LONG", "doc": "\n 长整数类型\r\n"}, {"name": "FLOAT", "doc": "\n 浮点数类型\r\n"}, {"name": "DOUBLE", "doc": "\n 双精度浮点数类型\r\n"}, {"name": "BOOLEAN", "doc": "\n 布尔类型\r\n"}, {"name": "DATE", "doc": "\n 日期类型\r\n"}, {"name": "OBJECT", "doc": "\n 对象类型\r\n"}, {"name": "NESTED", "doc": "\n 嵌套类型\r\n"}, {"name": "GEO_POINT", "doc": "\n 地理位置类型\r\n"}, {"name": "IP", "doc": "\n IP地址类型\r\n"}], "methods": [], "constructors": []}