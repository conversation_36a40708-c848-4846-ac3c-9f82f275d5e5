package org.dromara.app.service;

import org.dromara.app.domain.vo.InterviewResultResponseVo;

import java.util.List;

/**
 * 面试结果服务接口
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
public interface IInterviewResultService {

    /**
     * 获取面试结果摘要
     *
     * @param resultId 结果ID或会话ID
     * @return 面试结果摘要
     */
    InterviewResultResponseVo.InterviewResultSummary getResultSummary(String resultId);

    /**
     * 获取面试结果详情
     *
     * @param resultId 结果ID或会话ID
     * @return 面试结果详情
     */
    InterviewResultResponseVo.InterviewResultDetail getResultDetail(String resultId);

    /**
     * 获取性能指标
     *
     * @param resultId 结果ID
     * @return 性能指标数据
     */
    InterviewResultResponseVo.PerformanceMetrics getPerformanceMetrics(String resultId);

    /**
     * 保存到历史记录
     *
     * @param resultId 结果ID
     * @param title    自定义标题（可选）
     * @return 保存结果
     */
    InterviewResultResponseVo.SaveHistoryResponse saveToHistory(String resultId, String title);

    /**
     * 分享结果
     *
     * @param resultId 结果ID
     * @param platform 分享平台
     * @param content  分享内容（可选）
     * @return 分享结果
     */
    InterviewResultResponseVo.ShareResultResponse shareResult(String resultId, String platform, String content);

    /**
     * 获取提升计划
     *
     * @param resultId 结果ID
     * @param userId   用户ID（可选）
     * @return 提升计划
     */
    InterviewResultResponseVo.ImprovementPlan getImprovementPlan(String resultId, Long userId);

    /**
     * 获取学习资源推荐
     *
     * @param resultId 结果ID
     * @param limit    数量限制
     * @return 学习资源列表
     */
    List<InterviewResultResponseVo.LearningResource> getLearningResources(String resultId, Integer limit);

    /**
     * 创建面试结果
     *
     * @param sessionId 会话ID
     * @param resultData 结果数据
     * @return 结果ID
     */
    String createInterviewResult(String sessionId, Object resultData);

    /**
     * 更新面试结果
     *
     * @param resultId 结果ID
     * @param resultData 结果数据
     * @return 是否成功
     */
    Boolean updateInterviewResult(String resultId, Object resultData);

    /**
     * 删除面试结果
     *
     * @param resultId 结果ID
     * @return 是否成功
     */
    Boolean deleteInterviewResult(String resultId);

    /**
     * 获取用户面试结果列表
     *
     * @param userId 用户ID
     * @param limit  限制数量
     * @return 面试结果列表
     */
    List<InterviewResultResponseVo.InterviewResultSummary> getUserResults(Long userId, Integer limit);

    /**
     * 获取用户面试统计
     *
     * @param userId 用户ID
     * @return 统计数据
     */
    Object getUserStatistics(Long userId);

    /**
     * 生成面试报告
     *
     * @param resultId 结果ID
     * @return 报告内容
     */
    Object generateReport(String resultId);

}
