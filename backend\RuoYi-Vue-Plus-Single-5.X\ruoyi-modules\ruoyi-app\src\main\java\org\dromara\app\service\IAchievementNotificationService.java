package org.dromara.app.service;


import org.dromara.app.domain.vo.AchievementVo;
import org.dromara.app.domain.vo.UserAchievementVo;

import java.util.List;

/**
 * 成就通知服务接口
 *
 * <AUTHOR>
 */
public interface IAchievementNotificationService {

    /**
     * 发送成就解锁通知
     *
     * @param userId      用户ID
     * @param achievement 成就信息
     */
    void sendAchievementUnlockNotification(Long userId, AchievementVo achievement);

    /**
     * 批量发送成就解锁通知
     *
     * @param userId       用户ID
     * @param achievements 成就列表
     */
    void batchSendAchievementUnlockNotification(Long userId, List<AchievementVo> achievements);

    /**
     * 发送成就进度更新通知
     *
     * @param userId           用户ID
     * @param userAchievement  用户成就信息
     */
    void sendAchievementProgressNotification(Long userId, UserAchievementVo userAchievement);

    /**
     * 发送成就提醒通知
     *
     * @param userId      用户ID
     * @param achievement 成就信息
     * @param message     提醒消息
     */
    void sendAchievementReminderNotification(Long userId, AchievementVo achievement, String message);

    /**
     * 发送成就排行榜通知
     *
     * @param userId   用户ID
     * @param ranking  排名
     * @param category 类别
     */
    void sendLeaderboardNotification(Long userId, Integer ranking, String category);

    /**
     * 标记通知为已读
     *
     * @param userId         用户ID
     * @param notificationId 通知ID
     * @return 操作结果
     */
    Boolean markNotificationAsRead(Long userId, String notificationId);

    /**
     * 获取用户未读通知数量
     *
     * @param userId 用户ID
     * @return 未读通知数量
     */
    Long getUnreadNotificationCount(Long userId);

    /**
     * 获取用户通知列表
     *
     * @param userId 用户ID
     * @param limit  限制数量
     * @return 通知列表
     */
    List<NotificationVo> getUserNotifications(Long userId, Integer limit);

    /**
     * 清理过期通知
     *
     * @param days 保留天数
     * @return 清理数量
     */
    Integer cleanExpiredNotifications(Integer days);

    /**
     * 通知视图对象
     */
    class NotificationVo {
        private String id;
        private String type;
        private String title;
        private String message;
        private String data;
        private Boolean isRead;
        private java.util.Date createTime;

        // getters and setters
        public String getId() { return id; }
        public void setId(String id) { this.id = id; }
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public String getData() { return data; }
        public void setData(String data) { this.data = data; }
        public Boolean getIsRead() { return isRead; }
        public void setIsRead(Boolean isRead) { this.isRead = isRead; }
        public java.util.Date getCreateTime() { return createTime; }
        public void setCreateTime(java.util.Date createTime) { this.createTime = createTime; }
    }

}
