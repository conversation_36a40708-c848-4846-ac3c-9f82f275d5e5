package org.dromara.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.dromara.app.domain.InterviewQuestion;

import java.util.List;
import java.util.Map;

/**
 * 面试问题Mapper接口
 *
 * <AUTHOR> Assistant
 * @date 2025-07-19
 */
@Mapper
public interface InterviewQuestionMapper extends BaseMapper<InterviewQuestion> {

    /**
     * 根据岗位ID查询问题列表
     *
     * @param jobId      岗位ID
     * @param difficulty 难度等级
     * @param limit      限制数量
     * @return 问题列表
     */
    List<InterviewQuestion> selectByJobId(@Param("jobId") Long jobId,
                                        @Param("difficulty") Integer difficulty,
                                        @Param("limit") Integer limit);

    /**
     * 根据技术领域查询问题
     *
     * @param technicalDomain 技术领域
     * @param questionType    问题类型
     * @param limit          限制数量
     * @return 问题列表
     */
    List<InterviewQuestion> selectByTechnicalDomain(@Param("technicalDomain") String technicalDomain,
                                                  @Param("questionType") String questionType,
                                                  @Param("limit") Integer limit);

    /**
     * 查询多模态问题
     *
     * @param jobId 岗位ID
     * @param limit 限制数量
     * @return 多模态问题列表
     */
    List<InterviewQuestion> selectMultimodalQuestions(@Param("jobId") Long jobId,
                                                    @Param("limit") Integer limit);

    /**
     * 根据标签查询问题
     *
     * @param tags  标签列表
     * @param limit 限制数量
     * @return 问题列表
     */
    List<InterviewQuestion> selectByTags(@Param("tags") List<String> tags,
                                       @Param("limit") Integer limit);

    /**
     * 分页查询问题列表
     *
     * @param page           分页参数
     * @param jobId          岗位ID
     * @param questionType   问题类型
     * @param difficulty     难度等级
     * @param category       问题分类
     * @return 分页结果
     */
    IPage<InterviewQuestion> selectQuestionPage(Page<InterviewQuestion> page,
                                              @Param("jobId") Long jobId,
                                              @Param("questionType") String questionType,
                                              @Param("difficulty") Integer difficulty,
                                              @Param("category") String category);

    /**
     * 根据问题ID批量查询
     *
     * @param ids 问题ID列表
     * @return 问题列表
     */
    List<InterviewQuestion> selectByIds(@Param("list") List<Long> ids);

    /**
     * 根据分类查询问题数量
     *
     * @param category 问题分类
     * @return 问题数量
     */
    int countByCategory(@Param("category") String category);

    /**
     * 根据岗位ID查询问题数量
     *
     * @param jobId 岗位ID
     * @return 问题数量
     */
    int countByJobId(@Param("jobId") Long jobId);

    /**
     * 查询随机问题
     *
     * @param jobId        岗位ID
     * @param difficulty   难度等级
     * @param questionType 问题类型
     * @param limit        限制数量
     * @return 随机问题列表
     */
    List<InterviewQuestion> selectRandomQuestions(@Param("jobId") Long jobId,
                                                @Param("difficulty") Integer difficulty,
                                                @Param("questionType") String questionType,
                                                @Param("limit") Integer limit);

    /**
     * 查询问题统计信息
     *
     * @return 统计信息Map
     */
    Map<String, Object> selectQuestionStatistics();

    /**
     * 批量插入问题
     *
     * @param questions 问题列表
     * @return 插入数量
     */
    int batchInsert(@Param("list") List<InterviewQuestion> questions);

    /**
     * 批量更新问题状态
     *
     * @param ids      问题ID列表
     * @param status   新状态
     * @param updateBy 更新人
     * @return 更新数量
     */
    int batchUpdateStatus(@Param("ids") List<Long> ids,
                         @Param("status") String status,
                         @Param("updateBy") Long updateBy);

}
