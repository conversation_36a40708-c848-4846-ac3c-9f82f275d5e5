{"doc": "\n 参数配置 信息操作处理\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.system.domain.bo.SysConfigBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": "\n 获取参数配置列表\r\n"}, {"name": "export", "paramTypes": ["org.dromara.system.domain.bo.SysConfigBo", "jakarta.servlet.http.HttpServletResponse"], "doc": "\n 导出参数配置列表\r\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": "\n 根据参数编号获取详细信息\r\n\r\n @param configId 参数ID\r\n"}, {"name": "getConfigKey", "paramTypes": ["java.lang.String"], "doc": "\n 根据参数键名查询参数值\r\n\r\n @param configKey 参数Key\r\n"}, {"name": "add", "paramTypes": ["org.dromara.system.domain.bo.SysConfigBo"], "doc": "\n 新增参数配置\r\n"}, {"name": "edit", "paramTypes": ["org.dromara.system.domain.bo.SysConfigBo"], "doc": "\n 修改参数配置\r\n"}, {"name": "updateByKey", "paramTypes": ["org.dromara.system.domain.bo.SysConfigBo"], "doc": "\n 根据参数键名修改参数配置\r\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": "\n 删除参数配置\r\n\r\n @param configIds 参数ID串\r\n"}, {"name": "refreshCache", "paramTypes": [], "doc": "\n 刷新参数缓存\r\n"}], "constructors": []}