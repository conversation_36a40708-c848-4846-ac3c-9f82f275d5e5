package org.dromara.app.service;

import org.dromara.app.domain.vo.CacheStatisticsVo;

import java.util.List;
import java.util.Map;

/**
 * 缓存优化服务接口
 * 用于优化系统缓存策略和性能
 *
 * <AUTHOR>
 * @date 2025-01-19
 */
public interface ICacheOptimizationService {

    /**
     * 预热常用数据缓存
     */
    void warmupCache();

    /**
     * 清理过期缓存
     *
     * @return 清理的缓存数量
     */
    int cleanExpiredCache();

    /**
     * 获取缓存统计信息
     *
     * @return 缓存统计
     */
    CacheStatisticsVo getCacheStatistics();

    /**
     * 刷新指定缓存
     *
     * @param cacheKey 缓存键
     * @return 是否刷新成功
     */
    boolean refreshCache(String cacheKey);

    /**
     * 批量刷新缓存
     *
     * @param cacheKeys 缓存键列表
     * @return 刷新成功的数量
     */
    int batchRefreshCache(List<String> cacheKeys);

    /**
     * 获取热点数据
     *
     * @param limit 限制数量
     * @return 热点数据键值对
     */
    Map<String, Object> getHotData(int limit);

    /**
     * 设置缓存预热策略
     *
     * @param strategy 预热策略
     */
    void setCacheWarmupStrategy(String strategy);

    /**
     * 优化缓存配置
     */
    void optimizeCacheConfiguration();

    /**
     * 监控缓存性能
     *
     * @return 性能指标
     */
    Map<String, Object> monitorCachePerformance();

    /**
     * 分析缓存命中率
     *
     * @return 命中率分析结果
     */
    Map<String, Double> analyzeCacheHitRate();

    /**
     * 清理低频访问缓存
     *
     * @param threshold 访问频率阈值
     * @return 清理的缓存数量
     */
    int cleanLowFrequencyCache(int threshold);

    /**
     * 压缩缓存数据
     *
     * @return 压缩节省的空间大小（字节）
     */
    long compressCacheData();
}