package org.dromara.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.dromara.app.domain.LearningProgress;

import java.util.List;
import java.util.Map;

/**
 * 学习进度Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface LearningProgressMapper extends BaseMapper<LearningProgress> {

    /**
     * 根据用户ID查询学习进度
     *
     * @param userId 用户ID
     * @return 学习进度列表
     */
    @Select("SELECT * FROM app_learning_progress WHERE user_id = #{userId} ORDER BY last_study_time DESC")
    List<LearningProgress> selectByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID和状态查询学习进度
     *
     * @param userId 用户ID
     * @param status 状态
     * @return 学习进度列表
     */
    @Select("SELECT * FROM app_learning_progress WHERE user_id = #{userId} AND status = #{status} ORDER BY last_study_time DESC")
    List<LearningProgress> selectByUserIdAndStatus(@Param("userId") Long userId, @Param("status") String status);

    /**
     * 根据学习路径ID查询进度
     *
     * @param learningPathId 学习路径ID
     * @param userId 用户ID
     * @return 学习进度
     */
    @Select("SELECT * FROM app_learning_progress WHERE learning_path_id = #{learningPathId} AND user_id = #{userId}")
    LearningProgress selectByLearningPathAndUser(@Param("learningPathId") String learningPathId, @Param("userId") Long userId);

    /**
     * 根据资源ID查询进度
     *
     * @param resourceId 资源ID
     * @param userId 用户ID
     * @return 学习进度
     */
    @Select("SELECT * FROM app_learning_progress WHERE resource_id = #{resourceId} AND user_id = #{userId}")
    LearningProgress selectByResourceAndUser(@Param("resourceId") Long resourceId, @Param("userId") Long userId);

    /**
     * 查询用户学习统计
     *
     * @param userId 用户ID
     * @return 统计信息
     */
    @Select("SELECT " +
            "COUNT(*) as total_learning_items, " +
            "COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_items, " +
            "COUNT(CASE WHEN status = 'in_progress' THEN 1 END) as in_progress_items, " +
            "AVG(completion_percentage) as avg_completion_percentage, " +
            "SUM(actual_study_minutes) as total_study_minutes, " +
            "AVG(effectiveness_rating) as avg_effectiveness_rating, " +
            "AVG(satisfaction_rating) as avg_satisfaction_rating " +
            "FROM app_learning_progress WHERE user_id = #{userId}")
    Map<String, Object> selectUserLearningStatistics(@Param("userId") Long userId);

    /**
     * 查询最近的学习进度
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 学习进度列表
     */
    @Select("SELECT * FROM app_learning_progress " +
            "WHERE user_id = #{userId} AND last_study_time IS NOT NULL " +
            "ORDER BY last_study_time DESC " +
            "LIMIT #{limit}")
    List<LearningProgress> selectRecentLearningProgress(@Param("userId") Long userId, @Param("limit") Integer limit);

    /**
     * 查询正在进行的学习
     *
     * @param userId 用户ID
     * @return 学习进度列表
     */
    @Select("SELECT * FROM app_learning_progress " +
            "WHERE user_id = #{userId} AND status = 'in_progress' " +
            "ORDER BY last_study_time DESC")
    List<LearningProgress> selectOngoingLearning(@Param("userId") Long userId);

    /**
     * 查询已完成的学习
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 学习进度列表
     */
    @Select("SELECT * FROM app_learning_progress " +
            "WHERE user_id = #{userId} AND status = 'completed' " +
            "ORDER BY completion_time DESC " +
            "LIMIT #{limit}")
    List<LearningProgress> selectCompletedLearning(@Param("userId") Long userId, @Param("limit") Integer limit);

    /**
     * 查询学习效果统计
     *
     * @param userId 用户ID
     * @return 效果统计
     */
    @Select("SELECT " +
            "AVG(effectiveness_rating) as avg_effectiveness, " +
            "AVG(difficulty_rating) as avg_difficulty, " +
            "AVG(satisfaction_rating) as avg_satisfaction, " +
            "COUNT(CASE WHEN effectiveness_rating >= 4.0 THEN 1 END) as high_effectiveness_count, " +
            "COUNT(CASE WHEN satisfaction_rating >= 4.0 THEN 1 END) as high_satisfaction_count " +
            "FROM app_learning_progress " +
            "WHERE user_id = #{userId} AND status = 'completed'")
    Map<String, Object> selectLearningEffectivenessStats(@Param("userId") Long userId);

    /**
     * 查询学习时间统计
     *
     * @param userId 用户ID
     * @return 时间统计
     */
    @Select("SELECT " +
            "SUM(actual_study_minutes) as total_minutes, " +
            "AVG(actual_study_minutes) as avg_minutes_per_item, " +
            "MAX(actual_study_minutes) as max_minutes, " +
            "COUNT(DISTINCT DATE(last_study_time)) as study_days " +
            "FROM app_learning_progress " +
            "WHERE user_id = #{userId} AND actual_study_minutes > 0")
    Map<String, Object> selectStudyTimeStatistics(@Param("userId") Long userId);

    /**
     * 查询学习趋势数据
     *
     * @param userId 用户ID
     * @param days 天数
     * @return 趋势数据
     */
    @Select("SELECT " +
            "DATE(last_study_time) as study_date, " +
            "COUNT(*) as study_sessions, " +
            "SUM(actual_study_minutes) as total_minutes, " +
            "AVG(completion_percentage) as avg_completion " +
            "FROM app_learning_progress " +
            "WHERE user_id = #{userId} AND last_study_time >= DATE_SUB(NOW(), INTERVAL #{days} DAY) " +
            "GROUP BY DATE(last_study_time) " +
            "ORDER BY study_date DESC")
    List<Map<String, Object>> selectLearningTrend(@Param("userId") Long userId, @Param("days") Integer days);

    /**
     * 查询需要提醒的学习项目
     *
     * @return 需要提醒的学习进度列表
     */
    @Select("SELECT * FROM app_learning_progress " +
            "WHERE status = 'in_progress' " +
            "AND JSON_EXTRACT(learning_plan, '$.reminderSettings.enabled') = true " +
            "AND (last_study_time IS NULL OR last_study_time < DATE_SUB(NOW(), INTERVAL 1 DAY))")
    List<LearningProgress> selectItemsNeedingReminder();

    /**
     * 查询超期的学习项目
     *
     * @param userId 用户ID
     * @return 超期学习进度列表
     */
    @Select("SELECT * FROM app_learning_progress " +
            "WHERE user_id = #{userId} " +
            "AND status IN ('not_started', 'in_progress') " +
            "AND estimated_completion_time < NOW()")
    List<LearningProgress> selectOverdueLearning(@Param("userId") Long userId);

    /**
     * 更新学习统计
     *
     * @param progressId 进度ID
     * @param statisticsJson 统计JSON
     * @return 更新行数
     */
    @Select("UPDATE app_learning_progress " +
            "SET learning_statistics = #{statisticsJson} " +
            "WHERE id = #{progressId}")
    int updateLearningStatistics(@Param("progressId") Long progressId, @Param("statisticsJson") String statisticsJson);
}