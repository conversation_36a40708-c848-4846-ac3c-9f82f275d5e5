package org.dromara.app.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.app.domain.InterviewResult;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 面试结果视图对象 app_interview_result
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@Data
@AutoMapper(target = InterviewResult.class)
public class InterviewResultVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 结果ID
     */
    private String id;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 岗位ID
     */
    private Long jobId;

    /**
     * 岗位名称
     */
    private String jobName;

    /**
     * 公司名称
     */
    private String company;

    /**
     * 面试模式
     */
    private String mode;

    /**
     * 面试日期
     */
    private LocalDateTime date;

    /**
     * 面试时长
     */
    private String duration;

    /**
     * 总分
     */
    private Integer totalScore;

    /**
     * 等级（excellent,good,average,poor）
     */
    private String rank;

    /**
     * 等级文本
     */
    private String rankText;

    /**
     * 百分位数
     */
    private Integer percentile;

    /**
     * 已回答问题数
     */
    private Integer answeredQuestions;

    /**
     * 总问题数
     */
    private Integer totalQuestions;

    /**
     * 状态（completed,partial,cancelled）
     */
    private String status;

    /**
     * 主要优势（JSON数组）
     */
    private List<String> topStrengths;

    /**
     * 主要劣势（JSON数组）
     */
    private List<String> topWeaknesses;

    /**
     * 总体反馈
     */
    private String overallFeedback;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

}
