{"doc": "\n 公告 服务层\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectNoticeById", "paramTypes": ["java.lang.Long"], "doc": "\n 查询公告信息\r\n\r\n @param noticeId 公告ID\r\n @return 公告信息\r\n"}, {"name": "selectNoticeList", "paramTypes": ["org.dromara.system.domain.bo.SysNoticeBo"], "doc": "\n 查询公告列表\r\n\r\n @param notice 公告信息\r\n @return 公告集合\r\n"}, {"name": "insertNotice", "paramTypes": ["org.dromara.system.domain.bo.SysNoticeBo"], "doc": "\n 新增公告\r\n\r\n @param bo 公告信息\r\n @return 结果\r\n"}, {"name": "updateNotice", "paramTypes": ["org.dromara.system.domain.bo.SysNoticeBo"], "doc": "\n 修改公告\r\n\r\n @param bo 公告信息\r\n @return 结果\r\n"}, {"name": "deleteNoticeById", "paramTypes": ["java.lang.Long"], "doc": "\n 删除公告信息\r\n\r\n @param noticeId 公告ID\r\n @return 结果\r\n"}, {"name": "deleteNoticeByIds", "paramTypes": ["java.lang.Long[]"], "doc": "\n 批量删除公告信息\r\n\r\n @param noticeIds 需要删除的公告ID\r\n @return 结果\r\n"}], "constructors": []}