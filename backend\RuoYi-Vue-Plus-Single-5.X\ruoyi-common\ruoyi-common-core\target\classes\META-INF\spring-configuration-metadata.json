{"groups": [{"name": "thread-pool", "type": "org.dromara.common.core.config.properties.ThreadPoolProperties", "sourceType": "org.dromara.common.core.config.properties.ThreadPoolProperties"}], "properties": [{"name": "thread-pool.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否开启线程池", "sourceType": "org.dromara.common.core.config.properties.ThreadPoolProperties"}, {"name": "thread-pool.keep-alive-seconds", "type": "java.lang.Integer", "description": "线程池维护线程所允许的空闲时间", "sourceType": "org.dromara.common.core.config.properties.ThreadPoolProperties"}, {"name": "thread-pool.queue-capacity", "type": "java.lang.Integer", "description": "队列最大长度", "sourceType": "org.dromara.common.core.config.properties.ThreadPoolProperties"}], "hints": []}