<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 定义翠绿色主题渐变色 -->
    <linearGradient id="backgroundGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#00D4AA;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00B894;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="greenGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#00E5CC;stop-opacity:0.85" />
      <stop offset="100%" style="stop-color:#00D4AA;stop-opacity:0.85" />
    </linearGradient>
    <linearGradient id="greenGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#26E5D6;stop-opacity:0.85" />
      <stop offset="100%" style="stop-color:#00E5CC;stop-opacity:0.85" />
    </linearGradient>
    <linearGradient id="greenGradient3" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4DECDB;stop-opacity:0.85" />
      <stop offset="100%" style="stop-color:#26E5D6;stop-opacity:0.85" />
    </linearGradient>
    <linearGradient id="greenGradient4" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#74F2E0;stop-opacity:0.85" />
      <stop offset="100%" style="stop-color:#4DECDB;stop-opacity:0.85" />
    </linearGradient>
    <linearGradient id="greenGradient5" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#00D4AA;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#00B894;stop-opacity:0.9" />
    </linearGradient>
    <linearGradient id="greenGradient6" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#00E5CC;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#00D4AA;stop-opacity:0.8" />
    </linearGradient>
    <linearGradient id="greenGradient7" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#26E5D6;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#00E5CC;stop-opacity:0.8" />
    </linearGradient>
    
    <!-- 箭头标记 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" 
            refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#666" />
    </marker>
  </defs>
  
  <!-- 背景 -->
  <rect width="1200" height="800" fill="url(#backgroundGradient)"/>
  
  <!-- 标题 -->
  <text x="600" y="40" text-anchor="middle" font-family="Arial, sans-serif"
        font-size="24" font-weight="bold" fill="white">
    AI - 7个Agent协同工作流程图
  </text>
  
  <!-- 面试者输入区域 -->
  <rect x="50" y="80" width="200" height="60" rx="10" fill="url(#greenGradient4)" stroke="#00B894" stroke-width="2"/>
  <text x="150" y="105" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">面试者</text>
  <text x="150" y="125" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">视频+音频+文本输入</text>
  
  <!-- 视觉Agent -->
  <rect x="100" y="200" width="120" height="80" rx="10" fill="url(#greenGradient1)" stroke="#00B894" stroke-width="2"/>
  <text x="160" y="225" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">视觉Agent</text>
  <text x="160" y="245" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">面部表情分析</text>
  <text x="160" y="260" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">肢体语言识别</text>
  <text x="160" y="275" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">整体形象评估</text>

  <!-- 语音Agent -->
  <rect x="280" y="200" width="120" height="80" rx="10" fill="url(#greenGradient2)" stroke="#00B894" stroke-width="2"/>
  <text x="340" y="225" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">语音Agent</text>
  <text x="340" y="245" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">声纹情感分析</text>
  <text x="340" y="260" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">语速语调检测</text>
  <text x="340" y="275" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">压力指数计算</text>

  <!-- 文本Agent -->
  <rect x="460" y="200" width="120" height="80" rx="10" fill="url(#greenGradient3)" stroke="#00B894" stroke-width="2"/>
  <text x="520" y="225" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">文本Agent</text>
  <text x="520" y="245" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">语义理解分析</text>
  <text x="520" y="260" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">简历智能解析</text>
  <text x="520" y="275" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">回答质量评估</text>
  
  <!-- 决策Agent -->
  <rect x="320" y="350" width="140" height="80" rx="10" fill="url(#greenGradient5)" stroke="#00B894" stroke-width="2"/>
  <text x="390" y="375" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">决策Agent</text>
  <text x="390" y="395" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">多维数据融合</text>
  <text x="390" y="410" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">智能动态调权</text>
  <text x="390" y="425" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">综合评估决策</text>
  
  <!-- 公平性Agent -->
  <rect x="100" y="500" width="120" height="80" rx="10" fill="url(#greenGradient6)" stroke="#00B894" stroke-width="2"/>
  <text x="160" y="525" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">公平性Agent</text>
  <text x="160" y="545" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">偏见检测消除</text>
  <text x="160" y="560" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">敏感操作拦截</text>
  <text x="160" y="575" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">数据安全保障</text>

  <!-- 报告Agent -->
  <rect x="460" y="500" width="120" height="80" rx="10" fill="url(#greenGradient7)" stroke="#00B894" stroke-width="2"/>
  <text x="520" y="525" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">报告Agent</text>
  <text x="520" y="545" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">能力生长树</text>
  <text x="520" y="560" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">可视化呈现</text>
  <text x="520" y="575" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">改进方案生成</text>

  <!-- 调度Agent -->
  <rect x="800" y="350" width="120" height="80" rx="10" fill="url(#greenGradient1)" stroke="#00B894" stroke-width="2"/>
  <text x="860" y="375" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">调度Agent</text>
  <text x="860" y="395" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">资源智能调度</text>
  <text x="860" y="410" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">负载均衡优化</text>
  <text x="860" y="425" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">实时性能监控</text>
  
  <!-- 输出结果 -->
  <rect x="950" y="500" width="200" height="60" rx="10" fill="url(#greenGradient4)" stroke="#00B894" stroke-width="2"/>
  <text x="1050" y="525" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">面试评估报告</text>
  <text x="1050" y="545" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">综合评分+改进建议</text>
  
  <!-- 数据流箭头 -->
  <!-- 面试者到三个感知Agent -->
  <line x1="150" y1="140" x2="160" y2="200" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="150" y1="140" x2="340" y2="200" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="150" y1="140" x2="520" y2="200" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- 三个感知Agent到决策Agent -->
  <line x1="160" y1="280" x2="350" y2="350" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="340" y1="280" x2="390" y2="350" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="520" y1="280" x2="430" y2="350" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- 决策Agent到公平性Agent和报告Agent -->
  <line x1="350" y1="430" x2="200" y2="500" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="430" y1="430" x2="500" y2="500" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- 调度Agent的连接 -->
  <line x1="460" y1="390" x2="800" y2="390" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="800" y1="390" x2="600" y2="390" stroke="#666" stroke-width="2" stroke-dasharray="5,5"/>
  
  <!-- 报告Agent到最终输出 -->
  <line x1="580" y1="540" x2="950" y2="530" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- 公平性Agent的监控连接 -->
  <line x1="220" y1="540" x2="320" y2="540" stroke="#00D4AA" stroke-width="2" stroke-dasharray="3,3"/>
  <line x1="160" y1="500" x2="160" y2="430" stroke="#00D4AA" stroke-width="2" stroke-dasharray="3,3"/>
  
  <!-- 数据流说明 -->
  <text x="50" y="650" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">数据流说明：</text>
  <text x="50" y="675" font-family="Arial, sans-serif" font-size="12" fill="white">→ 实时数据流</text>
  <text x="200" y="675" font-family="Arial, sans-serif" font-size="12" fill="white">⟶ 控制信号</text>
  <text x="350" y="675" font-family="Arial, sans-serif" font-size="12" fill="#00D4AA">⟶ 监控反馈</text>

  <!-- 处理时间标注 -->
  <text x="50" y="720" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">性能指标：</text>
  <text x="50" y="745" font-family="Arial, sans-serif" font-size="12" fill="white">• 视觉分析延迟: &lt;100ms</text>
  <text x="250" y="745" font-family="Arial, sans-serif" font-size="12" fill="white">• 语音处理延迟: &lt;50ms</text>
  <text x="450" y="745" font-family="Arial, sans-serif" font-size="12" fill="white">• 文本分析延迟: &lt;200ms</text>
  <text x="650" y="745" font-family="Arial, sans-serif" font-size="12" fill="white">• 综合决策延迟: &lt;300ms</text>
  
  <!-- 协同工作说明框 -->
  <rect x="650" y="80" width="500" height="120" rx="10" fill="url(#greenGradient4)" stroke="#00B894" stroke-width="2"/>
  <text x="670" y="105" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">7个Agent协同工作机制：</text>
  <text x="670" y="125" font-family="Arial, sans-serif" font-size="11" fill="white">1. 视觉/语音/文本Agent并行处理多模态输入数据</text>
  <text x="670" y="140" font-family="Arial, sans-serif" font-size="11" fill="white">2. 决策Agent融合三维分析结果，进行智能权重调整</text>
  <text x="670" y="155" font-family="Arial, sans-serif" font-size="11" fill="white">3. 公平性Agent实时监控，确保评估公正性和数据安全</text>
  <text x="670" y="170" font-family="Arial, sans-serif" font-size="11" fill="white">4. 调度Agent优化资源分配，保障系统高性能运行</text>
  <text x="670" y="185" font-family="Arial, sans-serif" font-size="11" fill="white">5. 报告Agent生成可视化分析报告和个性化改进方案</text>
</svg>
