package org.dromara.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.dromara.app.domain.ActivitySummary;
import org.dromara.app.domain.enums.ActivityType;

/**
 * 用户活动总览Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Mapper
public interface ActivitySummaryMapper extends BaseMapper<ActivitySummary> {

    /**
     * 根据用户ID查询活动总览
     *
     * @param userId 用户ID
     * @return 活动总览
     */
    @Select("SELECT * FROM app_activity_summary WHERE user_id = #{userId} AND del_flag = '0'")
    ActivitySummary selectByUserId(@Param("userId") Long userId);

    /**
     * 更新用户活动总览
     *
     * @param userId       用户ID
     * @param activityType 活动类型
     * @param duration     时长
     * @return 更新行数
     */
    @Update("UPDATE app_activity_summary SET " +
        "total_duration = total_duration + #{duration}, " +
        "total_sessions = total_sessions + 1, " +
        "course_duration = CASE WHEN #{activityType} = 'COURSE' THEN course_duration + #{duration} ELSE course_duration END, " +
        "interview_duration = CASE WHEN #{activityType} = 'INTERVIEW' THEN interview_duration + #{duration} ELSE interview_duration END, " +
        "book_duration = CASE WHEN #{activityType} = 'BOOK' THEN book_duration + #{duration} ELSE book_duration END, " +
        "video_duration = CASE WHEN #{activityType} = 'VIDEO' THEN video_duration + #{duration} ELSE video_duration END, " +
        "exercise_duration = CASE WHEN #{activityType} = 'EXERCISE' THEN exercise_duration + #{duration} ELSE exercise_duration END, " +
        "document_duration = CASE WHEN #{activityType} = 'DOCUMENT' THEN document_duration + #{duration} ELSE document_duration END, " +
        "other_duration = CASE WHEN #{activityType} = 'OTHER' THEN other_duration + #{duration} ELSE other_duration END, " +
        "last_activity_time = NOW(), " +
        "update_time = NOW() " +
        "WHERE user_id = #{userId}")
    int updateSummary(@Param("userId") Long userId,
                      @Param("activityType") ActivityType activityType,
                      @Param("duration") Long duration);

    /**
     * 重置用户活动总览
     *
     * @param userId       用户ID
     * @param activityType 活动类型(可选，为空时重置所有)
     * @return 更新行数
     */
    @Update("UPDATE app_activity_summary SET " +
        "total_duration = CASE WHEN #{activityType} IS NULL THEN 0 ELSE total_duration END, " +
        "total_sessions = CASE WHEN #{activityType} IS NULL THEN 0 ELSE total_sessions END, " +
        "course_duration = CASE WHEN #{activityType} IS NULL OR #{activityType} = 'COURSE' THEN 0 ELSE course_duration END, " +
        "interview_duration = CASE WHEN #{activityType} IS NULL OR #{activityType} = 'INTERVIEW' THEN 0 ELSE interview_duration END, " +
        "book_duration = CASE WHEN #{activityType} IS NULL OR #{activityType} = 'BOOK' THEN 0 ELSE book_duration END, " +
        "video_duration = CASE WHEN #{activityType} IS NULL OR #{activityType} = 'VIDEO' THEN 0 ELSE video_duration END, " +
        "exercise_duration = CASE WHEN #{activityType} IS NULL OR #{activityType} = 'EXERCISE' THEN 0 ELSE exercise_duration END, " +
        "document_duration = CASE WHEN #{activityType} IS NULL OR #{activityType} = 'DOCUMENT' THEN 0 ELSE document_duration END, " +
        "other_duration = CASE WHEN #{activityType} IS NULL OR #{activityType} = 'OTHER' THEN 0 ELSE other_duration END, " +
        "update_time = NOW() " +
        "WHERE user_id = #{userId}")
    int resetSummary(@Param("userId") Long userId, @Param("activityType") ActivityType activityType);
}
