package org.dromara.app.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.app.domain.QuestionComment;
import org.dromara.app.domain.bo.QuestionCommentBo;
import org.dromara.app.domain.vo.QuestionCommentVO;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 题目评论Service接口
 *
 * <AUTHOR>
 */
public interface IQuestionCommentService extends IService<QuestionComment> {

    /**
     * 查询题目评论
     *
     * @param commentId 评论主键
     * @return 题目评论
     */
    QuestionCommentVO queryById(Long commentId);

    /**
     * 查询题目评论列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页查询条件
     * @return 题目评论分页列表
     */
    TableDataInfo<QuestionCommentVO> queryPageList(QuestionCommentBo bo, PageQuery pageQuery);

    /**
     * 查询题目评论列表
     *
     * @param bo 查询条件
     * @return 题目评论列表
     */
    List<QuestionCommentVO> queryList(QuestionCommentBo bo);

    /**
     * 新增题目评论
     *
     * @param bo 题目评论信息
     * @return 新增结果
     */
    Boolean insertByBo(QuestionCommentBo bo);

    /**
     * 修改题目评论
     *
     * @param bo 题目评论信息
     * @return 修改结果
     */
    Boolean updateByBo(QuestionCommentBo bo);

    /**
     * 校验并批量删除题目评论信息
     *
     * @param ids 待删除的主键集合
     * @return 删除结果
     */
    Boolean deleteWithValidByIds(Collection<Long> ids);

    /**
     * 获取题目评论列表（包含回复）
     *
     * @param questionId     题目ID
     * @param page           页码
     * @param pageSize       每页大小
     * @param orderBy        排序字段
     * @param orderDirection 排序方向
     * @return 评论列表
     */
    Map<String, Object> getQuestionComments(String questionId, Integer page, Integer pageSize,
                                            String orderBy, String orderDirection);

    /**
     * 创建题目评论
     *
     * @param userId     用户ID
     * @param questionId 题目ID
     * @param content    评论内容
     * @param parentId   父评论ID
     * @return 创建的评论
     */
    QuestionCommentVO createQuestionComment(Long userId, String questionId, String content, Long parentId);

    /**
     * 删除题目评论
     *
     * @param userId    用户ID
     * @param commentId 评论ID
     * @return 删除结果
     */
    Boolean deleteQuestionComment(Long userId, String commentId);

    /**
     * 点赞/取消点赞评论
     *
     * @param userId    用户ID
     * @param commentId 评论ID
     * @return 点赞结果
     */
    Map<String, Object> likeQuestionComment(Long userId, String commentId);

    /**
     * 获取评论的回复列表
     *
     * @param parentId 父评论ID
     * @return 回复列表
     */
    List<QuestionCommentVO> getRepliesByParentId(Long parentId);

    /**
     * 检查用户是否已点赞评论
     *
     * @param userId    用户ID
     * @param commentId 评论ID
     * @return 是否已点赞
     */
    Boolean checkUserLikeStatus(Long userId, Long commentId);

    /**
     * 批量删除评论（管理员功能）
     *
     * @param commentIds 评论ID列表
     * @param operatorId 操作者ID
     * @return 删除结果
     */
    Boolean batchDeleteComments(List<Long> commentIds, Long operatorId);

    /**
     * 审核评论（管理员功能）
     *
     * @param commentId  评论ID
     * @param status     审核状态
     * @param operatorId 操作者ID
     * @param reason     审核原因
     * @return 审核结果
     */
    Boolean auditComment(Long commentId, String status, Long operatorId, String reason);

    /**
     * 置顶/取消置顶评论
     *
     * @param commentId  评论ID
     * @param isTop      是否置顶
     * @param operatorId 操作者ID
     * @return 操作结果
     */
    Boolean toggleCommentTop(Long commentId, Boolean isTop, Long operatorId);

    /**
     * 获取评论统计信息
     *
     * @param questionId 题目ID
     * @return 统计信息
     */
    Map<String, Object> getCommentStatistics(String questionId);

    /**
     * 搜索评论
     *
     * @param keyword   搜索关键词
     * @param pageQuery 分页查询条件
     * @return 搜索结果
     */
    TableDataInfo<QuestionCommentVO> searchComments(String keyword, PageQuery pageQuery);

    /**
     * 举报评论
     *
     * @param userId      举报用户ID
     * @param commentId   评论ID
     * @param reason      举报原因
     * @param description 详细描述
     * @return 举报结果
     */
    Boolean reportComment(Long userId, String commentId, String reason, String description);

    /**
     * 导出评论列表
     *
     * @param bo 查询条件
     * @return 评论列表
     */
    List<QuestionCommentVO> exportCommentList(QuestionCommentBo bo);
}
