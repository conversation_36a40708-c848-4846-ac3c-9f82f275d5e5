package org.dromara.app.service;

import org.dromara.app.domain.InterviewReport;
import org.dromara.app.service.IMultimodalAnalysisService.DimensionScore;

import java.util.List;
import java.util.Map;

/**
 * 学习推荐服务接口
 *
 * <AUTHOR>
 */
public interface ILearningRecommendationService {

    /**
     * 基于面试报告生成学习路径推荐
     *
     * @param report 面试报告
     * @return 学习路径推荐列表
     */
    List<LearningPathRecommendation> generateLearningPaths(InterviewReport report);

    /**
     * 基于弱项生成学习推荐
     *
     * @param weaknesses 弱项列表
     * @param jobPosition 岗位信息
     * @return 学习路径推荐列表
     */
    List<LearningPathRecommendation> generatePathsByWeaknesses(List<String> weaknesses, String jobPosition);

    /**
     * 基于岗位生成学习推荐
     *
     * @param jobPosition 岗位信息
     * @param userLevel 用户水平
     * @return 学习路径推荐列表
     */
    List<LearningPathRecommendation> generatePathsByJobPosition(String jobPosition, String userLevel);

    /**
     * 基于维度评分生成学习推荐
     *
     * @param dimensionScores 维度评分列表
     * @param jobPosition 岗位信息
     * @return 学习路径推荐列表
     */
    List<LearningPathRecommendation> generatePathsByDimensionScores(List<DimensionScore> dimensionScores, String jobPosition);

    /**
     * 获取学习资源推荐
     *
     * @param skillArea 技能领域
     * @param difficulty 难度等级
     * @param resourceType 资源类型
     * @return 学习资源列表
     */
    List<LearningResource> getRecommendedResources(String skillArea, String difficulty, String resourceType);

    /**
     * 计算学习路径优先级
     *
     * @param weaknesses 弱项列表
     * @param dimensionScores 维度评分
     * @param jobPosition 岗位信息
     * @return 优先级映射
     */
    Map<String, Integer> calculateLearningPriorities(List<String> weaknesses, 
                                                    List<DimensionScore> dimensionScores, 
                                                    String jobPosition);

    /**
     * 个性化学习路径调整
     *
     * @param basePaths 基础学习路径
     * @param userProfile 用户画像
     * @return 调整后的学习路径
     */
    List<LearningPathRecommendation> personalizelearningPaths(List<LearningPathRecommendation> basePaths, 
                                                              UserProfile userProfile);

    /**
     * 获取学习路径详情
     *
     * @param pathId 路径ID
     * @return 学习路径详情
     */
    LearningPathRecommendation getLearningPathDetail(String pathId);

    /**
     * 学习路径推荐
     */
    class LearningPathRecommendation {
        private String id;
        private String title;
        private String description;
        private String category;
        private String skillArea;
        private Integer estimatedHours;
        private String difficulty;
        private Integer priority;
        private List<LearningResource> resources;
        private List<String> milestones;
        private List<String> prerequisites;
        private Map<String, Object> metadata;

        // Getters and Setters
        public String getId() { return id; }
        public void setId(String id) { this.id = id; }
        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        public String getCategory() { return category; }
        public void setCategory(String category) { this.category = category; }
        public String getSkillArea() { return skillArea; }
        public void setSkillArea(String skillArea) { this.skillArea = skillArea; }
        public Integer getEstimatedHours() { return estimatedHours; }
        public void setEstimatedHours(Integer estimatedHours) { this.estimatedHours = estimatedHours; }
        public String getDifficulty() { return difficulty; }
        public void setDifficulty(String difficulty) { this.difficulty = difficulty; }
        public Integer getPriority() { return priority; }
        public void setPriority(Integer priority) { this.priority = priority; }
        public List<LearningResource> getResources() { return resources; }
        public void setResources(List<LearningResource> resources) { this.resources = resources; }
        public List<String> getMilestones() { return milestones; }
        public void setMilestones(List<String> milestones) { this.milestones = milestones; }
        public List<String> getPrerequisites() { return prerequisites; }
        public void setPrerequisites(List<String> prerequisites) { this.prerequisites = prerequisites; }
        public Map<String, Object> getMetadata() { return metadata; }
        public void setMetadata(Map<String, Object> metadata) { this.metadata = metadata; }
    }

    /**
     * 学习资源
     */
    class LearningResource {
        private String id;
        private String title;
        private String type; // video, article, course, practice, book
        private String description;
        private String url;
        private String duration;
        private String difficulty;
        private Double rating;
        private String provider;
        private List<String> tags;
        private Boolean isFree;
        private String language;

        // Getters and Setters
        public String getId() { return id; }
        public void setId(String id) { this.id = id; }
        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        public String getUrl() { return url; }
        public void setUrl(String url) { this.url = url; }
        public String getDuration() { return duration; }
        public void setDuration(String duration) { this.duration = duration; }
        public String getDifficulty() { return difficulty; }
        public void setDifficulty(String difficulty) { this.difficulty = difficulty; }
        public Double getRating() { return rating; }
        public void setRating(Double rating) { this.rating = rating; }
        public String getProvider() { return provider; }
        public void setProvider(String provider) { this.provider = provider; }
        public List<String> getTags() { return tags; }
        public void setTags(List<String> tags) { this.tags = tags; }
        public Boolean getIsFree() { return isFree; }
        public void setIsFree(Boolean isFree) { this.isFree = isFree; }
        public String getLanguage() { return language; }
        public void setLanguage(String language) { this.language = language; }
    }

    /**
     * 用户画像
     */
    class UserProfile {
        private String userId;
        private String learningStyle; // visual, auditory, kinesthetic, reading
        private String preferredLanguage;
        private Integer availableHoursPerWeek;
        private String experienceLevel;
        private List<String> completedCourses;
        private List<String> interests;
        private Map<String, Object> preferences;

        // Getters and Setters
        public String getUserId() { return userId; }
        public void setUserId(String userId) { this.userId = userId; }
        public String getLearningStyle() { return learningStyle; }
        public void setLearningStyle(String learningStyle) { this.learningStyle = learningStyle; }
        public String getPreferredLanguage() { return preferredLanguage; }
        public void setPreferredLanguage(String preferredLanguage) { this.preferredLanguage = preferredLanguage; }
        public Integer getAvailableHoursPerWeek() { return availableHoursPerWeek; }
        public void setAvailableHoursPerWeek(Integer availableHoursPerWeek) { this.availableHoursPerWeek = availableHoursPerWeek; }
        public String getExperienceLevel() { return experienceLevel; }
        public void setExperienceLevel(String experienceLevel) { this.experienceLevel = experienceLevel; }
        public List<String> getCompletedCourses() { return completedCourses; }
        public void setCompletedCourses(List<String> completedCourses) { this.completedCourses = completedCourses; }
        public List<String> getInterests() { return interests; }
        public void setInterests(List<String> interests) { this.interests = interests; }
        public Map<String, Object> getPreferences() { return preferences; }
        public void setPreferences(Map<String, Object> preferences) { this.preferences = preferences; }
    }
}