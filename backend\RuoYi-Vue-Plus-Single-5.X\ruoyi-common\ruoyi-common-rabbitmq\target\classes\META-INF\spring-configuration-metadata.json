{"groups": [{"name": "spring.rabbitmq", "type": "org.dromara.common.rabbitmq.properties.RabbitMqProperties", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties"}, {"name": "spring.rabbitmq.listener", "type": "org.dromara.common.rabbitmq.properties.RabbitMqProperties$Listener", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties", "sourceMethod": "public org.dromara.common.rabbitmq.properties.RabbitMqProperties.Listener getListener() "}, {"name": "spring.rabbitmq.listener.retry", "type": "org.dromara.common.rabbitmq.properties.RabbitMqProperties$Retry", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties$Listener", "sourceMethod": "public org.dromara.common.rabbitmq.properties.RabbitMqProperties.Retry getRetry() "}, {"name": "spring.rabbitmq.retry", "type": "org.dromara.common.rabbitmq.properties.RabbitMqProperties$Retry", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties", "sourceMethod": "public org.dromara.common.rabbitmq.properties.RabbitMqProperties.Retry getRetry() "}], "properties": [{"name": "spring.rabbitmq.channel-cache-size", "type": "java.lang.Integer", "description": "通道缓存大小", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties"}, {"name": "spring.rabbitmq.connection-timeout", "type": "java.lang.Integer", "description": "连接超时时间（毫秒）", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties"}, {"name": "spring.rabbitmq.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用 RabbitMQ", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties"}, {"name": "spring.rabbitmq.host", "type": "java.lang.String", "description": "主机地址", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties"}, {"name": "spring.rabbitmq.listener.concurrency", "type": "java.lang.Integer", "description": "并发消费者数量", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties$Listener"}, {"name": "spring.rabbitmq.listener.max-concurrency", "type": "java.lang.Integer", "description": "最大并发消费者数量", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties$Listener"}, {"name": "spring.rabbitmq.listener.prefetch", "type": "java.lang.Integer", "description": "预取数量", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties$Listener"}, {"name": "spring.rabbitmq.listener.retry.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用重试", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties$Retry"}, {"name": "spring.rabbitmq.listener.retry.initial-interval", "type": "java.lang.Long", "description": "初始重试间隔（毫秒）", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties$Retry"}, {"name": "spring.rabbitmq.listener.retry.max-attempts", "type": "java.lang.Integer", "description": "最大重试次数", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties$Retry"}, {"name": "spring.rabbitmq.listener.retry.max-interval", "type": "java.lang.Long", "description": "最大重试间隔（毫秒）", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties$Retry"}, {"name": "spring.rabbitmq.listener.retry.multiplier", "type": "java.lang.Double", "description": "重试间隔倍数", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties$Retry"}, {"name": "spring.rabbitmq.password", "type": "java.lang.String", "description": "密码", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties"}, {"name": "spring.rabbitmq.port", "type": "java.lang.Integer", "description": "端口", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties"}, {"name": "spring.rabbitmq.retry.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用重试", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties$Retry"}, {"name": "spring.rabbitmq.retry.initial-interval", "type": "java.lang.Long", "description": "初始重试间隔（毫秒）", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties$Retry"}, {"name": "spring.rabbitmq.retry.max-attempts", "type": "java.lang.Integer", "description": "最大重试次数", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties$Retry"}, {"name": "spring.rabbitmq.retry.max-interval", "type": "java.lang.Long", "description": "最大重试间隔（毫秒）", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties$Retry"}, {"name": "spring.rabbitmq.retry.multiplier", "type": "java.lang.Double", "description": "重试间隔倍数", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties$Retry"}, {"name": "spring.rabbitmq.username", "type": "java.lang.String", "description": "用户名", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties"}, {"name": "spring.rabbitmq.virtual-host", "type": "java.lang.String", "description": "虚拟主机", "sourceType": "org.dromara.common.rabbitmq.properties.RabbitMqProperties"}], "hints": []}