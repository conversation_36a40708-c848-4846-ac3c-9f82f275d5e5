{"doc": "\n Caffeine缓存注解切面\r\n\r\n <AUTHOR>\r\n", "fields": [], "enumConstants": [], "methods": [{"name": "handleCaffeineCache", "paramTypes": ["org.aspectj.lang.ProceedingJoinPoint", "org.dromara.common.caffeine.annotation.CaffeineCache"], "doc": "\n @CaffeineCache注解处理\r\n"}, {"name": "handleCaffeinePut", "paramTypes": ["org.aspectj.lang.ProceedingJoinPoint", "org.dromara.common.caffeine.annotation.CaffeinePut"], "doc": "\n @CaffeinePut注解处理\r\n"}, {"name": "handleCaffeineEvict", "paramTypes": ["org.aspectj.lang.ProceedingJoinPoint", "org.dromara.common.caffeine.annotation.CaffeineEvict"], "doc": "\n @CaffeineEvict注解处理\r\n"}, {"name": "evictCache", "paramTypes": ["org.dromara.common.caffeine.annotation.CaffeineEvict", "java.lang.Object", "java.lang.reflect.Method", "java.lang.Object[]"], "doc": "\n 清除缓存\r\n"}, {"name": "generate<PERSON>ache<PERSON>ey", "paramTypes": ["java.lang.String", "java.lang.String", "java.lang.Object", "java.lang.reflect.Method", "java.lang.Object[]"], "doc": "\n 生成缓存键\r\n"}, {"name": "checkCondition", "paramTypes": ["java.lang.String", "java.lang.Object", "java.lang.reflect.Method", "java.lang.Object[]"], "doc": "\n 检查条件\r\n"}, {"name": "checkCondition", "paramTypes": ["java.lang.String", "java.lang.Object", "java.lang.reflect.Method", "java.lang.Object[]", "java.lang.Object"], "doc": "\n 检查条件\r\n"}, {"name": "parseTtl", "paramTypes": ["java.lang.String", "org.dromara.common.caffeine.annotation.TimeUnit", "java.lang.Object", "java.lang.reflect.Method", "java.lang.Object[]", "java.lang.Object"], "doc": "\n 解析过期时间\r\n\r\n @param expireExpression 过期时间表达式\r\n @param timeUnit         时间单位\r\n @param method           方法\r\n @param args             参数\r\n @param result           返回值\r\n @return 过期时间（秒），-1表示使用默认配置，0表示永不过期\r\n"}], "constructors": []}