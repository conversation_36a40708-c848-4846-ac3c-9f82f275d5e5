package org.dromara.app.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 支付订单返回VO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "支付订单返回信息")
public class PaymentOrderVo {

    /**
     * 订单ID
     */
    @Schema(description = "订单ID", example = "1")
    private Long orderId;

    /**
     * 订单号
     */
    @Schema(description = "订单号", example = "PAY20240101123456789")
    private String orderNo;

    /**
     * 商品ID
     */
    @Schema(description = "商品ID", example = "1")
    private Long productId;

    /**
     * 商品类型
     */
    @Schema(description = "商品类型", example = "book")
    private String productType;

    /**
     * 商品标题
     */
    @Schema(description = "商品标题", example = "技术面试完全指南")
    private String productTitle;

    /**
     * 支付金额
     */
    @Schema(description = "支付金额", example = "59.00")
    private BigDecimal amount;

    /**
     * 支付方式
     */
    @Schema(description = "支付方式", example = "alipay")
    private String paymentMethod;

    /**
     * 订单状态
     */
    @Schema(description = "订单状态", example = "pending", allowableValues = {"pending", "paid", "cancelled", "expired"})
    private String status;

    /**
     * 支付宝交易号
     */
    @Schema(description = "支付宝交易号")
    private String alipayTradeNo;

    /**
     * 支付页面URL
     */
    @Schema(description = "支付页面URL")
    private String payUrl;

    /**
     * 支付页面表单HTML
     */
    @Schema(description = "支付页面表单HTML")
    private String payForm;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 支付时间
     */
    @Schema(description = "支付时间")
    private LocalDateTime payTime;

    /**
     * 过期时间
     */
    @Schema(description = "过期时间")
    private LocalDateTime expireTime;

    /**
     * 备注信息
     */
    @Schema(description = "备注信息")
    private String remark;

    /**
     * 支付token
     */
    @Schema(description = "支付token，用于支付验证，有效期30分钟")
    private String payToken;
}
