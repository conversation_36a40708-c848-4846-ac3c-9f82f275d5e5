package org.dromara.app.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.dromara.app.domain.AiTool;
import org.dromara.app.domain.ToolCall;

import java.util.List;
import java.util.Map;

/**
 * AI工具服务接口
 *
 * <AUTHOR>
 */
public interface IToolService {

    // ========== 工具管理 ==========

    /**
     * 获取可用工具列表
     *
     * @param category 工具分类（可选）
     * @param userId   用户ID
     * @return 工具列表
     */
    List<AiTool> getAvailableTools(String category, Long userId);

    /**
     * 获取工具详情
     *
     * @param toolId 工具ID
     * @param userId 用户ID
     * @return 工具详情
     */
    AiTool getToolDetail(String toolId, Long userId);

    /**
     * 检查工具权限
     *
     * @param toolId 工具ID
     * @param userId 用户ID
     * @return 是否有权限
     */
    boolean checkToolPermission(String toolId, Long userId);

    /**
     * 注册工具
     *
     * @param tool 工具信息
     * @return 是否成功
     */
    boolean registerTool(AiTool tool);

    /**
     * 更新工具
     *
     * @param tool 工具信息
     * @return 是否成功
     */
    boolean updateTool(AiTool tool);

    /**
     * 删除工具
     *
     * @param toolId 工具ID
     * @return 是否成功
     */
    boolean deleteTool(String toolId);

    /**
     * 启用/禁用工具
     *
     * @param toolId  工具ID
     * @param enabled 是否启用
     * @return 是否成功
     */
    boolean toggleTool(String toolId, Boolean enabled);

    // ========== 工具调用 ==========

    /**
     * 执行工具调用
     *
     * @param toolId     工具ID
     * @param parameters 调用参数
     * @param context    调用上下文
     * @param userId     用户ID
     * @return 调用结果
     */
    ToolCallResult executeTool(String toolId, Map<String, Object> parameters, ToolCallContext context, Long userId);

    /**
     * 异步执行工具调用
     *
     * @param toolId     工具ID
     * @param parameters 调用参数
     * @param context    调用上下文
     * @param userId     用户ID
     * @return 调用记录ID
     */
    String executeToolAsync(String toolId, Map<String, Object> parameters, ToolCallContext context, Long userId);

    /**
     * 批量执行工具调用
     *
     * @param toolCalls 工具调用列表
     * @param userId    用户ID
     * @return 调用结果列表
     */
    List<ToolCallResult> executeBatchTools(List<BatchToolCall> toolCalls, Long userId);

    /**
     * 解析AI消息中的工具调用
     *
     * @param aiMessage AI消息内容
     * @return 工具调用列表
     */
    List<ParsedToolCall> parseToolCalls(String aiMessage);

    /**
     * 验证工具调用参数
     *
     * @param toolId     工具ID
     * @param parameters 参数
     * @return 验证结果
     */
    ParameterValidationResult validateParameters(String toolId, Map<String, Object> parameters);

    // ========== 调用记录 ==========

    /**
     * 获取工具调用记录
     *
     * @param userId   用户ID
     * @param pageNum  页码
     * @param pageSize 每页大小
     * @return 调用记录分页结果
     */
    Page<ToolCall> getToolCallHistory(Long userId, Integer pageNum, Integer pageSize);

    /**
     * 获取会话的工具调用记录
     *
     * @param sessionId 会话ID
     * @param userId    用户ID
     * @return 调用记录列表
     */
    List<ToolCall> getSessionToolCalls(String sessionId, Long userId);

    /**
     * 获取工具调用详情
     *
     * @param callId 调用记录ID
     * @param userId 用户ID
     * @return 调用详情
     */
    ToolCall getToolCallDetail(String callId, Long userId);

    /**
     * 重新执行工具调用
     *
     * @param callId 调用记录ID
     * @param userId 用户ID
     * @return 新的调用结果
     */
    ToolCallResult retryToolCall(String callId, Long userId);

    // ========== 统计和分析 ==========

    /**
     * 获取工具使用统计
     *
     * @param userId 用户ID
     * @return 统计信息
     */
    Map<String, Object> getToolUsageStats(Long userId);

    /**
     * 获取工具性能统计
     *
     * @param toolId 工具ID
     * @return 性能统计
     */
    Map<String, Object> getToolPerformanceStats(String toolId);

    /**
     * 初始化系统工具
     */
    void initSystemTools();

    // ========== 内部类定义 ==========

    /**
     * 工具调用结果
     */
    class ToolCallResult {
        private boolean success;
        private Object data;
        private String message;
        private String type;
        private Map<String, Object> metadata;
        private Long executionTime;
        private String callId;

        public ToolCallResult(boolean success, Object data, String message) {
            this.success = success;
            this.data = data;
            this.message = message;
        }

        // getters and setters
        public boolean isSuccess() {
            return success;
        }

        public void setSuccess(boolean success) {
            this.success = success;
        }

        public Object getData() {
            return data;
        }

        public void setData(Object data) {
            this.data = data;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public Map<String, Object> getMetadata() {
            return metadata;
        }

        public void setMetadata(Map<String, Object> metadata) {
            this.metadata = metadata;
        }

        public Long getExecutionTime() {
            return executionTime;
        }

        public void setExecutionTime(Long executionTime) {
            this.executionTime = executionTime;
        }

        public String getCallId() {
            return callId;
        }

        public void setCallId(String callId) {
            this.callId = callId;
        }
    }

    /**
     * 工具调用上下文
     */
    class ToolCallContext {
        private String sessionId;
        private String messageId;
        private String userQuery;
        private String aiReasoning;
        private Map<String, Object> environment;

        // getters and setters
        public String getSessionId() {
            return sessionId;
        }

        public void setSessionId(String sessionId) {
            this.sessionId = sessionId;
        }

        public String getMessageId() {
            return messageId;
        }

        public void setMessageId(String messageId) {
            this.messageId = messageId;
        }

        public String getUserQuery() {
            return userQuery;
        }

        public void setUserQuery(String userQuery) {
            this.userQuery = userQuery;
        }

        public String getAiReasoning() {
            return aiReasoning;
        }

        public void setAiReasoning(String aiReasoning) {
            this.aiReasoning = aiReasoning;
        }

        public Map<String, Object> getEnvironment() {
            return environment;
        }

        public void setEnvironment(Map<String, Object> environment) {
            this.environment = environment;
        }
    }

    /**
     * 批量工具调用
     */
    class BatchToolCall {
        private String toolId;
        private Map<String, Object> parameters;
        private ToolCallContext context;

        // getters and setters
        public String getToolId() {
            return toolId;
        }

        public void setToolId(String toolId) {
            this.toolId = toolId;
        }

        public Map<String, Object> getParameters() {
            return parameters;
        }

        public void setParameters(Map<String, Object> parameters) {
            this.parameters = parameters;
        }

        public ToolCallContext getContext() {
            return context;
        }

        public void setContext(ToolCallContext context) {
            this.context = context;
        }
    }

    /**
     * 解析的工具调用
     */
    class ParsedToolCall {
        private String toolName;
        private Map<String, Object> parameters;
        private String reasoning;

        // getters and setters
        public String getToolName() {
            return toolName;
        }

        public void setToolName(String toolName) {
            this.toolName = toolName;
        }

        public Map<String, Object> getParameters() {
            return parameters;
        }

        public void setParameters(Map<String, Object> parameters) {
            this.parameters = parameters;
        }

        public String getReasoning() {
            return reasoning;
        }

        public void setReasoning(String reasoning) {
            this.reasoning = reasoning;
        }
    }

    /**
     * 参数验证结果
     */
    class ParameterValidationResult {
        private boolean valid;
        private List<String> errors;
        private Map<String, Object> normalizedParameters;

        public ParameterValidationResult(boolean valid) {
            this.valid = valid;
        }

        // getters and setters
        public boolean isValid() {
            return valid;
        }

        public void setValid(boolean valid) {
            this.valid = valid;
        }

        public List<String> getErrors() {
            return errors;
        }

        public void setErrors(List<String> errors) {
            this.errors = errors;
        }

        public Map<String, Object> getNormalizedParameters() {
            return normalizedParameters;
        }

        public void setNormalizedParameters(Map<String, Object> normalizedParameters) {
            this.normalizedParameters = normalizedParameters;
        }
    }
}
