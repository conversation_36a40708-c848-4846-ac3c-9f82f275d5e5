package org.dromara.app.service;

import jakarta.servlet.http.HttpServletResponse;
import org.dromara.app.domain.bo.UserResumeBo;
import org.dromara.app.domain.vo.UserResumeUploadVo;
import org.dromara.app.domain.vo.UserResumeVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 用户简历服务接口
 *
 * <AUTHOR>
 */
public interface IUserResumeService {

    /**
     * 查询用户简历分页列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 分页结果
     */
    TableDataInfo<UserResumeVo> queryPageList(UserResumeBo bo, PageQuery pageQuery);

    /**
     * 查询用户的所有简历列表
     *
     * @param userId 用户ID
     * @return 简历列表
     */
    List<UserResumeVo> selectUserResumeList(Long userId);

    /**
     * 根据简历ID查询简历详情
     *
     * @param resumeId 简历ID
     * @return 简历详情
     */
    UserResumeVo queryById(Long resumeId);

    /**
     * 上传用户简历文件
     *
     * @param userId   用户ID
     * @param file     简历文件
     * @param fileName 原始文件名（可选）
     * @param fileType 文件类型（可选）
     * @return 上传结果
     */
    UserResumeUploadVo uploadResume(Long userId, MultipartFile file, String fileName, String fileType);

    /**
     * 新增用户简历
     *
     * @param bo 简历信息
     * @return 新增结果
     */
    Boolean insertByBo(UserResumeBo bo);

    /**
     * 修改用户简历
     *
     * @param bo 简历信息
     * @return 修改结果
     */
    Boolean updateByBo(UserResumeBo bo);

    /**
     * 重命名简历
     *
     * @param resumeId   简历ID
     * @param resumeName 新名称
     * @return 重命名结果
     */
    Boolean renameResume(Long resumeId, String resumeName);

    /**
     * 设置默认简历
     *
     * @param userId   用户ID
     * @param resumeId 简历ID
     * @return 设置结果
     */
    Boolean setDefaultResume(Long userId, Long resumeId);

    /**
     * 取消默认简历
     *
     * @param userId   用户ID
     * @param resumeId 简历ID
     * @return 取消结果
     */
    Boolean cancelDefaultResume(Long userId, Long resumeId);

    /**
     * 校验并批量删除用户简历信息
     *
     * @param ids     主键集合
     * @param isValid 是否校验,true-删除前校验,false-不校验
     * @return 删除结果
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 下载用户简历文件
     *
     * @param resumeId 简历ID
     * @param response 响应对象
     * @throws IOException IO异常
     */
    void downloadResume(Long resumeId, HttpServletResponse response) throws IOException;

    /**
     * 获取用户的默认简历
     *
     * @param userId 用户ID
     * @return 默认简历
     */
    UserResumeVo getDefaultResume(Long userId);

    /**
     * 预览简历文件内容
     *
     * @param resumeId 简历ID
     * @return 预览内容数据（包含content和type字段）
     * @throws Exception 预览异常
     */
    Map<String, Object> previewResumeContent(Long resumeId) throws Exception;

    /**
     * 获取简历结构化预览内容
     *
     * @param resumeId 简历ID
     * @return 结构化简历内容数据
     * @throws Exception 解析异常
     */
    Map<String, Object> getStructuredResumeContent(Long resumeId) throws Exception;

    /**
     * 生成简历预览图片
     *
     * @param resumeId 简历ID
     * @return 预览图片数据（包含imageUrl和thumbnailUrl字段）
     * @throws Exception 生成异常
     */
    Map<String, Object> generateResumePreviewImage(Long resumeId) throws Exception;

}
